from tests.factories import KitchenFactory
from cataloging_service.models import Kitchen


def create_kitchen_request(name="breakfast menu", config=None):
    return {
        "name": name,
        "config": config,
    }


def test_create_kitchen(app, property, client):
    payload = create_kitchen_request()
    payload["name"] = "cantonese kitchen"
    payload["config"] = [{"printer_id": "local_printer", "ip": "***********"}]

    url = "/v1/properties/" + property.id + "/kitchens"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["kitchen_id"] != None
    assert data["name"] == payload["name"]
    assert data["config"] == payload["config"]


def test_create_kitchen_without_ip(app, property, client):
    payload = create_kitchen_request()
    payload["name"] = "cantonese kitchen"
    payload["config"] = [{"printer_id": "local_printer"}]

    url = "/v1/properties/" + property.id + "/kitchens"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["reason"] == {'config': {'0': {'ip': ['Missing data for required field.']}}}


def test_list_kitchen(app, property, kitchen_repository, client):
    kitchen = kitchen_repository.persist(Kitchen(name="bar kitchen", property_id=property.id,
                                                 config=[{"printer_id": "printer 1"}]))
    kitchen2 = kitchen_repository.persist(Kitchen(name="bar kitchen", property_id=property.id,
                                                  config=[{"printer_id": "printer 1"}]))
    url = "/v1/properties/" + property.id + "/kitchens/"
    rv = client.get(url)
    data = rv.get_json()

    assert any(m["kitchen_id"] == kitchen.id for m in data)
    assert any(m["name"] == kitchen.name for m in data)
    assert any(m["config"] == kitchen.config for m in data)

    assert any(m["kitchen_id"] == kitchen2.id for m in data)
    assert any(m["name"] == kitchen2.name for m in data)
    assert any(m["config"] == kitchen2.config for m in data)


def test_get_kitchen(app, property, kitchen_repository, client):
    kitchen = kitchen_repository.persist(Kitchen(name="bar kitchen", property_id=property.id,
                                                 config=[{"printer_id": "printer 1"}]))
    url = "/v1/properties/" + property.id + "/kitchens/" + str(kitchen.id)
    rv = client.get(url)
    data = rv.get_json()

    assert data["kitchen_id"] == kitchen.id
    assert data["name"] == kitchen.name
    assert data["config"] == kitchen.config


def test_update_kitchen(app, property, kitchen_repository, client):
    kitchen = kitchen_repository.persist(Kitchen(name="bar kitchen", property_id=property.id,
                                                 config=[{"printer_id": "printer 1"}]))
    url = "/v1/properties/" + property.id + "/kitchens/" + str(kitchen.id)
    payload = {"name": "indian kitchen", "config": [{"printer_id": "local_printer", "ip": "***********"}]}
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["kitchen_id"] == kitchen.id
    assert data["name"] == payload["name"]
    assert data["config"] == payload["config"]


def test_update_kitchen_without_ip(app, property, kitchen_repository, client):
    kitchen = kitchen_repository.persist(Kitchen(name="bar kitchen", property_id=property.id,
                                                 config=[{"printer_id": "printer 1"}]))
    url = "/v1/properties/" + property.id + "/kitchens/" + str(kitchen.id)
    payload = {"name": "indian kitchen", "config": [{"printer_id": "local_printer"}]}
    rv = client.patch(url, json=payload)
    data = rv.get_json()
    print(data)
    assert data["reason"] == {'config': {'0': {'ip': ['Missing data for required field.']}}}


def test_soft_delete_kitchen(app, property, kitchen_repository, client):
    kitchen = kitchen_repository.persist(Kitchen(name="indian kitchen", property_id=property.id,
                                                 config=[{"printer_id": "printer 1"}]))
    url = "/v1/properties/" + property.id + "/kitchens/" + str(kitchen.id)
    payload = {"name": "indian kitchen", "config": [{"printer_id": "local_printer"}]}
    rv = client.delete(url)

    assert rv.status_code == 204
