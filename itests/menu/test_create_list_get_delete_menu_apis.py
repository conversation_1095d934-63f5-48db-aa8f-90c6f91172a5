import pytest
import datetime
from freezegun import freeze_time

from tests.factories import MenuFactory, MenuTimingFactory
from cataloging_service.constants import model_choices


def create_menu_request(name="breakfast menu", menu_types=["delivery"], description="Best food menu", display_name="top"):
    return {
        "name": name,
        "menu_types": menu_types,
        "description": description,
        "display_name": display_name,
        "menu_timings": [
            {
                "days": [
                    "THU"
                ],
                "end_time": "21:00:00",
                "start_time": "10:00:00"
            },
            {
                "days": [
                    "WED"
                ],
                "end_time": "23:00:00",
                "start_time": "20:00:00"
            }
        ]
    }


def test_create_menu_without_menu_timings(app, seller, client):
    payload = create_menu_request()
    payload["name"] = "Sunday breakfast delight"
    payload["code"] = "menu_without_timings"
    payload.pop("menu_timings")
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] != None
    assert data["name"] == payload["name"]
    assert data["menu_types"] == payload["menu_types"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]


def test_create_menu_without_name(app, seller, client):
    payload = create_menu_request()
    payload.pop("name")
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)

    assert rv.status_code == 400


def test_create_menu_without_menu_types(app, seller, client):
    payload = create_menu_request()
    payload.pop("menu_types")
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)

    assert rv.status_code == 400


def test_create_menu_without_display_name(app, seller, client):
    payload = create_menu_request(name="menu without display name")
    payload["code"] = "menu_with_out_displayname"
    payload.pop("display_name")
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] != None
    assert data["name"] == payload["name"]
    assert data["menu_types"] == payload["menu_types"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == None


def test_create_menu_with_menu_timings(app, seller, client):
    payload = create_menu_request(name="lunch menu", menu_types=["restaurant"],
                                  display_name="WEEKDAY lunch menu", description="sumptuous food")
    del payload["menu_timings"][-1]
    payload["code"] = "menu_with_menu_timings"
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] != None
    assert data["name"] == payload["name"]
    assert data["menu_types"] == payload["menu_types"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert len(data["menu_timings"]) == 1
    assert data["menu_timings"][0]["menu_timing_id"] != None
    assert data["menu_timings"][0]["days"] == ["THU"]
    assert data["menu_timings"][0]["end_time"] == "21:00:00"
    assert data["menu_timings"][0]["start_time"] == "10:00:00"


def test_create_menu_with_invalid_menu_types(app, seller, client):
    payload = create_menu_request(name="lunch menu", menu_types=["Invalid menu type"],
                                  display_name="WEEKDAY lunch menu", description="sumptuous food")
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()
    assert data["reason"]["menu_types"]["0"] == ["Invalid value."]


def test_create_menu_with_multiple_menu_timings(app, seller, client):
    payload = create_menu_request()
    payload["name"] = "brunch menu"
    payload["code"] = "uniqlomenu"
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] != None
    assert data["name"] == payload["name"]
    assert data["menu_types"] == payload["menu_types"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert len(data["menu_timings"]) == 2
    assert data["menu_timings"][0]["menu_timing_id"] != None
    assert data["menu_timings"][0]["days"] == ["THU"]
    assert data["menu_timings"][0]["end_time"] == "21:00:00"
    assert data["menu_timings"][0]["start_time"] == "10:00:00"
    assert data["menu_timings"][1]["menu_timing_id"] != None
    assert data["menu_timings"][1]["days"] == ["WED"]
    assert data["menu_timings"][1]["end_time"] == "23:00:00"
    assert data["menu_timings"][1]["start_time"] == "20:00:00"


def test_create_menu_with_invalid_days_in_menu_timings(app, seller, client):
    payload = create_menu_request()
    payload["name"] = "brunch menu"
    payload["menu_timings"] = [{"days": ["SUN", "MONDAY"], "start_time": "10:00:00", "end_time": "12:00:00"}]
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()
    assert data['reason']["menu_timings"]["0"]["days"] == ['days field has wrong format']


def test_create_menu_with_multiple_menu_types(app, seller, client):
    payload = create_menu_request(menu_types=["delivery", "restaurant"])
    payload["name"] = "lite brunch menu"
    payload["code"] = "menu_with_multiple_types"
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()
    assert data["menu_id"] != None
    assert data["name"] == payload["name"]
    assert data["menu_types"] == payload["menu_types"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]


def test_create_menu_with_code_that_already_exists(app, menu_repository, seller, client):
    menu_repository.persist(MenuFactory(name="freshwater fish menu", code="freshwater", seller_id=str(seller.id)))

    payload = create_menu_request()
    payload["name"] = "freshwater fish menu"
    payload["code"] = "freshwater"
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()
    assert data['reason'] == {'code': ['Menu code is duplicate']}

def test_create_menu_with_menu_name_that_already_exists_but_with_different_seller(app, alternate_seller, menu_repository, seller, client):
    menu_repository.persist(MenuFactory(name="saline fish menu", seller_id=str(seller.id)))

    payload = create_menu_request()
    payload["name"] = "freshwater fish menu"
    payload["code"] = "freshmenu"
    url = "/v1/sellers/" + str(alternate_seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()
    assert data["name"] == "freshwater fish menu"
    assert data["menu_id"] != None


def test_create_menu_with_menu_code(app, seller, menu_repository, client):
    payload = create_menu_request()
    payload.update({ "code": "menu-1" })
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert rv.status_code == 201
    assert data["menu_id"] != None
    assert data["name"] == payload["name"]
    assert data["menu_types"] == payload["menu_types"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["code"] == payload["code"]


def test_create_menu_with_duplicate_menu_code(app, seller, menu_repository, client):
    menu_repository.persist(MenuFactory(name="saline fish menu", seller_id=str(seller.id), code="menu-code"))
    payload = create_menu_request()
    payload.update({ "code": "menu-code" })
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)

    assert rv.status_code == 400

def test_create_menu_with_null_menu_code(app, seller, menu_repository, client):
    payload = create_menu_request()
    payload.update({ "code": None })
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert rv.status_code == 400
    assert data["reason"] == {"code": ["Field may not be null."]}


def test_list_all_menus(app, seller, menu_repository, client):
    menus = menu_repository.persist_all([MenuFactory(name="super saiyan lunch menu", seller_id=str(
        seller.id)), MenuFactory(name="super saiyan dinner menu", seller_id=str(seller.id))])
    url = "/v1/sellers/" + str(seller.id) + "/menus"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) >= 2
    assert any(menu["name"] == menus[0].name for menu in data)
    assert any(menu["description"] == menus[0].description for menu in data)
    assert any(menu["menu_types"] == menus[0].menu_types for menu in data)
    assert any(menu["display_name"] == menus[0].display_name for menu in data)
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_type" "display_name"].sort()


def test_get_all_menus_with_categories_and_item_counts(app, seller, menu_repository, client):
    menus = menu_repository.persist_all([MenuFactory(name="Lunch Menu", seller_id=str(
        seller.id)), MenuFactory(name="Breakfast Menu", seller_id=str(seller.id))])
    url = "/v1/sellers/" + str(seller.id) + "/menus/?compressed=False"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) >= 2
    assert any(menu["name"] == menus[0].name for menu in data)
    assert any(menu["description"] == menus[0].description for menu in data)
    assert any(menu["display_name"] == menus[0].display_name for menu in data)
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_type" "display_name", "menu_categories", "menu_items"].sort()


def test_get_menu(app, seller, menu_repository, client):
    menu = menu_repository.persist(MenuFactory(name="french cuisine menu", seller_id=str(seller.id)))
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.get(url)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["description"] == menu.description
    assert data["menu_types"] == menu.menu_types
    assert data["display_name"] == menu.display_name
    assert data["menu_timings"][0]["menu_timing_id"] != None
    assert data["menu_timings"][0]["days"] == ["SUN", "MON"]
    assert data["menu_timings"][0]["end_time"] == "22:00:00"
    assert data["menu_timings"][0]["start_time"] == "20:00:00"
    assert list(data.keys()).sort() == ["name", "menu_id", "description",
                                        "menu_types" "display_name", "menu_timings"].sort()


def test_search_all_menus_by_name(app, seller, menu_repository, client):
    menus = menu_repository.persist_all([MenuFactory(name="new york lunch menu", display_name="Hello's kitchen", seller_id=str(
        seller.id)), MenuFactory(name="french saiyan dinner menu", seller_id=str(seller.id))])
    url = "/v1/sellers/" + str(seller.id) + "/menus?name=new york lunch menu"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) == 1
    assert data[0]["name"] == "new york lunch menu"
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_types" "display_name"].sort()

    url = "/v1/sellers/" + str(seller.id) + "/menus?name=french saiyan"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) == 1
    assert data[0]["name"] == "french saiyan dinner menu"
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_types" "display_name"].sort()


def test_search_all_menus_by_display_name(app, seller, menu_repository, client):
    menus = menu_repository.persist_all([MenuFactory(name="devil york lunch menu", display_name="Hell's kitchen", seller_id=str(
        seller.id)), MenuFactory(name="dragon saiyan dinner menu", display_name="best continental", seller_id=str(seller.id))])
    url = "/v1/sellers/" + str(seller.id) + "/menus?name=Hell's kitchen"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) == 1
    assert data[0]["name"] == "devil york lunch menu"
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_types" "display_name"].sort()

    url = "/v1/sellers/" + str(seller.id) + "/menus?name=best continental"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) == 1
    assert data[0]["name"] == "dragon saiyan dinner menu"
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_types" "display_name"].sort()


def test_search_all_menus_by_menu_type(app, seller, menu_repository, client):
    menus = menu_repository.persist_all([MenuFactory(name="boston  lunch menu", menu_types=[model_choices.MenuTypeChoices.TAKE_AWAY.value], seller_id=str(
        seller.id)), MenuFactory(name="la  dinner menu", menu_types=[model_choices.MenuTypeChoices.ROOM_SERVICE.value], seller_id=str(seller.id))])

    url = "/v1/sellers/" + str(seller.id) + "/menus?menu_type=take_away"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) == 1
    assert data[0]["menu_types"] == ["take_away"]
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_types" "display_name"].sort()

    url = "/v1/sellers/" + str(seller.id) + "/menus?menu_type=room_service"
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) == 1
    assert data[0]["menu_types"] == ["room_service"]
    assert list(data[0].keys()).sort() == ["name", "menu_id", "description", "menu_types" "display_name"].sort()


def test_create_menu_with_invalid_seller(app, alternate_seller, menu_repository, seller, client):

    payload = create_menu_request()
    payload["name"] = "freshwater fish menu"
    payload["code"] = "freshfishmenu"
    url = "/v1/sellers/" + str(10111) + "/menus"
    rv = client.post(url, json=payload)
    data = rv.get_json()
    assert data['reason']['name'] == ['Seller ID is not valid']


def test_delete_menu(app,  menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="menu to be deleted", seller_id=str(
        seller.id)))
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.delete(url)
    assert rv.status_code == 204

    rv = client.get(url)

    data = rv.get_json()
    assert data == {}


@freeze_time("2021-03-27 01:00:02")  # sunday 01:00 AM time
def test_get_active_menus_with_overnight_timings(app, menu_repository, seller, client):
    start_time = datetime.datetime.strptime("19:00:00", '%H:%M:%S').time()
    end_time = datetime.datetime.strptime("02:00:00", '%H:%M:%S').time()
    menu = menu_repository.persist(MenuFactory(name="menu with custom timing", seller_id=str(seller.id), menu_timings=[
                                   MenuTimingFactory(days=b'["FRI"]', start_time=start_time, end_time=end_time)]))
    menu_repository.persist(MenuFactory(name="menu with custom timing 1", seller_id=str(seller.id), menu_timings=[
        MenuTimingFactory(days=b'["SAT"]', start_time=start_time, end_time=end_time)]))
    url = "/v1/sellers/" + str(seller.id) + "/menus?is_active=true"
    rv = client.get(url)

    data = rv.get_json()

    assert len(data) > 0
    assert any(m["menu_id"] == menu.id for m in data)


@freeze_time("2021-03-28 22:35:02")  # sunday 10:35 PM time
def test_get_active_menus_with_non_overnight_timings(app, menu_repository, seller, client):
    start_time = datetime.datetime.strptime("21:00:00", '%H:%M:%S').time()
    end_time = datetime.datetime.strptime("23:00:00", '%H:%M:%S').time()
    menu1 = menu_repository.persist(MenuFactory(name="menu with custom timing #1", seller_id=str(seller.id), menu_timings=[
        MenuTimingFactory(days=b'["SUN"]', start_time=start_time, end_time=end_time)]))
    menu3 = menu_repository.persist(MenuFactory(name="menu with custom timing #2", seller_id=str(seller.id), menu_timings=[
        MenuTimingFactory(days=b'["SAT"]', start_time=start_time, end_time=end_time)]))
    menu2 = menu_repository.persist(MenuFactory(name="menu with custom timing #3", seller_id=str(seller.id), menu_timings=[
        MenuTimingFactory(days=b'["SUN"]', start_time=start_time, end_time=end_time)]))
    url = "/v1/sellers/" + str(seller.id) + "/menus?is_active=true"
    rv = client.get(url)

    data = rv.get_json()
    assert len(data) > 0
    assert any(m["menu_id"] == menu1.id for m in data)
    assert any(m["menu_id"] == menu2.id for m in data)
    assert all(m["menu_id"] != menu3.id for m in data)
