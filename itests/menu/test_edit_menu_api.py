import pytest
import json
from tests.factories import ComboFactory, ItemFactory, ItemVariantFactory, MenuFactory, SideItemFactory, MenuCategoryFactory, ItemCustomisationFactory, ItemVariantFactory, VariantFactory, VariantGroupFactory
from cataloging_service.models import ComboItem


def get_edit_menu_request(name="luncheon", menu_types=["ROOM_SERVICE"], description="Sunday all day food menu",
                          display_name="best", menu_timings=None, menu_categories=None, ):
    return {
        "name": name,
        "menu_types": menu_types,
        "description": description,
        "display_name": display_name,
        "menu_timings": [
            {
                "days": ["THU"],
                "end_time": "21:00:00",
                "start_time": "10:00:00"
            },
            {
                "days": ["WED"],
                "end_time": "23:00:00",
                "start_time": "20:00:00"
            }
        ],
        "menu_categories": [
            {"name": "Dinner"},
            {"name": "Lunch"}
        ]
    }


def test_edit_menu_with_menu_name(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="italian cuisine menu", seller_id=str(seller.id)))
    payload = {"name": "Sushi menu", "code": "italiancuisine"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == payload["name"]
    assert data["menu_types"] == menu.menu_types
    assert data["description"] == menu.description
    assert data["display_name"] == menu.display_name
    assert data["menu_timings"][0]["menu_timing_id"] == menu.menu_timings[0].id
    assert data["menu_categories"][0]["name"] == menu.menu_categories[0].name


def test_edit_menu_display_name(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="german cuisine menu", seller_id=str(seller.id)))
    display_name = "breakfast menu"
    payload = {"display_name": display_name, "code": "german"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["menu_types"] == menu.menu_types
    assert data["description"] == menu.description
    assert data["display_name"] == display_name


def test_edit_menu_with_menu_types(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="japanese cuisine menu", seller_id=str(seller.id)))
    payload = {"menu_types": ["delivery"], "code": "japanesecuisine"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["menu_types"] == payload["menu_types"]
    assert data["description"] == menu.description
    assert data["display_name"] == menu.display_name


def test_edit_menu_with_description(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="indian cuisine menu", code="indian", seller_id=str(seller.id)))
    payload = {"description": "Indian cuisine menu", "code": "indian"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["menu_types"] == menu.menu_types
    assert data["description"] == payload["description"]
    assert data["display_name"] == menu.display_name


def test_edit_menu_with_new_menu_code(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="indian cuisine menu", seller_id=str(seller.id), code=None))
    payload = {"code": "unq-menu-code"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["description"] == menu.description
    assert data["display_name"] == menu.display_name
    assert data["code"] == payload["code"]


def test_edit_menu_with_duplicate_menu_code(app, menu_repository, seller, client):
    menu_repository.persist(MenuFactory(name="southindian cuisine menu",
                            seller_id=str(seller.id), code="south-menu-code"))
    menu = menu_repository.persist(MenuFactory(name="indian cuisine menu", seller_id=str(seller.id), code="menu-code"))
    payload = {"code": "south-menu-code"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert rv.status_code == 400
    assert data['reason'] == {'code': ['Menu code is duplicate']}


def test_edit_menu_with_menu_categories(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="konkan cuisine menu", seller_id=str(seller.id)))
    payload = {"menu_categories": [{"name": "Fish"}, {"name": "Soups"}], "code": "konkan"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["menu_types"] == menu.menu_types
    assert data["description"] == menu.description
    assert data["display_name"] == menu.display_name
    assert len(data["menu_categories"]) == 2
    assert data["menu_categories"][0]["menu_category_id"] != None
    assert data["menu_categories"][0]["name"] == payload["menu_categories"][0]["name"]
    assert data["menu_categories"][0]["display_order"] == 0
    assert data["menu_categories"][1]["menu_category_id"] != None
    assert data["menu_categories"][1]["name"] == payload["menu_categories"][1]["name"]
    assert data["menu_categories"][1]["display_order"] == 1


def test_edit_menu_with_new_menu_timings(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="goan cuisine menu", seller_id=str(seller.id)))
    payload = {"menu_timings": [{"days": ["SUN"], "end_time": "23:00:00", "start_time": "09:00:00"}], "code":"goan"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["code"] == "goan"
    assert data["menu_types"] == menu.menu_types
    assert data["description"] == menu.description
    assert data["display_name"] == menu.display_name
    assert len(data["menu_timings"]) == 1
    assert data["menu_timings"][0]["menu_timing_id"] != None
    assert data["menu_timings"][0]["days"] == ["SUN"]
    assert data["menu_timings"][0]["start_time"] == "09:00:00"
    assert data["menu_timings"][0]["end_time"] == "23:00:00"


def test_edit_menu_with_no_change_in_existing_menu_timings(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="kannada cuisine menu",
                                   code="kannadacuisine", seller_id=str(seller.id)))

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    get_data = client.get(url).get_json()
    rv = client.patch(url, json={"code": "kannadacuisine"})
    data = rv.get_json()

    assert data["menu_id"] == menu.id
    assert data["name"] == menu.name
    assert data["menu_types"] == menu.menu_types
    assert data["description"] == menu.description
    assert data["display_name"] == menu.display_name
    assert len(data["menu_timings"]) == 2
    assert data["menu_timings"][0]["menu_timing_id"] == get_data["menu_timings"][0]["menu_timing_id"]
    assert data["menu_timings"][0]["days"] == get_data["menu_timings"][0]["days"]
    assert data["menu_timings"][0]["start_time"] == get_data["menu_timings"][0]["start_time"]
    assert data["menu_timings"][0]["end_time"] == get_data["menu_timings"][0]["end_time"]
    assert data["menu_timings"][1]["menu_timing_id"] == get_data["menu_timings"][1]["menu_timing_id"]
    assert data["menu_timings"][1]["days"] == get_data["menu_timings"][1]["days"]
    assert data["menu_timings"][1]["start_time"] == get_data["menu_timings"][1]["start_time"]
    assert data["menu_timings"][1]["end_time"] == get_data["menu_timings"][1]["end_time"]


def test_edit_menu_with_new_menu_combos(app, seller, menu_repository, sku_category,  combo_repository, client):
    combo1 = combo_repository.persist(ComboFactory(name="Pizza + garlic breadsticks combo",
                                                   sku_category_code=sku_category.code, prep_time="00:30:00"))
    combo2 = combo_repository.persist(ComboFactory(name="Wings + Soup combo",
                                                   sku_category_code=sku_category.code,  prep_time="00:15:00"))
    menu = menu_repository.persist(MenuFactory(name="keralan cuisine menu", seller_id=str(seller.id)))
    payload = {"code": "kerala cuisine", }
    payload["menu_combos"] = [{"combo_id": combo1.id}, {"combo_id": combo2.id, "sold_out": True}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_combos"]) == 2
    assert data["menu_combos"][0]["menu_combo_id"] != None
    assert data["menu_combos"][0]["combo"]["name"] == "Pizza + garlic breadsticks combo"
    assert data["menu_combos"][0]["combo"]["combo_id"] == combo1.id
    assert data["menu_combos"][0]["sold_out"] == False
    assert data["menu_combos"][1]["menu_combo_id"] != None
    assert data["menu_combos"][1]["combo"]["name"] == "Wings + Soup combo"
    assert data["menu_combos"][1]["combo"]["combo_id"] == combo2.id
    assert data["menu_combos"][1]["sold_out"] == True


def test_edit_menu_with_menu_combos_not_set(app, menu_repository, combo_repository, sku_category, seller, client):
    combo1 = combo_repository.persist(ComboFactory(name="Pasta + garlic breadsticks combo",
                                                   sku_category_code=sku_category.code, prep_time="00:30:00"))
    combo2 = combo_repository.persist(ComboFactory(name="winglets + Soup combo",
                                                   sku_category_code=sku_category.code,  prep_time="00:15:00"))
    menu = menu_repository.persist(MenuFactory(name="tamil cuisine menu", seller_id=str(seller.id)))

    payload = {"code": "tamil cuisine", }
    payload["menu_combos"] = [{"combo_id": combo1.id}, {"combo_id": combo2.id, "sold_out": True}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    client.patch(url, json=payload)

    payload = {"code": "tamil cuisine", }
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_combos"][0]["menu_combo_id"] != None
    assert data["menu_combos"][0]["combo"]["name"] == "Pasta + garlic breadsticks combo"
    assert data["menu_combos"][0]["sold_out"] == False
    assert data["menu_combos"][1]["menu_combo_id"] != None
    assert data["menu_combos"][1]["combo"]["name"] == "winglets + Soup combo"
    assert data["menu_combos"][1]["sold_out"] == True


def test_edit_menu_with_invalid_menu_combo_request(app, menu_repository, seller, sku_category, combo_repository, client):
    combo1 = ComboFactory(name="Pizza + coffee breadsticks combo",
                          sku_category_code=sku_category.code,  prep_time="00:30:00")
    combo2 = ComboFactory(name="Wings + tea combo", sku_category_code=sku_category.code,  prep_time="00:15:00")

    payload = {}
    payload["menu_combos"] = [{"combo_id": combo1.id}, {"combo_id": combo2.id, "sold_out": True}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/1"
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert rv.status_code == 400
    assert data["reason"]["menu_combos"]["0"]["combo_id"] == ["Field may not be null."]


def test_edit_menu_with_new_menu_items(app, seller, menu_repository, sku_category, item_repository, client):
    item1 = item_repository.persist(ItemFactory(name="lasagna", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item2 = item_repository.persist(ItemFactory(name="ravioli", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    menu = menu_repository.persist(MenuFactory(name="andhran cuisine menu", seller_id=str(seller.id)))

    payload = {"code": "andhran"}
    payload["menu_items"] = [{"item_id": item1.id}, {"item_id": item2.id, "sold_out": True}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_items"]) == 2
    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["name"] == "lasagna"
    assert data["menu_items"][0]["item"]["item_id"] == item1.id
    assert data["menu_items"][0]["sold_out"] == False
    assert data["menu_items"][1]["menu_item_id"] != None
    assert data["menu_items"][1]["item"]["name"] == "ravioli"
    assert data["menu_items"][1]["item"]["item_id"] == item2.id
    assert data["menu_items"][1]["sold_out"] == True


def test_edit_menu_with_available_and_unavailable_menu_items(app, seller, menu_repository, sku_category, kitchen,
                                                             item_repository, client):
    url = "/v1/sellers/" + str(seller.id) + "/items"
    item_payload = {"name": "lasssi", "prep_time": "00:15:00", "sold_out": False,  "code": "lasssi",
                    "sku_category_code": sku_category.code, "use_as_side": False, "kitchen_id": kitchen.id,
                    "food_type": "vegetarian", "contains_alcohol": False}
    rv = client.post(url, json=item_payload)
    item_data = rv.get_json()
    menu = menu_repository.persist(MenuFactory(name="andean cuisine menu", seller_id=str(seller.id)))

    payload = {"code": "andean"}
    payload["menu_items"] = [{"item_id": item_data["item_id"]}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_items"]) == 1
    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["name"] == "lasssi"
    assert data["menu_items"][0]["item"]["item_id"] == item_data["item_id"]
    assert data["menu_items"][0]["sold_out"] == False

    url = "/v1/sellers/" + str(seller.id) + "/items/" + str(item_data["item_id"])
    rv = client.patch(url, json={"active": False, "code":"andean"})
    item_data = rv.get_json()

    assert item_data["active"] == False


def test_edit_menu_with_available_and_unavailable_menu_combos(app, seller, menu_repository, combo_repository, kitchen,
                                                              sku_category, item_repository, client):
    url = "/v1/sellers/" + str(seller.id) + "/items"
    item_payload = {"name": "ras gulla", "prep_time": "00:15:00", "sold_out": False,  "code": "rasgulla",
                    "sku_category_code": sku_category.code, "use_as_side": False, "food_type": "vegan",
                    "kitchen_id": kitchen.id, "contains_alcohol": False}
    rv = client.post(url, json=item_payload)
    item_data = rv.get_json()
    menu = menu_repository.persist(MenuFactory(name="atlassian cuisine menu", seller_id=str(seller.id)))

    combo1 = combo_repository.persist(ComboFactory(name="atlassian red wings + crushers",
                                                   sku_category_code=sku_category.code,
                                                   combo_items=[ComboItem(item_id=item_data["item_id"])],
                                                   seller_id=seller.id))

    payload = {"menu_combos": [{"combo_id": combo1.id}],"code": "sweetmenu"}

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_combos"]) == 1
    assert data["menu_combos"][0]["menu_combo_id"] != None
    assert data["menu_combos"][0]["combo"]["name"] == "atlassian red wings + crushers"
    assert data["menu_combos"][0]["sold_out"] == False

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo1.id)
    rv = client.patch(url, json={"active": False, "code":"sweetmenu"})
    item_data = rv.get_json()

    assert item_data["active"] == False

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo1.id)
    rv = client.get(url)
    data = rv.get_json()

    assert data["active"] == False


def test_edit_menu_with_new_menu_items_with_item_variants_side_items_and_customisations(app, seller, menu_repository, sku_category, item_repository, client):
    item1 = item_repository.persist(ItemFactory(name="bbq chicken pizza", seller_id=seller.id,
                                                sku_category_code=sku_category.code))

    item2 = item_repository.persist(ItemFactory(name="7up", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    variant_group = item_repository.persist(VariantGroupFactory(seller_id=seller.id))
    variant = item_repository.persist(VariantFactory(name="Test Variant", variant_group=variant_group, display_order=1))
    item_variant1 = item_repository.persist(ItemVariantFactory(
        name="Crust", sku_category_code=sku_category.code, item_id=item1.id))
    item_customisation = item_repository.persist(ItemCustomisationFactory(
        item_id=item1.id, item_variant_id=item_variant1.id, display_order=0, delta_price=120, variant_id=variant.id))
    side_item = item_repository.persist(SideItemFactory(item_id=item1.id, side_item_id=item2.id))

    menu = menu_repository.persist(MenuFactory(name="menu item cuisine menu", seller_id=str(seller.id)))

    # adding menu_item for the first_item
    payload = {"code": "bbqmenu"}
    payload["menu_items"] = [{"item_id": item1.id}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_items"]) == 1
    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["name"] == "bbq chicken pizza"
    assert data["menu_items"][0]["item"]["item_id"] == item1.id
    assert data["menu_items"][0]["sold_out"] == False

    assert len(data["menu_items"][0]["menu_item_item_customisations"]) == 1
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id

    assert len(data["menu_items"][0]["menu_item_item_variants"]) == 1
    assert data["menu_items"][0]["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert data["menu_items"][0]["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id

    assert len(data["menu_items"][0]["menu_item_side_items"]) == 1
    assert data["menu_items"][0]["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert data["menu_items"][0]["menu_item_side_items"][0]["side_item_id"] == side_item.id

    # unsetting all menu_item_item_customisations, menu_item_item_variants and menu_item_side_items
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id) + "/menu-items/" + \
        str(data["menu_items"][0]["menu_item_id"])
    payload = {"item_id": item1.id, "menu_item_item_customisations": [],
               "menu_item_item_variants": [], "menu_item_side_items": []}
    client.patch(url, json=payload)

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.get(url)
    data = rv.get_json()

    assert len(data["menu_items"]) == 1
    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["name"] == "bbq chicken pizza"
    assert data["menu_items"][0]["item"]["item_id"] == item1.id
    assert data["menu_items"][0]["sold_out"] == False

    assert len(data["menu_items"][0]["menu_item_item_customisations"]) == 0
    assert len(data["menu_items"][0]["menu_item_item_variants"]) == 0
    assert len(data["menu_items"][0]["menu_item_side_items"]) == 0

    # setting menu_item_item_variants, menu_item_item_customisations and menu_item_side_items
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id) + "/menu-items/" + \
        str(data["menu_items"][0]["menu_item_id"])
    payload = {"item_id": item1.id, "menu_item_item_customisations": [{"item_customisation_id": item_customisation.id}],
               "menu_item_item_variants": [{"item_variant_id": item_variant1.id}],
               "menu_item_side_items": [{"side_item_id": side_item.id}]}
    client.patch(url, json=payload)

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.get(url)
    data = rv.get_json()

    assert len(data["menu_items"][0]["menu_item_item_customisations"]) == 1
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id

    assert len(data["menu_items"][0]["menu_item_item_variants"]) == 1
    assert data["menu_items"][0]["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert data["menu_items"][0]["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id

    assert len(data["menu_items"][0]["menu_item_side_items"]) == 1
    assert data["menu_items"][0]["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert data["menu_items"][0]["menu_item_side_items"][0]["side_item_id"] == side_item.id


def test_edit_menu_set_unset_and_reset_menu_items_with_item_variants_side_items_and_item_customisations(app, seller, menu_repository, sku_category, item_repository, client):
    item1 = item_repository.persist(ItemFactory(name="bbq peri peri chicken pizza", seller_id=seller.id,
                                                sku_category_code=sku_category.code))

    item2 = item_repository.persist(ItemFactory(name="fanta", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item3 = item_repository.persist(ItemFactory(name="meatlovers pizza", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    variant_group = item_repository.persist(VariantGroupFactory(seller_id=seller.id))
    variant = item_repository.persist(VariantFactory(name="Test Variant", variant_group=variant_group, display_order=1))
    item_variant1 = item_repository.persist(ItemVariantFactory(
        name="Crust", sku_category_code=sku_category.code, item_id=item1.id))
    item_customisation = item_repository.persist(ItemCustomisationFactory(
        item_id=item1.id, item_variant_id=item_variant1.id, display_order=0, delta_price=120, variant_id=variant.id))
    side_item = item_repository.persist(SideItemFactory(item_id=item1.id, side_item_id=item2.id))

    item_variant2 = item_repository.persist(ItemVariantFactory(
        name="Crust", sku_category_code=sku_category.code, item_id=item3.id))
    item_customisation2 = item_repository.persist(ItemCustomisationFactory(
        item_id=item3.id, item_variant_id=item_variant2.id, display_order=0, delta_price=120, variant_id=variant.id))
    side_item2 = item_repository.persist(SideItemFactory(item_id=item3.id, side_item_id=item2.id))

    menu = menu_repository.persist(MenuFactory(name="menu combo cuisine menu", seller_id=str(seller.id)))

    # adding menu_item for the first_item
    payload = {"code": "pizzaitalianmenu"}
    payload["menu_items"] = [{"item_id": item1.id}, {"item_id": item3.id}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_items"]) == 2
    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["name"] == item1.name
    assert data["menu_items"][0]["item"]["item_id"] == item1.id
    assert data["menu_items"][0]["sold_out"] == False

    assert len(data["menu_items"][0]["menu_item_item_customisations"]) == 1
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id

    assert len(data["menu_items"][0]["menu_item_item_variants"]) == 1
    assert data["menu_items"][0]["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert data["menu_items"][0]["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id

    assert len(data["menu_items"][0]["menu_item_side_items"]) == 1
    assert data["menu_items"][0]["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert data["menu_items"][0]["menu_item_side_items"][0]["side_item_id"] == side_item.id

    assert len(data["menu_items"][1]["menu_item_item_customisations"]) == 1
    assert data["menu_items"][1]["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert data["menu_items"][1]["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation2.id

    assert len(data["menu_items"][1]["menu_item_item_variants"]) == 1
    assert data["menu_items"][1]["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert data["menu_items"][1]["menu_item_item_variants"][0]["item_variant_id"] == item_variant2.id

    assert len(data["menu_items"][1]["menu_item_side_items"]) == 1
    assert data["menu_items"][1]["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert data["menu_items"][1]["menu_item_side_items"][0]["side_item_id"] == side_item2.id

    payload = {"menu_items": [], "code": "pizzaitalian"}

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()
    assert len(data["menu_items"]) == 0

    payload["menu_items"] = [{"item_id": item1.id}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_items"]) == 1
    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["name"] == item1.name
    assert data["menu_items"][0]["item"]["item_id"] == item1.id
    assert data["menu_items"][0]["sold_out"] == False

    assert len(data["menu_items"][0]["menu_item_item_customisations"]) == 1
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert data["menu_items"][0]["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id

    assert len(data["menu_items"][0]["menu_item_item_variants"]) == 1
    assert data["menu_items"][0]["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert data["menu_items"][0]["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id

    assert len(data["menu_items"][0]["menu_item_side_items"]) == 1
    assert data["menu_items"][0]["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert data["menu_items"][0]["menu_item_side_items"][0]["side_item_id"] == side_item.id


def test_edit_menu_with_menu_items_not_set(app, seller, menu_repository, sku_category, item_repository, client):
    payload = {}
    item1 = item_repository.persist(ItemFactory(name="spaghetti", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item2 = item_repository.persist(ItemFactory(name="raviolo", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    menu = menu_repository.persist(MenuFactory(name="orissan cuisine menu", seller_id=str(seller.id)))
    payload = {"code": "orissian"}
    payload["menu_items"] = [{"item_id": item1.id}, {"item_id": item2.id, "sold_out": True}]
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    client.patch(url, json=payload)

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json={"code": "orissian"})
    data = rv.get_json()

    assert len(data["menu_items"]) == 2
    assert data["code"] == "orissian"
    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["name"] == "spaghetti"
    assert data["menu_items"][0]["sold_out"] == False
    assert data["menu_items"][1]["menu_item_id"] != None
    assert data["menu_items"][1]["item"]["name"] == "raviolo"
    assert data["menu_items"][1]["sold_out"] == True


def test_edit_menu_with_invalid_menu_item_request(app, menu_repository, seller, client):
    item1 = ItemFactory(name="top ramen")
    item2 = ItemFactory(name="ramen")

    payload = {}
    payload["menu_items"] = [{"item_id": item1.id}, {"item_id": item2.id, "sold_out": True}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/1"
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert rv.status_code == 400
    assert data["reason"]["menu_items"]["0"]["item_id"] == ["Field may not be null."]
    assert data["reason"]["menu_items"]["1"]["item_id"] == ["Field may not be null."]


def test_edit_menu_with_new_menu_item_categories(app, menu_repository, item_repository, sku_category, seller, client):
    item1 = item_repository.persist(ItemFactory(name="vermicelli", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item2 = item_repository.persist(ItemFactory(name="antipasti", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    menu = menu_repository.persist(MenuFactory(name="maharashtrian cuisine menu", seller_id=str(seller.id)))

    payload = {"menu_items": [{"item_id": item1.id}, {"item_id": item2.id}], "code": "plainitalianpasta"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    payload = {"code": "plainitalianpasta"}
    payload["menu_items"] = [{"menu_item_id": data["menu_items"][0]["menu_item_id"], "item_id": item1.id,
                              "menu_item_categories":  [{"menu_category_id": menu.menu_categories[0].id}]}, {
        "menu_item_id": data["menu_items"][1]["menu_item_id"], "item_id": item2.id,
        "menu_item_categories":  [{"menu_category_id": menu.menu_categories[0].id}]}]

    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert len(modified_data["menu_items"][0]["menu_item_categories"]) == 1
    assert len(modified_data["menu_items"][1]["menu_item_categories"]) == 1
    assert modified_data["menu_items"][0]["menu_item_categories"][0]["menu_item_category_id"] != None
    assert modified_data["menu_items"][0]["menu_item_categories"][0]["menu_item_id"] == data["menu_items"][0]["menu_item_id"]
    assert modified_data["menu_items"][0]["menu_item_categories"][0]["menu_category"]["menu_category_id"] == menu.menu_categories[0].id

    assert modified_data["menu_items"][1]["menu_item_categories"][0]["menu_item_category_id"] != None
    assert modified_data["menu_items"][1]["menu_item_categories"][0]["menu_item_id"] == data["menu_items"][1]["menu_item_id"]
    assert modified_data["menu_items"][1]["menu_item_categories"][0]["menu_category"]["menu_category_id"] == menu.menu_categories[0].id


def test_edit_menu_with_menu_item_categories_not_set(app, menu_repository, item_repository, sku_category, seller, client):
    item1 = item_repository.persist(ItemFactory(name="spaghetto", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item2 = item_repository.persist(ItemFactory(name="penne", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    menu = menu_repository.persist(MenuFactory(name="gujarati cuisine menu", seller_id=str(seller.id)))

    payload = {"menu_items": [{"item_id": item1.id}, {"item_id": item2.id}], "code":"gujaratimenu"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    payload = {"code": "italianpasta"}
    payload["menu_items"] = [{"menu_item_id": data["menu_items"][0]["menu_item_id"], "item_id": item1.id,
                              "menu_item_categories":  [{"menu_category_id": menu.menu_categories[0].id}]}, {
        "menu_item_id": data["menu_items"][1]["menu_item_id"], "item_id": item2.id,
        "menu_item_categories":  [{"menu_category_id": menu.menu_categories[0].id}]}]

    client.patch(url, json=payload)

    rv = client.get(url)
    data = rv.get_json()
    payload = {"code": "italianpasta"}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert len(modified_data["menu_items"][0]["menu_item_categories"]) == 1
    assert len(modified_data["menu_items"][1]["menu_item_categories"]) == 1
    assert modified_data["menu_items"][0]["menu_item_categories"][0]["menu_item_category_id"] != None
    assert modified_data["menu_items"][0]["menu_item_categories"][0]["menu_item_id"] == \
        data["menu_items"][0]["menu_item_id"]
    assert modified_data["menu_items"][0]["menu_item_categories"][0]["menu_category"]["menu_category_id"] == \
        menu.menu_categories[0].id

    assert modified_data["menu_items"][1]["menu_item_categories"][0]["menu_item_category_id"] != None
    assert modified_data["menu_items"][1]["menu_item_categories"][0]["menu_item_id"] == \
        data["menu_items"][1]["menu_item_id"]
    assert modified_data["menu_items"][1]["menu_item_categories"][0]["menu_category"]["menu_category_id"] == \
        menu.menu_categories[0].id


def test_edit_menu_with_invalid_menu_item_category_request(app, menu_repository, seller, client):
    url = "/v1/sellers/" + str(seller.id) + "/menus/1"
    rv = client.get(url)

    payload = {"menu_combos": [{"menu_combo_id": 1, "combo_id": None,
                                "menu_combo_categories": [{"menu_category_id": 1}]}, ]}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert rv.status_code == 400
    assert modified_data["reason"]["menu_combos"]["0"]["combo_id"] == ["Field may not be null."]


def test_edit_menu_with_new_menu_combo_categories(app, seller, item_repository, menu_repository, sku_category, combo_repository, client):
    item1 = item_repository.persist(ItemFactory(name="alfredo pasta", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    menu = menu_repository.persist(MenuFactory(name="chinese cuisine menu", seller_id=str(seller.id)))
    combo1 = combo_repository.persist(ComboFactory(name="chicken red wings + crushers",
                                                   sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item1.id)], seller_id=seller.id))
    combo2 = combo_repository.persist(ComboFactory(
        name="soup + lollipop", sku_category_code=sku_category.code,  seller_id=seller.id))
    payload = {"menu_combos": [{"combo_id": combo1.id}, {"combo_id": combo2.id}], "code": "chinesecuisine"}

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    payload["menu_combos"]= [{"menu_combo_id": data["menu_combos"][0]["menu_combo_id"], "combo_id": combo1.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][0]["menu_category_id"]}]},
                               {"menu_combo_id": data["menu_combos"][1]["menu_combo_id"], "combo_id": combo2.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][0]["menu_category_id"]}]}]

    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert len(modified_data["menu_combos"][0]["menu_combo_categories"]) == 1
    assert len(modified_data["menu_combos"][1]["menu_combo_categories"]) == 1
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_combo_category_id"] != None
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_combo_id"] == data["menu_combos"][0]["menu_combo_id"]
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_category"] != None

    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_combo_category_id"] != None
    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_combo_id"] == data["menu_combos"][1]["menu_combo_id"]
    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_category"] != None


def test_edit_menu_with_menu_combo_categories_not_set(app, item_repository, combo_repository, sku_category, menu_repository, seller, client):
    item1 = item_repository.persist(ItemFactory(name="risotto", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    menu = menu_repository.persist(MenuFactory(name="north korean cuisine menu", seller_id=str(seller.id)))
    combo1 = combo_repository.persist(ComboFactory(name="chicken white wings + crushers",
                                                   sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item1.id)], seller_id=seller.id))
    combo2 = combo_repository.persist(ComboFactory(
        name="tomato soup + lollipop", sku_category_code=sku_category.code,  seller_id=seller.id))
    payload = {"menu_combos": [{"combo_id": combo1.id}, {"combo_id": combo2.id}], "code": "tomatosoup"}

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    payload = {"code": "tomatosoup"}
    payload["menu_combos"]= [{"menu_combo_id": data["menu_combos"][0]["menu_combo_id"], "combo_id": combo1.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][0]["menu_category_id"]}]},
                               {"menu_combo_id": data["menu_combos"][1]["menu_combo_id"], "combo_id": combo2.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][0]["menu_category_id"]}]}]
    client.patch(url, json=payload)

    payload = {"code": "tomatosoup"}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert len(modified_data["menu_combos"][0]["menu_combo_categories"]) == 1
    assert len(modified_data["menu_combos"][1]["menu_combo_categories"]) == 1
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_combo_category_id"] != None
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_combo_id"] == data["menu_combos"][0]["menu_combo_id"]
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_category"] != None

    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_combo_category_id"] != None
    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_combo_id"] == data["menu_combos"][1]["menu_combo_id"]
    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_category"] != None


def test_edit_menu_with_updated_menu_combo_categories(app, item_repository, combo_repository, sku_category, menu_repository, seller, client):
    item1 = item_repository.persist(ItemFactory(name="carbonara", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    menu = menu_repository.persist(MenuFactory(name="mongolian cuisine menu", menu_categories=[
                                   MenuCategoryFactory(), MenuCategoryFactory(name="Test")], seller_id=str(seller.id)))
    combo1 = combo_repository.persist(ComboFactory(name="chicken hot fire wings + crushers",
                                                   sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item1.id)], seller_id=seller.id))
    combo2 = combo_repository.persist(ComboFactory(
        name="mushroom soup + lollipop", sku_category_code=sku_category.code,  seller_id=seller.id))
    payload = {"menu_combos": [{"combo_id": combo1.id}, {"combo_id": combo2.id}], "code": "msoup"}

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    payload = {"code": "msoup"}
    payload["menu_combos"]= [{"menu_combo_id": data["menu_combos"][0]["menu_combo_id"], "combo_id": combo1.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][0]["menu_category_id"]}]},
                               {"menu_combo_id": data["menu_combos"][1]["menu_combo_id"], "combo_id": combo2.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][0]["menu_category_id"]}]}]
    client.patch(url, json=payload)

    payload["menu_combos"]= [{"menu_combo_id": data["menu_combos"][0]["menu_combo_id"], "combo_id": combo1.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][1]["menu_category_id"]}]},
                               {"menu_combo_id": data["menu_combos"][1]["menu_combo_id"], "combo_id": combo2.id,
                                "menu_combo_categories": [{"menu_category_id":
                                                           data["menu_categories"][1]["menu_category_id"]}]}]

    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert len(modified_data["menu_combos"][0]["menu_combo_categories"]) == 1
    assert len(modified_data["menu_combos"][1]["menu_combo_categories"]) == 1
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_combo_category_id"] != None
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_combo_id"] == data["menu_combos"][0]["menu_combo_id"]
    assert modified_data["menu_combos"][0]["menu_combo_categories"][0]["menu_category"]["menu_category_id"] == data["menu_categories"][1]["menu_category_id"]

    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_combo_category_id"] != None
    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_combo_id"] == data["menu_combos"][1]["menu_combo_id"]
    assert modified_data["menu_combos"][1]["menu_combo_categories"][0]["menu_category"]["menu_category_id"] == data["menu_categories"][1]["menu_category_id"]


def test_edit_menu_with_invalid_menu_combo_category_request(app, menu_repository, seller, combo_repository, client):
    url = "/v1/sellers/" + str(seller.id) + "/menus/1"
    payload = {}
    payload = {"menu_combos": [{"menu_combo_id": 1, "combo_id": None,
                                "menu_combo_categories": [{"menu_category_id": 1}]}]}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert rv.status_code == 400
    assert modified_data["reason"]["menu_combos"]["0"]["combo_id"] == ["Field may not be null."]


def test_edit_menu_with_new_menu_item_item_variants(app, seller, menu_repository, sku_category, item_repository, client):
    menu = menu_repository.persist(MenuFactory(name="Pizza menu", seller_id=str(seller.id)))
    item1 = item_repository.persist(ItemFactory(name="Caprese", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item_variant1 = item_repository.persist(ItemVariantFactory(item_id=item1.id, sku_category_code=sku_category.code))
    item_variant2 = item_repository.persist(ItemVariantFactory(
        name="Crust", sku_category_code=sku_category.code, item_id=item1.id))
    payload = {"menu_items": [{"item_id": item1.id}], "code": "capresemenu"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    menu_data = rv.get_json()

    item_id = menu_data["menu_items"][0]["item"]["item_id"]
    menu_item_id = menu_data["menu_items"][0]["menu_item_id"]

    menu_item_edit_url = url + "/menu-items/" + str(menu_item_id)

    payload = {"item_id": item1.id, "menu_item_id": menu_item_id, "menu_item_item_variants": [
        {"menu_item_id": menu_item_id, "item_variant_id": item_variant1.id}, {"menu_item_id": menu_item_id, "item_variant_id": item_variant2.id}]}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_item_variants"]) == 2
    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][1]["item_variant_id"] == item_variant2.id

    rv = client.patch(menu_item_edit_url, json=payload)
    modified_data = rv.get_json()

    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][1]["item_variant_id"] == item_variant2.id


def test_edit_menu_with_menu_item_item_variants_not_set(app, item_repository, sku_category, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="Pasta menu", seller_id=str(seller.id)))
    item1 = item_repository.persist(ItemFactory(name="soprano", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item_variant1 = item_repository.persist(ItemVariantFactory(item_id=item1.id, sku_category_code=sku_category.code))
    item_variant2 = item_repository.persist(ItemVariantFactory(
        name="Crust", sku_category_code=sku_category.code, item_id=item1.id))

    payload = {"menu_items": [{"item_id": item1.id}], "code": "pastamenu"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    menu_data = rv.get_json()

    item_id = menu_data["menu_items"][0]["item"]["item_id"]
    menu_item_id = menu_data["menu_items"][0]["menu_item_id"]

    menu_item_edit_url = url + "/menu-items/" + str(menu_item_id)

    payload = {"item_id": item1.id, "code": "pastamenu", "menu_item_id": menu_item_id, "menu_item_item_variants": [
        {"menu_item_id": menu_item_id, "item_variant_id": item_variant1.id}, {"menu_item_id": menu_item_id,
                                                                              "item_variant_id": item_variant2.id}]}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_item_variants"]) == 2
    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][1]["item_variant_id"] == item_variant2.id

    # payload = {}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_item_variants"]) == 2
    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][0]["item_variant_id"] == item_variant1.id
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_item_variant_id"] != None
    assert menu_item_updated_data["menu_item_item_variants"][1]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_variants"][1]["item_variant_id"] == item_variant2.id


def test_edit_menu_with_invalid_menu_item_item_variants_request(app, menu_repository, seller, client):
    url = "/v1/sellers/" + str(seller.id) + "/menus/1"

    payload = {}
    payload["menu_items"] = [{"item_id": 1, "menu_item_id": None,
                              "menu_item_item_variants": [{"item_variant_id": 2}, {"item_variant_id": None}]}]

    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert rv.status_code == 400
    assert modified_data["reason"]["menu_items"]["0"]["menu_item_id"] == ["Field may not be null."]
    assert modified_data["reason"]["menu_items"]["0"]["menu_item_item_variants"]["1"]["item_variant_id"] == [
        "Field may not be null."]


def test_edit_menu_with_new_menu_item_side_items(app, seller, menu_repository, sku_category, item_repository, client):
    menu = menu_repository.persist(MenuFactory(name="Piazza menu", seller_id=str(seller.id)))
    item1 = item_repository.persist(ItemFactory(name="Bruschetta", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item2 = item_repository.persist(ItemFactory(name="Panzenella", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    side_item = item_repository.persist(SideItemFactory(item_id=item1.id, side_item_id=item2.id))

    payload = {"menu_items": [{"item_id": item1.id}], "code": "piazza"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    menu_data = rv.get_json()
    menu_item_id = menu_data["menu_items"][0]["menu_item_id"]
    payload = {}

    menu_item_edit_url = url + "/menu-items/" + str(menu_item_id)

    payload = {"item_id": item1.id, "menu_item_side_items": [{"side_item_id": side_item.id}]}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_side_items"]) == 1
    assert menu_item_updated_data["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert menu_item_updated_data["menu_item_side_items"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_side_items"][0]["side_item_id"] == side_item.id


def test_edit_menu_with_menu_item_side_items_not_set(app, seller, menu_repository, sku_category, item_repository, client):
    menu = menu_repository.persist(MenuFactory(name="Fast food menu", seller_id=str(seller.id)))
    item1 = item_repository.persist(ItemFactory(name="Pomodoro", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item2 = item_repository.persist(ItemFactory(name="Agliata", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    side_item = item_repository.persist(SideItemFactory(item_id=item1.id, side_item_id=item2.id))

    payload = {"menu_items": [{"item_id": item1.id}], "code": "fastfoodmenu"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)

    menu_data = rv.get_json()
    menu_item_id = menu_data["menu_items"][0]["menu_item_id"]

    menu_item_edit_url = url + "/menu-items/" + str(menu_item_id)

    payload = {"item_id": item1.id, "menu_item_side_items": [{"side_item_id": side_item.id}]}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_side_items"]) == 1
    assert menu_item_updated_data["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert menu_item_updated_data["menu_item_side_items"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_side_items"][0]["side_item_id"] == side_item.id

    # payload = {}
    payload = {"item_id": item1.id, "menu_item_side_items": [{"side_item_id": side_item.id}]}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_side_items"]) == 1
    assert menu_item_updated_data["menu_item_side_items"][0]["menu_item_side_item_id"] != None
    assert menu_item_updated_data["menu_item_side_items"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_side_items"][0]["side_item_id"] == side_item.id


def test_edit_menu_with_invalid_menu_item_side_items(app, menu_repository, seller, client):
    url = "/v1/sellers/" + str(seller.id) + "/menus/1"

    payload = {}
    payload["menu_items"] = [{"item_id": 1, "menu_item_id": None, "menu_item_side_items": [{"side_item_id": 2}, {"side_item_id": None}]},
                             {"item_id": 1, "menu_item_id": 2, "menu_item_side_items": [{"side_item_id": 2}, {"side_item_id": None}]}]

    payload["menu_item_side_items"] = [{"menu_item_id": None,
                                        "side_item_id": 1}, {"menu_item_id": 1, "side_item_id": None}]

    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert rv.status_code == 400

    assert modified_data["reason"]["menu_items"]["0"]["menu_item_id"] == ["Field may not be null."]
    assert modified_data["reason"]["menu_items"]["1"]["menu_item_side_items"]["1"]["side_item_id"] == [
        "Field may not be null."]


def test_edit_menu_with_combo_item_variants(app, menu_repository, item_repository, seller, sku_category, item, client):
    menu = menu_repository.persist(MenuFactory(name="FF food menu", seller_id=str(seller.id)))
    item1 = item_repository.persist(ItemFactory(name="calzone", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    item_variant = item_repository.persist(ItemVariantFactory(item_id=item1.id, sku_category_code=sku_category.code))
    payload = {"code": "calzone menu"}
    payload["menu_items"] = [{"item_id": item1.id, "item_variant_id": item_variant.id}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["menu_items"][0]["menu_item_id"] != None
    assert data["menu_items"][0]["item"]["item_id"] == item1.id
    assert data["menu_items"][0]["item_variant"]["item_variant_id"] == item_variant.id
    assert data["menu_items"][0]["item"]["name"] == item1.name


def test_edit_menu_with_new_menu_item_item_customisations(app, seller, menu_repository, sku_category, item_repository, client):
    menu = menu_repository.persist(MenuFactory(name="Singaporean menu", seller_id=str(seller.id)))
    item1 = item_repository.persist(ItemFactory(name="Gnocchi", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    variant_group = item_repository.persist(VariantGroupFactory(seller_id=seller.id))
    variant = item_repository.persist(VariantFactory(name="Test Variant", variant_group=variant_group, display_order=1))
    item_variant1 = item_repository.persist(ItemVariantFactory(
        name="Crust", sku_category_code=sku_category.code, item_id=item1.id))
    item_customisation = item_repository.persist(ItemCustomisationFactory(
        item_id=item1.id, item_variant_id=item_variant1.id, display_order=0, delta_price=120, variant_id=variant.id))

    payload = {"menu_items": [{"item_id": item1.id}], "code": "singaporeanmenu"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    menu_data = rv.get_json()

    item_id = menu_data["menu_items"][0]["item"]["item_id"]
    menu_item_id = menu_data["menu_items"][0]["menu_item_id"]

    menu_item_edit_url = url + "/menu-items/" + str(menu_item_id)

    payload = {"item_id": item1.id, "menu_item_id": menu_item_id,
               "menu_item_item_customisations": [{"item_customisation_id": item_customisation.id}]}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_item_customisations"]) == 1
    assert menu_item_updated_data["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert menu_item_updated_data["menu_item_item_customisations"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id

    rv = client.patch(menu_item_edit_url, json=payload)
    modified_data = rv.get_json()

    assert len(modified_data["menu_item_item_customisations"]) == 1
    assert modified_data["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert modified_data["menu_item_item_customisations"][0]["menu_item_id"] == menu_item_id
    assert modified_data["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id


def test_edit_menu_with_menu_item_item_customisations_not_set(app, item_repository, sku_category, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="Indonesian menu", seller_id=str(seller.id)))

    item1 = item_repository.persist(ItemFactory(name="Soul Pizza", seller_id=seller.id,
                                                sku_category_code=sku_category.code))
    variant_group = item_repository.persist(VariantGroupFactory(seller_id=seller.id))
    variant = item_repository.persist(VariantFactory(name="Test Variant", variant_group=variant_group, display_order=1))
    item_variant1 = item_repository.persist(ItemVariantFactory(
        name="Crust", sku_category_code=sku_category.code, item_id=item1.id))
    item_customisation = item_repository.persist(ItemCustomisationFactory(
        item_id=item1.id, item_variant_id=item_variant1.id, display_order=0, delta_price=120, variant_id=variant.id))

    payload = {"menu_items": [{"item_id": item1.id}], "code": "indonesianmenu"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    menu_data = rv.get_json()

    item_id = menu_data["menu_items"][0]["item"]["item_id"]
    menu_item_id = menu_data["menu_items"][0]["menu_item_id"]

    menu_item_edit_url = url + "/menu-items/" + str(menu_item_id)

    payload = {"item_id": item1.id, "menu_item_id": menu_item_id,
               "menu_item_item_customisations": [{"item_customisation_id": item_customisation.id}]}
    menu_item_updated_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_item_customisations"]) == 1
    assert menu_item_updated_data["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert menu_item_updated_data["menu_item_item_customisations"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id

    payload = {}
    modified_data = client.patch(menu_item_edit_url, json=payload).get_json()

    assert len(menu_item_updated_data["menu_item_item_customisations"]) == 1
    assert menu_item_updated_data["menu_item_item_customisations"][0]["menu_item_item_customisation_id"] != None
    assert menu_item_updated_data["menu_item_item_customisations"][0]["menu_item_id"] == menu_item_id
    assert menu_item_updated_data["menu_item_item_customisations"][0]["item_customisation_id"] == item_customisation.id


def test_edit_menu_combos(app, seller, menu_repository, sku_category,  combo_repository, client):
    combo1 = combo_repository.persist(ComboFactory(name="Gnocchi + garlic breadsticks combo",
                                                   sku_category_code=sku_category.code, prep_time="00:30:00"))
    combo2 = combo_repository.persist(ComboFactory(name="Fish and chip + Soup combo",
                                                   sku_category_code=sku_category.code,  prep_time="00:15:00"))
    menu = menu_repository.persist(MenuFactory(name="hawaiaan cuisine menu", seller_id=str(seller.id)))
    payload = {"code": "italian-british-combo"}
    payload["menu_combos"] = [{"combo_id": combo1.id}, {"combo_id": combo2.id, "sold_out": True}]

    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert len(data["menu_combos"]) == 2
    assert data["code"] == "italian-british-combo"
    assert data["menu_combos"][0]["menu_combo_id"] != None
    assert data["menu_combos"][0]["combo"]["name"] == "Gnocchi + garlic breadsticks combo"
    assert data["menu_combos"][0]["combo"]["combo_id"] == combo1.id
    assert data["menu_combos"][0]["sold_out"] == False
    assert data["menu_combos"][1]["menu_combo_id"] != None
    assert data["menu_combos"][1]["combo"]["name"] == "Fish and chip + Soup combo"
    assert data["menu_combos"][1]["combo"]["combo_id"] == combo2.id
    assert data["menu_combos"][1]["sold_out"] == True

    menu_combo_id = data["menu_combos"][0]["menu_combo_id"]

    combo_edit_url = url + "/menu-combos/" + str(menu_combo_id)
    payload = {"combo_id": combo1.id, "sold_out": True}
    rv = client.patch(combo_edit_url, json=payload)
    data = rv.get_json()

    assert data["menu_combo_id"] == menu_combo_id
    assert data["sold_out"] == True


def test_edit_menu_with_invalid_days_in_menu_timings(app, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="meghalayan  cuisine menu", seller_id=str(seller.id)))
    payload = {"menu_timings": [{"days": ["SUN", "MONDAY"], "end_time": "23:00:00", "start_time": "09:00:00"}],
               "code": "meghalayancuisine"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data['reason']["menu_timings"]["0"]["days"] == ['days field has wrong format']


def test_edit_menu_with_code_that_is_already_assigned_to_another_menu(app, menu_repository, seller, client):
    menu_repository.persist(MenuFactory(name="jodhpuri cuisine menu", code="duplicatemenucode",
                                        seller_id=str(seller.id)))
    turkish_menu = menu_repository.persist(MenuFactory(name="turkish cuisine menu", code="uniquecode",
                                                       seller_id=str(seller.id)))

    payload = {"name": "jodhpuri cuisine menu", "code": "duplicatemenucode"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(turkish_menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data['reason'] == {'code': ['Menu code is duplicate']}


def test_edit_menu_with_same_name(app, menu_repository, seller, client):
    georgian_cuisine_menu = menu_repository.persist(MenuFactory(name="georgian cuisine menu", seller_id=str(seller.id)))

    payload = {"name": "georgian cuisine menu", "code": "georgian"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(georgian_cuisine_menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["name"] == "georgian cuisine menu"
    assert data["menu_id"] == georgian_cuisine_menu.id


def test_edit_menu_with_name_that_is_already_assigned_to_another_menu_but_with_different_seller(app,
                                                                                                alternate_seller,
                                                                                                menu_repository,
                                                                                                seller, client):
    menu_repository.persist(MenuFactory(name="jaipuri cuisine menu", seller_id=str(alternate_seller.id)))
    polishian_menu = menu_repository.persist(MenuFactory(name="polishian cuisine menu", seller_id=str(seller.id)))

    payload = {"name": "jaipuri cuisine menu", "code": "jaipurimenu"}
    url = "/v1/sellers/" + str(seller.id) + "/menus/" + str(polishian_menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["name"] == "jaipuri cuisine menu"
    assert data["menu_id"] == polishian_menu.id


def test_edit_menu_with_invalid_seller(app, alternate_seller, menu_repository, seller, client):
    menu = menu_repository.persist(MenuFactory(name="russian cuisine menu", seller_id=str(alternate_seller.id)))

    payload = {"name": "jaipuri cuisine menu", "code":"jaipuricode"}
    url = "/v1/sellers/" + str(121212) + "/menus/" + str(menu.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()
    assert data['reason']['name'] == ['Seller ID is not valid']
