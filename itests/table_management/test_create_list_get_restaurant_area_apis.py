from tests.factories import RestaurantAreaFactory, RestaurantTableFactory


def create_restaurant_area_request(name="Lawn"):
    return {
        "name": name,
        "tables": [
            {
                "seats_count": 4,
                "number_of_tables": 2,
            },
            {
                "seats_count": 2,
                "number_of_tables": 1,
            }
        ]
    }


def test_create_restaurant_area_with_tables_and_seats(app, seller, client):
    payload = create_restaurant_area_request()
    url = "/v1/sellers/" + str(seller.id) + "/areas"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["name"] == payload["name"]
    assert data["seller_id"] == str(seller.id)
    assert len(data["tables"]) == 3

    assert rv.status_code == 201


def test_create_restaurant_area_without_name(app, seller, client):
    payload = create_restaurant_area_request()
    payload.pop("name")
    url = "/v1/sellers/" + str(seller.id) + "/areas"
    rv = client.post(url, json=payload)

    assert rv.status_code == 400


def test_create_restaurant_area_without_tables(app, seller, client):
    payload = create_restaurant_area_request()
    payload.pop("tables")
    url = "/v1/sellers/" + str(seller.id) + "/areas"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["name"] == payload["name"]
    assert data["seller_id"] == str(seller.id)

    assert rv.status_code == 201


def test_add_tables_to_restaurant_area(app, seller, restaurant_area_repository, client):
    seller_id = str(seller.id)
    restaurant_area = restaurant_area_repository.persist(RestaurantAreaFactory(name="Lawm", seller_id=seller_id))
    payload = create_restaurant_area_request()
    payload.pop("name")
    url = "/v1/sellers/" + str(seller.id) + "/areas/" + str(restaurant_area.id) + "/tables"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["name"] == restaurant_area.name
    assert data["seller_id"] == restaurant_area.seller_id
    assert len(data["tables"]) == 3
    for table in data["tables"]:
        assert table["number_of_seats"] in [2, 4]

    assert rv.status_code == 201


def test_list_all_restaurant_areas(app, seller, restaurant_area_repository, client):
    seller_id = str(seller.id)
    restaurant_areas = restaurant_area_repository.persist_all([RestaurantAreaFactory(name="Lawm", seller_id=seller_id),
                                                               RestaurantAreaFactory(name="Rooftop", seller_id=seller_id)])
    url = 'v1/sellers/' + seller_id + '/areas'
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) >= 2
    assert any(restaurant_area["name"] == restaurant_areas[0].name for restaurant_area in data)

    assert rv.status_code == 200


def test_get_restaurant_area(app, seller, restaurant_area_repository, client):
    seller_id = str(seller.id)
    restaurant_area = restaurant_area_repository.persist(RestaurantAreaFactory(name="Lawm", seller_id=seller_id))
    url = 'v1/sellers/' + seller_id + '/areas/' + str(restaurant_area.id)
    rv = client.get(url)
    data = rv.get_json()

    assert data["name"] == restaurant_area.name
    assert data["seller_id"] == restaurant_area.seller_id

    assert rv.status_code == 200


def test_get_tables_by_seller_id(app, seller, restaurant_area_repository, restaurant_table_repository, client):
    seller_id = str(seller.id)
    restaurant_areas = restaurant_area_repository.persist_all([RestaurantAreaFactory(name="Lawm", seller_id=seller_id),
                                                               RestaurantAreaFactory(name="Rooftop", seller_id=seller_id)])
    restaurant_table = restaurant_table_repository.persist(RestaurantTableFactory(area_id=restaurant_areas[0].id,
                                                                                        seller_id=seller.id))
    url = 'v1/sellers/' + seller_id + '/tables'
    rv = client.get(url)
    data = rv.get_json()

    assert len(data) >= 1
    assert any(restaurant_table.name == table["name"] for table in data)

    assert rv.status_code == 200
