import pytest
from tests.factories import ItemFactory, ComboFactory, ItemVariantFactory
from cataloging_service.models import ComboItem
from cataloging_service.constants.model_choices import FOOD_TYPE_CHOICES, FoodTypeChoices


def create_combo_request(sku_category):
    return {
        "name": "coffee",
        "display_name": "super hot filtered coffee",
        "description": "hot filter coffee",
        "prep_time": "00:1:00",
        "contains_alcohol": True,
        "pre_tax_price": 10.0,
        "cost": 11.00,
        "sku_category_code": sku_category.code
    }


def test_create_combo_without_combo_items(app, sku_category, seller, client):
    payload = create_combo_request(sku_category)
    payload["name"] = "java coffee"
    payload["code"] = "javacoffee"
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["combo_id"] != None
    assert data["name"] == payload["name"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["prep_time"] == "0:01:00"
    assert data["contains_alcohol"]
    assert data["pre_tax_price"] == str(payload["pre_tax_price"])
    assert data["sku_id"] != None
    assert data["active"]


def test_create_combo_with_combo_items(app, seller, sku_category, item, client):
    payload = create_combo_request(sku_category)
    payload["name"] = "pizza + coffee combo"
    payload["code"] = "pizzacoffee"
    payload["display_name"] = "margherita pizza + americano coffee"
    payload["prep_time"] = "00:20:00"
    payload["contains_alcohol"] = False
    payload["combo_items"] = [{"item_id": item.id}]
    payload["sku_category_code"] = sku_category.code
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["combo_id"] != None
    assert data["name"] == payload["name"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["prep_time"] == "0:20:00"
    assert data["contains_alcohol"] == False
    assert data["pre_tax_price"] == str(payload["pre_tax_price"])
    assert data["sku_id"] != None
    assert data["sku_category_code"] == str(sku_category.code)
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name
    assert data["active"]
    assert data["food_type"] == FoodTypeChoices.VEGETARIAN.value


def test_create_combo_with_non_veg_combo_items(app, seller, sku_category, non_vegetarian_item, client):
    payload = create_combo_request(sku_category)
    payload["name"] = "non veg pizza + coffee combo"
    payload["code"] = "noncoffee"
    payload["display_name"] = "margherita pizza + americano coffee"
    payload["prep_time"] = "00:20:00"
    payload["contains_alcohol"] = False
    payload["combo_items"] = [{"item_id": non_vegetarian_item.id}]
    payload["sku_category_code"] = sku_category.code
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["combo_id"] != None
    assert data["name"] == payload["name"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["code"] == payload["code"]
    assert data["prep_time"] == "0:20:00"
    assert data["contains_alcohol"] == False
    assert data["pre_tax_price"] == str(payload["pre_tax_price"])
    assert data["sku_id"] != None
    assert data["sku_category_code"] == str(sku_category.code)
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == non_vegetarian_item.id
    assert data["combo_items"][0]["item"]["name"] == non_vegetarian_item.name
    assert data["active"]
    assert data["food_type"] == FoodTypeChoices.NON_VEGETARIAN.value


def test_create_combo_with_combo_item_variants(app, item_repository, seller, sku_category,  client):
    item1 = item_repository.persist(ItemFactory(name="Ziti", seller_id=seller.id, sku_category_code=sku_category.code))
    item_variant = item_repository.persist(ItemVariantFactory(item_id=item1.id, sku_category_code=sku_category.code))
    payload = create_combo_request(sku_category)
    payload["name"] = "pasta + coffee combo"
    payload["display_name"] = "margherita pizza + americano coffee"
    payload["code"] = "ziticofee"
    payload["prep_time"] = "00:20:00"
    payload["contains_alcohol"] = False
    payload["combo_items"] = [{"item_id": item1.id, "item_variant_id": item_variant.id}]
    payload["sku_category_code"] = sku_category.code
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["combo_id"] != None
    assert data["name"] == payload["name"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["prep_time"] == "0:20:00"
    assert data["contains_alcohol"] == False
    assert data["pre_tax_price"] == str(payload["pre_tax_price"])
    assert data["sku_id"] != None
    assert data["sku_category_code"] == str(sku_category.code)
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item1.id
    assert data["combo_items"][0]["item_variant"]["item_variant_id"] == item_variant.id
    assert data["combo_items"][0]["item"]["name"] == "Ziti"
    assert data["active"]
    assert data["food_type"] == FoodTypeChoices.VEGETARIAN.value


def test_create_combo_with_combo_code(app, sku_category, seller, client):
    payload = create_combo_request(sku_category)
    payload.update({"code": "unq-combo-code"})
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["combo_id"] != None
    assert data["name"] == payload["name"]
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["prep_time"] == "0:01:00"
    assert data["contains_alcohol"]
    assert data["pre_tax_price"] == str(payload["pre_tax_price"])
    assert data["sku_id"] != None
    assert data["active"]
    assert data["code"] == payload["code"]


def test_create_combo_with_duplicate_combo_code(app, sku_category, combo_repository, seller, client):
    combo_repository.persist(ComboFactory(name="chicken wings", seller_id=str(seller.id),
                                          sku_category_code=sku_category.code, code="combo-code"))
    payload = create_combo_request(sku_category)
    payload.update({"code": "combo-code"})
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)

    assert rv.status_code == 400


def test_create_combo_with_null_combo_code(app, sku_category, seller, client):
    payload = create_combo_request(sku_category)
    payload.update({"code": None})
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert rv.status_code == 400


def test_get_combos(app, seller, sku_category, combo_repository, client):
    combos = combo_repository.persist_all([ComboFactory(name="Coke + popcorn", sku_category_code=sku_category.code, seller_id=seller.id),
                                           ComboFactory(name="wings + crushers", sku_category_code=sku_category.code, seller_id=seller.id)])
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.get(url)
    data = rv.get_json()
    assert len(data) >= 2
    assert any(combo["name"] == combos[0].name for combo in data)
    assert any(combo["description"] == combos[0].description for combo in data)
    assert any(combo["combo_id"] == combos[0].id for combo in data)
    assert any(combo["sku_id"] == combos[0].sku_id for combo in data)
    assert any(combo["display_name"] == combos[0].display_name for combo in data)
    assert any(combo["prep_time"] == combos[0].prep_time for combo in data)
    assert any(combo["pre_tax_price"] == str(combos[0].pre_tax_price) for combo in data)
    assert any(combo["pre_tax_price"] == str(combos[0].pre_tax_price) for combo in data)


def test_get_combo_details(app, seller, sku_category, combo_repository, item, client):
    combo = combo_repository.persist(ComboFactory(name="chicken wings + crushers",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()

    assert data["combo_id"] == combo.id
    assert data["name"] == "chicken wings + crushers"
    assert data["description"] == "Pizza + coke combo"
    assert data["display_name"] == "Pizza + coke deluxe combo"
    assert data["contains_alcohol"] == False
    assert data["prep_time"] == "00:20:00"
    assert data["pre_tax_price"] == '500.0'
    assert data["sku_id"] != None
    assert data["cost"] == '620.0'
    assert data["active"]
    assert data["food_type"] == FoodTypeChoices.VEGETARIAN.value

    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name


def test_edit_combo_with_combo_name_and_combo_items(app, sku_category, combo_repository, seller, item, client):
    combo = combo_repository.persist(ComboFactory(name="chicken wings",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    payload = create_combo_request(sku_category)
    payload["name"] = "cake + coffee combo"
    payload["description"] = "pizza + coffee deluxe combo"
    payload["contains_alcohol"] = False
    payload["code"] = "cwingscrushers"
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["combo_id"] == combo.id
    assert data["name"] == "cake + coffee combo"
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["prep_time"] == "0:01:00"
    assert data["pre_tax_price"] == str(payload["pre_tax_price"])
    assert data["sku_id"] != None
    assert data["cost"] == str(payload["cost"])
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name
    assert data["active"]


def test_edit_combo_with_combo_name_and_non_veg_combo_items(app, sku_category, combo_repository, seller, item, non_vegetarian_item, client):
    non_veg_combo = combo_repository.persist(ComboFactory(name="non veg wings",
                                                          sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(non_veg_combo.id)
    payload = create_combo_request(sku_category)
    payload["name"] = "non veg cake + coffee combo"
    payload["description"] = "pizza + coffee deluxe combo"
    payload["contains_alcohol"] = False
    payload["code"] = "nccrushers"
    payload["combo_items"] = [{"item_id": item.id}, {"item_id": non_vegetarian_item.id}]
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert data["combo_id"] == non_veg_combo.id
    assert data["name"] == "non veg cake + coffee combo"
    assert data["description"] == payload["description"]
    assert data["display_name"] == payload["display_name"]
    assert data["prep_time"] == "0:01:00"
    assert data["pre_tax_price"] == str(payload["pre_tax_price"])
    assert data["sku_id"] != None
    assert data["cost"] == str(payload["cost"])
    assert len(data["combo_items"]) == 2
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["active"]


def test_edit_combo_with_combo_description(app, seller, combo_repository, sku_category, item, client):
    combo = combo_repository.persist(ComboFactory(name="chicken crushers",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()
    payload = {"description": "Custom Description", "code": "ccrusherswithdesc", }
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["combo_id"] == combo.id
    assert modified_data["name"] == data["name"]
    assert modified_data["description"] == "Custom Description"
    assert modified_data["display_name"] == data["display_name"]
    assert modified_data["pre_tax_price"] == data["pre_tax_price"]
    assert modified_data["sku_id"] == data["sku_id"]
    assert modified_data["cost"] == data["cost"]
    assert modified_data["contains_alcohol"] == data["contains_alcohol"]
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name
    assert data["active"]


def test_edit_combo_without_prep_time(app, seller, combo_repository, sku_category, item, client):
    combo = combo_repository.persist(ComboFactory(name="red wings + crushers",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()
    payload = {"prep_time": "0:22:30", "code": "uccrushers", }
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["combo_id"] == combo.id
    assert modified_data["name"] == data["name"]
    assert modified_data["description"] == data["description"]
    assert modified_data["display_name"] == data["display_name"]
    assert modified_data["prep_time"] == "0:22:30"
    assert modified_data["pre_tax_price"] == data["pre_tax_price"]
    assert modified_data["sku_id"] == data["sku_id"]
    assert modified_data["cost"] == data["cost"]
    assert modified_data["contains_alcohol"] == data["contains_alcohol"]
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name
    assert data["active"]


def test_edit_combo_with_contains_alcohol(app, seller, combo_repository, sku_category, item, client):
    payload = {"contains_alcohol": False, "code": "rccrushers", }
    combo = combo_repository.persist(ComboFactory(name="chicken red crushers",
                                                  sku_category_code=sku_category.code,
                                                  combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["combo_id"] == combo.id
    assert modified_data["name"] == data["name"]
    assert modified_data["description"] == data["description"]
    assert modified_data["display_name"] == data["display_name"]
    assert modified_data["pre_tax_price"] == data["pre_tax_price"]
    assert modified_data["sku_id"] == data["sku_id"]
    assert modified_data["cost"] == data["cost"]
    assert modified_data["contains_alcohol"] == False
    assert data["active"]
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name


def test_edit_combo_with_pre_tax_price(app, seller, combo_repository, sku_category, item, client):
    combo = combo_repository.persist(ComboFactory(name="veg red wings + crushers",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()

    payload = {"code": "vredwings", "pre_tax_price": 99.99}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["combo_id"] == combo.id
    assert modified_data["name"] == data["name"]
    assert modified_data["description"] == data["description"]
    assert modified_data["display_name"] == data["display_name"]
    assert modified_data["pre_tax_price"] == '99.99'
    assert modified_data["sku_id"] == data["sku_id"]
    assert modified_data["cost"] == data["cost"]
    assert modified_data["contains_alcohol"] == False
    assert data["active"]
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name


def test_edit_combo_with_cost(app, seller, sku_category, combo_repository, item, client):
    combo = combo_repository.persist(ComboFactory(name="veg crushers",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()

    payload = {"cost": 999.99, "code": "ccrushersv", }
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["combo_id"] == combo.id
    assert modified_data["name"] == data["name"]
    assert modified_data["description"] == data["description"]
    assert modified_data["display_name"] == data["display_name"]
    assert modified_data["pre_tax_price"] == data["pre_tax_price"]
    assert modified_data["sku_id"] == data["sku_id"]
    assert modified_data["cost"] == '999.99'
    assert modified_data["contains_alcohol"] == False
    assert data["active"]
    assert len(data["combo_items"]) == 1
    assert data["combo_items"][0]["combo_item_id"] != None
    assert data["combo_items"][0]["item"]["item_id"] == item.id
    assert data["combo_items"][0]["item"]["name"] == item.name


def test_edit_combo_with_sku_category_change(app, seller, sku_category, alcohol_sku_category, combo_repository, item, client):
    combo = combo_repository.persist(ComboFactory(name="chicken fire red wings + crushers",
                                                  sku_category_code=sku_category.code, seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()

    payload = {"sku_category_code": alcohol_sku_category.code, "code": "fireccrushers", }
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["combo_id"] == combo.id
    assert modified_data["name"] == data["name"]
    assert modified_data["description"] == data["description"]
    assert modified_data["display_name"] == data["display_name"]
    assert modified_data["pre_tax_price"] == data["pre_tax_price"]
    assert modified_data["sku_id"] == data["sku_id"]
    assert modified_data["sku_category_code"] == alcohol_sku_category.code
    assert modified_data["cost"] == data["cost"]
    assert modified_data["contains_alcohol"] == False
    assert data["active"]


def test_edit_combo_with_combo_items(app, seller, combo_repository, sku_category, item, item_repository, client):
    item1 = item_repository.persist(ItemFactory(name="Sandwich", seller_id=seller.id))
    item2 = item_repository.persist(ItemFactory(name="Banana Milkshake", seller_id=seller.id))
    combo = combo_repository.persist(ComboFactory(name="chicken hot sour red wings + crushers",
                                                  sku_category_code=sku_category.code,
                                                  combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)
    data = rv.get_json()

    payload = {"code": "ccrushers", "combo_items": [{"item_id": item1.id}, {"item_id": item2.id}]}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["combo_id"] == combo.id
    assert modified_data["name"] == data["name"]
    assert modified_data["description"] == data["description"]
    assert modified_data["display_name"] == data["display_name"]
    assert modified_data["pre_tax_price"] == data["pre_tax_price"]
    assert modified_data["sku_id"] == data["sku_id"]
    assert modified_data["cost"] == data["cost"]
    assert modified_data["contains_alcohol"] == False
    assert data["active"]
    assert len(modified_data["combo_items"]) == 2
    assert modified_data["combo_items"][0]["combo_item_id"] != None
    assert modified_data["combo_items"][0]["item"]["item_id"] == item1.id
    assert modified_data["combo_items"][0]["item"]["name"] == "Sandwich"
    assert modified_data["combo_items"][1]["combo_item_id"] != None
    assert modified_data["combo_items"][1]["item"]["item_id"] == item2.id
    assert modified_data["combo_items"][1]["item"]["name"] == "Banana Milkshake"


def test_edit_combo_with_new_combo_code(app, seller, sku_category, combo_repository, client):
    combo = combo_repository.persist(ComboFactory(name="kfried chicken", seller_id=str(seller.id),
                                                  sku_category_code=sku_category.code, code=None))
    payload = {"code": "combo-code"}
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.patch(url, json=payload)
    data = rv.get_json()

    assert rv.status_code == 200
    assert data["name"] == combo.name
    assert data["description"] == combo.description
    assert data["display_name"] == combo.display_name
    assert data["sku_id"] != None
    assert data["code"] == payload["code"]


def test_edit_combo_with_duplicate_combo_code(app, seller, sku_category, combo_repository, client):

    combo1 = combo_repository.persist(ComboFactory(name="sfried chicken", seller_id=str(seller.id),
                                                   sku_category_code=sku_category.code, code="combo-code"))
    combo = combo_repository.persist(ComboFactory(name="fried chicken", seller_id=str(seller.id),
                                                  sku_category_code=sku_category.code, code="combo-code1"))
    payload = {"code": "combo-code"}
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.patch(url, json=payload)

    assert rv.status_code == 400


def test_search_combos(app, seller, sku_category, non_vegetarian_item, combo_repository, client):
    combos = combo_repository.persist_all([ComboFactory(name="Fanta + popcorn", sku_category_code=sku_category.code, food_type=FoodTypeChoices.VEGAN.value, seller_id=seller.id),
                                           ComboFactory(name="bungee wrap",
                                                        sku_category_code=sku_category.code, seller_id=seller.id),
                                           ComboFactory(name="non veg red wings + crushers", food_type=FoodTypeChoices.NON_VEGETARIAN.value,
                                                        sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=non_vegetarian_item.id)], seller_id=seller.id)])
    url = "/v1/sellers/" + str(seller.id) + "/combos?name=fanta"
    rv = client.get(url)
    data = rv.get_json()
    assert len(data) == 1
    assert data[0]["name"] == "Fanta + popcorn"

    url = "/v1/sellers/" + str(seller.id) + "/combos?name=bungee"
    rv = client.get(url)
    data = rv.get_json()
    assert len(data) == 1
    assert data[0]["name"] == "bungee wrap"

    url = "/v1/sellers/" + str(seller.id) + "/combos?name=non veg red wings"
    rv = client.get(url)
    data = rv.get_json()
    assert len(data) == 1
    assert data[0]["name"] == "non veg red wings + crushers"

    url = "/v1/sellers/" + str(seller.id) + "/combos?food_type=vegetarian"
    rv = client.get(url)
    data = rv.get_json()
    assert len(data) >= 1
    assert all(item["food_type"] == FoodTypeChoices.VEGETARIAN.value for item in data)

    url = "/v1/sellers/" + str(seller.id) + "/combos?food_type=non_vegetarian"
    rv = client.get(url)
    data = rv.get_json()
    assert len(data) >= 1
    assert all(item["food_type"] == FoodTypeChoices.NON_VEGETARIAN.value for item in data)

    url = "/v1/sellers/" + str(seller.id) + "/combos?food_type=vegan"
    rv = client.get(url)
    data = rv.get_json()
    assert len(data) == 1
    assert all(item["food_type"] == FoodTypeChoices.VEGAN.value for item in data)


def test_search_combos_by_display_name(app, seller, sku_category, combo_repository, client):
    combos = combo_repository.persist_all([ComboFactory(name="Maaza + popcorn + nuggets", display_name="best movie combo", sku_category_code=sku_category.code, seller_id=seller.id),
                                           ComboFactory(name="jalapeno nacho chicken + wrap", display_name="best spicy movie combo",  sku_category_code=sku_category.code, seller_id=seller.id)])
    url = "/v1/sellers/" + str(seller.id) + "/combos?name=best movie combo"

    rv = client.get(url)
    data = rv.get_json()
    assert len(data) == 1
    assert data[0]["name"] == "Maaza + popcorn + nuggets"

    url = "/v1/sellers/" + str(seller.id) + "/combos?name=best spicy movie combo"

    rv = client.get(url)
    data = rv.get_json()
    assert len(data) == 1
    assert data[0]["name"] == "jalapeno nacho chicken + wrap"


def test_create_combo_with_invalid_prep_time(app, sku_category, seller, client):
    payload = create_combo_request(sku_category)
    payload["prep_time"] = "invalid prep time"
    url = "/v1/sellers/" + str(seller.id) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["reason"]["prep_time"] == ['prep_time field has wrong format']


def test_edit_combo_with_invalid_prep_time(app, seller, combo_repository, sku_category, item, item_repository, client):
    combo = combo_repository.persist(ComboFactory(name="chicken popcorn + crushers",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)

    payload = {"prep_time": "invalid_preptime"}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["reason"]["prep_time"] == ['prep_time field has wrong format']


def test_edit_combo_with_combo_name_that_already_exists(app, seller, combo_repository, sku_category, item, item_repository, client):
    combo_repository.persist(ComboFactory(name="super deluxe whopper",
                                          sku_category_code=sku_category.code, code="testduplicatecode",
                                          combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    deluxe_whopper_combo = combo_repository.persist(ComboFactory(name="deluxe whopper",
                                                                 sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(deluxe_whopper_combo.id)
    rv = client.get(url)

    payload = {"name": "super deluxe whopper", "code": "testduplicatecode"}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["reason"] == {'code': ['Combo code is duplicate']}


def test_edit_combo_with_same_combo_name(app, seller, combo_repository, sku_category, item, item_repository, client):
    combo = combo_repository.persist(ComboFactory(name="sushi + sake combo",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)

    payload = {"name": "sushi + sake combo", "code": "sushicombo",}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()

    assert modified_data["name"] == "sushi + sake combo"
    assert modified_data["combo_id"] == combo.id


def test_edit_combo_mark_combo_not_active(app, seller, combo_repository, sku_category, item, item_repository, client):
    combo = combo_repository.persist(ComboFactory(name="sushila+ sake combo",
                                                  sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.get(url)

    payload = {"active": False, "code": "sushilasakecombo",}
    rv = client.patch(url, json=payload)
    modified_data = rv.get_json()
    assert modified_data["active"] == False
    assert modified_data["combo_id"] == combo.id


def test_create_combo_with_same_combo_name_but_with_different_seller(app, seller, alternate_seller, combo_repository, sku_category, item, item_repository, client):
    combo = combo_repository.persist(ComboFactory(name="clam + sake combo",
                                                  sku_category_code=sku_category.code,
                                                  combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    payload = create_combo_request(sku_category)
    payload["name"] = combo.name
    payload["code"] = "unique_code"
    url = "/v1/sellers/" + str(alternate_seller.id) + "/combos"
    rv = client.post(url, json=payload)

    created_combo = rv.get_json()

    assert created_combo["name"] == payload["name"]
    assert created_combo["combo_id"] != None


def test_edit_combo_with_same_combo_name_but_with_different_seller(app, seller, alternate_seller, combo_repository, sku_category, item, item_repository, client):
    combo1 = combo_repository.persist(ComboFactory(name="soup + sake combo",
                                                   sku_category_code=sku_category.code,
                                                   combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))
    combo2 = combo_repository.persist(ComboFactory(name="cheese + sake combo",
                                                   sku_category_code=sku_category.code,
                                                   combo_items=[ComboItem(item_id=item.id)], seller_id=alternate_seller.id))

    payload = {"name":  combo1.name, "code": "soup",}
    url = "/v1/sellers/" + str(alternate_seller.id) + "/combos/" + str(combo2.id)
    rv = client.patch(url, json=payload)

    created_combo = rv.get_json()
    assert created_combo["name"] == combo1.name
    assert created_combo["combo_id"] == combo2.id


def test_create_combo_with_invalid_seller(app, seller, alternate_seller, combo_repository, sku_category, item, item_repository, client):
    payload = create_combo_request(sku_category)
    payload["code"] = "testcode"
    url = "/v1/sellers/" + str(121212) + "/combos"
    rv = client.post(url, json=payload)
    data = rv.get_json()

    assert data["code"] == 500


def test_edit_combo_with_invalid_seller(app, seller, alternate_seller, combo_repository, sku_category, item, item_repository, client):
    combo1 = combo_repository.persist(ComboFactory(name="super + sake combo",
                                                   sku_category_code=sku_category.code, combo_items=[ComboItem(item_id=item.id)], seller_id=seller.id))

    payload = {"name":  combo1.name,  "code": "supersakecombo"}
    url = "/v1/sellers/" + str(1212121) + "/combos/" + str(combo1.id)
    rv = client.patch(url, json=payload)

    data = rv.get_json()
    assert data['reason']['name'] == ['Seller ID is not valid']


def test_delete_combo(app, combo_repository, seller, sku_category, client):
    combo = combo_repository.persist(ComboFactory(name="item to be deleted", sku_category_code=sku_category.code, seller_id=str(
        seller.id)))
    url = "/v1/sellers/" + str(seller.id) + "/combos/" + str(combo.id)
    rv = client.delete(url)
    assert rv.status_code == 204

    rv = client.get(url)

    data = rv.get_json()
    assert data == {}


def test_create_combo_with_different_items(app, seller, sku_category, vegan_item, non_vegetarian_item, item, item_repository, client):
    item = item_repository.persist(ItemFactory(
        name="Grill Sandwich", food_type=FoodTypeChoices.VEGETARIAN.value, seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/"
    payload = create_combo_request(sku_category)
    payload["name"] = "java1 coffee"
    payload["combo_items"] = [{"item_id": item.id}, {"item_id": vegan_item.id}]
    rv = client.post(url, json=payload)

    veg_combo_data = rv.get_json()
    assert veg_combo_data["food_type"] == FoodTypeChoices.VEGETARIAN.value

    payload["name"] = "french coffee"
    payload["combo_items"] = [{"item_id": item.id}, {"item_id": vegan_item.id}, {"item_id": non_vegetarian_item.id}]
    rv = client.post(url, json=payload)

    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.NON_VEGETARIAN.value

    payload["combo_items"] = [{"item_id": vegan_item.id}]
    rv = client.patch(url + str(non_veg_combo_data["combo_id"]), json=payload)
    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.VEGAN.value

    payload["combo_items"] = [{"item_id": vegan_item.id}, {"item_id": item.id}]
    rv = client.patch(url + str(non_veg_combo_data["combo_id"]), json=payload)
    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.VEGETARIAN.value

    payload["combo_items"] = [{"item_id": vegan_item.id}, {"item_id": item.id}, {"item_id": non_vegetarian_item.id}]
    rv = client.patch(url + str(non_veg_combo_data["combo_id"]), json=payload)
    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.NON_VEGETARIAN.value


def test_create_combo_with_different_items(app, seller, sku_category, vegan_item, non_vegetarian_item, item, item_repository, client):
    item = item_repository.persist(ItemFactory(
        name="Grill Sandwich", food_type=FoodTypeChoices.VEGETARIAN.value, seller_id=seller.id))
    url = "/v1/sellers/" + str(seller.id) + "/combos/"
    payload = create_combo_request(sku_category)
    payload["name"] = "java1 coffee"
    payload["code"] = "jcofe"
    payload["combo_items"] = [{"item_id": item.id}, {"item_id": vegan_item.id}]
    rv = client.post(url, json=payload)

    veg_combo_data = rv.get_json()
    assert veg_combo_data["food_type"] == FoodTypeChoices.VEGETARIAN.value

    payload["name"] = "french coffee"
    payload["code"] = "fcofe"
    payload["combo_items"] = [{"item_id": item.id}, {"item_id": vegan_item.id}, {"item_id": non_vegetarian_item.id}]
    rv = client.post(url, json=payload)

    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.NON_VEGETARIAN.value

    payload["combo_items"] = [{"item_id": vegan_item.id}]
    rv = client.patch(url + str(non_veg_combo_data["combo_id"]), json=payload)
    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.VEGAN.value

    payload["combo_items"] = [{"item_id": vegan_item.id}, {"item_id": item.id}]
    rv = client.patch(url + str(non_veg_combo_data["combo_id"]), json=payload)
    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.VEGETARIAN.value

    payload["combo_items"] = [{"item_id": vegan_item.id}, {"item_id": item.id}, {"item_id": non_vegetarian_item.id}]
    rv = client.patch(url + str(non_veg_combo_data["combo_id"]), json=payload)
    non_veg_combo_data = rv.get_json()
    assert non_veg_combo_data["food_type"] == FoodTypeChoices.NON_VEGETARIAN.value
