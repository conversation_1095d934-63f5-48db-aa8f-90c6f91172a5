from cataloging_service.infrastructure.repositories.hotel_repository import PropertyRepository
import pytest

from cataloging_service.infrastructure.repositories.seller_repository import SellerRepository
from cataloging_service.infrastructure.repositories.item_repository import ItemRepository
from cataloging_service.infrastructure.repositories.combo_repository import ComboRepository
from cataloging_service.infrastructure.repositories.sku_repository import SkuRepository
from cataloging_service.infrastructure.repositories.menu_repository import MenuRepository
from cataloging_service.infrastructure.repositories.restaurant_area_repository import RestaurantAreaRepository
from cataloging_service.infrastructure.repositories.restaurant_table_repository import RestaurantTableRepository
from cataloging_service.infrastructure.repositories.hotel_repository import PropertyRepository
from cataloging_service.infrastructure.repositories.kitchen_repository import KitchenRepository

@pytest.fixture(scope='session', autouse=True)
def seller_repository():
    return SellerRepository()

@pytest.fixture(scope='session', autouse=True)
def item_repository():
    return ItemRepository()

@pytest.fixture(scope='session', autouse=True)
def combo_repository():
    return ComboRepository()

@pytest.fixture(scope='session', autouse=True)
def sku_repository():
    return SkuRepository()

@pytest.fixture(scope='session', autouse=True)
def menu_repository():
    return MenuRepository()

@pytest.fixture(scope='session', autouse=True)
def restaurant_area_repository():
    return RestaurantAreaRepository()

@pytest.fixture(scope='session', autouse=True)
def restaurant_table_repository():
    return RestaurantTableRepository()

@pytest.fixture(scope='session', autouse=True)
def property_repository():
    return PropertyRepository()

@pytest.fixture(scope='session', autouse=True)
def kitchen_repository():
    return KitchenRepository()
