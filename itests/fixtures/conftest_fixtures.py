from cataloging_service.models import Property
from cataloging_service.constants.model_choices import FoodTypeChoices
import pytest
from tests.factories import SellerFactory, ItemFactory, SkuCategoryFactory, CityFactory, StateFactory, CountryFactory, \
    SellerCategoryFactory, PropertyFactory, KitchenFactory


@pytest.fixture(scope="session", autouse=True)
def seller(seller_repository, property):
    seller = SellerFactory(seller_id="1", property_id=property.id)
    seller_repository.persist(seller)
    seller_repository.session().commit()
    yield seller


@pytest.fixture(scope="session", autouse=True)
def property(property_repository):
    property = PropertyFactory()
    property_repository.persist(property)
    property_repository.session().commit()
    yield property

@pytest.fixture(scope="session", autouse=True)
def kitchen(kitchen_repository, property):
    kitchen = KitchenFactory(property_id=property.id)
    kitchen_repository.persist(kitchen)
    kitchen_repository.session().commit()
    yield kitchen


@pytest.fixture(scope="session", autouse=True)
def alternate_seller(seller_repository, property):
    country = CountryFactory(name="USA", iso_code="006")
    state = StateFactory(name="New York", country=country)
    city = CityFactory(name="New York", state=state)
    property_id = property.id
    seller_category = SellerCategoryFactory(name="second best seller category", code="second_best")
    alternate_seller = SellerFactory(name="Bailey", seller_id="2", city=city, seller_category=seller_category,
                                     property_id=property_id)
    seller_with_different_currency = SellerFactory(name="New York Restaurant", seller_id="3", city=city,
                                                   property_id=property_id, seller_category=seller_category,
                                                   base_currency_code="EUR")
    seller_repository.persist(alternate_seller)
    seller_repository.session().commit()
    seller_repository.persist(seller_with_different_currency)
    seller_repository.session().commit()
    yield alternate_seller


@pytest.fixture(scope="session", autouse=True)
def item(item_repository, seller):
    item = ItemFactory(name="Shepherd pie", seller_id=seller.id)
    item_repository.persist(item)
    item_repository.session().commit()
    yield item


@pytest.fixture(scope="session", autouse=True)
def non_vegetarian_item(item_repository, seller):
    item = ItemFactory(name="Non Veg Shepherd pie", food_type=FoodTypeChoices.NON_VEGETARIAN.value, seller_id=seller.id)
    item_repository.persist(item)
    item_repository.session().commit()
    yield item


@pytest.fixture(scope="session", autouse=True)
def vegan_item(item_repository, seller):
    item = ItemFactory(name="Vegan Shepherd pie", food_type=FoodTypeChoices.VEGAN.value, seller_id=seller.id)
    item_repository.persist(item)
    item_repository.session().commit()
    yield item


@pytest.fixture(scope="session", autouse=True)
def sku_category(sku_repository):
    food_sku_category = SkuCategoryFactory(name="Food", hsn_sac="996331", code="food")
    sku_repository.persist(food_sku_category)
    sku_repository.session().commit()
    yield food_sku_category


@pytest.fixture(scope="session", autouse=True)
def alcohol_sku_category(sku_repository):
    alcohol_sku_category = SkuCategoryFactory(name="Alcohol", hsn_sac="996332", code="alcohol")
    sku_repository.persist(alcohol_sku_category)
    sku_repository.session().commit()
    yield alcohol_sku_category
