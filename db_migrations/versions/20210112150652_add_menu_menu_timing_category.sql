-- revision: '20210112150652_add_menu_menu_timing_category'
-- down_revision: '20201209131416_bronze_shield_addition'

-- upgrade
--
-- Name: menu; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE menu (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id SERIAL PRIMARY KEY,
    name character varying NOT NULL,
    description character varying NOT NULL,
    display_name character varying NOT NULL,
    seller_id character varying NOT NULL,
    unique(seller_id, name)
);

ALTER TABLE ONLY menu
    ADD CONSTRAINT menu_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id);


--
-- Name: menu_timing; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE menu_timing (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id SERIAL PRIMARY KEY,
    days bytea NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    menu_id int NOT NULL
);

ALTER TABLE ONLY menu_timing
    ADD CONSTRAINT menu_timing_menu_id_fkey FOREIGN KEY (menu_id) REFERENCES menu(id) ON DELETE CASCADE;


--
-- Name: category; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE menu_category (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id SERIAL PRIMARY KEY,
    name character varying NOT NULL,
    display_order integer NOT NULL,
    menu_id int NOT NULL,
    unique(menu_id, name)
);

ALTER TABLE ONLY menu_category
    ADD CONSTRAINT category_menu_id_fkey FOREIGN KEY (menu_id) REFERENCES menu(id) ON DELETE CASCADE;


-- downgrade
DROP TABLE menu_timing;
DROP TABLE menu_category;
DROP TABLE menu;