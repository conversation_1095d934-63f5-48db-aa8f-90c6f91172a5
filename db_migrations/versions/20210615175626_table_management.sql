-- revision: '20210615175626_table_management'
-- down_revision: '20210324183423_is_hotel_superhero_setup_completed'

-- upgrade
CREATE TABLE restaurant_area (
  created_at timestamptz,
  modified_at timestamptz,
  is_deleted boolean NOT NULL DEFAULT FALSE,
  id serial NOT NULL,
  seller_id character varying NOT NULL,
  name character varying NULL,
  display_order integer NULL,
  CONSTRAINT restaurant_area_pkey PRIMARY KEY (id)
);


ALTER TABLE ONLY restaurant_table
    DROP CONSTRAINT restaurant_table_pkey;

CREATE SEQUENCE restaurant_table_seq;

ALTER TABLE restaurant_table ADD COLUMN id SERIAL PRIMARY KEY;
ALTER TABLE restaurant_table ALTER COLUMN id SET DEFAULT nextval('restaurant_table_seq');
ALTER TABLE restaurant_table ADD COLUMN area_id integer;
ALTER TABLE restaurant_table ADD COLUMN name character varying;
ALTER TABLE restaurant_table ADD COLUMN current_status character varying;
ALTER TABLE restaurant_table ADD COLUMN status_updated_at timestamptz;
ALTER TABLE restaurant_table ADD COLUMN x integer;
ALTER TABLE restaurant_table ADD COLUMN y integer;
ALTER TABLE restaurant_table ADD COLUMN width integer;
ALTER TABLE restaurant_table ADD COLUMN height integer;
ALTER TABLE restaurant_table ADD COLUMN is_deleted boolean NOT NULL DEFAULT FALSE;


CREATE TABLE table_seat (
  created_at timestamptz,
  modified_at timestamptz,
  id serial NOT NULL,
  table_id integer,
  is_deleted boolean NOT NULL DEFAULT FALSE,
  CONSTRAINT table_seat_pkey PRIMARY KEY (id)
);

ALTER TABLE ONLY restaurant_table
    ADD CONSTRAINT restaurant_table_area_id_fkey FOREIGN KEY (area_id) REFERENCES restaurant_area(id) ON DELETE CASCADE;
    
ALTER TABLE ONLY table_seat
    ADD CONSTRAINT table_seat_table_id_fkey FOREIGN KEY (table_id) REFERENCES restaurant_table(id) ON DELETE CASCADE;
    
ALTER TABLE ONLY restaurant_area
    ADD CONSTRAINT restaurant_area_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id) ON DELETE CASCADE;

-- downgrade

DROP TABLE table_seat;

ALTER TABLE ONLY restaurant_table
    DROP CONSTRAINT restaurant_table_pkey;

ALTER TABLE ONLY restaurant_table
    DROP CONSTRAINT restaurant_table_area_id_fkey;

DROP SEQUENCE restaurant_table_seq CASCADE;

ALTER TABLE restaurant_table DROP COLUMN id;
ALTER TABLE restaurant_table ALTER COLUMN table_number TYPE VARCHAR;
ALTER TABLE restaurant_table DROP COLUMN area_id;
ALTER TABLE restaurant_table DROP COLUMN name;
ALTER TABLE restaurant_table DROP COLUMN current_status;
ALTER TABLE restaurant_table DROP COLUMN status_updated_at;
ALTER TABLE restaurant_table DROP COLUMN x;
ALTER TABLE restaurant_table DROP COLUMN y;
ALTER TABLE restaurant_table DROP COLUMN width;
ALTER TABLE restaurant_table DROP COLUMN height;
ALTER TABLE restaurant_table DROP COLUMN is_deleted;


DROP TABLE restaurant_area;