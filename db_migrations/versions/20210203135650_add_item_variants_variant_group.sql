-- revision: '20210203135650_add_item_variants_variant_group'
-- down_revision: '20210203111106_add_combo_combo_item'

-- upgrade

--
-- Name: variant_group, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE variant_group (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    name character varying NOT NULL,
    display_name character varying,
    can_select_multiple boolean NOT NULL,
    can_select_quantity boolean NOT NULL,
    minimum_selectable_quantity int,
    maximum_selectable_quantity int,
    is_customisation boolean NOT NULL,
    seller_id character varying NOT NULL
);

--
-- Name: variant, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE variant (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    name character varying NOT NULL,
    display_order int NOT NULL,
    variant_group_id int NOT NULL
);

ALTER TABLE ONLY variant
    ADD CONSTRAINT variant_variant_group_id_fkey FOREIGN KEY (variant_group_id) REFERENCES variant_group(id) on DELETE CASCADE;


--
-- Name: item_variant_variant_association, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE item_variant_variant_association (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    item_variant_id int NOT NULL,
    variant_id int NOT NULL
);

ALTER TABLE ONLY item_variant_variant_association
    ADD CONSTRAINT item_variant_variant_association_item_variant_id_fkey FOREIGN KEY (item_variant_id) REFERENCES item_variant(id) on DELETE CASCADE;
ALTER TABLE ONLY item_variant_variant_association
    ADD CONSTRAINT item_variant_variant_association_variant_id_fkey FOREIGN KEY (variant_id) REFERENCES variant(id) on DELETE CASCADE;


--
-- Name: side_item, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE side_item (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    item_id int NOT NULL,
    side_item_id int NOT NULL
);

ALTER TABLE ONLY side_item
    ADD CONSTRAINT side_item_item_item_id_fkey FOREIGN KEY (item_id) REFERENCES item(id) on DELETE CASCADE;
ALTER TABLE ONLY side_item
    ADD CONSTRAINT side_item_side_item_id_fkey FOREIGN KEY (side_item_id) REFERENCES item(id) on DELETE CASCADE;

-- downgrade

DROP TABLE item_variant_variant_association;
DROP TABLE variant;
DROP TABLE variant_group;
DROP TABLE side_item;