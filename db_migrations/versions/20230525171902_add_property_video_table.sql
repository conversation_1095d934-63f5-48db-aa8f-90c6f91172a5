-- revision: '20230525171902_add_property_video_table'
-- down_revision: '20220803023534_added_column_has_slab_based_taxation'

-- upgrade
CREATE TABLE property_video (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id serial NOT NULL,
    video_url text NOT NULL,
    property_id character varying NOT NULL,
    tag_description text,
    sort_order integer,
    CONSTRAINT property_video_pkey PRIMARY KEY (id),
    CONSTRAINT property_video_url_key UNIQUE (video_url),
    CONSTRAINT property_video_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id)
);
CREATE INDEX ix_property_video_fkey_property_id ON property_video USING btree (property_id);

-- downgrade
DROP TABLE property_video;
