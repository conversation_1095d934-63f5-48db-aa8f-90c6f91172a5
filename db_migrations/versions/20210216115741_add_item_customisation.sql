-- revision: '20210216115741_add_item_customisation'
-- down_revision: '20210210190251_add_menu_item_item_variant_and__menu_item_item_side'

-- upgrade
--
-- Name: item_customisation, Type: TABLE, Schema: public; Owner: -
--
CREATE TABLE item_customisation (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,

    display_order int NOT NULL,
    delta_price decimal NOT NULL,

    item_id int,
    item_variant_id int,
    variant_id int NOT NULL
);

ALTER TABLE ONLY item_customisation
    ADD CONSTRAINT item_customisation_item_id_fkey FOREIGN KEY (item_id) REFERENCES item(id) ON DELETE CASCADE;

ALTER TABLE ONLY item_customisation
    ADD CONSTRAINT item_customisation_item_variant_id_fkey FOREIGN KEY (item_variant_id) REFERENCES item_variant(id) ON DELETE CASCADE;

ALTER TABLE ONLY item_customisation
    ADD CONSTRAINT item_customisation_variant_id_fkey FOREIGN KEY (variant_id) REFERENCES variant(id) ON DELETE CASCADE;



--
-- Name: menu_item_item_customisation, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE menu_item_item_customisation (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    menu_item_id int NOT NULL,
    item_customisation_id int NOT NULL
);

ALTER TABLE ONLY menu_item_item_customisation
    ADD CONSTRAINT menu_item_item_customisation_menu_id_fkey FOREIGN KEY (menu_item_id) REFERENCES menu_item(id) ON DELETE CASCADE;

ALTER TABLE ONLY menu_item_item_customisation
    ADD CONSTRAINT menu_item_item_customisation_item_customisation_id_fkey FOREIGN KEY (item_customisation_id) REFERENCES item_customisation(id) ON DELETE CASCADE;

-- downgrade
DROP TABLE menu_item_item_customisation;
DROP TABLE item_customisation;
