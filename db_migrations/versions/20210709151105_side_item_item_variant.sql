-- revision: '20210709151105_side_item_item_variant'
-- down_revision: '20210609191150_current_business_date_column'

-- upgrade
ALTER TABLE side_item ADD COLUMN side_item_variant_id INTEGER;
ALTER TABLE side_item ADD CONSTRAINT side_item_item_variant_id_fkey FOREIGN KEY (side_item_variant_id) REFERENCES item_variant(id) on DELETE CASCADE;
ALTER TABLE table_seat ADD COLUMN seat_number integer;

-- downgrade
ALTER TABLE side_item DROP CONSTRAINT side_item_item_variant_id_fkey;
ALTER TABLE side_item DROP COLUMN side_item_variant_id;
ALTER TABLE table_seat DROP COLUMN seat_number;
