-- revision: '20210203111106_add_combo_combo_item'
-- down_revision: '20210201151256_add_rack_rate_in_property_sku'

-- upgrade
--
-- Name: combo, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE combo (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    name character varying NOT NULL,
    display_name character varying,
    description character varying,
    contains_alcohol boolean NOT NULL,
    prep_time interval NOT NULL,
    pre_tax_price decimal NOT NULL,
    cost decimal NOT NULL,
    seller_id character varying NOT NULL,
    sku_id int NOT NULL,
    sku_category_code character varying NOT NULL
);


ALTER TABLE ONLY combo
    ADD CONSTRAINT combo_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id);


--
-- Name: combo_item; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE combo_item (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    combo_id int NOT NULL,
    item_id int NOT NULL,
    item_variant_id int
);

ALTER TABLE ONLY combo_item
    ADD CONSTRAINT combo_item_item_id_fkey FOREIGN KEY (item_id) REFERENCES item(id) ON DELETE CASCADE;


ALTER TABLE ONLY combo_item
    ADD CONSTRAINT combo_item_combo_id_fkey FOREIGN KEY (combo_id) REFERENCES combo(id) ON DELETE CASCADE;


ALTER TABLE ONLY combo_item
    ADD CONSTRAINT combo_item_item_variant_id_fkey FOREIGN KEY (item_variant_id) REFERENCES item_variant(id) ON DELETE CASCADE;

--
-- Name: menu_combo; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE menu_combo (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    combo_id int NOT NULL,
    menu_id int NOT NULL,
    display_order int NOT NULL,
    sold_out boolean NOT NULL
);

ALTER TABLE ONLY menu_combo
    ADD CONSTRAINT menu_combo_combo_id_fkey FOREIGN KEY (combo_id) REFERENCES combo(id) ON DELETE CASCADE;


ALTER TABLE ONLY menu_combo
    ADD CONSTRAINT menu_combo_menu_id_fkey FOREIGN KEY (menu_id) REFERENCES menu(id) ON DELETE CASCADE;

--
-- Name: menu_combo_category, Type: TABLE, Schema: public; Owner: -
--

CREATE TABLE menu_combo_category(
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    menu_combo_id int NOT NULL,
    menu_category_id int NOT NULL
);

ALTER TABLE ONLY menu_combo_category
    ADD CONSTRAINT menu_combo_category_menu_category_id_fkey FOREIGN KEY (menu_category_id) REFERENCES menu_category(id) ON DELETE CASCADE;

ALTER TABLE ONLY menu_combo_category
    ADD CONSTRAINT menu_combo_category_menu_combo_id_fkey FOREIGN KEY (menu_combo_id) REFERENCES menu_combo(id) ON DELETE CASCADE;


-- downgrade
DROP TABLE menu_combo_category;
DROP TABLE combo_item;
DROP TABLE menu_combo;
DROP TABLE combo;
