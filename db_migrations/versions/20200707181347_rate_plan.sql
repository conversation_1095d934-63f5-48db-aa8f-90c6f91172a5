-- revision: '20200707181347_rate_plan'
-- down_revision: '20200716194026_tenant_configs'

-- upgrade
CREATE TABLE addon(
	created_at timestamptz NULL,
	modified_at timestamptz NULL,
	id serial NOT NULL,
    name character varying(100) NOT NULL,
    code character varying(100) NOT NULL,
    CONSTRAINT addon_pkey PRIMARY KEY (id)
);


CREATE TABLE new_rate_plan(
    created_at timestamptz NULL,
	modified_at timestamptz NULL,
	id serial NOT NULL,
    name character varying(100) NOT NULL,
    code character varying(100) NOT NULL,
    CONSTRAINT new_rate_plan_pkey PRIMARY KEY (id)
);

CREATE TABLE new_rate_plan_config(
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    property_id character varying(100) NOT NULL,
    rate_plan_id int4 NOT NULL,
    room_type_id int4 NOT NULL,
    CONSTRAINT _unique_property_room_type_rate_plan PRIMARY KEY (property_id, rate_plan_id, room_type_id),
    CONSTRAINT new_rate_plan_config_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id),
    CONSTRAINT new_rate_plan_config_rate_plan_id_fkey FOREIGN KEY (rate_plan_id) REFERENCES new_rate_plan(id),
    CONSTRAINT new_rate_plan_config_room_type_id_fkey FOREIGN KEY (room_type_id) REFERENCES room_type(id)
);

CREATE TABLE rate_plan_addon(
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    rate_plan_id int4 NOT NULL,
    addon_id int4 NOT NULL,
    CONSTRAINT _unique_rate_plan_addon PRIMARY KEY (rate_plan_id, addon_id),
    CONSTRAINT rate_plan_addon_rate_plan_id_fkey FOREIGN KEY (rate_plan_id) REFERENCES new_rate_plan(id),
    CONSTRAINT rate_plan_addon_addon_id_fkey FOREIGN KEY (addon_id) REFERENCES addon(id)
);

-- downgrade

DROP TABLE new_rate_plan_config;
DROP TABLE addon
DROP TABLE new_rate_plan
DROP TABLE rate_plan_addon;
