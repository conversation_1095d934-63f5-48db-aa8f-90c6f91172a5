-- revision: '20200716194026_tenant_configs'
-- down_revision: '20200709184303_alter_room_type_pincode'

-- upgrade
CREATE TABLE currency_conversion_rate (
currency_conversion_rate_id serial NOT NULL,
created_at timestamptz NULL,
modified_at timestamptz NULL,
from_currency character varying NOT NULL,
to_currency character varying NOT NULL,
conversion_rate DECIMAL(15, 4) NOT NULL,
start_date DATE NOT NULL,
end_date DATE NOT NULL,
property_id character varying NOT NULL
);

ALTER TABLE currency_conversion_rate
    ADD CONSTRAINT currency_conversion_rate_pkey PRIMARY KEY (currency_conversion_rate_id);

ALTER TABLE currency_conversion_rate
    ADD CONSTRAINT currency_conversion_rate_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);

ALTER TABLE tenant_config DROP CONSTRAINT tenant_config_pkey;

ALTER TABLE tenant_config ADD COLUMN created_at timestamptz NULL;
ALTER TABLE tenant_config ADD COLUMN modified_at timestamptz NULL;
ALTER TABLE tenant_config ADD COLUMN tenant_config_id serial NOT NULL;
ALTER TABLE tenant_config ADD COLUMN active boolean default FALSE;
ALTER TABLE tenant_config ADD COLUMN property_id character varying NULL;

ALTER TABLE tenant_config
    ADD CONSTRAINT tenant_config_pkey PRIMARY KEY (tenant_config_id);

ALTER TABLE ONLY tenant_config
    ADD CONSTRAINT _unique_config_name_property_id UNIQUE (config_name, property_id);

ALTER TABLE tenant_config ADD UNIQUE(config_name, property_id);

CREATE UNIQUE INDEX _unique_config_name_with_property_null ON tenant_config (config_name, (property_id IS NULL)) WHERE property_id IS NULL;

ALTER TABLE tenant_config
    ADD CONSTRAINT tenant_config_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);

ALTER TABLE property ADD COLUMN base_currency_code character varying NULL;

ALTER TABLE seller ADD COLUMN base_currency_code character varying NULL;

-- downgrade
ALTER TABLE currency_conversion_rate DROP CONSTRAINT currency_conversion_rate_property_id_fkey;
DROP TABLE currency_conversion_rate;

DROP index _unique_config_name_with_property_null;
ALTER TABLE tenant_config DROP CONSTRAINT tenant_config_property_id_fkey;
ALTER TABLE tenant_config DROP CONSTRAINT tenant_config_pkey;
ALTER TABLE tenant_config DROP COLUMN property_id;
ALTER TABLE tenant_config DROP COLUMN active;
ALTER TABLE tenant_config DROP COLUMN tenant_config_id;
ALTER TABLE tenant_config DROP COLUMN modified_at;
ALTER TABLE tenant_config DROP COLUMN created_at;
ALTER TABLE tenant_config ADD CONSTRAINT tenant_config_pkey PRIMARY KEY (config_name);

ALTER TABLE property DROP COLUMN base_currency_code;

ALTER TABLE seller DROP COLUMN base_currency_code;