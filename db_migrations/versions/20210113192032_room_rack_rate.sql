-- revision: '20210113192032_room_rack_rate'
-- down_revision: '20210112150652_add_menu_menu_timing_category'

-- upgrade
CREATE TABLE room_rack_rate (
    room_rack_rate_id CHARACTER VARYING PRIMARY KEY,
    property_id CHARACTER VARYING NOT NULL,
    room_type_id INTEGER NOT NULL,
    adult_count INTEGER NOT NULL,
    rack_rate NUMERIC(15, 4) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    modified_at timestamp with time zone DEFAULT now()
);

ALTER TABLE ONLY room_rack_rate
    ADD CONSTRAINT room_rack_rate_uc1 UNIQUE (property_id, room_type_id, adult_count);

-- downgrade
ALTER TABLE room_rack_rate DROP CONSTRAINT room_rack_rate_uc1;
DROP TABLE room_rack_rate;
