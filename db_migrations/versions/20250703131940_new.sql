-- revision: '20250703131940_new'
-- down_revision: '20250621000000_department_profit_center'

-- upgrade
DROP TABLE IF EXISTS property_brand;
ALTER TABLE property DROP COLUMN IF EXISTS brand_id;

ALTER TABLE department_template DROP CONSTRAINT IF EXISTS department_template_brand_id_fkey;
ALTER TABLE department_template DROP COLUMN IF EXISTS brand_id;

ALTER TABLE profit_center_template DROP CONSTRAINT IF EXISTS profit_center_template_brand_id_fkey;
ALTER TABLE profit_center_template DROP COLUMN IF EXISTS brand_id;

ALTER TABLE sku DROP COLUMN IF EXISTS default_department_template_code;

DROP INDEX IF EXISTS ix_property_brand_id;
ALTER TABLE property DROP CONSTRAINT IF EXISTS fk_property_brand;

DROP TABLE IF EXISTS brand_department_template_map;
DROP INDEX IF EXISTS idx_brand_department_template;

DROP TABLE IF EXISTS brand_profit_center_template_map;
DROP INDEX IF EXISTS idx_brand_profit_center_template;

CREATE TABLE brand_department_template_map (
    brand_id INTEGER NOT NULL REFERENCES brand(id) ON DELETE CASCADE,
    department_template_id INTEGER NOT NULL REFERENCES department_template(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    modified_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (brand_id, department_template_id)
);

CREATE TABLE brand_profit_center_template_map (
    brand_id INTEGER NOT NULL REFERENCES brand(id) ON DELETE CASCADE,
    profit_center_template_id INTEGER NOT NULL REFERENCES profit_center_template(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    modified_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (brand_id, profit_center_template_id)
);

CREATE TABLE sku_department_template_map (
    sku_id INTEGER NOT NULL,
    brand_id INTEGER NOT NULL,
    department_template_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    modified_at TIMESTAMPTZ DEFAULT now(),
    PRIMARY KEY (sku_id, brand_id, department_template_id),
    FOREIGN KEY (sku_id) REFERENCES sku(id) ON DELETE CASCADE,
    FOREIGN KEY (brand_id, department_template_id)
        REFERENCES brand_department_template_map(brand_id, department_template_id)
        ON DELETE CASCADE
);

CREATE TABLE sku_profit_center_template_map (
    sku_id INTEGER NOT NULL,
    brand_id INTEGER NOT NULL,
    profit_center_template_id INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    modified_at TIMESTAMPTZ DEFAULT now(),
    PRIMARY KEY (sku_id, brand_id, profit_center_template_id),
    FOREIGN KEY (sku_id) REFERENCES sku(id) ON DELETE CASCADE,
    FOREIGN KEY (brand_id, profit_center_template_id)
        REFERENCES brand_profit_center_template_map(brand_id, profit_center_template_id)
        ON DELETE CASCADE
);

ALTER TABLE sku
DROP COLUMN IF EXISTS profit_center_template_code;
ALTER TABLE transaction_default_mapping
ADD COLUMN department_template_code VARCHAR(50);

CREATE INDEX ix_transaction_default_mapping_department_template_code
ON transaction_default_mapping (department_template_code);

-- downgrade
