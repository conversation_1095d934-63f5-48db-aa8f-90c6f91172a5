-- revision: '20250621000000_department_profit_center'
-- down_revision: '20250415020832_adding_audits'

-- upgrade

-- Department Templates (Brand Level) - Simplified version
CREATE TABLE department_template (
    id SERIAL PRIMARY KEY,
    brand_id INTEGER NOT NULL REFERENCES brand(id),
    code VA<PERSON>HAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    financial_code VARCHAR(20),
    parent_id INTEGER REFERENCES department_template(id) ON DELETE RESTRICT,
    description TEXT,
    auto_create_on_property_launch BOOLEAN NOT NULL DEFAULT TRUE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT uq_department_template_brand_code UNIQUE (brand_id, code)
);


CREATE INDEX idx_department_template_brand_id ON department_template(brand_id);
CREATE INDEX idx_department_template_parent_code ON department_template(parent_id);
CREATE INDEX idx_department_template_active ON department_template(is_active);

-- Profit Center Templates (Brand Level) - For seller auto-creation
CREATE TABLE profit_center_template (
    id SERIAL PRIMARY KEY,
    brand_id INTEGER NOT NULL REFERENCES brand(id),
    code VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    department_template_code VARCHAR(50) NOT NULL,
    system_interface VARCHAR(100), -- POS system interface (INTERNAL_POS, EXTERNAL_POS, MANUAL)
    description TEXT,
    auto_create_on_property_launch BOOLEAN NOT NULL DEFAULT TRUE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT uq_profit_center_template_brand_code UNIQUE (brand_id, code)
);

CREATE INDEX idx_profit_center_template_brand_id ON profit_center_template(brand_id);
CREATE INDEX idx_profit_center_template_dept_code ON profit_center_template(department_template_code);
CREATE INDEX idx_profit_center_template_active ON profit_center_template(is_active);

-- Property Departments (Property Level)
CREATE TABLE department (
    id SERIAL PRIMARY KEY,
    property_id VARCHAR NOT NULL REFERENCES property(id),
    template_code VARCHAR(50),
    code VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    financial_code VARCHAR(20),
    parent_id INTEGER REFERENCES department(id),
    description TEXT,
    is_custom BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT uq_department_property_code UNIQUE (property_id, code)
);

CREATE INDEX idx_department_property_id ON department(property_id);
CREATE INDEX idx_department_parent_id ON department(parent_id);
CREATE INDEX idx_department_template_code ON department(template_code);
CREATE INDEX idx_department_active ON department(is_active);

-- Enhance SKU Master (Minimal Changes)
ALTER TABLE sku ADD COLUMN default_department_template_code VARCHAR(50);
ALTER TABLE sku ADD COLUMN profit_center_template_code VARCHAR(50);
ALTER TABLE sku ADD COLUMN auto_create_seller_sku BOOLEAN NOT NULL DEFAULT FALSE;

CREATE INDEX idx_sku_dept_template ON sku(default_department_template_code);
CREATE INDEX idx_sku_pc_template ON sku(profit_center_template_code);

-- Enhance Property SKU
ALTER TABLE property_sku ADD COLUMN department_id INTEGER REFERENCES department(id);
CREATE INDEX idx_property_sku_department ON property_sku(department_id);

-- Enhance Seller (Keep as profit centers)
ALTER TABLE seller ADD COLUMN department_id INTEGER REFERENCES department(id);
ALTER TABLE seller ADD COLUMN created_from_template_code VARCHAR(50);
ALTER TABLE seller ADD COLUMN system_interface VARCHAR(100); -- POS interface
ALTER TABLE seller ADD COLUMN is_auto_created BOOLEAN NOT NULL DEFAULT FALSE;

CREATE INDEX idx_seller_department ON seller(department_id);
CREATE INDEX idx_seller_template_code ON seller(created_from_template_code);

-- Enhance Seller SKU (Department from SKU, not seller)
ALTER TABLE seller_sku ADD COLUMN department_id INTEGER REFERENCES department(id);
CREATE INDEX idx_seller_sku_department ON seller_sku(department_id);

-- Transaction & Payment Models (Keep from original PR)
CREATE TABLE transaction_master (
    id SERIAL PRIMARY KEY,
    transaction_code VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    property_id VARCHAR NOT NULL REFERENCES property(id),
    entity_type VARCHAR(50) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL,
    transaction_id VARCHAR(100) NOT NULL,
    operational_unit_id VARCHAR(100) NOT NULL,
    operational_unit_type VARCHAR(50) NOT NULL,
    source VARCHAR(100) NOT NULL,
    gl_code VARCHAR(50),
    erp_id VARCHAR(100),
    is_merge BOOLEAN NOT NULL DEFAULT FALSE,
    particulars TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    transaction_details JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_transaction_master_property_id ON transaction_master(property_id);
CREATE INDEX idx_transaction_master_entity_type ON transaction_master(entity_type);
CREATE INDEX idx_transaction_master_transaction_type ON transaction_master(transaction_type);
CREATE INDEX idx_transaction_master_transaction_id ON transaction_master(transaction_id);
CREATE INDEX idx_transaction_master_operational_unit ON transaction_master(operational_unit_id);
CREATE INDEX idx_transaction_master_status ON transaction_master(status);

CREATE TABLE transaction_default_mapping (
    id SERIAL PRIMARY KEY,
    brand_id INTEGER NOT NULL REFERENCES brand(id),
    transaction_type VARCHAR(50) NOT NULL,
    transaction_type_code VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    default_gl_code VARCHAR(50),
    default_erp_id VARCHAR(100),
    default_particulars TEXT,
    default_is_merge BOOLEAN NOT NULL DEFAULT FALSE,
    transaction_details JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT uq_transaction_default_mapping UNIQUE (brand_id, transaction_type, transaction_type_code, entity_type)
);

CREATE INDEX idx_transaction_default_brand_id ON transaction_default_mapping(brand_id);
CREATE INDEX idx_transaction_default_transaction_type ON transaction_default_mapping(transaction_type);
CREATE INDEX idx_transaction_default_transaction_type_code ON transaction_default_mapping(transaction_type_code);
CREATE INDEX idx_transaction_default_entity_type ON transaction_default_mapping(entity_type);

-- downgrade

-- Drop indexes and columns in reverse order
DROP INDEX IF EXISTS idx_seller_sku_department;
ALTER TABLE seller_sku DROP COLUMN IF EXISTS department_id;

DROP INDEX IF EXISTS idx_seller_department;
DROP INDEX IF EXISTS idx_seller_template_code;
ALTER TABLE seller DROP COLUMN IF EXISTS department_id;
ALTER TABLE seller DROP COLUMN IF EXISTS created_from_template_code;
ALTER TABLE seller DROP COLUMN IF EXISTS system_interface;
ALTER TABLE seller DROP COLUMN IF EXISTS is_auto_created;

DROP INDEX IF EXISTS idx_property_sku_department;
ALTER TABLE property_sku DROP COLUMN IF EXISTS department_id;

DROP INDEX IF EXISTS idx_sku_dept_template;
DROP INDEX IF EXISTS idx_sku_pc_template;
ALTER TABLE sku DROP COLUMN IF EXISTS default_department_template_code;
ALTER TABLE sku DROP COLUMN IF EXISTS profit_center_template_code;
ALTER TABLE sku DROP COLUMN IF EXISTS auto_create_seller_sku;

DROP TABLE IF EXISTS property_payment_method;
DROP TABLE IF EXISTS payment_method;
DROP TABLE IF EXISTS transaction_default_mapping;
DROP TABLE IF EXISTS transaction_master;
DROP TABLE IF EXISTS department;
DROP TABLE IF EXISTS profit_center_template;
DROP TABLE IF EXISTS department_template;
