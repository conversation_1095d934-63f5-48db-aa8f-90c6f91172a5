-- revision: '20200605113837_initial_migration'
-- down_revision: ''

-- upgrade
CREATE TYPE ac_choices AS ENUM (
    'CENTRALISED',
    'SPLIT',
    'WINDOW'
);


--
-- Name: account_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE account_type_choices AS ENUM (
    'SAVINGS',
    'CURRENT'
);


--
-- Name: application_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE application_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: brand_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE brand_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: breakfast_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE breakfast_type_choices AS ENUM (
    'A_LA_CARTE',
    'BUFFET',
    'FIXED'
);


--
-- Name: bucket_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE bucket_choices AS ENUM (
    'ON_REQUEST',
    'ALL_ROOMS'
);


--
-- Name: building_style_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE building_style_choices AS ENUM (
    'INDEPENDENT_SINGLE',
    'INDEPENDENT_MULTIPLE',
    'PART_OF_SINGLE_BUILDING',
    'PART_OF_MULTIPLE_BUILDING',
    'FLOORS_IN_EACH_BUILDING'
);


--
-- Name: channel_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE channel_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: connection_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE connection_choices AS ENUM (
    'CABLE',
    'DTH'
);


--
-- Name: extra_bed_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE extra_bed_choices AS ENUM (
    'MATTRESS',
    'FOLDING_BED',
    'SINGLE_BED'
);


--
-- Name: fan_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE fan_type_choices AS ENUM (
    'CEILING',
    'STANDING'
);


--
-- Name: file_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE file_type_choices AS ENUM (
    'COMPLETE_AGREEMENT',
    'AGREEMENT_FIRST_PAGE',
    'AGREEMENT_EXPIRY',
    'AGREEMENT_DETAILS',
    'SERVICE_TAX_ONE',
    'SERVICE_TAX_TWO',
    'LUXURY_TAX_ONE',
    'LUXURY_TAX_TWO',
    'OTA_NOC',
    'GI_NOC',
    'LEASE_DOCUMENT',
    'ELECTRIC_BILL',
    'PARTNER_CANCELLED_CHEQUE',
    'HOTEL_REGISTRATION_DOCUMENT',
    'PAN_CARD',
    'OLD_FRANCHISE_TERMINATION_DOCUMENT',
    'PARTNER_OLD_PHOTO',
    'PARTNER_PROFESSIONAL_PHOTO',
    'FACADE_IMAGE',
    'HIGH_RES_IMAGE',
    'MID_RES_IMAGE',
    'LOW_RES_IMAGE',
    'TREEBO_SERVICE_TAX',
    'TREEBO_CANCELLED_CHEQUE',
    'GENERIC_IMAGE',
    'FILE_TYPE_C_FORM'
);


--
-- Name: gender_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE gender_choices AS ENUM (
    'M',
    'F'
);


--
-- Name: heater_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE heater_choices AS ENUM (
    'ON_REQUEST',
    'ALL_ROOMS'
);


--
-- Name: lock_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE lock_type_choices AS ENUM (
    'ELECTRONIC',
    'MANUAL'
);


--
-- Name: mosquito_repellent_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE mosquito_repellent_choices AS ENUM (
    'ON_REQUEST',
    'ALL_ROOMS'
);


--
-- Name: neighbourhood_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE neighbourhood_choices AS ENUM (
    'QUIET',
    'RESIDENTIAL',
    'GREEN',
    'BUSY_MARKET',
    'MAIN_ROAD',
    'OTHERS'
);


--
-- Name: parking_location_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE parking_location_choices AS ENUM (
    'INDOORS_BASEMENT',
    'OUTDOORS_UNCOVERED'
);


--
-- Name: pool_location_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE pool_location_choices AS ENUM (
    'INDOORS_COVERED',
    'OUTDOORS_UNCOVERED'
);


--
-- Name: pricing_mapping_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE pricing_mapping_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: pricing_policy_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE pricing_policy_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: property_brand_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE property_brand_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: property_sku_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE property_sku_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: property_style_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE property_style_choices AS ENUM (
    'HERITAGE',
    'MODERN',
    'GUEST_HOUSE',
    'PLAIN_VANILLA',
    'OTHERS'
);


--
-- Name: property_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE property_type_choices AS ENUM (
    'HOMESTAY',
    'HOMESTAY_COTTAGE_VILLA',
    'HOTEL',
    'RESORT',
    'SERVICE_APARTMENT'
);


--
-- Name: provider_brand_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE provider_brand_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: provider_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE provider_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: room_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE room_type_choices AS ENUM (
    'ACACIA',
    'MAHOGANY',
    'MAPLE',
    'OAK',
    'OFFLINE'
);


--
-- Name: seller_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE seller_type_choices AS ENUM (
    'MARKETPLACE',
    'RESELLER'
);


--
-- Name: service_area_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE service_area_choices AS ENUM (
    'DINING_AREA',
    'RESTAURANT',
    'ROOM'
);


--
-- Name: sku_choice; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE sku_choice AS ENUM (
    'sku',
    'bundle'
);


--
-- Name: station_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE station_choices AS ENUM (
    'AIRPORT',
    'RAILWAY_STATION',
    'INTER_CITY_BUS_STAND',
    'LOCAL_BUS_STAND',
    'METRO_STATION',
    'LOCAL_TRAIN_STATION'
);


--
-- Name: status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: status_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE status_choices AS ENUM (
    'NEAR_CONFIRMED',
    'NOT_SIGNING',
    'SIGNED',
    'DROPPED_POST_SIGNING',
    'LIVE',
    'CHURNED'
);


--
-- Name: stove_availability_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE stove_availability_choices AS ENUM (
    'ALL_ROOMS',
    'ON_REQUEST'
);


--
-- Name: stove_type_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE stove_type_choices AS ENUM (
    'LPG',
    'INDUCTION'
);


--
-- Name: sub_channel_status; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE sub_channel_status AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


--
-- Name: superheropriceslabchoices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE superheropriceslabchoices AS ENUM (
    'PLATINUM',
    'SILVER',
    'GOLD_OPS',
    'GOLD_SALES'
);


--
-- Name: tax_choice; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE tax_choice AS ENUM (
    'unit',
    'composite',
    'derived'
);


--
-- Name: tv_choices; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE tv_choices AS ENUM (
    'LCD',
    'CRT'
);

CREATE TABLE alembic_version (
    version_num character varying(32) NOT NULL
);


--
-- Name: amenity_ac; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_ac (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    ac_type ac_choices
);


--
-- Name: amenity_ac_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_ac_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_ac_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_ac_id_seq OWNED BY amenity_ac.id;


--
-- Name: amenity_breakfast; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_breakfast (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    type breakfast_type_choices,
    service_area service_area_choices,
    non_veg boolean,
    rotational boolean
);


--
-- Name: amenity_breakfast_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_breakfast_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_breakfast_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_breakfast_id_seq OWNED BY amenity_breakfast.id;


--
-- Name: amenity_disable_friendly; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_disable_friendly (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    ramp_available boolean,
    wheelchair_count integer,
    disable_friendly_room_available boolean,
    disable_friendly_rooms character varying(500)
);


--
-- Name: amenity_disable_friendly_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_disable_friendly_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_disable_friendly_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_disable_friendly_id_seq OWNED BY amenity_disable_friendly.id;


--
-- Name: amenity_elevator; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_elevator (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    floors_accessible character varying(100)
);


--
-- Name: amenity_elevator_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_elevator_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_elevator_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_elevator_id_seq OWNED BY amenity_elevator.id;


--
-- Name: amenity_gym; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_gym (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    open_time time without time zone,
    close_time time without time zone,
    equipments_available character varying(1000),
    active boolean
);


--
-- Name: amenity_gym_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_gym_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_gym_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_gym_id_seq OWNED BY amenity_gym.id;


--
-- Name: amenity_heater; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_heater (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    availability heater_choices,
    charges character varying(500)
);


--
-- Name: amenity_heater_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_heater_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_heater_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_heater_id_seq OWNED BY amenity_heater.id;


--
-- Name: amenity_hot_water; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_hot_water (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    central_geyser boolean,
    room_geyser boolean,
    from_time time without time zone,
    to_time time without time zone
);


--
-- Name: amenity_hot_water_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_hot_water_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_hot_water_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_hot_water_id_seq OWNED BY amenity_hot_water.id;


--
-- Name: amenity_intercom; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_intercom (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    all_rooms_connected boolean
);


--
-- Name: amenity_intercom_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_intercom_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_intercom_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_intercom_id_seq OWNED BY amenity_intercom.id;


--
-- Name: amenity_laundry; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_laundry (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    pickup_time character varying(500),
    drop_time character varying(500),
    is_external boolean
);


--
-- Name: amenity_laundry_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_laundry_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_laundry_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_laundry_id_seq OWNED BY amenity_laundry.id;


--
-- Name: amenity_parking; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_parking (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    location parking_location_choices,
    max_two_wheelers integer,
    max_four_wheelers integer,
    charges character varying(500)
);


--
-- Name: amenity_parking_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_parking_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_parking_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_parking_id_seq OWNED BY amenity_parking.id;


--
-- Name: amenity_payment; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_payment (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    amex_accepted boolean,
    wallet_accepted boolean
);


--
-- Name: amenity_payment_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_payment_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_payment_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_payment_id_seq OWNED BY amenity_payment.id;


--
-- Name: amenity_private_cab; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_private_cab (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    charges character varying(500)
);


--
-- Name: amenity_private_cab_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_private_cab_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_private_cab_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_private_cab_id_seq OWNED BY amenity_private_cab.id;


--
-- Name: amenity_public_washroom; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_public_washroom (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    gender_segregated boolean
);


--
-- Name: amenity_public_washroom_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_public_washroom_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_public_washroom_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_public_washroom_id_seq OWNED BY amenity_public_washroom.id;


--
-- Name: amenity_spa; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_spa (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    open_time time without time zone,
    close_time time without time zone,
    active boolean
);


--
-- Name: amenity_spa_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_spa_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_spa_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_spa_id_seq OWNED BY amenity_spa.id;


--
-- Name: amenity_stove; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_stove (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    stove_type stove_type_choices,
    availability stove_availability_choices
);


--
-- Name: amenity_stove_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_stove_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_stove_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_stove_id_seq OWNED BY amenity_stove.id;


--
-- Name: amenity_summary; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_summary (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    summary text NOT NULL
);


--
-- Name: amenity_summary_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_summary_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_summary_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_summary_id_seq OWNED BY amenity_summary.id;


--
-- Name: amenity_swimming_pool; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_swimming_pool (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    location pool_location_choices,
    pool_size character varying(100),
    open_time time without time zone,
    close_time time without time zone,
    active boolean
);


--
-- Name: amenity_swimming_pool_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_swimming_pool_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_swimming_pool_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_swimming_pool_id_seq OWNED BY amenity_swimming_pool.id;


--
-- Name: amenity_tv; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_tv (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    vendor character varying(100),
    tv_type tv_choices,
    connection_type connection_choices,
    size character varying(50)
);


--
-- Name: amenity_tv_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_tv_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_tv_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_tv_id_seq OWNED BY amenity_tv.id;


--
-- Name: amenity_twin_bed; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE amenity_twin_bed (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    joinable boolean
);


--
-- Name: amenity_twin_bed_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE amenity_twin_bed_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: amenity_twin_bed_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE amenity_twin_bed_id_seq OWNED BY amenity_twin_bed.id;


--
-- Name: application; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE application (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id character varying(100) NOT NULL,
    name character varying(100) NOT NULL,
    status application_status,
    channel_id character varying(100) NOT NULL,
    description character varying(400)
);


--
-- Name: bank_detail; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE bank_detail (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    account_name character varying(100) NOT NULL,
    account_number character varying(50) NOT NULL,
    account_type account_type_choices,
    ifsc_code character varying(100) NOT NULL,
    bank character varying(100) NOT NULL,
    branch character varying(100)
);


--
-- Name: bank_detail_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE bank_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: bank_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE bank_detail_id_seq OWNED BY bank_detail.id;


--
-- Name: banquet_hall; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE banquet_hall (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    name character varying(100),
    floor integer,
    capacity integer,
    size character varying(100)
);


--
-- Name: banquet_hall_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE banquet_hall_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: banquet_hall_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE banquet_hall_id_seq OWNED BY banquet_hall.id;


--
-- Name: bar; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE bar (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    name character varying(100),
    open_time time without time zone,
    last_order_time time without time zone,
    close_time time without time zone,
    room_start_time time without time zone,
    room_end_time time without time zone
);


--
-- Name: bar_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE bar_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: bar_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE bar_id_seq OWNED BY bar.id;


--
-- Name: brand; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE brand (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    code character varying(100) NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(100) NOT NULL,
    legal_name character varying(100) NOT NULL,
    short_description character varying(400),
    long_description text,
    status brand_status,
    logo_path text NOT NULL,
    color character varying(20)
);


--
-- Name: brand_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE brand_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: brand_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE brand_id_seq OWNED BY brand.id;


--
-- Name: breakfast_cuisine; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE breakfast_cuisine (
    id integer NOT NULL,
    cuisine_id integer NOT NULL,
    breakfast_id integer NOT NULL
);


--
-- Name: breakfast_cuisine_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE breakfast_cuisine_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: breakfast_cuisine_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE breakfast_cuisine_id_seq OWNED BY breakfast_cuisine.id;


--
-- Name: channel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE channel (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id character varying(100) NOT NULL,
    name character varying(100) NOT NULL,
    status channel_status,
    description character varying(400)
);


--
-- Name: city; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE city (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    state_id integer NOT NULL,
    latitude numeric(9,6),
    longitude numeric(9,6),
    cluster_id integer
);


--
-- Name: city_alias; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE city_alias (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    city_id integer NOT NULL
);


--
-- Name: city_alias_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE city_alias_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: city_alias_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE city_alias_id_seq OWNED BY city_alias.id;


--
-- Name: city_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE city_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: city_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE city_id_seq OWNED BY city.id;


--
-- Name: cluster; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE cluster (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    region_id integer
);


--
-- Name: cluster_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE cluster_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cluster_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE cluster_id_seq OWNED BY cluster.id;


--
-- Name: country; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE country (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL
);


--
-- Name: country_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE country_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: country_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE country_id_seq OWNED BY country.id;


--
-- Name: cs_user; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE cs_user (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    email character varying(255) NOT NULL,
    password character varying(255) NOT NULL,
    active boolean
);


--
-- Name: cs_user_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE cs_user_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cs_user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE cs_user_id_seq OWNED BY cs_user.id;


--
-- Name: cuisine; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE cuisine (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(50)
);


--
-- Name: cuisine_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE cuisine_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: cuisine_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE cuisine_id_seq OWNED BY cuisine.id;


--
-- Name: description; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE description (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    property_description character varying(5000),
    acacia_description character varying(5000),
    oak_description character varying(5000),
    maple_description character varying(5000),
    mahogany_description character varying(5000),
    trilight_one character varying(500),
    trilight_two character varying(500),
    trilight_three character varying(500)
);


--
-- Name: description_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE description_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: description_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE description_id_seq OWNED BY description.id;


--
-- Name: facility_category; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE facility_category (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    category character varying(100) NOT NULL
);


--
-- Name: facility_category_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE facility_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: facility_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE facility_category_id_seq OWNED BY facility_category.id;


--
-- Name: facility_category_mapping; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE facility_category_mapping (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    category_id integer NOT NULL,
    facility_name character varying NOT NULL
);


--
-- Name: facility_category_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE facility_category_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: facility_category_mapping_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE facility_category_mapping_id_seq OWNED BY facility_category_mapping.id;


--
-- Name: global_policy; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE global_policy (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    policy_type character varying(80) NOT NULL,
    title character varying(80) NOT NULL,
    description character varying(800) NOT NULL,
    display_in_need_to_know boolean,
    display_in_policy boolean
);


--
-- Name: global_policy_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE global_policy_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: global_policy_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE global_policy_id_seq OWNED BY global_policy.id;


--
-- Name: google_drive_base_folder; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE google_drive_base_folder (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying,
    folder_name character varying(50) NOT NULL,
    folder_id character varying(50) NOT NULL,
    folder_link character varying(500) NOT NULL,
    property_documents_file_id character varying(50),
    property_documents_link character varying(500),
    property_images_file_id character varying(50),
    property_images_link character varying(500)
);


--
-- Name: google_drive_base_folder_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE google_drive_base_folder_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: google_drive_base_folder_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE google_drive_base_folder_id_seq OWNED BY google_drive_base_folder.id;


--
-- Name: google_drive_file; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE google_drive_file (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying,
    file_id character varying(50),
    file_name character varying(100),
    file_type file_type_choices NOT NULL
);


--
-- Name: google_drive_file_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE google_drive_file_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: google_drive_file_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE google_drive_file_id_seq OWNED BY google_drive_file.id;


--
-- Name: guest_facing_process; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE guest_facing_process (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    checkin_time time without time zone,
    checkout_time time without time zone,
    free_early_checkin time without time zone,
    free_late_checkout time without time zone,
    early_checkin_fee character varying(100) NOT NULL,
    late_checkout_fee character varying(100) NOT NULL,
    checkin_grace_time integer,
    checkout_grace_time integer,
    switch_over_time time without time zone
);


--
-- Name: guest_facing_process_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE guest_facing_process_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: guest_facing_process_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE guest_facing_process_id_seq OWNED BY guest_facing_process.id;


--
-- Name: guest_type; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE guest_type (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    type character varying(20) NOT NULL
);


--
-- Name: guest_type_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE guest_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: guest_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE guest_type_id_seq OWNED BY guest_type.id;


--
-- Name: guest_type_property; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE guest_type_property (
    id integer NOT NULL,
    guest_type_id integer NOT NULL,
    property_id character varying NOT NULL
);


--
-- Name: guest_type_property_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE guest_type_property_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: guest_type_property_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE guest_type_property_id_seq OWNED BY guest_type_property.id;


--
-- Name: landmark; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE landmark (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying,
    type character varying(50),
    name character varying(100) NOT NULL,
    latitude numeric(9,6),
    longitude numeric(9,6),
    distance_from_property numeric(5,2),
    property_direction character varying(1000),
    hatchback_cab_fare numeric(7,2),
    sedan_cab_fare numeric(7,2)
);


--
-- Name: landmark_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE landmark_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: landmark_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE landmark_id_seq OWNED BY landmark.id;


--
-- Name: locality; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE locality (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    city_id integer NOT NULL,
    micro_market_id integer,
    latitude numeric(9,6),
    longitude numeric(9,6)
);


--
-- Name: locality_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE locality_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: locality_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE locality_id_seq OWNED BY locality.id;


--
-- Name: location; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE location (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    latitude numeric(9,6),
    longitude numeric(9,6),
    pincode integer,
    postal_address character varying(1000),
    maps_link character varying(1000),
    micro_market_id integer,
    locality_id integer,
    city_id integer NOT NULL,
    legal_address text,
    legal_city_id integer,
    legal_pincode integer
);


--
-- Name: location_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE location_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: location_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE location_id_seq OWNED BY location.id;


--
-- Name: lock_monitor; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW lock_monitor AS
 SELECT COALESCE(((blockingl.relation)::regclass)::text, blockingl.locktype) AS locked_item,
    (now() - blockeda.query_start) AS waiting_duration,
    blockeda.pid AS blocked_pid,
    blockeda.query AS blocked_query,
    blockedl.mode AS blocked_mode,
    blockinga.pid AS blocking_pid,
    blockinga.query AS blocking_query,
    blockingl.mode AS blocking_mode
   FROM (((pg_locks blockedl
     JOIN pg_stat_activity blockeda ON ((blockedl.pid = blockeda.pid)))
     JOIN pg_locks blockingl ON ((((blockingl.transactionid = blockedl.transactionid) OR ((blockingl.relation = blockedl.relation) AND (blockingl.locktype = blockedl.locktype))) AND (blockedl.pid <> blockingl.pid))))
     JOIN pg_stat_activity blockinga ON (((blockingl.pid = blockinga.pid) AND (blockinga.datid = blockeda.datid))))
  WHERE ((NOT blockedl.granted) AND (blockinga.datname = current_database()));


--
-- Name: micro_market; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE micro_market (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    city_id integer NOT NULL
);


--
-- Name: micro_market_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE micro_market_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: micro_market_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE micro_market_id_seq OWNED BY micro_market.id;


--
-- Name: migration_detail; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE migration_detail (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    migration_sheet_name character varying(200) NOT NULL,
    is_success boolean,
    old_hotel boolean,
    error_message character varying(1000)
);


--
-- Name: migration_detail_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE migration_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: migration_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE migration_detail_id_seq OWNED BY migration_detail.id;


--
-- Name: neighbouring_place; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE neighbouring_place (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    nearest_hospital character varying(1000),
    utility_shops character varying(1000),
    restaurants character varying(1000),
    tourist_spots character varying(1000),
    corporate_offices character varying(1000),
    popular_malls character varying(1000),
    shopping_streets character varying(1000),
    city_centre character varying(1000)
);


--
-- Name: neighbouring_place_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE neighbouring_place_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: neighbouring_place_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE neighbouring_place_id_seq OWNED BY neighbouring_place.id;


--
-- Name: notification; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE notification (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    type character varying(100),
    receivers character varying(1000)
);


--
-- Name: notification_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE notification_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: notification_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE notification_id_seq OWNED BY notification.id;


--
-- Name: ota; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE ota (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying NOT NULL,
    ota_code character varying NOT NULL,
    mm_ota_code character varying NOT NULL,
    unirate_ota_code character varying NOT NULL,
    rate_push_enabled boolean NOT NULL,
    promo_push_enabled boolean NOT NULL,
    inventory_push_enabled boolean NOT NULL,
    promo_push_api boolean NOT NULL,
    promo_disable_api boolean NOT NULL,
    rcs_push_complete boolean NOT NULL,
    rcs_callback_complete boolean NOT NULL,
    unirate_push_complete boolean NOT NULL,
    rcs_push_time timestamp with time zone,
    rcs_callback_time timestamp with time zone,
    unirate_push_time timestamp with time zone
);


--
-- Name: ota_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE ota_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ota_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE ota_id_seq OWNED BY ota.id;


--
-- Name: ota_property; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE ota_property (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    ota_id integer NOT NULL,
    rate_push_complete boolean,
    promo_push_complete boolean,
    inventory_push_complete boolean,
    rcs_push_complete boolean NOT NULL,
    rcs_callback_complete boolean NOT NULL,
    unirate_push_complete boolean NOT NULL,
    rcs_push_time timestamp with time zone,
    rcs_callback_time timestamp with time zone,
    unirate_push_time timestamp with time zone,
    ota_hotel_commission double precision
);


--
-- Name: ota_property_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE ota_property_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ota_property_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE ota_property_id_seq OWNED BY ota_property.id;


--
-- Name: ota_property_mapping; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE ota_property_mapping (
    id integer NOT NULL,
    ota_property_id integer NOT NULL,
    ota_hotel_code character varying NOT NULL,
    username character varying,
    access_token character varying
);


--
-- Name: ota_property_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE ota_property_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ota_property_mapping_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE ota_property_mapping_id_seq OWNED BY ota_property_mapping.id;


--
-- Name: ota_rate_plan_mapping; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE ota_rate_plan_mapping (
    id integer NOT NULL,
    ota_property_id integer NOT NULL,
    room_type integer NOT NULL,
    rate_plan integer NOT NULL,
    ota_rate_plan_code character varying NOT NULL,
    ota_rate_plan_name character varying,
    rate_push_enabled boolean
);


--
-- Name: ota_rate_plan_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE ota_rate_plan_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ota_rate_plan_mapping_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE ota_rate_plan_mapping_id_seq OWNED BY ota_rate_plan_mapping.id;


--
-- Name: ota_room_mapping; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE ota_room_mapping (
    id integer NOT NULL,
    ota_property_id integer NOT NULL,
    room_type_id integer NOT NULL,
    ota_room_code character varying NOT NULL,
    ota_room_name character varying
);


--
-- Name: ota_room_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE ota_room_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ota_room_mapping_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE ota_room_mapping_id_seq OWNED BY ota_room_mapping.id;


--
-- Name: owner; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE owner (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    first_name character varying(50) NOT NULL,
    middle_name character varying(50),
    last_name character varying(50),
    gender gender_choices,
    email character varying(50),
    phone_number character varying(50),
    date_of_birth date,
    occupation character varying(50),
    education character varying(50)
);


--
-- Name: owner_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE owner_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: owner_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE owner_id_seq OWNED BY owner.id;


--
-- Name: ownership; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE ownership (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    owner_id integer NOT NULL,
    property_id character varying NOT NULL,
    "primary" boolean
);


--
-- Name: ownership_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE ownership_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ownership_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE ownership_id_seq OWNED BY ownership.id;


--
-- Name: param; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE param (
    id integer NOT NULL,
    entity character varying(100) NOT NULL,
    field character varying(100) NOT NULL,
    value character varying(700) NOT NULL,
    definition text,
    validate boolean
);


--
-- Name: param_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE param_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: param_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE param_id_seq OWNED BY param.id;


--
-- Name: pos_menu_category; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE pos_menu_category (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying NOT NULL,
    seller_category_id integer NOT NULL,
    color character varying,
    icon character varying
);


--
-- Name: pos_menu_category_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE pos_menu_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pos_menu_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE pos_menu_category_id_seq OWNED BY pos_menu_category.id;


--
-- Name: pricing_mapping; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE pricing_mapping (
    id integer NOT NULL,
    pricing_policy_id integer NOT NULL,
    channel_id character varying(100) NOT NULL,
    sub_channel_id character varying(100) NOT NULL,
    status pricing_mapping_status
);


--
-- Name: pricing_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE pricing_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pricing_mapping_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE pricing_mapping_id_seq OWNED BY pricing_mapping.id;


--
-- Name: pricing_policy; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE pricing_policy (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    code character varying(100) NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(100) NOT NULL,
    description text,
    status pricing_policy_status,
    is_default boolean
);


--
-- Name: pricing_policy_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE pricing_policy_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pricing_policy_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE pricing_policy_id_seq OWNED BY pricing_policy.id;


--
-- Name: properties_sku_categories; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE properties_sku_categories (
    property_id character varying NOT NULL,
    sku_category_id integer NOT NULL
);


--
-- Name: property; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id character varying NOT NULL,
    hx_id character varying(10),
    status status_choices,
    name character varying(100),
    old_name character varying(100) NOT NULL,
    legal_name character varying(100),
    signed_date date,
    contractual_launch_date date,
    launched_date date,
    churned_date date,
    is_test boolean DEFAULT false NOT NULL
);


--
-- Name: property_amenity; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_amenity (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    public_washroom_id integer,
    elevator_id integer,
    lobby_ac boolean,
    lobby_furniture boolean,
    lobby_smoke_alarm boolean,
    security boolean,
    pantry boolean,
    cloak_room boolean,
    travel_desk boolean,
    room_service boolean,
    roof_top_cafe boolean,
    pool_table boolean,
    pets_allowed boolean,
    parking_id integer,
    private_cab_id integer,
    iron_board_count integer,
    driver_quarters_count integer,
    disable_friendly_id integer,
    swimming_pool_id integer,
    gym_id integer,
    spa_id integer,
    laundry_id integer,
    breakfast_id integer,
    payment_id integer
);


--
-- Name: property_amenity_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_amenity_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_amenity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_amenity_id_seq OWNED BY property_amenity.id;


--
-- Name: property_brand; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_brand (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    brand_id integer NOT NULL,
    status property_brand_status
);


--
-- Name: property_brand_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_brand_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_brand_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_brand_id_seq OWNED BY property_brand.id;


--
-- Name: property_detail; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_detail (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    neighbourhood_type neighbourhood_choices NOT NULL,
    neighbourhood_detail character varying(500),
    property_type property_type_choices NOT NULL,
    property_style property_style_choices NOT NULL,
    style_detail character varying(500),
    construction_year integer,
    building_style building_style_choices NOT NULL,
    unmarried_couple_allowed boolean,
    local_id_allowed boolean,
    floor_count integer,
    star_rating integer,
    previous_franchise boolean,
    reception_landline character varying(200),
    reception_mobile character varying(200),
    bank_detail_id integer,
    vat_number character varying(15),
    tin character varying(15),
    pan character varying(15),
    service_tax_number character varying(15),
    luxury_tax_number character varying(15),
    is_leased boolean,
    gstin character varying(30),
    ext_id integer,
    provider_hotel_code character varying(100),
    legal_signature character varying(500),
    navision_code character varying(10),
    sold_as_id integer,
    is_housekeeping_enabled boolean DEFAULT false NOT NULL,
    has_lut boolean DEFAULT false NOT NULL,
    churn_initiation_date timestamp with time zone,
    is_partner_pricing_enabled boolean DEFAULT false NOT NULL,
    is_ds_pricing_enabled boolean DEFAULT false NOT NULL,
    superhero_price_slab superheropriceslabchoices
);


--
-- Name: property_detail_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_detail_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_detail_id_seq OWNED BY property_detail.id;


--
-- Name: property_image; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_image (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    path text NOT NULL,
    property_id character varying NOT NULL,
    room_type_config_id integer,
    tag_description text,
    sort_order integer
);


--
-- Name: property_image_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_image_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_image_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_image_id_seq OWNED BY property_image.id;


--
-- Name: property_landmark; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_landmark (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    landmark_id integer NOT NULL,
    property_id character varying NOT NULL,
    type character varying(50) NOT NULL,
    distance_from_property numeric(5,2) DEFAULT 0.00 NOT NULL,
    property_direction character varying(1000),
    hatchback_cab_fare numeric(7,2),
    sedan_cab_fare numeric(7,2)
);


--
-- Name: property_landmark_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_landmark_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_landmark_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_landmark_id_seq OWNED BY property_landmark.id;


--
-- Name: property_policy; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_policy (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    policy_type character varying(80) NOT NULL,
    title character varying(80) NOT NULL,
    description character varying(800) NOT NULL,
    display_in_need_to_know boolean,
    display_in_policy boolean
);


--
-- Name: property_policy_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_policy_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_policy_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_policy_id_seq OWNED BY property_policy.id;


--
-- Name: property_policy_map; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_policy_map (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_detail_id integer,
    property_policy_id integer
);


--
-- Name: property_policy_map_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_policy_map_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_policy_map_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_policy_map_id_seq OWNED BY property_policy_map.id;


--
-- Name: property_sku; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE property_sku (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    sku_id integer NOT NULL,
    property_id character varying NOT NULL,
    status property_sku_status,
    saleable boolean,
    display_name character varying(100)
);


--
-- Name: property_sku_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE property_sku_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: property_sku_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE property_sku_id_seq OWNED BY property_sku.id;


--
-- Name: provider; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE provider (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    code character varying(100) NOT NULL,
    status provider_status
);


--
-- Name: provider_brand; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE provider_brand (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    provider_id integer NOT NULL,
    brand_id integer NOT NULL,
    status provider_brand_status
);


--
-- Name: provider_brand_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE provider_brand_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: provider_brand_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE provider_brand_id_seq OWNED BY provider_brand.id;


--
-- Name: provider_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE provider_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: provider_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE provider_id_seq OWNED BY provider.id;


--
-- Name: provider_room_mapping; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE provider_room_mapping (
    id integer NOT NULL,
    ext_id integer NOT NULL,
    room_type_id integer NOT NULL,
    ext_room_code character varying(100) NOT NULL,
    ext_room_name character varying(50),
    display_name character varying(50),
    ext_description text
);


--
-- Name: provider_room_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE provider_room_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: provider_room_mapping_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE provider_room_mapping_id_seq OWNED BY provider_room_mapping.id;


--
-- Name: rate_plan; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE rate_plan (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    plan character varying NOT NULL,
    unirate_rate_plan_code character varying,
    crs_rate_plan_code character varying,
    bb_rate_plan_code character varying,
    ext_id integer,
    treebo_plan_id integer
);


--
-- Name: rate_plan_configuration; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE rate_plan_configuration (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    rate_plan_id integer NOT NULL
);


--
-- Name: rate_plan_configuration_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE rate_plan_configuration_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: rate_plan_configuration_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE rate_plan_configuration_id_seq OWNED BY rate_plan_configuration.id;


--
-- Name: rate_plan_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE rate_plan_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: rate_plan_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE rate_plan_id_seq OWNED BY rate_plan.id;


--
-- Name: region; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE region (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL
);


--
-- Name: region_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE region_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: region_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE region_id_seq OWNED BY region.id;


--
-- Name: restaurant; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE restaurant (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    name character varying(100),
    non_veg boolean,
    open_time time without time zone,
    last_order_time time without time zone,
    close_time time without time zone,
    a_la_carte boolean,
    buffet boolean,
    outside_food_allowed boolean,
    baby_milk_served boolean,
    baby_milk_timing character varying(500),
    handwash_present boolean,
    washroom_present boolean,
    egg_served boolean,
    jain_food_served boolean,
    room_service_start_time time without time zone,
    room_service_end_time time without time zone
);


--
-- Name: restaurant_cuisine; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE restaurant_cuisine (
    id integer NOT NULL,
    cuisine_id integer NOT NULL,
    restaurant_id integer NOT NULL
);


--
-- Name: restaurant_cuisine_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE restaurant_cuisine_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: restaurant_cuisine_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE restaurant_cuisine_id_seq OWNED BY restaurant_cuisine.id;


--
-- Name: restaurant_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE restaurant_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: restaurant_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE restaurant_id_seq OWNED BY restaurant.id;


--
-- Name: restaurant_table; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE restaurant_table (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    seller_id character varying NOT NULL,
    table_number character varying NOT NULL
);


--
-- Name: role; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE role (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(80) NOT NULL,
    description character varying(255)
);


--
-- Name: role_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE role_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: role_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE role_id_seq OWNED BY role.id;


--
-- Name: roles_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE roles_users (
    id integer NOT NULL,
    user_id integer,
    role_id integer
);


--
-- Name: roles_users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE roles_users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: roles_users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE roles_users_id_seq OWNED BY roles_users.id;


--
-- Name: room; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE room (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    room_number character varying(30) NOT NULL,
    room_type_id integer NOT NULL,
    building_number character varying,
    floor_number integer,
    size character varying(100),
    is_active boolean,
    room_type_config_id integer,
    room_size numeric,
    linked_room_identifier integer
);


--
-- Name: room_amenity; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE room_amenity (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    room_id integer NOT NULL,
    mini_fridge boolean,
    balcony boolean,
    kitchenette boolean,
    kitchenette_utensils boolean,
    king_sized_beds boolean,
    queen_sized_beds boolean,
    single_beds boolean,
    wardrobe boolean,
    locker_available boolean,
    microwave boolean,
    luggage_shelf boolean,
    study_table_chair boolean,
    sofa_chair boolean,
    coffee_table boolean,
    other_furniture boolean,
    smoking_room boolean,
    bath_tub boolean,
    shower_curtain boolean,
    smoke_alarm boolean,
    shower_cabinets boolean,
    living_room boolean,
    dining_table boolean,
    windows boolean,
    treebo_toiletries boolean,
    fan_type fan_type_choices,
    lock_type lock_type_choices,
    bucket_mug bucket_choices,
    mosquito_repellent mosquito_repellent_choices,
    heater_id integer,
    twin_bed_id integer,
    intercom_id integer,
    hot_water_id integer,
    tv_id integer,
    ac_id integer,
    stove_id integer
);


--
-- Name: room_amenity_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE room_amenity_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: room_amenity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE room_amenity_id_seq OWNED BY room_amenity.id;


--
-- Name: room_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE room_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: room_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE room_id_seq OWNED BY room.id;


--
-- Name: room_type; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE room_type (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    type room_type_choices NOT NULL,
    bb_room_type_code character varying,
    crs_room_type_code character varying,
    unirate_room_type_code character varying,
    code character varying
);


--
-- Name: room_type_configuration; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE room_type_configuration (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    room_type_id integer NOT NULL,
    extra_bed extra_bed_choices,
    min_occupancy integer,
    max_occupancy character varying(20),
    adults integer,
    mm_id character varying(50),
    children integer,
    max_total integer,
    min_room_size numeric,
    display_name character varying(50),
    ext_id integer,
    ext_room_code character varying(100),
    ext_room_name character varying(50),
    ext_rate_plan_code character varying(100),
    ext_rate_plan_name character varying(100),
    description text
);


--
-- Name: room_type_configuration_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE room_type_configuration_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: room_type_configuration_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE room_type_configuration_id_seq OWNED BY room_type_configuration.id;


--
-- Name: room_type_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE room_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: room_type_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE room_type_id_seq OWNED BY room_type.id;


--
-- Name: ruptub_legal_entities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE ruptub_legal_entities (
    id integer NOT NULL,
    state_id integer NOT NULL,
    gstin character varying(15) NOT NULL,
    date_of_registration date NOT NULL,
    address_line_1 character varying(100) NOT NULL,
    address_line_2 character varying(100) NOT NULL,
    address_city character varying(100) NOT NULL,
    address_pincode character varying(6) NOT NULL,
    legal_name character varying(32) NOT NULL
);


--
-- Name: ruptub_legal_entities_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE ruptub_legal_entities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: ruptub_legal_entities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE ruptub_legal_entities_id_seq OWNED BY ruptub_legal_entities.id;


--
-- Name: seller; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE seller (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    seller_id character varying,
    name character varying(100) NOT NULL,
    property_id character varying,
    seller_category_id integer NOT NULL,
    city_id integer NOT NULL,
    legal_city_id integer,
    gstin character varying(20),
    legal_name character varying(100),
    legal_address text,
    legal_pincode character varying(20),
    phone_number character varying(30),
    status character varying(50) NOT NULL,
    legal_signature character varying(500),
    pincode character varying(20)
);


--
-- Name: seller_category; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE seller_category (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    code character varying(20) NOT NULL,
    is_active boolean
);


--
-- Name: seller_category_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE seller_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: seller_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE seller_category_id_seq OWNED BY seller_category.id;


--
-- Name: seller_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE seller_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: seller_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE seller_id_seq OWNED BY seller.id;


--
-- Name: seller_sku; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE seller_sku (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    sku_id integer NOT NULL,
    seller_id character varying NOT NULL,
    sku_category_code character varying(30) NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(100) NOT NULL,
    description text,
    is_active boolean,
    is_sellable boolean,
    menu_category_id integer,
    pretax_price numeric(10,2),
    icon text
);


--
-- Name: seller_sku_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE seller_sku_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: seller_sku_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE seller_sku_id_seq OWNED BY seller_sku.id;


--
-- Name: seller_type_history; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE seller_type_history (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    from_seller_model seller_type_choices NOT NULL,
    to_seller_model seller_type_choices NOT NULL,
    date date NOT NULL
);


--
-- Name: seller_type_history_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE seller_type_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: seller_type_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE seller_type_history_id_seq OWNED BY seller_type_history.id;


--
-- Name: sku; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE sku (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    code character varying(100),
    name character varying(100) NOT NULL,
    is_modular boolean,
    hsn_sac character varying(100),
    description text,
    saleable boolean,
    category_id integer NOT NULL,
    chargeable_per_occupant boolean,
    identifier character varying(400),
    sku_count integer,
    bundle_rule_id integer,
    display_name character varying(100),
    default_list_price numeric(10,2),
    default_sale_price numeric(10,2),
    tag character varying(100),
    flat_count_for_creation integer,
    sku_type sku_choice NOT NULL,
    tax_type tax_choice NOT NULL,
    is_active boolean,
    sku_details json
);


--
-- Name: sku_activation; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE sku_activation (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    property_id character varying NOT NULL,
    sku_id integer NOT NULL,
    service_id integer NOT NULL
);


--
-- Name: sku_activation_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE sku_activation_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sku_activation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE sku_activation_id_seq OWNED BY sku_activation.id;


--
-- Name: sku_attribute; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE sku_attribute (
    id integer NOT NULL,
    sku_id integer NOT NULL,
    key character varying(100) NOT NULL,
    value character varying(200) NOT NULL
);


--
-- Name: sku_attribute_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE sku_attribute_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sku_attribute_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE sku_attribute_id_seq OWNED BY sku_attribute.id;


--
-- Name: sku_bundle; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE sku_bundle (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    bundle_id integer NOT NULL,
    sku_id integer NOT NULL,
    count integer
);


--
-- Name: sku_bundle_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE sku_bundle_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sku_bundle_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE sku_bundle_id_seq OWNED BY sku_bundle.id;


--
-- Name: sku_category; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE sku_category (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    hsn_sac character varying(100),
    status status,
    code character varying(100) NOT NULL
);


--
-- Name: sku_category_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE sku_category_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sku_category_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE sku_category_id_seq OWNED BY sku_category.id;


--
-- Name: sku_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE sku_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sku_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE sku_id_seq OWNED BY sku.id;


--
-- Name: state; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE state (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    country_id integer NOT NULL,
    code integer
);


--
-- Name: state_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE state_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: state_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE state_id_seq OWNED BY state.id;


--
-- Name: sub_channel; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE sub_channel (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id character varying(100) NOT NULL,
    name character varying(100) NOT NULL,
    status sub_channel_status,
    channel_id character varying(100) NOT NULL,
    description character varying(400)
);


--
-- Name: system_property; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE system_property (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    value character varying(50)
);


--
-- Name: system_property_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE system_property_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: system_property_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE system_property_id_seq OWNED BY system_property.id;


--
-- Name: transport_station; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE transport_station (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    type station_choices NOT NULL,
    name character varying(100) NOT NULL,
    latitude numeric(9,6),
    longitude numeric(9,6)
);


--
-- Name: transport_station_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE transport_station_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: transport_station_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE transport_station_id_seq OWNED BY transport_station.id;


--
-- Name: transport_station_property; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE transport_station_property (
    created_at timestamp with time zone,
    modified_at timestamp with time zone,
    id integer NOT NULL,
    transport_station_id integer NOT NULL,
    property_id character varying NOT NULL,
    distance_from_property numeric(5,2),
    property_direction character varying(1000),
    hatchback_cab_fare numeric(7,2),
    sedan_cab_fare numeric(7,2)
);


--
-- Name: transport_station_property_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE transport_station_property_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: transport_station_property_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE transport_station_property_id_seq OWNED BY transport_station_property.id;


--
-- Name: amenity_ac id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_ac ALTER COLUMN id SET DEFAULT nextval('amenity_ac_id_seq'::regclass);


--
-- Name: amenity_breakfast id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_breakfast ALTER COLUMN id SET DEFAULT nextval('amenity_breakfast_id_seq'::regclass);


--
-- Name: amenity_disable_friendly id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_disable_friendly ALTER COLUMN id SET DEFAULT nextval('amenity_disable_friendly_id_seq'::regclass);


--
-- Name: amenity_elevator id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_elevator ALTER COLUMN id SET DEFAULT nextval('amenity_elevator_id_seq'::regclass);


--
-- Name: amenity_gym id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_gym ALTER COLUMN id SET DEFAULT nextval('amenity_gym_id_seq'::regclass);


--
-- Name: amenity_heater id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_heater ALTER COLUMN id SET DEFAULT nextval('amenity_heater_id_seq'::regclass);


--
-- Name: amenity_hot_water id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_hot_water ALTER COLUMN id SET DEFAULT nextval('amenity_hot_water_id_seq'::regclass);


--
-- Name: amenity_intercom id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_intercom ALTER COLUMN id SET DEFAULT nextval('amenity_intercom_id_seq'::regclass);


--
-- Name: amenity_laundry id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_laundry ALTER COLUMN id SET DEFAULT nextval('amenity_laundry_id_seq'::regclass);


--
-- Name: amenity_parking id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_parking ALTER COLUMN id SET DEFAULT nextval('amenity_parking_id_seq'::regclass);


--
-- Name: amenity_payment id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_payment ALTER COLUMN id SET DEFAULT nextval('amenity_payment_id_seq'::regclass);


--
-- Name: amenity_private_cab id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_private_cab ALTER COLUMN id SET DEFAULT nextval('amenity_private_cab_id_seq'::regclass);


--
-- Name: amenity_public_washroom id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_public_washroom ALTER COLUMN id SET DEFAULT nextval('amenity_public_washroom_id_seq'::regclass);


--
-- Name: amenity_spa id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_spa ALTER COLUMN id SET DEFAULT nextval('amenity_spa_id_seq'::regclass);


--
-- Name: amenity_stove id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_stove ALTER COLUMN id SET DEFAULT nextval('amenity_stove_id_seq'::regclass);


--
-- Name: amenity_summary id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_summary ALTER COLUMN id SET DEFAULT nextval('amenity_summary_id_seq'::regclass);


--
-- Name: amenity_swimming_pool id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_swimming_pool ALTER COLUMN id SET DEFAULT nextval('amenity_swimming_pool_id_seq'::regclass);


--
-- Name: amenity_tv id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_tv ALTER COLUMN id SET DEFAULT nextval('amenity_tv_id_seq'::regclass);


--
-- Name: amenity_twin_bed id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_twin_bed ALTER COLUMN id SET DEFAULT nextval('amenity_twin_bed_id_seq'::regclass);


--
-- Name: bank_detail id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY bank_detail ALTER COLUMN id SET DEFAULT nextval('bank_detail_id_seq'::regclass);


--
-- Name: banquet_hall id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY banquet_hall ALTER COLUMN id SET DEFAULT nextval('banquet_hall_id_seq'::regclass);


--
-- Name: bar id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY bar ALTER COLUMN id SET DEFAULT nextval('bar_id_seq'::regclass);


--
-- Name: brand id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY brand ALTER COLUMN id SET DEFAULT nextval('brand_id_seq'::regclass);


--
-- Name: breakfast_cuisine id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY breakfast_cuisine ALTER COLUMN id SET DEFAULT nextval('breakfast_cuisine_id_seq'::regclass);


--
-- Name: city id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY city ALTER COLUMN id SET DEFAULT nextval('city_id_seq'::regclass);


--
-- Name: city_alias id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY city_alias ALTER COLUMN id SET DEFAULT nextval('city_alias_id_seq'::regclass);


--
-- Name: cluster id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY cluster ALTER COLUMN id SET DEFAULT nextval('cluster_id_seq'::regclass);


--
-- Name: country id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY country ALTER COLUMN id SET DEFAULT nextval('country_id_seq'::regclass);


--
-- Name: cs_user id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY cs_user ALTER COLUMN id SET DEFAULT nextval('cs_user_id_seq'::regclass);


--
-- Name: cuisine id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY cuisine ALTER COLUMN id SET DEFAULT nextval('cuisine_id_seq'::regclass);


--
-- Name: description id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY description ALTER COLUMN id SET DEFAULT nextval('description_id_seq'::regclass);


--
-- Name: facility_category id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY facility_category ALTER COLUMN id SET DEFAULT nextval('facility_category_id_seq'::regclass);


--
-- Name: facility_category_mapping id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY facility_category_mapping ALTER COLUMN id SET DEFAULT nextval('facility_category_mapping_id_seq'::regclass);


--
-- Name: global_policy id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY global_policy ALTER COLUMN id SET DEFAULT nextval('global_policy_id_seq'::regclass);


--
-- Name: google_drive_base_folder id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY google_drive_base_folder ALTER COLUMN id SET DEFAULT nextval('google_drive_base_folder_id_seq'::regclass);


--
-- Name: google_drive_file id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY google_drive_file ALTER COLUMN id SET DEFAULT nextval('google_drive_file_id_seq'::regclass);


--
-- Name: guest_facing_process id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_facing_process ALTER COLUMN id SET DEFAULT nextval('guest_facing_process_id_seq'::regclass);


--
-- Name: guest_type id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_type ALTER COLUMN id SET DEFAULT nextval('guest_type_id_seq'::regclass);


--
-- Name: guest_type_property id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_type_property ALTER COLUMN id SET DEFAULT nextval('guest_type_property_id_seq'::regclass);


--
-- Name: landmark id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY landmark ALTER COLUMN id SET DEFAULT nextval('landmark_id_seq'::regclass);


--
-- Name: locality id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY locality ALTER COLUMN id SET DEFAULT nextval('locality_id_seq'::regclass);


--
-- Name: location id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY location ALTER COLUMN id SET DEFAULT nextval('location_id_seq'::regclass);


--
-- Name: micro_market id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY micro_market ALTER COLUMN id SET DEFAULT nextval('micro_market_id_seq'::regclass);


--
-- Name: migration_detail id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY migration_detail ALTER COLUMN id SET DEFAULT nextval('migration_detail_id_seq'::regclass);


--
-- Name: neighbouring_place id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY neighbouring_place ALTER COLUMN id SET DEFAULT nextval('neighbouring_place_id_seq'::regclass);


--
-- Name: notification id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY notification ALTER COLUMN id SET DEFAULT nextval('notification_id_seq'::regclass);


--
-- Name: ota id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota ALTER COLUMN id SET DEFAULT nextval('ota_id_seq'::regclass);


--
-- Name: ota_property id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property ALTER COLUMN id SET DEFAULT nextval('ota_property_id_seq'::regclass);


--
-- Name: ota_property_mapping id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property_mapping ALTER COLUMN id SET DEFAULT nextval('ota_property_mapping_id_seq'::regclass);


--
-- Name: ota_rate_plan_mapping id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_rate_plan_mapping ALTER COLUMN id SET DEFAULT nextval('ota_rate_plan_mapping_id_seq'::regclass);


--
-- Name: ota_room_mapping id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_room_mapping ALTER COLUMN id SET DEFAULT nextval('ota_room_mapping_id_seq'::regclass);


--
-- Name: owner id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY owner ALTER COLUMN id SET DEFAULT nextval('owner_id_seq'::regclass);


--
-- Name: ownership id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY ownership ALTER COLUMN id SET DEFAULT nextval('ownership_id_seq'::regclass);


--
-- Name: param id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY param ALTER COLUMN id SET DEFAULT nextval('param_id_seq'::regclass);


--
-- Name: pos_menu_category id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY pos_menu_category ALTER COLUMN id SET DEFAULT nextval('pos_menu_category_id_seq'::regclass);


--
-- Name: pricing_mapping id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_mapping ALTER COLUMN id SET DEFAULT nextval('pricing_mapping_id_seq'::regclass);


--
-- Name: pricing_policy id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_policy ALTER COLUMN id SET DEFAULT nextval('pricing_policy_id_seq'::regclass);


--
-- Name: property_amenity id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity ALTER COLUMN id SET DEFAULT nextval('property_amenity_id_seq'::regclass);


--
-- Name: property_brand id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_brand ALTER COLUMN id SET DEFAULT nextval('property_brand_id_seq'::regclass);


--
-- Name: property_detail id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_detail ALTER COLUMN id SET DEFAULT nextval('property_detail_id_seq'::regclass);


--
-- Name: property_image id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_image ALTER COLUMN id SET DEFAULT nextval('property_image_id_seq'::regclass);


--
-- Name: property_landmark id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_landmark ALTER COLUMN id SET DEFAULT nextval('property_landmark_id_seq'::regclass);


--
-- Name: property_policy id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_policy ALTER COLUMN id SET DEFAULT nextval('property_policy_id_seq'::regclass);


--
-- Name: property_policy_map id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_policy_map ALTER COLUMN id SET DEFAULT nextval('property_policy_map_id_seq'::regclass);


--
-- Name: property_sku id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_sku ALTER COLUMN id SET DEFAULT nextval('property_sku_id_seq'::regclass);


--
-- Name: provider id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider ALTER COLUMN id SET DEFAULT nextval('provider_id_seq'::regclass);


--
-- Name: provider_brand id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_brand ALTER COLUMN id SET DEFAULT nextval('provider_brand_id_seq'::regclass);


--
-- Name: provider_room_mapping id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_room_mapping ALTER COLUMN id SET DEFAULT nextval('provider_room_mapping_id_seq'::regclass);


--
-- Name: rate_plan id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan ALTER COLUMN id SET DEFAULT nextval('rate_plan_id_seq'::regclass);


--
-- Name: rate_plan_configuration id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan_configuration ALTER COLUMN id SET DEFAULT nextval('rate_plan_configuration_id_seq'::regclass);


--
-- Name: region id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY region ALTER COLUMN id SET DEFAULT nextval('region_id_seq'::regclass);


--
-- Name: restaurant id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant ALTER COLUMN id SET DEFAULT nextval('restaurant_id_seq'::regclass);


--
-- Name: restaurant_cuisine id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant_cuisine ALTER COLUMN id SET DEFAULT nextval('restaurant_cuisine_id_seq'::regclass);


--
-- Name: role id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY role ALTER COLUMN id SET DEFAULT nextval('role_id_seq'::regclass);


--
-- Name: roles_users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY roles_users ALTER COLUMN id SET DEFAULT nextval('roles_users_id_seq'::regclass);


--
-- Name: room id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY room ALTER COLUMN id SET DEFAULT nextval('room_id_seq'::regclass);


--
-- Name: room_amenity id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity ALTER COLUMN id SET DEFAULT nextval('room_amenity_id_seq'::regclass);


--
-- Name: room_type id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type ALTER COLUMN id SET DEFAULT nextval('room_type_id_seq'::regclass);


--
-- Name: room_type_configuration id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type_configuration ALTER COLUMN id SET DEFAULT nextval('room_type_configuration_id_seq'::regclass);


--
-- Name: ruptub_legal_entities id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY ruptub_legal_entities ALTER COLUMN id SET DEFAULT nextval('ruptub_legal_entities_id_seq'::regclass);


--
-- Name: seller id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller ALTER COLUMN id SET DEFAULT nextval('seller_id_seq'::regclass);


--
-- Name: seller_category id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_category ALTER COLUMN id SET DEFAULT nextval('seller_category_id_seq'::regclass);


--
-- Name: seller_sku id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_sku ALTER COLUMN id SET DEFAULT nextval('seller_sku_id_seq'::regclass);


--
-- Name: seller_type_history id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_type_history ALTER COLUMN id SET DEFAULT nextval('seller_type_history_id_seq'::regclass);


--
-- Name: sku id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku ALTER COLUMN id SET DEFAULT nextval('sku_id_seq'::regclass);


--
-- Name: sku_activation id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_activation ALTER COLUMN id SET DEFAULT nextval('sku_activation_id_seq'::regclass);


--
-- Name: sku_attribute id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_attribute ALTER COLUMN id SET DEFAULT nextval('sku_attribute_id_seq'::regclass);


--
-- Name: sku_bundle id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_bundle ALTER COLUMN id SET DEFAULT nextval('sku_bundle_id_seq'::regclass);


--
-- Name: sku_category id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_category ALTER COLUMN id SET DEFAULT nextval('sku_category_id_seq'::regclass);


--
-- Name: state id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY state ALTER COLUMN id SET DEFAULT nextval('state_id_seq'::regclass);


--
-- Name: system_property id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY system_property ALTER COLUMN id SET DEFAULT nextval('system_property_id_seq'::regclass);


--
-- Name: transport_station id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station ALTER COLUMN id SET DEFAULT nextval('transport_station_id_seq'::regclass);


--
-- Name: transport_station_property id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station_property ALTER COLUMN id SET DEFAULT nextval('transport_station_property_id_seq'::regclass);


--
-- Name: room_type _unique_bb_room; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type
    ADD CONSTRAINT _unique_bb_room UNIQUE (bb_room_type_code);


--
-- Name: city_alias _unique_city_alias; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY city_alias
    ADD CONSTRAINT _unique_city_alias UNIQUE (name, city_id);


--
-- Name: room_type _unique_cm_room; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type
    ADD CONSTRAINT _unique_cm_room UNIQUE (unirate_room_type_code);


--
-- Name: room_type _unique_crs_room; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type
    ADD CONSTRAINT _unique_crs_room UNIQUE (crs_room_type_code);


--
-- Name: provider_room_mapping _unique_external_room_mapping_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_room_mapping
    ADD CONSTRAINT _unique_external_room_mapping_key UNIQUE (ext_id, room_type_id, ext_room_code);


--
-- Name: locality _unique_locality_city; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY locality
    ADD CONSTRAINT _unique_locality_city UNIQUE (city_id, name);


--
-- Name: pricing_mapping _unique_pricing_policy_mapping_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_mapping
    ADD CONSTRAINT _unique_pricing_policy_mapping_key UNIQUE (pricing_policy_id, channel_id, sub_channel_id);


--
-- Name: property_landmark _unique_property_landmark; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_landmark
    ADD CONSTRAINT _unique_property_landmark UNIQUE (landmark_id, property_id);


--
-- Name: ota_property _unique_property_ota; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property
    ADD CONSTRAINT _unique_property_ota UNIQUE (property_id, ota_id);


--
-- Name: ota_room_mapping _unique_property_ota_room; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_room_mapping
    ADD CONSTRAINT _unique_property_ota_room UNIQUE (ota_property_id, room_type_id);


--
-- Name: ownership _unique_property_owner; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ownership
    ADD CONSTRAINT _unique_property_owner UNIQUE (owner_id, property_id);


--
-- Name: rate_plan_configuration _unique_property_rate_plan; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan_configuration
    ADD CONSTRAINT _unique_property_rate_plan UNIQUE (property_id, rate_plan_id);


--
-- Name: room _unique_property_room_number; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room
    ADD CONSTRAINT _unique_property_room_number UNIQUE (property_id, room_number);


--
-- Name: room_type_configuration _unique_property_room_type; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type_configuration
    ADD CONSTRAINT _unique_property_room_type UNIQUE (property_id, room_type_id);


--
-- Name: property_sku _unique_property_sku; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_sku
    ADD CONSTRAINT _unique_property_sku UNIQUE (sku_id, property_id);


--
-- Name: transport_station_property _unique_property_station; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station_property
    ADD CONSTRAINT _unique_property_station UNIQUE (property_id, transport_station_id);


--
-- Name: sku_activation _unique_sku_activation_service_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_activation
    ADD CONSTRAINT _unique_sku_activation_service_key UNIQUE (property_id, sku_id, service_id);


--
-- Name: city _unique_state_city; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY city
    ADD CONSTRAINT _unique_state_city UNIQUE (state_id, name);


--
-- Name: state _unique_state_country; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY state
    ADD CONSTRAINT _unique_state_country UNIQUE (country_id, name);


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: amenity_ac amenity_ac_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_ac
    ADD CONSTRAINT amenity_ac_pkey PRIMARY KEY (id);


--
-- Name: amenity_breakfast amenity_breakfast_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_breakfast
    ADD CONSTRAINT amenity_breakfast_pkey PRIMARY KEY (id);


--
-- Name: amenity_disable_friendly amenity_disable_friendly_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_disable_friendly
    ADD CONSTRAINT amenity_disable_friendly_pkey PRIMARY KEY (id);


--
-- Name: amenity_elevator amenity_elevator_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_elevator
    ADD CONSTRAINT amenity_elevator_pkey PRIMARY KEY (id);


--
-- Name: amenity_gym amenity_gym_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_gym
    ADD CONSTRAINT amenity_gym_pkey PRIMARY KEY (id);


--
-- Name: amenity_heater amenity_heater_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_heater
    ADD CONSTRAINT amenity_heater_pkey PRIMARY KEY (id);


--
-- Name: amenity_hot_water amenity_hot_water_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_hot_water
    ADD CONSTRAINT amenity_hot_water_pkey PRIMARY KEY (id);


--
-- Name: amenity_intercom amenity_intercom_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_intercom
    ADD CONSTRAINT amenity_intercom_pkey PRIMARY KEY (id);


--
-- Name: amenity_laundry amenity_laundry_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_laundry
    ADD CONSTRAINT amenity_laundry_pkey PRIMARY KEY (id);


--
-- Name: amenity_parking amenity_parking_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_parking
    ADD CONSTRAINT amenity_parking_pkey PRIMARY KEY (id);


--
-- Name: amenity_payment amenity_payment_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_payment
    ADD CONSTRAINT amenity_payment_pkey PRIMARY KEY (id);


--
-- Name: amenity_private_cab amenity_private_cab_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_private_cab
    ADD CONSTRAINT amenity_private_cab_pkey PRIMARY KEY (id);


--
-- Name: amenity_public_washroom amenity_public_washroom_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_public_washroom
    ADD CONSTRAINT amenity_public_washroom_pkey PRIMARY KEY (id);


--
-- Name: amenity_spa amenity_spa_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_spa
    ADD CONSTRAINT amenity_spa_pkey PRIMARY KEY (id);


--
-- Name: amenity_stove amenity_stove_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_stove
    ADD CONSTRAINT amenity_stove_pkey PRIMARY KEY (id);


--
-- Name: amenity_summary amenity_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_summary
    ADD CONSTRAINT amenity_summary_pkey PRIMARY KEY (id);


--
-- Name: amenity_swimming_pool amenity_swimming_pool_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_swimming_pool
    ADD CONSTRAINT amenity_swimming_pool_pkey PRIMARY KEY (id);


--
-- Name: amenity_tv amenity_tv_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_tv
    ADD CONSTRAINT amenity_tv_pkey PRIMARY KEY (id);


--
-- Name: amenity_twin_bed amenity_twin_bed_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_twin_bed
    ADD CONSTRAINT amenity_twin_bed_pkey PRIMARY KEY (id);


--
-- Name: application application_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY application
    ADD CONSTRAINT application_name_key UNIQUE (name);


--
-- Name: application application_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY application
    ADD CONSTRAINT application_pkey PRIMARY KEY (id);


--
-- Name: bank_detail bank_detail_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY bank_detail
    ADD CONSTRAINT bank_detail_pkey PRIMARY KEY (id);


--
-- Name: banquet_hall banquet_hall_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY banquet_hall
    ADD CONSTRAINT banquet_hall_pkey PRIMARY KEY (id);


--
-- Name: bar bar_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY bar
    ADD CONSTRAINT bar_pkey PRIMARY KEY (id);


--
-- Name: brand brand_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY brand
    ADD CONSTRAINT brand_code_key UNIQUE (code);


--
-- Name: brand brand_display_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY brand
    ADD CONSTRAINT brand_display_name_key UNIQUE (display_name);


--
-- Name: brand brand_legal_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY brand
    ADD CONSTRAINT brand_legal_name_key UNIQUE (legal_name);


--
-- Name: brand brand_logo_path_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY brand
    ADD CONSTRAINT brand_logo_path_key UNIQUE (logo_path);


--
-- Name: brand brand_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY brand
    ADD CONSTRAINT brand_name_key UNIQUE (name);


--
-- Name: brand brand_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY brand
    ADD CONSTRAINT brand_pkey PRIMARY KEY (id);


--
-- Name: breakfast_cuisine breakfast_cuisine_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY breakfast_cuisine
    ADD CONSTRAINT breakfast_cuisine_pkey PRIMARY KEY (id);


--
-- Name: channel channel_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY channel
    ADD CONSTRAINT channel_name_key UNIQUE (name);


--
-- Name: channel channel_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY channel
    ADD CONSTRAINT channel_pkey PRIMARY KEY (id);


--
-- Name: city_alias city_alias_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY city_alias
    ADD CONSTRAINT city_alias_pkey PRIMARY KEY (id);


--
-- Name: city city_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY city
    ADD CONSTRAINT city_pkey PRIMARY KEY (id);


--
-- Name: cluster cluster_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY cluster
    ADD CONSTRAINT cluster_name_key UNIQUE (name);


--
-- Name: cluster cluster_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY cluster
    ADD CONSTRAINT cluster_pkey PRIMARY KEY (id);


--
-- Name: country country_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY country
    ADD CONSTRAINT country_name_key UNIQUE (name);


--
-- Name: country country_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY country
    ADD CONSTRAINT country_pkey PRIMARY KEY (id);


--
-- Name: cs_user cs_user_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY cs_user
    ADD CONSTRAINT cs_user_email_key UNIQUE (email);


--
-- Name: cs_user cs_user_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY cs_user
    ADD CONSTRAINT cs_user_pkey PRIMARY KEY (id);


--
-- Name: cuisine cuisine_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY cuisine
    ADD CONSTRAINT cuisine_name_key UNIQUE (name);


--
-- Name: cuisine cuisine_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY cuisine
    ADD CONSTRAINT cuisine_pkey PRIMARY KEY (id);


--
-- Name: description description_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY description
    ADD CONSTRAINT description_pkey PRIMARY KEY (id);


--
-- Name: facility_category facility_category_category_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY facility_category
    ADD CONSTRAINT facility_category_category_key UNIQUE (category);


--
-- Name: facility_category_mapping facility_category_mapping_facility_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY facility_category_mapping
    ADD CONSTRAINT facility_category_mapping_facility_name_key UNIQUE (facility_name);


--
-- Name: facility_category_mapping facility_category_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY facility_category_mapping
    ADD CONSTRAINT facility_category_mapping_pkey PRIMARY KEY (id);


--
-- Name: facility_category facility_category_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY facility_category
    ADD CONSTRAINT facility_category_pkey PRIMARY KEY (id);


--
-- Name: global_policy global_policy_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY global_policy
    ADD CONSTRAINT global_policy_pkey PRIMARY KEY (id);


--
-- Name: google_drive_base_folder google_drive_base_folder_folder_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY google_drive_base_folder
    ADD CONSTRAINT google_drive_base_folder_folder_name_key UNIQUE (folder_name);


--
-- Name: google_drive_base_folder google_drive_base_folder_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY google_drive_base_folder
    ADD CONSTRAINT google_drive_base_folder_pkey PRIMARY KEY (id);


--
-- Name: google_drive_file google_drive_file_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY google_drive_file
    ADD CONSTRAINT google_drive_file_pkey PRIMARY KEY (id);


--
-- Name: guest_facing_process guest_facing_process_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_facing_process
    ADD CONSTRAINT guest_facing_process_pkey PRIMARY KEY (id);


--
-- Name: guest_type guest_type_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_type
    ADD CONSTRAINT guest_type_pkey PRIMARY KEY (id);


--
-- Name: guest_type_property guest_type_property_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_type_property
    ADD CONSTRAINT guest_type_property_pkey PRIMARY KEY (id);


--
-- Name: guest_type guest_type_type_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_type
    ADD CONSTRAINT guest_type_type_key UNIQUE (type);


--
-- Name: landmark landmark_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY landmark
    ADD CONSTRAINT landmark_pkey PRIMARY KEY (id);


--
-- Name: locality locality_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY locality
    ADD CONSTRAINT locality_pkey PRIMARY KEY (id);


--
-- Name: location location_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY location
    ADD CONSTRAINT location_pkey PRIMARY KEY (id);


--
-- Name: micro_market micro_market_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY micro_market
    ADD CONSTRAINT micro_market_pkey PRIMARY KEY (id);


--
-- Name: migration_detail migration_detail_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY migration_detail
    ADD CONSTRAINT migration_detail_pkey PRIMARY KEY (id);


--
-- Name: neighbouring_place neighbouring_place_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY neighbouring_place
    ADD CONSTRAINT neighbouring_place_pkey PRIMARY KEY (id);


--
-- Name: notification notification_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY notification
    ADD CONSTRAINT notification_pkey PRIMARY KEY (id);


--
-- Name: notification notification_type_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY notification
    ADD CONSTRAINT notification_type_key UNIQUE (type);


--
-- Name: ota ota_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota
    ADD CONSTRAINT ota_name_key UNIQUE (name);


--
-- Name: ota ota_ota_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota
    ADD CONSTRAINT ota_ota_code_key UNIQUE (ota_code);


--
-- Name: ota ota_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota
    ADD CONSTRAINT ota_pkey PRIMARY KEY (id);


--
-- Name: ota_property_mapping ota_property_mapping_ota_property_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property_mapping
    ADD CONSTRAINT ota_property_mapping_ota_property_id_key UNIQUE (ota_property_id);


--
-- Name: ota_property_mapping ota_property_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property_mapping
    ADD CONSTRAINT ota_property_mapping_pkey PRIMARY KEY (id);


--
-- Name: ota_property ota_property_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property
    ADD CONSTRAINT ota_property_pkey PRIMARY KEY (id);


--
-- Name: ota_rate_plan_mapping ota_rate_plan_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_rate_plan_mapping
    ADD CONSTRAINT ota_rate_plan_mapping_pkey PRIMARY KEY (id);


--
-- Name: ota_room_mapping ota_room_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_room_mapping
    ADD CONSTRAINT ota_room_mapping_pkey PRIMARY KEY (id);


--
-- Name: ota ota_unirate_ota_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota
    ADD CONSTRAINT ota_unirate_ota_code_key UNIQUE (unirate_ota_code);


--
-- Name: owner owner_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY owner
    ADD CONSTRAINT owner_pkey PRIMARY KEY (id);


--
-- Name: ownership ownership_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ownership
    ADD CONSTRAINT ownership_pkey PRIMARY KEY (id);


--
-- Name: param param_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY param
    ADD CONSTRAINT param_pkey PRIMARY KEY (id);


--
-- Name: pos_menu_category pos_menu_category_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pos_menu_category
    ADD CONSTRAINT pos_menu_category_pkey PRIMARY KEY (id);


--
-- Name: pricing_mapping pricing_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_mapping
    ADD CONSTRAINT pricing_mapping_pkey PRIMARY KEY (id);


--
-- Name: pricing_policy pricing_policy_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_policy
    ADD CONSTRAINT pricing_policy_code_key UNIQUE (code);


--
-- Name: pricing_policy pricing_policy_display_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_policy
    ADD CONSTRAINT pricing_policy_display_name_key UNIQUE (display_name);


--
-- Name: pricing_policy pricing_policy_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_policy
    ADD CONSTRAINT pricing_policy_name_key UNIQUE (name);


--
-- Name: pricing_policy pricing_policy_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_policy
    ADD CONSTRAINT pricing_policy_pkey PRIMARY KEY (id);


--
-- Name: properties_sku_categories properties_sku_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY properties_sku_categories
    ADD CONSTRAINT properties_sku_categories_pkey PRIMARY KEY (property_id, sku_category_id);


--
-- Name: property_amenity property_amenity_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_pkey PRIMARY KEY (id);


--
-- Name: property_brand property_brand_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_brand
    ADD CONSTRAINT property_brand_pkey PRIMARY KEY (id);


--
-- Name: property_detail property_detail_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_detail
    ADD CONSTRAINT property_detail_pkey PRIMARY KEY (id);


--
-- Name: property property_hx_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property
    ADD CONSTRAINT property_hx_id_key UNIQUE (hx_id);


--
-- Name: property_image property_image_path_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_image
    ADD CONSTRAINT property_image_path_key UNIQUE (path);


--
-- Name: property_image property_image_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_image
    ADD CONSTRAINT property_image_pkey PRIMARY KEY (id);


--
-- Name: property_landmark property_landmark_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_landmark
    ADD CONSTRAINT property_landmark_pkey PRIMARY KEY (id);


--
-- Name: property property_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property
    ADD CONSTRAINT property_name_key UNIQUE (name);


--
-- Name: property property_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property
    ADD CONSTRAINT property_pkey PRIMARY KEY (id);


--
-- Name: property_policy_map property_policy_map_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_policy_map
    ADD CONSTRAINT property_policy_map_pkey PRIMARY KEY (id);


--
-- Name: property_policy property_policy_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_policy
    ADD CONSTRAINT property_policy_pkey PRIMARY KEY (id);


--
-- Name: property_sku property_sku_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_sku
    ADD CONSTRAINT property_sku_pkey PRIMARY KEY (id);


--
-- Name: provider_brand provider_brand_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_brand
    ADD CONSTRAINT provider_brand_pkey PRIMARY KEY (id);


--
-- Name: provider provider_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider
    ADD CONSTRAINT provider_code_key UNIQUE (code);


--
-- Name: provider provider_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider
    ADD CONSTRAINT provider_name_key UNIQUE (name);


--
-- Name: provider provider_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider
    ADD CONSTRAINT provider_pkey PRIMARY KEY (id);


--
-- Name: provider_room_mapping provider_room_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_room_mapping
    ADD CONSTRAINT provider_room_mapping_pkey PRIMARY KEY (id);


--
-- Name: rate_plan rate_plan_bb_rate_plan_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan
    ADD CONSTRAINT rate_plan_bb_rate_plan_code_key UNIQUE (bb_rate_plan_code);


--
-- Name: rate_plan_configuration rate_plan_configuration_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan_configuration
    ADD CONSTRAINT rate_plan_configuration_pkey PRIMARY KEY (id);


--
-- Name: rate_plan rate_plan_crs_rate_plan_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan
    ADD CONSTRAINT rate_plan_crs_rate_plan_code_key UNIQUE (crs_rate_plan_code);


--
-- Name: rate_plan rate_plan_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan
    ADD CONSTRAINT rate_plan_pkey PRIMARY KEY (id);


--
-- Name: rate_plan rate_plan_plan_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan
    ADD CONSTRAINT rate_plan_plan_key UNIQUE (plan);


--
-- Name: rate_plan rate_plan_unirate_rate_plan_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan
    ADD CONSTRAINT rate_plan_unirate_rate_plan_code_key UNIQUE (unirate_rate_plan_code);


--
-- Name: region region_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY region
    ADD CONSTRAINT region_name_key UNIQUE (name);


--
-- Name: region region_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY region
    ADD CONSTRAINT region_pkey PRIMARY KEY (id);


--
-- Name: restaurant_cuisine restaurant_cuisine_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant_cuisine
    ADD CONSTRAINT restaurant_cuisine_pkey PRIMARY KEY (id);


--
-- Name: restaurant restaurant_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant
    ADD CONSTRAINT restaurant_pkey PRIMARY KEY (id);


--
-- Name: restaurant_table restaurant_table_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant_table
    ADD CONSTRAINT restaurant_table_pkey PRIMARY KEY (seller_id, table_number);


--
-- Name: role role_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY role
    ADD CONSTRAINT role_name_key UNIQUE (name);


--
-- Name: role role_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY role
    ADD CONSTRAINT role_pkey PRIMARY KEY (id);


--
-- Name: roles_users roles_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY roles_users
    ADD CONSTRAINT roles_users_pkey PRIMARY KEY (id);


--
-- Name: room_amenity room_amenity_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_pkey PRIMARY KEY (id);


--
-- Name: room room_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room
    ADD CONSTRAINT room_pkey PRIMARY KEY (id);


--
-- Name: room_type room_type_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type
    ADD CONSTRAINT room_type_code_key UNIQUE (code);


--
-- Name: room_type_configuration room_type_configuration_mm_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type_configuration
    ADD CONSTRAINT room_type_configuration_mm_id_key UNIQUE (mm_id);


--
-- Name: room_type_configuration room_type_configuration_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type_configuration
    ADD CONSTRAINT room_type_configuration_pkey PRIMARY KEY (id);


--
-- Name: room_type room_type_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type
    ADD CONSTRAINT room_type_pkey PRIMARY KEY (id);


--
-- Name: room_type room_type_type_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type
    ADD CONSTRAINT room_type_type_key UNIQUE (type);


--
-- Name: ruptub_legal_entities ruptub_legal_entities_gstin_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ruptub_legal_entities
    ADD CONSTRAINT ruptub_legal_entities_gstin_key UNIQUE (gstin);


--
-- Name: ruptub_legal_entities ruptub_legal_entities_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ruptub_legal_entities
    ADD CONSTRAINT ruptub_legal_entities_pkey PRIMARY KEY (id);


--
-- Name: ruptub_legal_entities ruptub_legal_entities_state_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ruptub_legal_entities
    ADD CONSTRAINT ruptub_legal_entities_state_id_key UNIQUE (state_id);


--
-- Name: seller_category seller_category_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_category
    ADD CONSTRAINT seller_category_code_key UNIQUE (code);


--
-- Name: seller_category seller_category_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_category
    ADD CONSTRAINT seller_category_name_key UNIQUE (name);


--
-- Name: seller_category seller_category_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_category
    ADD CONSTRAINT seller_category_pkey PRIMARY KEY (id);


--
-- Name: seller seller_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller
    ADD CONSTRAINT seller_pkey PRIMARY KEY (id);


--
-- Name: seller seller_seller_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller
    ADD CONSTRAINT seller_seller_id_key UNIQUE (seller_id);


--
-- Name: seller_sku seller_sku_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_sku
    ADD CONSTRAINT seller_sku_pkey PRIMARY KEY (id);


--
-- Name: seller_type_history seller_type_history_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_type_history
    ADD CONSTRAINT seller_type_history_pkey PRIMARY KEY (id);


--
-- Name: sku_activation sku_activation_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_activation
    ADD CONSTRAINT sku_activation_pkey PRIMARY KEY (id);


--
-- Name: sku_attribute sku_attribute_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_attribute
    ADD CONSTRAINT sku_attribute_pkey PRIMARY KEY (id);


--
-- Name: sku_bundle sku_bundle_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_bundle
    ADD CONSTRAINT sku_bundle_pkey PRIMARY KEY (id);


--
-- Name: sku_category sku_category_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_category
    ADD CONSTRAINT sku_category_code_key UNIQUE (code);


--
-- Name: sku_category sku_category_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_category
    ADD CONSTRAINT sku_category_name_key UNIQUE (name);


--
-- Name: sku_category sku_category_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_category
    ADD CONSTRAINT sku_category_pkey PRIMARY KEY (id);


--
-- Name: sku sku_code_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku
    ADD CONSTRAINT sku_code_key UNIQUE (code);


--
-- Name: sku sku_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku
    ADD CONSTRAINT sku_pkey PRIMARY KEY (id);


--
-- Name: state state_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY state
    ADD CONSTRAINT state_pkey PRIMARY KEY (id);


--
-- Name: sub_channel sub_channel_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sub_channel
    ADD CONSTRAINT sub_channel_name_key UNIQUE (name);


--
-- Name: sub_channel sub_channel_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sub_channel
    ADD CONSTRAINT sub_channel_pkey PRIMARY KEY (id);


--
-- Name: system_property system_property_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY system_property
    ADD CONSTRAINT system_property_name_key UNIQUE (name);


--
-- Name: system_property system_property_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY system_property
    ADD CONSTRAINT system_property_pkey PRIMARY KEY (id);


--
-- Name: transport_station transport_station_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station
    ADD CONSTRAINT transport_station_name_key UNIQUE (name);


--
-- Name: transport_station transport_station_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station
    ADD CONSTRAINT transport_station_pkey PRIMARY KEY (id);


--
-- Name: transport_station_property transport_station_property_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station_property
    ADD CONSTRAINT transport_station_property_pkey PRIMARY KEY (id);


--
-- Name: sku_bundle unique_bundle_index; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_bundle
    ADD CONSTRAINT unique_bundle_index UNIQUE (bundle_id, sku_id);


--
-- Name: param unique_entity_param_index; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY param
    ADD CONSTRAINT unique_entity_param_index UNIQUE (entity, field, value, validate);


--
-- Name: ix_amenity_summary_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_amenity_summary_fkey_property_id ON amenity_summary USING btree (property_id);


--
-- Name: ix_description_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_description_fkey_property_id ON description USING btree (property_id);


--
-- Name: ix_guest_facing_process_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_guest_facing_process_fkey_property_id ON guest_facing_process USING btree (property_id);


--
-- Name: ix_location_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_location_fkey_property_id ON location USING btree (property_id);


--
-- Name: ix_neighbouring_place_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_neighbouring_place_fkey_property_id ON neighbouring_place USING btree (property_id);


--
-- Name: ix_ownership_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_ownership_fkey_property_id ON ownership USING btree (property_id);


--
-- Name: ix_property_brand_fkey_brand_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_property_brand_fkey_brand_id ON property_brand USING btree (brand_id);


--
-- Name: ix_property_brand_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_property_brand_fkey_property_id ON property_brand USING btree (property_id);


--
-- Name: ix_property_detail_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_property_detail_fkey_property_id ON property_detail USING btree (property_id);


--
-- Name: ix_property_image_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_property_image_fkey_property_id ON property_image USING btree (property_id);


--
-- Name: ix_property_landmark_fkey_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_property_landmark_fkey_property_id ON property_landmark USING btree (property_id);


--
-- Name: ix_property_sku_property_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_property_sku_property_id ON property_sku USING btree (property_id);


--
-- Name: ix_property_sku_sku_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_property_sku_sku_id ON property_sku USING btree (sku_id);


--
-- Name: ix_seller_sku_seller_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_seller_sku_seller_id ON seller_sku USING btree (seller_id);


--
-- Name: ix_seller_sku_sku_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_seller_sku_sku_id ON seller_sku USING btree (sku_id);


--
-- Name: ix_sku_bundle_bundle_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_sku_bundle_bundle_id ON sku_bundle USING btree (bundle_id);


--
-- Name: ix_sku_bundle_rule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_sku_bundle_rule_id ON sku USING btree (bundle_rule_id);


--
-- Name: ix_sku_bundle_sku_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_sku_bundle_sku_id ON sku_bundle USING btree (sku_id);


--
-- Name: ix_sku_category_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_sku_category_id ON sku USING btree (category_id);


--
-- Name: ix_sku_identifier; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_sku_identifier ON sku USING btree (identifier);


--
-- Name: ix_sku_tag; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX ix_sku_tag ON sku USING btree (tag);


--
-- Name: amenity_summary amenity_summary_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY amenity_summary
    ADD CONSTRAINT amenity_summary_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: application application_channel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY application
    ADD CONSTRAINT application_channel_id_fkey FOREIGN KEY (channel_id) REFERENCES channel(id);


--
-- Name: banquet_hall banquet_hall_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY banquet_hall
    ADD CONSTRAINT banquet_hall_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: bar bar_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY bar
    ADD CONSTRAINT bar_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: breakfast_cuisine breakfast_cuisine_breakfast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY breakfast_cuisine
    ADD CONSTRAINT breakfast_cuisine_breakfast_id_fkey FOREIGN KEY (breakfast_id) REFERENCES amenity_breakfast(id);


--
-- Name: breakfast_cuisine breakfast_cuisine_cuisine_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY breakfast_cuisine
    ADD CONSTRAINT breakfast_cuisine_cuisine_id_fkey FOREIGN KEY (cuisine_id) REFERENCES cuisine(id);


--
-- Name: city_alias city_alias_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY city_alias
    ADD CONSTRAINT city_alias_city_id_fkey FOREIGN KEY (city_id) REFERENCES city(id);


--
-- Name: city city_cluster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY city
    ADD CONSTRAINT city_cluster_id_fkey FOREIGN KEY (cluster_id) REFERENCES cluster(id);


--
-- Name: city city_state_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY city
    ADD CONSTRAINT city_state_id_fkey FOREIGN KEY (state_id) REFERENCES state(id);


--
-- Name: cluster cluster_region_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY cluster
    ADD CONSTRAINT cluster_region_id_fkey FOREIGN KEY (region_id) REFERENCES region(id);


--
-- Name: description description_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY description
    ADD CONSTRAINT description_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: facility_category_mapping facility_category_mapping_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY facility_category_mapping
    ADD CONSTRAINT facility_category_mapping_category_id_fkey FOREIGN KEY (category_id) REFERENCES facility_category(id);


--
-- Name: google_drive_base_folder google_drive_base_folder_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY google_drive_base_folder
    ADD CONSTRAINT google_drive_base_folder_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: google_drive_file google_drive_file_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY google_drive_file
    ADD CONSTRAINT google_drive_file_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: guest_facing_process guest_facing_process_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_facing_process
    ADD CONSTRAINT guest_facing_process_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: guest_type_property guest_type_property_guest_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_type_property
    ADD CONSTRAINT guest_type_property_guest_type_id_fkey FOREIGN KEY (guest_type_id) REFERENCES guest_type(id);


--
-- Name: guest_type_property guest_type_property_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY guest_type_property
    ADD CONSTRAINT guest_type_property_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: landmark landmark_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY landmark
    ADD CONSTRAINT landmark_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: locality locality_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY locality
    ADD CONSTRAINT locality_city_id_fkey FOREIGN KEY (city_id) REFERENCES city(id);


--
-- Name: locality locality_micro_market_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY locality
    ADD CONSTRAINT locality_micro_market_id_fkey FOREIGN KEY (micro_market_id) REFERENCES micro_market(id);


--
-- Name: location location_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY location
    ADD CONSTRAINT location_city_id_fkey FOREIGN KEY (city_id) REFERENCES city(id);


--
-- Name: location location_legal_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY location
    ADD CONSTRAINT location_legal_city_id_fkey FOREIGN KEY (legal_city_id) REFERENCES city(id);


--
-- Name: location location_locality_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY location
    ADD CONSTRAINT location_locality_id_fkey FOREIGN KEY (locality_id) REFERENCES locality(id);


--
-- Name: location location_micro_market_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY location
    ADD CONSTRAINT location_micro_market_id_fkey FOREIGN KEY (micro_market_id) REFERENCES micro_market(id);


--
-- Name: location location_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY location
    ADD CONSTRAINT location_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: micro_market micro_market_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY micro_market
    ADD CONSTRAINT micro_market_city_id_fkey FOREIGN KEY (city_id) REFERENCES city(id);


--
-- Name: neighbouring_place neighbouring_place_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY neighbouring_place
    ADD CONSTRAINT neighbouring_place_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: ota_property_mapping ota_property_mapping_ota_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property_mapping
    ADD CONSTRAINT ota_property_mapping_ota_property_id_fkey FOREIGN KEY (ota_property_id) REFERENCES ota_property(id);


--
-- Name: ota_property ota_property_ota_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property
    ADD CONSTRAINT ota_property_ota_id_fkey FOREIGN KEY (ota_id) REFERENCES ota(id);


--
-- Name: ota_property ota_property_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_property
    ADD CONSTRAINT ota_property_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: ota_rate_plan_mapping ota_rate_plan_mapping_ota_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_rate_plan_mapping
    ADD CONSTRAINT ota_rate_plan_mapping_ota_property_id_fkey FOREIGN KEY (ota_property_id) REFERENCES ota_property(id);


--
-- Name: ota_rate_plan_mapping ota_rate_plan_mapping_rate_plan_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_rate_plan_mapping
    ADD CONSTRAINT ota_rate_plan_mapping_rate_plan_fkey FOREIGN KEY (rate_plan) REFERENCES rate_plan(id);


--
-- Name: ota_rate_plan_mapping ota_rate_plan_mapping_room_type_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_rate_plan_mapping
    ADD CONSTRAINT ota_rate_plan_mapping_room_type_fkey FOREIGN KEY (room_type) REFERENCES room_type(id);


--
-- Name: ota_room_mapping ota_room_mapping_ota_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_room_mapping
    ADD CONSTRAINT ota_room_mapping_ota_property_id_fkey FOREIGN KEY (ota_property_id) REFERENCES ota_property(id);


--
-- Name: ota_room_mapping ota_room_mapping_room_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ota_room_mapping
    ADD CONSTRAINT ota_room_mapping_room_type_id_fkey FOREIGN KEY (room_type_id) REFERENCES room_type(id);


--
-- Name: ownership ownership_owner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ownership
    ADD CONSTRAINT ownership_owner_id_fkey FOREIGN KEY (owner_id) REFERENCES owner(id);


--
-- Name: ownership ownership_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ownership
    ADD CONSTRAINT ownership_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: pos_menu_category pos_menu_category_seller_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pos_menu_category
    ADD CONSTRAINT pos_menu_category_seller_category_id_fkey FOREIGN KEY (seller_category_id) REFERENCES seller_category(id);


--
-- Name: pricing_mapping pricing_mapping_channel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_mapping
    ADD CONSTRAINT pricing_mapping_channel_id_fkey FOREIGN KEY (channel_id) REFERENCES channel(id);


--
-- Name: pricing_mapping pricing_mapping_pricing_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_mapping
    ADD CONSTRAINT pricing_mapping_pricing_policy_id_fkey FOREIGN KEY (pricing_policy_id) REFERENCES pricing_policy(id);


--
-- Name: pricing_mapping pricing_mapping_sub_channel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY pricing_mapping
    ADD CONSTRAINT pricing_mapping_sub_channel_id_fkey FOREIGN KEY (sub_channel_id) REFERENCES sub_channel(id);


--
-- Name: properties_sku_categories properties_sku_categories_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY properties_sku_categories
    ADD CONSTRAINT properties_sku_categories_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: properties_sku_categories properties_sku_categories_sku_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY properties_sku_categories
    ADD CONSTRAINT properties_sku_categories_sku_category_id_fkey FOREIGN KEY (sku_category_id) REFERENCES sku_category(id);


--
-- Name: property_amenity property_amenity_breakfast_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_breakfast_id_fkey FOREIGN KEY (breakfast_id) REFERENCES amenity_breakfast(id);


--
-- Name: property_amenity property_amenity_disable_friendly_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_disable_friendly_id_fkey FOREIGN KEY (disable_friendly_id) REFERENCES amenity_disable_friendly(id);


--
-- Name: property_amenity property_amenity_elevator_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_elevator_id_fkey FOREIGN KEY (elevator_id) REFERENCES amenity_elevator(id);


--
-- Name: property_amenity property_amenity_gym_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_gym_id_fkey FOREIGN KEY (gym_id) REFERENCES amenity_gym(id);


--
-- Name: property_amenity property_amenity_laundry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_laundry_id_fkey FOREIGN KEY (laundry_id) REFERENCES amenity_laundry(id);


--
-- Name: property_amenity property_amenity_parking_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_parking_id_fkey FOREIGN KEY (parking_id) REFERENCES amenity_parking(id);


--
-- Name: property_amenity property_amenity_payment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_payment_id_fkey FOREIGN KEY (payment_id) REFERENCES amenity_payment(id);


--
-- Name: property_amenity property_amenity_private_cab_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_private_cab_id_fkey FOREIGN KEY (private_cab_id) REFERENCES amenity_private_cab(id);


--
-- Name: property_amenity property_amenity_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: property_amenity property_amenity_public_washroom_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_public_washroom_id_fkey FOREIGN KEY (public_washroom_id) REFERENCES amenity_public_washroom(id);


--
-- Name: property_amenity property_amenity_spa_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_spa_id_fkey FOREIGN KEY (spa_id) REFERENCES amenity_spa(id);


--
-- Name: property_amenity property_amenity_swimming_pool_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_amenity
    ADD CONSTRAINT property_amenity_swimming_pool_id_fkey FOREIGN KEY (swimming_pool_id) REFERENCES amenity_swimming_pool(id);


--
-- Name: property_brand property_brand_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_brand
    ADD CONSTRAINT property_brand_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brand(id);


--
-- Name: property_brand property_brand_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_brand
    ADD CONSTRAINT property_brand_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: property_detail property_detail_bank_detail_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_detail
    ADD CONSTRAINT property_detail_bank_detail_id_fkey FOREIGN KEY (bank_detail_id) REFERENCES bank_detail(id);


--
-- Name: property_detail property_detail_ext_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_detail
    ADD CONSTRAINT property_detail_ext_id_fkey FOREIGN KEY (ext_id) REFERENCES provider(id);


--
-- Name: property_detail property_detail_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_detail
    ADD CONSTRAINT property_detail_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: property_detail property_detail_sold_as_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_detail
    ADD CONSTRAINT property_detail_sold_as_id_fkey FOREIGN KEY (sold_as_id) REFERENCES param(id);


--
-- Name: property_image property_image_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_image
    ADD CONSTRAINT property_image_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: property_image property_image_room_type_config_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_image
    ADD CONSTRAINT property_image_room_type_config_id_fkey FOREIGN KEY (room_type_config_id) REFERENCES room_type_configuration(id);


--
-- Name: property_landmark property_landmark_landmark_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_landmark
    ADD CONSTRAINT property_landmark_landmark_id_fkey FOREIGN KEY (landmark_id) REFERENCES landmark(id);


--
-- Name: property_landmark property_landmark_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_landmark
    ADD CONSTRAINT property_landmark_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: property_policy_map property_policy_map_property_detail_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_policy_map
    ADD CONSTRAINT property_policy_map_property_detail_id_fkey FOREIGN KEY (property_detail_id) REFERENCES property_detail(id);


--
-- Name: property_policy_map property_policy_map_property_policy_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_policy_map
    ADD CONSTRAINT property_policy_map_property_policy_id_fkey FOREIGN KEY (property_policy_id) REFERENCES property_policy(id);


--
-- Name: property_sku property_sku_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_sku
    ADD CONSTRAINT property_sku_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: property_sku property_sku_sku_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY property_sku
    ADD CONSTRAINT property_sku_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id);


--
-- Name: provider_brand provider_brand_brand_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_brand
    ADD CONSTRAINT provider_brand_brand_id_fkey FOREIGN KEY (brand_id) REFERENCES brand(id);


--
-- Name: provider_brand provider_brand_provider_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_brand
    ADD CONSTRAINT provider_brand_provider_id_fkey FOREIGN KEY (provider_id) REFERENCES provider(id);


--
-- Name: provider_room_mapping provider_room_mapping_ext_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_room_mapping
    ADD CONSTRAINT provider_room_mapping_ext_id_fkey FOREIGN KEY (ext_id) REFERENCES provider(id);


--
-- Name: provider_room_mapping provider_room_mapping_room_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY provider_room_mapping
    ADD CONSTRAINT provider_room_mapping_room_type_id_fkey FOREIGN KEY (room_type_id) REFERENCES room_type(id);


--
-- Name: rate_plan_configuration rate_plan_configuration_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan_configuration
    ADD CONSTRAINT rate_plan_configuration_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: rate_plan_configuration rate_plan_configuration_rate_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan_configuration
    ADD CONSTRAINT rate_plan_configuration_rate_plan_id_fkey FOREIGN KEY (rate_plan_id) REFERENCES rate_plan(id);


--
-- Name: rate_plan rate_plan_ext_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan
    ADD CONSTRAINT rate_plan_ext_id_fkey FOREIGN KEY (ext_id) REFERENCES provider(id);


--
-- Name: rate_plan rate_plan_treebo_plan_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY rate_plan
    ADD CONSTRAINT rate_plan_treebo_plan_id_fkey FOREIGN KEY (treebo_plan_id) REFERENCES rate_plan(id);


--
-- Name: restaurant_cuisine restaurant_cuisine_cuisine_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant_cuisine
    ADD CONSTRAINT restaurant_cuisine_cuisine_id_fkey FOREIGN KEY (cuisine_id) REFERENCES cuisine(id);


--
-- Name: restaurant_cuisine restaurant_cuisine_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant_cuisine
    ADD CONSTRAINT restaurant_cuisine_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES restaurant(id);


--
-- Name: restaurant restaurant_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant
    ADD CONSTRAINT restaurant_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: restaurant_table restaurant_table_seller_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY restaurant_table
    ADD CONSTRAINT restaurant_table_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id);


--
-- Name: roles_users roles_users_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY roles_users
    ADD CONSTRAINT roles_users_role_id_fkey FOREIGN KEY (role_id) REFERENCES role(id);


--
-- Name: roles_users roles_users_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY roles_users
    ADD CONSTRAINT roles_users_user_id_fkey FOREIGN KEY (user_id) REFERENCES cs_user(id);


--
-- Name: room_amenity room_amenity_ac_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_ac_id_fkey FOREIGN KEY (ac_id) REFERENCES amenity_ac(id);


--
-- Name: room_amenity room_amenity_heater_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_heater_id_fkey FOREIGN KEY (heater_id) REFERENCES amenity_heater(id);


--
-- Name: room_amenity room_amenity_hot_water_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_hot_water_id_fkey FOREIGN KEY (hot_water_id) REFERENCES amenity_hot_water(id);


--
-- Name: room_amenity room_amenity_intercom_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_intercom_id_fkey FOREIGN KEY (intercom_id) REFERENCES amenity_intercom(id);


--
-- Name: room_amenity room_amenity_room_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_room_id_fkey FOREIGN KEY (room_id) REFERENCES room(id);


--
-- Name: room_amenity room_amenity_stove_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_stove_id_fkey FOREIGN KEY (stove_id) REFERENCES amenity_stove(id);


--
-- Name: room_amenity room_amenity_tv_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_tv_id_fkey FOREIGN KEY (tv_id) REFERENCES amenity_tv(id);


--
-- Name: room_amenity room_amenity_twin_bed_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_amenity
    ADD CONSTRAINT room_amenity_twin_bed_id_fkey FOREIGN KEY (twin_bed_id) REFERENCES amenity_twin_bed(id);


--
-- Name: room room_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room
    ADD CONSTRAINT room_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: room room_room_type_config_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room
    ADD CONSTRAINT room_room_type_config_id_fkey FOREIGN KEY (room_type_config_id) REFERENCES room_type_configuration(id);


--
-- Name: room room_room_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room
    ADD CONSTRAINT room_room_type_id_fkey FOREIGN KEY (room_type_id) REFERENCES room_type(id);


--
-- Name: room_type_configuration room_type_configuration_ext_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type_configuration
    ADD CONSTRAINT room_type_configuration_ext_id_fkey FOREIGN KEY (ext_id) REFERENCES provider(id);


--
-- Name: room_type_configuration room_type_configuration_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type_configuration
    ADD CONSTRAINT room_type_configuration_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: room_type_configuration room_type_configuration_room_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY room_type_configuration
    ADD CONSTRAINT room_type_configuration_room_type_id_fkey FOREIGN KEY (room_type_id) REFERENCES room_type(id);


--
-- Name: ruptub_legal_entities ruptub_legal_entities_state_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY ruptub_legal_entities
    ADD CONSTRAINT ruptub_legal_entities_state_id_fkey FOREIGN KEY (state_id) REFERENCES state(id);


--
-- Name: seller seller_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller
    ADD CONSTRAINT seller_city_id_fkey FOREIGN KEY (city_id) REFERENCES city(id);


--
-- Name: seller seller_legal_city_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller
    ADD CONSTRAINT seller_legal_city_id_fkey FOREIGN KEY (legal_city_id) REFERENCES city(id);


--
-- Name: seller seller_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller
    ADD CONSTRAINT seller_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: seller seller_seller_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller
    ADD CONSTRAINT seller_seller_category_id_fkey FOREIGN KEY (seller_category_id) REFERENCES seller_category(id);


--
-- Name: seller_sku seller_sku_menu_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_sku
    ADD CONSTRAINT seller_sku_menu_category_id_fkey FOREIGN KEY (menu_category_id) REFERENCES pos_menu_category(id);


--
-- Name: seller_sku seller_sku_seller_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_sku
    ADD CONSTRAINT seller_sku_seller_id_fkey FOREIGN KEY (seller_id) REFERENCES seller(seller_id);


--
-- Name: seller_sku seller_sku_sku_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_sku
    ADD CONSTRAINT seller_sku_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id);


--
-- Name: seller_type_history seller_type_history_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY seller_type_history
    ADD CONSTRAINT seller_type_history_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: sku_activation sku_activation_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_activation
    ADD CONSTRAINT sku_activation_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id) ON DELETE CASCADE;


--
-- Name: sku_activation sku_activation_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_activation
    ADD CONSTRAINT sku_activation_service_id_fkey FOREIGN KEY (service_id) REFERENCES param(id);


--
-- Name: sku_activation sku_activation_sku_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_activation
    ADD CONSTRAINT sku_activation_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id);


--
-- Name: sku_attribute sku_attribute_sku_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_attribute
    ADD CONSTRAINT sku_attribute_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id) ON DELETE CASCADE;


--
-- Name: sku_bundle sku_bundle_bundle_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_bundle
    ADD CONSTRAINT sku_bundle_bundle_id_fkey FOREIGN KEY (bundle_id) REFERENCES sku(id) ON DELETE CASCADE;


--
-- Name: sku sku_bundle_rule_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku
    ADD CONSTRAINT sku_bundle_rule_id_fkey FOREIGN KEY (bundle_rule_id) REFERENCES param(id);


--
-- Name: sku_bundle sku_bundle_sku_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku_bundle
    ADD CONSTRAINT sku_bundle_sku_id_fkey FOREIGN KEY (sku_id) REFERENCES sku(id) ON DELETE CASCADE;


--
-- Name: sku sku_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sku
    ADD CONSTRAINT sku_category_id_fkey FOREIGN KEY (category_id) REFERENCES sku_category(id);


--
-- Name: state state_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY state
    ADD CONSTRAINT state_country_id_fkey FOREIGN KEY (country_id) REFERENCES country(id);


--
-- Name: sub_channel sub_channel_channel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY sub_channel
    ADD CONSTRAINT sub_channel_channel_id_fkey FOREIGN KEY (channel_id) REFERENCES channel(id);


--
-- Name: transport_station_property transport_station_property_property_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station_property
    ADD CONSTRAINT transport_station_property_property_id_fkey FOREIGN KEY (property_id) REFERENCES property(id);


--
-- Name: transport_station_property transport_station_property_transport_station_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY transport_station_property
    ADD CONSTRAINT transport_station_property_transport_station_id_fkey FOREIGN KEY (transport_station_id) REFERENCES transport_station(id);

-- downgrade
