-- revision: '20210210190251_add_menu_item_item_variant_and__menu_item_item_side'
-- down_revision: '20210208182345_add_is_msme_and_tan'

-- upgrade

--
-- Name: menu_item_item_variant, Type: TABLE, Schema: public; Owner: -
--
CREATE TABLE menu_item_item_variant (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    menu_item_id int NOT NULL,
    item_variant_id int NOT NULL
);

ALTER TABLE ONLY menu_item_item_variant
    ADD CONSTRAINT menu_item_item_variant_menu_id_fkey FOREIGN KEY (menu_item_id) REFERENCES menu_item(id) ON DELETE CASCADE;

ALTER TABLE ONLY menu_item_item_variant
    ADD CONSTRAINT menu_item_item_variant_item_variant_id_fkey FOREIGN KEY (item_variant_id) REFERENCES item_variant(id) ON DELETE CASCADE;


-- Name: menu_item_side_item, Type: TABLE, Schema: public; Owner: -

CREATE TABLE menu_item_side_item (
    created_at timestamp WITH time zone,
    modified_at timestamp WITH time zone,
    is_deleted boolean NOT NULL DEFAULT FALSE,
    id SERIAL PRIMARY KEY,
    menu_item_id int NOT NULL,
    side_item_id int NOT NULL
);

ALTER TABLE ONLY menu_item_side_item
    ADD CONSTRAINT menu_item_item_sides_menu_id_fkey FOREIGN KEY (menu_item_id) REFERENCES menu_item(id) ON DELETE CASCADE;

ALTER TABLE ONLY menu_item_side_item
    ADD CONSTRAINT menu_item_side_items_item_side_id_fkey FOREIGN KEY (side_item_id) REFERENCES side_item(id) ON DELETE CASCADE;


-- downgrade
DROP TABLE menu_item_item_variant;
DROP TABLE menu_item_side_item;