-- revision: '20250703140000_fix_department_template_parent_field'
-- down_revision: '20250703131940_new'

-- upgrade

-- Fix department_template table to use parent_code instead of parent_id
-- This aligns the database schema with the code which uses parent_code everywhere

-- Add parent_code column
ALTER TABLE department_template ADD COLUMN parent_code VARCHAR(50);

-- Create index on parent_code
CREATE INDEX ix_department_template_parent_code ON department_template(parent_code);

-- Drop the old parent_id column and its constraint
ALTER TABLE department_template DROP CONSTRAINT IF EXISTS department_template_parent_id_fkey;
ALTER TABLE department_template DROP COLUMN IF EXISTS parent_id;

-- downgrade

-- Restore parent_id column
ALTER TABLE department_template ADD COLUMN parent_id INTEGER;

-- Add foreign key constraint
ALTER TABLE department_template ADD CONSTRAINT department_template_parent_id_fkey 
    FOREIGN KEY (parent_id) REFERENCES department_template(id) ON DELETE RESTRICT;

-- Create index on parent_id
CREATE INDEX ix_department_template_parent_id ON department_template(parent_id);

-- Drop parent_code column and index
DROP INDEX IF EXISTS ix_department_template_parent_code;
ALTER TABLE department_template DROP COLUMN IF EXISTS parent_code;
