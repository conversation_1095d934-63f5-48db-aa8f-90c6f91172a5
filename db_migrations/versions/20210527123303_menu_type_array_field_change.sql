-- revision: '20210527123303_menu_type_array_field_change'
-- down_revision: '20210401170241_add_menu_types_cost_item_customisation'

-- upgrade
alter table menu alter column menu_type type character varying[] using array[lower(menu_type)];
alter table menu rename menu_type to menu_types;

alter table combo add column food_type character varying default 'vegetarian';
alter table item add column food_type character varying default 'vegetarian';

update combo set food_type = 'vegetarian' where is_vegetarian = true;
update combo set food_type = 'non_vegetarian' where is_vegetarian = false;

update item set food_type = 'vegetarian' where is_vegetarian = true;
update item set food_type = 'non_vegetarian' where is_vegetarian = false;

-- downgrade
alter table menu alter column menu_types type character varying using coalesce(menu_types[1],'');
alter table menu rename menu_types to menu_type;

alter table combo drop column food_type;
alter table item drop column food_type;