-- revision: '20201209131416_bronze_shield_addition'
-- down_revision: '20200904111245_seller_id_in_tenant_config'

-- upgrade
CREATE TYPE hygiene_shield_name_choices_new AS ENUM ('GOLD', 'PLATINUM', 'BRONZE');
ALTER TABLE property_detail ALTER COLUMN hygiene_shield_name TYPE hygiene_shield_name_choices_new USING (hygiene_shield_name::text::hygiene_shield_name_choices_new);
DROP TYPE hygiene_shield_name_choices;
ALTER TYPE hygiene_shield_name_choices_new RENAME TO hygiene_shield_name_choices;

-- downgrade
CREATE TYPE hygiene_shield_name_choices_new AS ENUM ('GOLD', 'PLATINUM');
DELETE FROM property_detail WHERE hygiene_shield_name = 'BRONZE';
ALTER TABLE property_detail ALTER COLUMN hygiene_shield_name TYPE hygiene_shield_name_choices_new USING (hygiene_shield_name::text::hygiene_shield_name_choices_new);
DROP TYPE hygiene_shield_name_choices;
ALTER TYPE hygiene_shield_name_choices_new RENAME TO hygiene_shield_name_choices;