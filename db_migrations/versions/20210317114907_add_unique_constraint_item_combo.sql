-- revision: '20210317114907_add_unique_constraint_item_combo'
-- down_revision: '20210216115741_add_item_customisation'

-- upgrade
ALTER TABLE menu DROP constraint menu_seller_id_name_key;
CREATE UNIQUE INDEX item_name_seller_id_unique_key ON item (name, seller_id) WHERE is_deleted=false;
CREATE UNIQUE INDEX combo_name_seller_id_unique_key ON combo (name, seller_id) WHERE is_deleted=false;
CREATE UNIQUE INDEX menu_name_seller_id_unique_key ON menu (name, seller_id) WHERE is_deleted=false;

-- downgrade

DROP INDEX item_name_seller_id_unique_key;
DROP INDEX combo_name_seller_id_unique_key;
DROP INDEX menu_name_seller_id_unique_key;