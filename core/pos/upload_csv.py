from collections import defaultdict
from decimal import Decimal

from fuzzywuzzy import fuzz
from marshmallow import ValidationError

from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import SkuRepository, MenuCategoryRepository, SellerSkuRepository
from cataloging_service.models import Sku, SellerSku

MAXIMUM_MATCH_RATIO_LIMIT = 100


class POSMenuUpload:

    def upload_csv(self, pos_menus):
        error_dict = defaultdict(list)
        for line_number, pos_menu in enumerate(pos_menus, start=2):
            try:
                self.validate_input(pos_menu)
            except ValidationError as e:
                error_dict[line_number].append(str(e))

        if error_dict:
            return error_dict

        self.save_and_publish_menu(pos_menus)

    def validate_input(self, pos_menu):
        self.validate_sku_category_code(pos_menu['sku_category_code'])
        self.validate_item_name(pos_menu['seller_category_id'], pos_menu['menu_category_code'], pos_menu['item_name'],
                                pos_menu['is_override'])

    @staticmethod
    def validate_sku_category_code(sku_category_code):
        is_valid_sku_category_code = SkuRepository.rget_sku_category_by_code(sku_category_code.lower())
        if not is_valid_sku_category_code:
            raise ValidationError('Not a valid sku category code')

    @staticmethod
    def validate_item_name(seller_category_id, menu_category_code, item_name, is_override):

        pos_menu_category = MenuCategoryRepository.get_menu_categories(seller_category_id)
        is_valid_menu_category_code = [pmc for pmc in pos_menu_category if
                                       pmc.name.lower() == menu_category_code.lower()]
        if not is_valid_menu_category_code:
            raise ValidationError('Not a valid menu category code')

        if not is_override:
            fuzzy_match_item_name = None
            fuzzy_match_limit = 90
            all_saleable_items = SellerSkuRepository.get_all_seller_skus()
            all_saleable_items_names = [saleable_item.name.lower() for saleable_item in all_saleable_items]
            for saleable_item_name in all_saleable_items_names:
                ratio = fuzz.ratio(item_name.lower(), saleable_item_name)
                if ratio == MAXIMUM_MATCH_RATIO_LIMIT:
                    break
                if ratio >= fuzzy_match_limit:
                    fuzzy_match_limit = ratio
                    fuzzy_match_item_name = saleable_item_name
            if fuzzy_match_item_name:
                raise ValidationError("The name that is very close to {item_name} is {fuzzy_match_item_name}".format(
                    item_name=item_name.lower(), fuzzy_match_item_name=fuzzy_match_item_name))

    def save_and_publish_menu(self, pos_menus):

        skus = self._save_sku(pos_menus)

        sku_ids = [sku.id for sku in skus]
        self._bulk_push_created_skus(sku_ids)

        seller_id = None
        seller_category_id = None
        for menu in pos_menus:
            seller_id = menu['seller_id']
            seller_category_id = menu['seller_category_id']
            break

        self._save_seller_sku(seller_category_id, seller_id, skus, pos_menus)

    @atomic_operation
    def _save_sku(self, pos_menus):
        sku_categories = SkuRepository.rget_sku_categories()
        sku_category_code_to_id_mapping = {sc.name.lower(): sc.id for sc in sku_categories}
        pos_menu_skus = []
        exisiting_skus = []
        for row in pos_menus:
            exisiting_sku = SkuRepository.rget_sku_by_name(name=row['item_name'])
            if not exisiting_sku:
                sku = Sku(name=row['item_name'],
                          tax_type="unit",
                          sku_type="sku",
                          saleable=True,
                          category_id=sku_category_code_to_id_mapping.get(row['sku_category_code'].lower()),
                          chargeable_per_occupant=False,
                          is_active=False)
                pos_menu_skus.append(sku)
            else:
                exisiting_skus.append(exisiting_sku)
        pos_menu_skus = SkuRepository().persist_all(pos_menu_skus)
        return pos_menu_skus + exisiting_skus

    @atomic_operation
    def _save_seller_sku(self, seller_category_id, seller_id, skus, pos_menus):
        pos_menu_categories = MenuCategoryRepository.get_menu_categories(seller_category_id)
        menu_category_code_to_id_mapping = {mc.name.lower(): mc.id for mc in pos_menu_categories}

        item_name_to_pos_menu_mapping = {pos_menu['item_name']: pos_menu for pos_menu in pos_menus}
        seller_skus = []
        for sku in skus:
            pos_menu = item_name_to_pos_menu_mapping.get(sku.name)
            menu_category_id = menu_category_code_to_id_mapping.get(pos_menu['menu_category_code'].lower())
            if not menu_category_id:
                raise ValueError("No menu category found for {s}".format(s=pos_menu['menu_category_code']))
            seller_sku = SellerSku(
                sku_id=sku.id,
                seller_id=seller_id,
                sku_category_code=sku.code,
                name=sku.name,
                display_name=sku.name,
                menu_category_id=menu_category_id,
                pretax_price=Decimal(pos_menu['pretax_price'])
            )
            seller_skus.append(seller_sku)
        seller_skus = SellerSkuRepository().persist_all(seller_skus)
        return seller_skus

    def _bulk_push_created_skus(self, skus_ids):
        service_provider.sku_service.publish_sku(skus_ids)
