from cataloging_service.models import RoomAmenity, AmenityTwinBed


def create_room_amenities(property_launch, room, room_types):
    amenity = RoomAmenity()
    amenity.room = room
    room_type = [rt for rt in room_types if rt['id'] == room.room_type_id][0]
    room_type_detail = [rt for rt in property_launch['room_type_details'] if
                        rt['room_type'].lower() == room_type['type'].lower()][0]

    bed_type = room_type_detail['bed_type']
    if bed_type == 'king_bed':
        amenity.king_sized_beds = True
    elif bed_type == 'queen_bed':
        amenity.queen_sized_beds = True
    elif bed_type == 'single_bed':
        amenity.single_beds = True
    elif bed_type == 'joinable_twin_bed':
        bed = AmenityTwinBed()
        bed.joinable = True
        amenity.twin_bed = bed
    elif bed_type == 'non_joinable_twin_bed':
        bed = AmenityTwinBed()
        bed.joinable = False
        amenity.twin_bed = bed
    else:
        raise RuntimeError('Unknown bed type: {t}'.format(t=bed_type))
    return amenity
