import re
from collections import namedtuple
from functools import partial

from fuzzywuzzy import process
from sqlalchemy import false

from cataloging_service.extensions import cache
from cataloging_service.infrastructure.repositories import PropertyRepository
from cataloging_service.models import Property
from core.common.sqlalchemy_utils import order_clause_by_list

common_keywords = ['treebotrend', 'treebotryst', 'treebotrip']
common_keywords_pattern = "{}".format("|".join(common_keywords))
common_keywords_regex = re.compile(common_keywords_pattern, re.IGNORECASE)

only_alphanum_regex = re.compile('[^A-Za-z0-9]+', re.IGNORECASE)

PropertyData = namedtuple('PropertyData', ['id', 'name', 'sanitized_name'])


def _property_name_processor(data, **kwargs):
    if isinstance(data, PropertyData):
        return process.default_processor(data.sanitized_name, **kwargs)
    return process.default_processor(data, **kwargs)


@cache.cached(timeout=1800, key_prefix='fuzzy_all_property_ids_names')
def _get_all_property_ids_name_map():
    result_set = PropertyRepository().get_all_properties(only_id_name=True, include_test_entries=True)
    return [PropertyData(r.id, r.name, _sanitized_name(r.name)) for r in result_set]


def _sanitized_name(property_name):
    property_name = only_alphanum_regex.sub('', property_name)
    return common_keywords_regex.sub('', property_name)


def _fuzzy_matched_property_ids(string, base_cutoff=50):
    """
    :param string: String to be fuzzy matched
    :param base_cutoff: base cutoff is a score by fuzzywuzzy below which the string is not considered a match
    :return:
    """
    property_name_id_map = _get_all_property_ids_name_map()
    query = only_alphanum_regex.sub('', string)
    extractor = partial(
        process.extractBests,
        **{
            'query': query,
            'choices': property_name_id_map,
            'processor': _property_name_processor,
        }
    )
    data = extractor(score_cutoff=base_cutoff, limit=None)
    matched_property_ids = [pdata.id for pdata, score in data]
    return matched_property_ids


def filter_fuzzy(string, query=None, similarity_score_limit=50):
    """
    :param string: String to be fuzzy matched
    :param query: Takes a property query
    :param similarity_score_limit: Similarity scores below this wont be returned
    :return:
    """
    matched_ids = _fuzzy_matched_property_ids(string=string, base_cutoff=similarity_score_limit)
    if not matched_ids:
        return query.filter(false())
    query = query.filter(Property.id.in_(matched_ids))
    ordering = order_clause_by_list(matched_ids, Property.id)
    return query.order_by(ordering)
