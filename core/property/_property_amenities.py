from cataloging_service.constants.model_choices import ParkingChoices, SwimmingPoolChoices, BreakfastChoices
from cataloging_service.models import Property<PERSON>menity, AmenityElevator, AmenityParking, \
    AmenityDisableFriendly, Bar, Restaurant, BanquetHall, AmenitySwimmingPool, AmenityGym, AmenitySpa, AmenityLaundry, \
    AmenityPrivateCab, AmenityPayment, AmenityBreakfast, AmenityPublicWashroom

STANDARD_OPEN_TIME = '10:00 AM'
STANDARD_CLOSE_TIME = '05:00 PM'


def start_end_from_timing(timing_string):
    timing = timing_string.split('-')
    start = timing[0].strip()
    end = timing[-1].strip()
    return start, end


def create_property_amenities(property, property_launch):
    amenity = PropertyAmenity()
    amenitities = [amenity]
    amenity.property = property

    amenity.security = property_launch['facilities']['is_security_guard_available']
    amenity.roof_top_cafe = property_launch['facilities']['is_roof_top_cafe_available']
    amenity.pool_table = property_launch['facilities']['is_pool_table_available']

    if property_launch['facilities']['is_public_washroom_available']:
        washroom = AmenityPublicWashroom()
        washroom.gender_segregated = False
        amenity.public_washroom = washroom

    if property_launch['facilities']['is_elevator_available']:
        elevator = AmenityElevator()
        elevator.floors_accessible = property_launch['total_floors']
        amenity.elevator = elevator

    if property_launch['facilities']['is_parking_available']:
        parking = AmenityParking()
        parking.location = ParkingChoices.LOCATION_OUTDOORS_UNCOVERED
        parking.max_two_wheelers = 0
        parking.max_four_wheelers = 0
        parking.charges = 0
        amenity.parking = parking

    if property_launch['facilities']['is_disabled_friendly']:
        disability = AmenityDisableFriendly()
        disability.wheelchair_count = 0
        disability.ramp_available = True
        disability.disable_friendly_rooms = ""
        disability.disable_friendly_room_available = True if disability.disable_friendly_rooms else False
        amenity.disable_friendly = disability

    if property_launch['facilities']['is_bar_available']:
        bar = Bar()
        bar.name = ''
        bar.property = property
        start, end = start_end_from_timing(property_launch['bar_timings'])
        bar.open_time = start
        bar.close_time = end

        bar.last_order_time = bar.close_time
        bar.room_start_time = bar.open_time
        bar.room_end_time = bar.last_order_time
        amenitities.append(bar)

    if property_launch['facilities']['is_restaurant_available']:
        restaurant = Restaurant()
        restaurant.name = ''
        restaurant.property_id = property.id
        start, end = start_end_from_timing(property_launch['restaurant_timings'])
        restaurant.open_time = start
        restaurant.close_time = end
        restaurant.last_order_time = restaurant.close_time
        restaurant.room_service_start_time = restaurant.open_time
        restaurant.room_service_end_time = restaurant.close_time
        amenitities.append(restaurant)

        # restaurant.non_veg = self.read_boolean(non_veg)
        # restaurant.a_la_carte = self.read_boolean(a_la_carte)
        # restaurant.buffet = self.read_boolean(buffet)
        # restaurant.outside_food_allowed = self.read_boolean(outside_food)
        # restaurant.baby_milk_served = self.read_boolean(baby_milk)
        # restaurant.washroom_present = self.read_boolean(washroom)
        # restaurant.handwash_present = self.read_boolean(handwash)
        # restaurant.baby_milk_timing = milk_timings
        # restaurant.egg_served = self.read_boolean(eggs)
        # restaurant.jain_food_served = self.read_boolean(jain)
        # restaurant.cusines

    if property_launch['facilities']['is_banquet_hall_available']:
        hall = BanquetHall()
        hall.property = property
        hall.name = ''
        hall.floor = 0
        hall.capacity = 0
        hall.size = 0
        amenitities.append(hall)

    if property_launch['facilities']['is_pool_available']:
        pool = AmenitySwimmingPool()
        pool.location = SwimmingPoolChoices.LOCATION_OUTDOORS_UNCOVERED
        pool.open_time = STANDARD_OPEN_TIME
        pool.close_time = STANDARD_CLOSE_TIME
        pool.pool_size = 0
        pool.active = True
        amenity.swimming_pool = pool

    if property_launch['facilities']['is_gym_available']:
        gym = AmenityGym()
        gym.open_time = STANDARD_OPEN_TIME
        gym.close_time = STANDARD_CLOSE_TIME
        gym.equipments_available = ""
        gym.active = True
        amenity.gym = gym

    if property_launch['facilities']['is_spa_available']:
        spa = AmenitySpa()
        spa.open_time = STANDARD_OPEN_TIME
        spa.close_time = STANDARD_CLOSE_TIME
        spa.active = True
        amenity.spa = spa

    if property_launch['facilities']['is_laundry_available']:
        laundry = AmenityLaundry()
        start, end = start_end_from_timing(property_launch['laundry_timings'])
        laundry.pickup_time = start
        laundry.drop_time = end
        laundry.is_external = True
        amenity.laundry = laundry

    if property_launch['facilities']['is_private_cab_service_available']:
        cab = AmenityPrivateCab()
        cab.charges = 0
        amenity.private_cab = cab

    if property_launch['facilities']['is_amex_payment_available']:
        payment = AmenityPayment()
        payment.amex_accepted = True
        payment.wallet_accepted = True
        amenity.payment = payment

    if True:
        breakfast = AmenityBreakfast()
        breakfast.type = BreakfastChoices.BREAKFAST_FIXED
        breakfast.service_area = BreakfastChoices.SERVICE_AREA_DINING_AREA
        breakfast.non_veg = False
        breakfast.rotational = False
        amenity.breakfast = breakfast

    if property_launch['room_service_timings']:
        amenity.room_service = True

    # amenity.lobby_ac = self.read_boolean(ac_row[0])
    # amenity.lobby_furniture = self.read_boolean(furniture_row[0])
    # amenity.lobby_smoke_alarm = self.read_boolean(smoke_alarm_row[0])
    # amenity.iron_board_count = self.read_number(iron_board_row[0])
    # amenity.pantry = self.read_boolean(kitchen_row[0])
    # amenity.cloak_room = self.read_boolean(cloak_row[0])
    # amenity.driver_quarters_count = self.read_number(driver_row[0])
    # amenity.travel_desk = self.read_boolean(travel_row[0])
    # amenity.pets_allowed = self.read_boolean(pets_row[0])

    return amenitities
