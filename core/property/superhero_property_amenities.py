from cataloging_service.constants.model_choices import BreakfastChoices
from cataloging_service.models import PropertyAmenity, AmenityBreakfast

STANDARD_OPEN_TIME = '10:00 AM'
STANDARD_CLOSE_TIME = '05:00 PM'


def start_end_from_timing(timing_string):
    timing = timing_string.split('-')
    start = timing[0].strip()
    end = timing[-1].strip()
    return start, end


def create_property_amenities(property, property_launch):
    amenity = PropertyAmenity()
    amenitities = [amenity]
    amenity.property = property

    if True:
        breakfast = AmenityBreakfast()
        breakfast.type = BreakfastChoices.BREAKFAST_FIXED
        breakfast.service_area = BreakfastChoices.SERVICE_AREA_DINING_AREA
        breakfast.non_veg = False
        breakfast.rotational = False
        amenity.breakfast = breakfast

    return amenitities
