from cataloging_service.constants.model_choices import TvChoices, AcChoices, RoomAmenityChoices
from cataloging_service.models import RoomAmenity, AmenityTwinBed, AmenityIntercom, AmenityHotWater, AmenityTV, \
    AmenityHeater, AmenityAC
from core.property._property_amenities import STANDARD_CLOSE_TIME, STANDARD_OPEN_TIME


def create_room_amenities(property_launch, room, room_types):
    amenity = RoomAmenity()
    amenity.room = room
    room_type = [rt for rt in room_types if rt['id'] == room.room_type_id][0]
    room_type_detail = [rt for rt in property_launch['room_type_details'] if
                        rt['room_type'].lower() == room_type['type'].lower()][0]

    amenity.wardrobe = room_type_detail['is_wardrobe_available']
    amenity.luggage_shelf = room_type_detail['is_luggage_shelf_available']
    amenity.coffee_table = room_type_detail['is_coffee_table_available']
    amenity.smoking_room = room_type_detail['is_smoking_permitted']
    amenity.study_table_chair = room_type_detail['is_study_table_available']
    amenity.mini_fridge = room_type_detail['is_mini_fridge_available']
    amenity.kitchenette = room_type_detail['is_kitchenette_available']
    amenity.microwave = room_type_detail['is_microwave_oven_available']
    amenity.sofa_chair = room_type_detail['is_sofa_chair_available']
    amenity.dining_table = room_type_detail['is_dining_table_available']
    if property_launch['facilities']['is_moquito_repellant_available']:
        amenity.mosquito_repellent = RoomAmenityChoices.ON_REQUEST

    bed_type = room_type_detail['bed_type']
    if bed_type == 'king_bed':
        amenity.king_sized_beds = True
    elif bed_type == 'queen_bed':
        amenity.queen_sized_beds = True
    elif bed_type == 'single_bed':
        amenity.single_beds = True
    elif bed_type == 'joinable_twin_bed':
        bed = AmenityTwinBed()
        bed.joinable = True
        amenity.twin_bed = bed
    elif bed_type == 'non_joinable_twin_bed':
        bed = AmenityTwinBed()
        bed.joinable = False
        amenity.twin_bed = bed
    else:
        raise RuntimeError('Unknown bed type: {t}'.format(t=bed_type))

    if property_launch['facilities']['is_private_intercom_available']:
        intercom = AmenityIntercom()
        intercom.all_rooms_connected = False
        amenity.intercom = intercom

    if property_launch['facilities']['is_hot_water_available']:
        hot_water = AmenityHotWater()
        hot_water.central_geyser = False
        hot_water.room_geyser = False
        hot_water.from_time = STANDARD_OPEN_TIME
        hot_water.to_time = STANDARD_CLOSE_TIME
        amenity.hot_water = hot_water

    if property_launch['facilities']['is_tv_available']:
        tv = AmenityTV()
        tv.connection_type = TvChoices.CONNECTION_DTH
        tv.vendor = ''
        tv.tv_type = TvChoices.TV_CRT
        tv.size = ""
        amenity.tv = tv

    if property_launch['facilities']['is_room_heater_available']:
        heater = AmenityHeater()
        heater.availability = RoomAmenityChoices.ON_REQUEST
        heater.charges = 0
        amenity.heater = heater

    if property_launch['facilities']['is_ac_available']:
        ac = AmenityAC()
        ac.ac_type = AcChoices.AC_WINDOWS
        amenity.ac = ac

    # amenity.lock_type = self.read_enum(room_lock_row[1])
    # amenity.fan_type = self.read_enum(fan_row[1])
    # amenity.treebo_toiletries = self.read_boolean(toiletteries_row[0])
    # amenity.bucket_mug = self.read_enum(bucket_mug_row[1])
    # amenity.balcony = self.is_room_number_applicable_for_ammenity(room.room_number, balcony_row[0])
    # amenity.kitchenette_utensils = self.is_room_number_applicable_for_ammenity(room.room_number,
    #                                                                            kitchenette_utensils_row[0])
    #
    # amenity.locker_available = self.is_room_number_applicable_for_ammenity(room.room_number, locker_row[0])
    # amenity.other_furniture = self.is_room_number_applicable_for_ammenity(room.room_number,
    #                                                                       other_furniture_row[0])
    # amenity.smoke_alarm = self.is_room_number_applicable_for_ammenity(room.room_number, smoke_alarm_row[0])
    # amenity.bath_tub = self.is_room_number_applicable_for_ammenity(room.room_number, bath_tub_row[0])
    # amenity.shower_curtain = self.is_room_number_applicable_for_ammenity(room.room_number,
    #                                                                      shower_curtain_row[0])
    # amenity.windows = self.is_room_number_applicable_for_ammenity(room.room_number, windows_row[0])
    # amenity.shower_cabinets = self.is_room_number_applicable_for_ammenity(room.room_number,
    #                                                                       shower_cabinet_row[0])
    # amenity.living_room = self.is_room_number_applicable_for_ammenity(room.room_number, living_room_row[0])
    # amenity.stove_id = self.create_stove(stove_row).id
    return amenity
