from collections import defaultdict

from marshmallow import ValidationError

from cataloging_service.constants import constants
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.repositories import repo_provider

class HygieneShieldUpload:

    def upload_csv(self, hygiene_shield_details):
        error_dict = defaultdict(list)
        for line_number, hygiene_shield_detail in enumerate(hygiene_shield_details, start=2):
            try:
                self.validate_input(hygiene_shield_detail)
            except ValidationError as e:
                error_dict[line_number].append(str(e))

        if error_dict:
            return error_dict

        self.save_hygiene_shield_details(hygiene_shield_details)

    def validate_input(self, hygiene_shield_detail):
        self.validate_hygiene_shield(hygiene_shield_detail['hygiene_shield_name'])

    @staticmethod
    def validate_hygiene_shield(hygiene_shield_name):
        if hygiene_shield_name not in constants.SHIELD_DETAILS:
            raise ValidationError('Not a valid hygiene shield type')

    def save_hygiene_shield_details(self, hygiene_shield_details):
        properties = []
        shield_name ={}
        property_service = service_provider.property_service
        for hygiene_shield_detail in hygiene_shield_details:
            property_id = hygiene_shield_detail['property_id']
            properties.append(property_id)
            shield_name[property_id] =hygiene_shield_detail['hygiene_shield_name']
        properties = property_service.get_properties_in_bulk(properties)
        for property in properties:
            property.property_detail.hygiene_shield_name = shield_name[property.id]
        repo_provider.property_repository.persist_all(properties)
        repo_provider.property_repository.session().commit()
