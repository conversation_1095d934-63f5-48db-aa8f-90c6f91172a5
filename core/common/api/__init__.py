# -*- coding: utf-8 -*-
from __future__ import division

import logging

import time
from flask import make_response, request
from flask.views import MethodView

from core.common.api.api_response import APIResponse, response
from core.common.api.exception_handler import APIExceptionHandler

logger = logging.getLogger(__name__)


class BaseAPI(MethodView):
    exception_handler = APIExceptionHandler()

    def dispatch_request(self, *args, **kwargs):

        stime = time.time()
        logger.info("Received API request: {request.url} {request.method} DATA:{request.data}".format(request=request))
        try:
            response = super(BaseAPI, self).dispatch_request(*args, **kwargs)
        except Exception as e:
            handler = self.exception_handler[e]
            if not handler:
                raise
            response = handler(e, context={})

        response = self.make_response(response)
        etime = time.time()
        logger.info("API: {self}, response time: {time:.2f} seconds with status code: "
                    "{response.status_code}".format(response=response, self=self, time=etime - stime))
        return response

    def make_response(self, resp):
        if resp is response:
            return resp.make()
        return make_response(resp)

    def __str__(self):
        return str(self.__class__.__name__)
