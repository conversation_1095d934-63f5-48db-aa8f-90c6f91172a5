import json

from flask import g, Response
from werkzeug.local import LocalProxy

from core.common.api.exception_handler import _fully_qualified_name


class APIResponse:

    def __init__(self):
        self.data = {}
        self._errors = []
        self.meta = {}
        self.status_code = 200

    def add_error(self, error_title, error_code='', error_detail='', error_type='', http_status_code=400):
        self.status_code = http_status_code
        self._errors.append({
            'title': error_title,
            'code': error_code,
            'detail': error_detail,
            'type': error_type,
        })

    def add_error_from_exception(self, exc: Exception, detail=None):
        kwargs = getattr(exc, 'kwargs', {})
        errors = kwargs.get('errors') or []
        if isinstance(errors, dict):
            errors = ['{k}: {v}'.format(k=k, v=v) for k, v in errors.items()]
        message = getattr(exc, 'message', '')
        detail = detail or getattr(exc, 'detail', '')
        code = getattr(exc, 'code', '')

        if not message:
            message = str(exc)

        if not detail:
            detail = getattr(exc, 'kwargs', {}).get('detail')
            if not detail:
                detail = message

        if not errors:
            errors = [message]

        [self.add_error(
            error_title=error,
            error_code=code,
            error_detail=detail,
            error_type=_fully_qualified_name(exc),
            http_status_code=500
        ) for error in errors]

    @property
    def payload(self):
        return {
            'data': self.data,
            'errors': self._errors,
            'meta': self.meta
        }

    def make(self):
        return Response(response=json.dumps(self.payload), status=self.status_code, content_type='application/json')


def _get_or_create_response() -> APIResponse:
    response = getattr(g, 'treebo_api_response', None)
    if response is None:
        response = APIResponse()
        setattr(g, 'treebo_api_response', response)
    return response


response = LocalProxy(_get_or_create_response)  # LocalProxy creates a lazy property of a callable
