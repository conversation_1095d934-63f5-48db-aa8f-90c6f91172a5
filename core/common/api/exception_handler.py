import inspect
import logging
from collections import defaultdict


logger = logging.getLogger(__name__)


class APIExceptionHandler:

    def __init__(self):
        self.registry = defaultdict()

    def register(self, exc):

        def wrapper(func):
            try:
                _exc = iter(exc)
            except TypeError:
                _exc = [exc]

            for ex in _exc:
                self.registry[_fully_qualified_name(ex)] = func
            return func

        return wrapper

    def __getitem__(self, exc: Exception):
        handler_name = _fully_qualified_name(exc)
        if not handler_name in self.registry:
            return default_handler
        return self.registry[handler_name]


def default_handler(exc, context=None):
    from core.common.api.api_response import response
    logger.exception("{c} failed due to {e}".format(c=context, e=exc))
    response.add_error_from_exception(exc)
    return response


def _fully_qualified_name(obj):
    if not inspect.isclass(obj):
        _obj = obj.__class__
    else:
        _obj = obj
    return "{m}.{kls}".format(m=_obj.__module__, kls=_obj.__name__)
