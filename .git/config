[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
	ignorecase = true
	precomposeunicode = true
[remote "origin"]
	url = **************:treebo-noss/cataloging-service.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
[branch "feat/new-onboarding"]
	remote = origin
	merge = refs/heads/feat/new-onboarding
	vscode-merge-base = origin/feat/new-onboarding
[branch "feat/PROM-18630-upgrade-python-version"]
	remote = origin
	merge = refs/heads/feat/PROM-18630-upgrade-python-version
