from cataloging_service.client.service_registry_client import ServiceRegistryClient
import logging
import traceback

import requests
from flask import current_app
from treebo_commons.request_tracing.context import get_current_tenant_id
from cataloging_service.exceptions import ExternalClientException

logger = logging.getLogger(__name__)


class AuthZClient:
    @staticmethod
    def assign_role(auth_id, resource_id, role, application_id):
        try:
            authz_payload = {
                "role": role,
                "resource_id": resource_id,
                "resource_type": "hotel",
            }
            if not resource_id:
                del authz_payload["resource_id"]
            headers = {
                "X-Application-Id": application_id,
                "Content-Type": "application/json",
                "X-Tenant-Id": get_current_tenant_id(),
            }
            url = ServiceRegistryClient.get_authz_service_url() + "/api/v1/users/{0}/roles/".format(
                auth_id
            )

            response = requests.post(url, json=authz_payload, headers=headers)
            if response.status_code != 201:
                logger.error(
                    "Error while assigning role: {text} with status {status_code} \
                        with hotel_id {resource_id}".format(
                        text=response.text,
                        status_code=response.status_code,
                        resource_id=resource_id,
                    )
                )
                raise ExternalClientException(
                    "Error occurred while assigning role: " + response.text + " for " + resource_id,
                    response.status_code,
                )
            return True

        except Exception as e:
            logger.exception(e)
            traceback.print_exc()
            raise
