import logging

import requests
from treebo_commons.request_tracing.context import get_current_tenant_id, get_current_hotel_id

from cataloging_service.client.dtos.role_privilege_dto import RolePrivilegesDTO
from cataloging_service.client.service_registry_client import ServiceRegistryClient
from cataloging_service.exceptions import ExternalClientException

logger = logging.getLogger(__name__)


class RoleManagerClient:

    @staticmethod
    def get_privilege_by_role_name(role_name):
        headers = {
            "Content-Type": "application/json",
            "X-Tenant-Id": get_current_tenant_id(),
            "X-Hotel-Id": get_current_hotel_id(),
        }

        url = ServiceRegistryClient.get_role_manager_url() + "/role-manager/roles/{role_name}/privileges".format(
            role_name=role_name)
        logger.info("Role manager url: {url}".format(url=url))
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            err_msg = "couldn't fetch role privilege for hotel {0}".format(get_current_hotel_id())
            logger.error(err_msg)
            raise ExternalClientException(err_msg, response.status_code)
        return [RolePrivilegesDTO.from_json(r) for r in response.json()["data"]]
