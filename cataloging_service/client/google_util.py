import os

from oauth2client.service_account import ServiceAccountCredentials

from cataloging_service.constants import constants


class GoogleUtil:
    KEY_FILES = ['key/cataloging-service-cs1.json', 'key/cataloging-service-cs2.json', 'key/cataloging-service-cs3.json',
                 'key/cataloging-service-cs4.json']
    @staticmethod
    def get_credentials(credential_number):
        project_root = os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        scopes = [constants.GOOGLE_DRIVE_ALL_SCOPE, constants.GOOGLE_SHEET_READ_SCOPE]

        mod_index = credential_number % len(GoogleUtil.KEY_FILES)
        return ServiceAccountCredentials.from_json_keyfile_name(
            os.path.join(project_root, GoogleUtil.KEY_FILES[mod_index]), scopes)
