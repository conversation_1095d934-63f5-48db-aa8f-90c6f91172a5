from cataloging_service.client.service_registry_client import ServiceRegistryClient
import logging

import requests
from flask import current_app as app

from cataloging_service.constants import constants, error_codes
from cataloging_service.exceptions import CatalogingServiceException

logger = logging.getLogger(__name__)


class EmailClient:
    def send_email(self, subject, recipients, body_text, body_html, bcc_recipients=None,
                   reply_mail=constants.DEFAULT_EMAIL_SENDER,
                   sender=constants.DEFAULT_EMAIL_SENDER):

        if not recipients:
            logger.error("Recipients cannot be an empty list")
            raise CatalogingServiceException(error_codes.INVALID_MESSAGE)

        if not subject or not (body_text or body_html):
            logger.error("Email subject and body are mandatory")
            raise CatalogingServiceException(error_codes.INVALID_MESSAGE)

        subject = "[%s] %s" % (app.config[constants.CONFIG_ENVIRONMENT], subject)
        recipients = EmailClient.get_filtered_recipients(recipients)
        bcc_recipients = EmailClient.get_filtered_recipients(bcc_recipients)

        receivers = {"to": recipients, "cc": [], "bcc": []}
        if bcc_recipients:
            receivers["bcc"] = bcc_recipients

        message = {
            "subject": subject,
            "sender": sender,
            "receivers": receivers,
            "consumer": constants.CATALOGING_SERVICE_EMAIL_CONSUMER,
            "reply_to": reply_mail,
            "attachments": [],
        }

        if body_text:
            message["body_text"] = body_text

        if body_html:
            message["body_html"] = body_html

        payload = {"data": message}
        logger.info("Sending Email %s to %s, %s" % (subject, recipients, bcc_recipients))

        try:
            url = ServiceRegistryClient.get_notification_service_url() + "/v1/notification/email/"
            response = requests.post(url, json=payload)
        except Exception as exception:
            logger.error("Exception wile sending mail. %s" % exception)
            return False

        logger.info("Status code %s" % response.status_code)
        logger.debug("Response %s" % response.content)

        return response.status_code == constants.STATUS_OK

    @staticmethod
    def get_filtered_recipients(recipients):
        if not recipients:
            return recipients

        recipients = list(set(recipients))
        if app.config[constants.CONFIG_ENVIRONMENT] == constants.PRODUCTION_ENVIRONMENT:
            return recipients
        recipients = list(filter(lambda recipient: recipient in app.config[constants.CONFIG_DEV_EMAILS], recipients))
        if len(recipients) == 0:
            recipients = list(app.config[constants.CONFIG_DEV_EMAILS])
        return recipients
