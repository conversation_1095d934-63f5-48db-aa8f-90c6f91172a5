import logging
import requests
from treebo_commons.utils import dateutils
from treebo_commons.request_tracing.context import get_current_tenant_id
from cataloging_service.constants import constants
from cataloging_service.exceptions import ExternalClientException
from cataloging_service.client.service_registry_client import ServiceRegistryClient


logger = logging.getLogger(__name__)


def get_headers(property_id):
    return {
        "Content-Type": "application/json",
        "X-Tenant-Id": get_current_tenant_id(),
        "X-Hotel-Id": property_id,
        "X-User-Type": "backend-system"
    }


class RateManagerClient:

    @staticmethod
    def create_package(property_id, package_name, inclusions):
        data = dict(property_id=property_id, package_name=package_name, inclusions=inclusions)
        headers = get_headers(property_id)
        url = ServiceRegistryClient.get_rate_manager_service_url() + "/rate-manager/v1/packages"
        response = requests.post(url, json=data, headers=headers)

        if response.status_code not in [200, 201]:
            logger.error(
                "Error while creating package: {text} with status {status_code} \
                for property {property} and package {package}".format(
                    text=response.text,
                    status_code=str(response.status_code),
                    property=property_id,
                    package=package_name,
                )
            )
            err_msg = (
                "Error occurred while creating package {0}, for property {1}. Error - {2}".format(
                    package_name, property_id, response.text
                )
            )
            raise ExternalClientException(err_msg, response.status_code)
        return response.json()["data"]["package"]

    @staticmethod
    def get_all_rate_plans(property_id):
        headers = get_headers(property_id)
        url = (
            ServiceRegistryClient.get_rate_manager_service_url()
            + "/rate-manager/v1/rate-plans?property_id="
            + property_id
        )
        response = requests.get(url, headers=headers)

        if response.status_code != 200:
            err_msg = "couldn't fetch rate plans for hotel {0}".format(property_id)
            logger.error(err_msg)
            raise ExternalClientException(err_msg, response.status_code)
        return response.json()["data"]["rate_plans"]

    @staticmethod
    def create_rateplan(
        name,
        short_code,
        description,
        package_id,
        child_policy,
        payment_policy,
        cancellation_policies,
        property_id,
        restrictions,
        room_type_occupancy_mappings,
    ):
        policies = dict(
            cancellation_policies=cancellation_policies,
            child_policy=child_policy,
            payment_policy=payment_policy,
        )
        data = dict(
            name=name,
            short_code=short_code,
            description=description,
            package_id=package_id,
            policies=policies,
            property_id=property_id,
            room_type_occupancy_mappings=room_type_occupancy_mappings,
        )
        if restrictions:
            data["restrictions"] = restrictions

        headers = get_headers(property_id)
        url = ServiceRegistryClient.get_rate_manager_service_url() + "/rate-manager/v1/rate-plans"
        response = requests.post(url, json=data, headers=headers)

        if response.status_code not in [200, 201]:
            logger.error(
                "Error while creating rate plan : {text} with status {status_code} \
                for property {property} rate plan {rate_plan}".format(
                    text=response.text,
                    status_code=str(response.status_code),
                    property=property_id,
                    rate_plan=name,
                )
            )
            raise ExternalClientException(
                "Error occurred while creating rate {0}, for property {1}. Error - {2}".format(
                    name, property_id, response.text
                ),
                response.status_code,
            )
        return response.json()["data"]["rate_plan"]

    @staticmethod
    def update_linkage_for_base_rooms_for_old_property_migration(property_id, room_code, adult_count, price):
        headers = get_headers(property_id)

        url = (
                ServiceRegistryClient.get_rate_manager_service_url() + "/rate-manager/v1/room-base-rates"
        )

        room_type_with_occupancy = dict(
            room_type_id=room_code,
            adult_count=adult_count
        )

        linkage_equation = dict(
            percentage_change=constants.LINKAGE_PERCENTAGE_CHANGE_NUMBER,
            fixed_change=price
        )

        other_room_category_linkage_detail = dict(
            room_type_id=room_code,
            adult_count=1
        )

        linkage = dict(
            linkage_equation=linkage_equation,
            linkage_type="other_room_category",
            linkage_category="explicit",
            other_room_category_linkage_detail=other_room_category_linkage_detail
        )
        start_date = dateutils.date_to_ymd_str(dateutils.current_date())
        end_date = dateutils.date_to_ymd_str(dateutils.add(dateutils.current_date(), months=24))
        data = dict(
            start_date=start_date,
            end_date=end_date,
            property_id=property_id,
            room_types_with_occupancy=[room_type_with_occupancy],
            linkage=linkage
        )
        response = requests.post(url, json=data, headers=headers)

        if response.status_code not in [200, 201]:
            logger.error(
                "Error while updating linkage : {text} with status {status_code} \
                for property {property}".format(
                    text=response.text,
                    status_code=str(response.status_code),
                    property=property_id
                )
            )
