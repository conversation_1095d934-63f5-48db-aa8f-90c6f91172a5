import logging

from apiclient import discovery
from googleapiclient.http import MediaIoBaseUpload
from httplib2 import Http

from cataloging_service.client.google_util import GoogleUtil
from cataloging_service.constants import error_codes, constants
from cataloging_service.exceptions import CatalogingServiceException

logger = logging.getLogger(__name__)


class GoogleDriveFileResource:
    def __init__(self, file_id=None, link=None, name=None):
        self.file_id = file_id
        self.link = link
        self.name = name


class GoogleDriveClient:
    _service = None

    FILE_NAME = 'name'
    FILE_MIME_TYPE = 'mimeType'
    FILE_PARENTS = 'parents'
    MIME_TYPE_FOLDER = 'application/vnd.google-apps.folder'
    API_NAME = 'drive'
    API_VERSION = 'v3'
    FILE_ID = 'id'
    WEB_VIEW_LINK = 'webViewLink'

    def __init__(self):
        self.current_credential_number = 0
        self.initialize(self.current_credential_number)

    def create_folder(self, parent_file_id, folder_name):
        logger.info('Creating folder %s under folder id %s' % (folder_name, parent_file_id))

        file_metadata = dict()
        file_metadata[self.FILE_NAME] = folder_name
        file_metadata[self.FILE_MIME_TYPE] = self.MIME_TYPE_FOLDER
        file_metadata[self.FILE_PARENTS] = [parent_file_id]

        file = None
        for retry_count in range(constants.GOOGLE_CLIENT_MAX_RETRY):
            try:
                file = self._service.files().create(body=file_metadata, fields='id, webViewLink').execute()
                break
            except Exception as exception:
                logger.error('Error while creating folder. %s' % repr(exception))
                if retry_count + 1 == constants.GOOGLE_CLIENT_MAX_RETRY:
                    raise exception
                self.current_credential_number += 1
                self.initialize(self.current_credential_number)

        logger.info('Folder creation response is: %s' % file)

        return GoogleDriveFileResource(file[self.FILE_ID], file[self.WEB_VIEW_LINK], folder_name)

    def upload_file(self, stream, mime_type, parent_folder_id, file_name):
        logger.info('Uploading file %s under folder id %s with mimetype %s' % (file_name, parent_folder_id, mime_type))

        file_metadata = dict()
        file_metadata[self.FILE_NAME] = file_name
        file_metadata[self.FILE_PARENTS] = [parent_folder_id]

        media = MediaIoBaseUpload(stream, mime_type, resumable=True)
        response = None
        for retry_count in range(constants.GOOGLE_CLIENT_MAX_RETRY):
            try:
                request = self._service.files().create(media_body=media, body=file_metadata, fields=self.FILE_ID)
                while response is None:
                    status, response = request.next_chunk()
                    if not (response or status):
                        logger.error('Error while uploading file. Response is %s. Status is %s' % (response, status))
                        raise CatalogingServiceException(error_codes.FAILED_TO_UPLOAD_FILE)
                    else:
                        logger.info('Chunk uploaded successfully. Moving on to the next file chunk.')
                break
            except Exception as exception:
                logger.error('Error while creating folder. %s' % exception)
                if retry_count + 1 == constants.GOOGLE_CLIENT_MAX_RETRY:
                    raise exception
                self.current_credential_number += 1
                self.initialize(self.current_credential_number)

        logger.info('File upload successful. Response is %s' % response)
        return GoogleDriveFileResource(file_id=response[self.FILE_ID], name=file_name)

    def delete_file(self, file_id):
        for retry_count in range(constants.GOOGLE_CLIENT_MAX_RETRY):
            try:
                logger.info('Deleting file with file_id %s' % file_id)
                self._service.files().delete(fileId=file_id).execute()
                logger.info('Deletion complete')
                break
            except Exception as exception:
                logger.error('Error while creating folder. %s' % repr(exception))
                if retry_count + 1 == constants.GOOGLE_CLIENT_MAX_RETRY:
                    raise exception
                self.current_credential_number += 1
                self.initialize(self.current_credential_number)

    def initialize(self, credential_number):
        logger.info('Initializing Google Drive Client')
        credentials = GoogleUtil.get_credentials(credential_number)
        http_auth = credentials.authorize(Http())
        try:
            self._service = discovery.build(self.API_NAME, self.API_VERSION, http=http_auth)
        except Exception as exception:
            logger.error('Could not instantiate google drive client %s' % repr(exception))

        logger.info('Service object successfully created')
