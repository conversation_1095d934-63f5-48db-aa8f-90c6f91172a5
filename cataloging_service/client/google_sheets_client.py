import logging

from googleapiclient import discovery
from httplib2 import Http

from cataloging_service.client.google_util import GoogleUtil
from cataloging_service.constants import constants

logger = logging.getLogger(__name__)


class GoogleSheetsClient:
    API_NAME = 'sheets'
    API_VERSION = 'v4'

    DISCOVERY_URL = 'https://sheets.googleapis.com/$discovery/rest?version=v4'

    def __init__(self):
        self.current_credential_number = 0
        self.initialize(self.current_credential_number)

    def read_rows(self, sheet_id, sheet_name, first_row, last_row):
        range_name = '%s!%s:%s' % (sheet_name, first_row, last_row)
        result = None
        for retry_count in range(constants.GOOGLE_CLIENT_MAX_RETRY):
            try:
                result = self._service.spreadsheets().values().get(spreadsheetId=sheet_id, range=range_name).execute()
                break
            except Exception as exception:
                logger.error('Error while reading sheets: %s' % repr(exception))
                if retry_count + 1 == constants.GOOGLE_CLIENT_MAX_RETRY:
                    raise exception
                self.current_credential_number += 1
                self.initialize(self.current_credential_number)

        if 'values' in result:
            return result['values']

        return []

    def read_row(self, sheet_id, sheet_name, row_number, min_columns=None, trim_contents=True):
        range_name = '%s!%s:%s' % (sheet_name, row_number, row_number)
        result = None

        for retry_count in range(constants.GOOGLE_CLIENT_MAX_RETRY):
            try:
                result = self._service.spreadsheets().values().get(spreadsheetId=sheet_id, range=range_name).execute()
                break
            except Exception as exception:
                logger.error('Error while reading sheets: %s' % repr(exception))
                if retry_count + 1 == constants.GOOGLE_CLIENT_MAX_RETRY:
                    raise exception
                self.current_credential_number += 1
                self.initialize(self.current_credential_number)

        if 'values' in result:
            row = result['values'][0]
            if min_columns and len(row) < min_columns:
                difference = min_columns - len(row)
                row.extend(difference * [''])

            if trim_contents:
                row = [row_item.strip() for row_item in row]
            return row

        return []

    def initialize(self, credential_number):
        logger.info('Initializing Sheets Client')
        credentials = GoogleUtil.get_credentials(credential_number)
        http_auth = credentials.authorize(Http())

        try:
            self._service = discovery.build(self.API_NAME, self.API_VERSION, http=http_auth,
                                            discoveryServiceUrl=self.DISCOVERY_URL)
        except Exception as exception:
            logger.error('Could not instantiate google sheets client %s' % repr(exception))
