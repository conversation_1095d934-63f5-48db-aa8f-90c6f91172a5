import json
import logging
import requests

from cataloging_service.client.service_registry_client import ServiceRegistryClient


logger = logging.getLogger(__name__)


class ProwlClient:
    def get_hotel_users(self, name=None, cs_id=None):
        users = []
        if not name and not cs_id:
            return users

        request_url = ServiceRegistryClient.get_prowl_service_url() + "/prowl/rest/v3/hotel-users"
        if name:
            request_url = request_url + '?hotel_name=' + name
        elif cs_id:
            request_url = request_url + '?cs_id=' + cs_id

        try:
            response = requests.get(request_url, timeout=10)
        except Exception as exception:
            logger.error('Error while fetching hotel users. %s' % exception)
            return []

        if response.status_code != 200:
            logger.error('Got status code %s for GET %s' % (response.status_code, request_url))
            return []

        response_dict = json.loads(response.content.decode('utf-8'))

        if response_dict['status'] != 'success':
            logger.error('Got status %s for GET %s' % (response_dict['status'], request_url))
            return []

        logger.info('Successfully fetched QAM Info')
        return response_dict['data']['users']
