class ExpenseItemDTO:
    def __init__(self, addon_code, description, expense_item_id, name, short_name, sku_category_id, sku_id, linked=None):
        self.addon_code = addon_code
        self.description = description
        self.expense_item_id = expense_item_id
        self.linked = linked
        self.name = name
        self.short_name = short_name
        self.sku_category_id = sku_category_id
        self.sku_id = sku_id

    @staticmethod
    def from_json(data):
        return ExpenseItemDTO(addon_code=data.get('addon_code'), description=data.get('description'),
                              expense_item_id=data.get('expense_item_id'),
                              name=data.get('name'), short_name=data.get('short_name'),
                              sku_category_id=data.get('sku_category_id'), linked=data.get('linked'), sku_id=data.get('sku_id'))