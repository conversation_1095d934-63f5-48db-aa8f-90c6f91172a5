from cataloging_service.client.service_registry_client import ServiceRegistryClient
from cataloging_service.client.core.base_client import BaseExternalClient
from flask import current_app

from cataloging_service.client.dtos.crs_dtos import ExpenseItemDTO


class CRSClient(BaseExternalClient):
    page_map = {
        "get_all_expense_items": dict(
            type=BaseExternalClient.CallTypes.GET,
            url_regex="/v1/expense-items?include_linked={include_linked}",
        )
    }

    def get_domain(self):
        return ServiceRegistryClient.get_crs_service_url()

    def get_all_expense_items(self, include_linked: bool):
        page_name = "get_all_expense_items"
        url_params = dict(include_linked=include_linked)
        response = self.make_call(page_name, url_parameters=url_params)
        if not response.is_success():
            raise Exception(
                "CRS API Error. Status Code: {0}, Errors: {1}".format(
                    response.response_code, response.errors
                )
            )
        return [ExpenseItemDTO.from_json(obj) for obj in response.data]
