import logging
import re

import boto3

from cataloging_service.constants.constants import S3ACL

logger = logging.getLogger(__name__)


class AmazonS3Client(object):
    def upload_files(self, bucket_name, file_list, s3_path):
        logger.info('Details are %s, %s, %s' % (bucket_name, file_list, s3_path))
        client = boto3.client("s3", verify=False)

        uploaded_files = []

        for folder_path, file_name in file_list:

            key = s3_path + '/' + self.refine_file_name_for_s3(file_name)

            try:
                client.upload_file(folder_path + '/' + file_name, bucket_name, key,
                                   ExtraArgs={'ACL': S3ACL.BUCKET_OWNER_FULL_CONTROL.value})
                uploaded_files.append("/" + key)
            except Exception as e:
                logger.exception('Error while uploading file %s, %s', folder_path, file_name)

        return uploaded_files

    def refine_file_name_for_s3(self, name):
        return re.sub(r'([\s\-\(\)]+)', '_', name)
