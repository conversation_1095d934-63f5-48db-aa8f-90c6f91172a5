from cataloging_service.client.service_registry_client import ServiceRegistryClient
import logging
import traceback

import requests
from flask import current_app
from treebo_commons.request_tracing.context import get_current_tenant_id
from cataloging_service.exceptions import ExternalClientException

logger = logging.getLogger(__name__)


class AuthNClient:
    @staticmethod
    def create_user(first_name, last_name, email, pin):
        try:
            data = dict(
                first_name=first_name,
                last_name=last_name,
                password=pin,
                app_type="web",
                phone_number=None,
                email=email,
                redirect_url=".",
            )
            headers = {"Content-Type": "application/json", "X-Tenant-Id": get_current_tenant_id()}

            url = ServiceRegistryClient.get_authn_service_url() + "/treeboauth/profile/v1/register"
            response = requests.post(url, json=data, headers=headers)

            if response.status_code != 201:
                logger.error(
                    "Error while creating user: {text} with status {status_code} for email {email}".format(
                        text=response.text, status_code=response.status_code, email=email
                    )
                )
                raise ExternalClientException(
                    "Error occurred while creating user: " + response.text + " for " + email,
                    response.status_code,
                )
            return response.json()["data"]["auth_id"]

        except Exception as e:
            logger.exception(e)
            traceback.print_exc()
            raise
