from cataloging_service.client.amazon_s3_client import AmazonS3Client
from cataloging_service.client.email_client import EmailClient
from cataloging_service.client.google_drive_client import GoogleDriveClient
from cataloging_service.client.google_sheets_client import GoogleSheetsClient
from cataloging_service.client.prowl_client import ProwlClient


class ClientProvider:
    def __init__(self):
        self.google_drive_client = GoogleDriveClient()
        self.email_client = EmailClient()
        self.google_sheets_client = GoogleSheetsClient()
        self.prowl_client = ProwlClient()
        self.amazon_s3_client = AmazonS3Client()


client_provider = ClientProvider()
