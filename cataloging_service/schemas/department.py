"""
Simplified Pydantic schemas for department/profit center architecture
Provides request/response validation for the simplified 3-table approach
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, ConfigDict
from pydantic.types import PositiveInt

from cataloging_service.common.schema_registry import swag_schema
from cataloging_service.schemas.common import BasePydanticAPISchema


# ============================================================================
# Department Template Schemas
# ============================================================================

@swag_schema
class DepartmentTemplateCreateSchema(BasePydanticAPISchema):
    """Schema for creating department templates"""

    brand_ids: List[PositiveInt] = Field(..., description="List of Brand IDs this template is associated with")
    code: str = Field(..., min_length=2, max_length=50, description="Unique department code")
    name: str = Field(..., min_length=1, max_length=255, description="Department name")
    financial_code: Optional[str] = Field(None, max_length=20, description="Financial/GL code")
    parent_code: Optional[str] = Field(None, max_length=50, description="Parent department code")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    auto_create_on_property_launch: bool = Field(True, description="Auto-create on property launch")
    is_active: bool = Field(True, description="Whether template is active")


@swag_schema
class DepartmentTemplateUpdateSchema(BasePydanticAPISchema):
    """Schema for updating department templates (partial updates)"""

    brand_ids: Optional[List[PositiveInt]] = Field(None, description="List of Brand IDs this template is associated with")
    code: Optional[str] = Field(None, min_length=2, max_length=50, description="Unique department code")
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Department name")
    financial_code: Optional[str] = Field(None, max_length=20, description="Financial/GL code")
    parent_code: Optional[str] = Field(None, max_length=50, description="Parent department code")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    auto_create_on_property_launch: Optional[bool] = Field(None, description="Auto-create on property launch")
    is_active: Optional[bool] = Field(None, description="Whether template is active")


@swag_schema
class DepartmentTemplateResponseSchema(BasePydanticAPISchema):
    """Schema for department template responses"""
    
    id: PositiveInt
    brand_ids: List[PositiveInt]
    code: str
    name: str
    financial_code: Optional[str] = None
    parent_code: Optional[str] = None
    description: Optional[str] = None
    auto_create_on_property_launch: bool
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Profit Center Template Schemas
# ============================================================================

@swag_schema
class ProfitCenterTemplateCreateSchema(BasePydanticAPISchema):
    """Schema for creating profit center templates"""
    
    brand_ids: List[PositiveInt] = Field(..., description="List of Brand IDs this template is associated with")
    code: str = Field(..., min_length=2, max_length=50, description="Unique profit center code")
    name: str = Field(..., min_length=1, max_length=255, description="Profit center name")
    department_template_code: str = Field(..., max_length=50, description="Associated department template code")
    system_interface: Optional[str] = Field(None, max_length=100, description="POS system interface")
    description: Optional[str] = Field(None, max_length=1000, description="Profit center description")
    auto_create_on_property_launch: bool = Field(True, description="Auto-create on property launch")
    is_active: bool = Field(True, description="Whether template is active")


@swag_schema
class ProfitCenterTemplateResponseSchema(BasePydanticAPISchema):
    """Schema for profit center template responses"""
    
    id: PositiveInt
    brand_ids: List[PositiveInt]
    code: str
    name: str
    department_template_code: str
    system_interface: Optional[str] = None
    description: Optional[str] = None
    auto_create_on_property_launch: bool
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Department Schemas (Property Level)
# ============================================================================

@swag_schema
class DepartmentCreateSchema(BasePydanticAPISchema):
    """Schema for creating property departments"""
    
    property_id: str = Field(..., min_length=1, max_length=50, description="Property ID")
    code: str = Field(..., min_length=2, max_length=50, description="Unique department code within property")
    name: str = Field(..., min_length=1, max_length=255, description="Department name")
    template_code: Optional[str] = Field(None, max_length=50, description="Source template code")
    financial_code: Optional[str] = Field(None, max_length=20, description="Financial/GL code")
    parent_id: Optional[PositiveInt] = Field(None, description="Parent department ID")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    is_custom: bool = Field(False, description="Whether department is custom (not from template)")
    is_active: bool = Field(True, description="Whether department is active")


@swag_schema
class DepartmentResponseSchema(BasePydanticAPISchema):
    """Schema for department responses"""
    
    id: PositiveInt
    property_id: str
    code: str
    name: str
    template_code: Optional[str] = None
    financial_code: Optional[str] = None
    parent_id: Optional[PositiveInt] = None
    description: Optional[str] = None
    is_custom: bool
    is_active: bool
    created_at: datetime
    modified_at: datetime


# ============================================================================
# Enhanced SKU Schemas
# ============================================================================

@swag_schema
class SkuUpdateSchema(BasePydanticAPISchema):
    """Schema for updating SKU with department/profit center template references"""
    
    default_department_template_code: Optional[str] = Field(None, max_length=50, description="Default department template code")
    profit_center_template_code: Optional[str] = Field(None, max_length=50, description="Profit center template code")
    auto_create_seller_sku: Optional[bool] = Field(None, description="Auto-create seller SKU from template")


@swag_schema
class PropertySkuUpdateSchema(BasePydanticAPISchema):
    """Schema for updating property SKU with department assignment"""
    
    department_id: Optional[PositiveInt] = Field(None, description="Department ID for this property SKU")


@swag_schema
class SellerUpdateSchema(BasePydanticAPISchema):
    """Schema for updating seller with profit center enhancements"""
    
    department_id: Optional[PositiveInt] = Field(None, description="Department ID")
    created_from_template_code: Optional[str] = Field(None, max_length=50, description="Source template code")
    system_interface: Optional[str] = Field(None, max_length=100, description="POS system interface")
    is_auto_created: Optional[bool] = Field(None, description="Whether auto-created from template")


@swag_schema
class SellerSkuUpdateSchema(BasePydanticAPISchema):
    """Schema for updating seller SKU with department assignment"""
    
    department_id: Optional[PositiveInt] = Field(None, description="Department ID (from SKU template, not seller)")


# ============================================================================
# Response Schemas for Enhanced Models
# ============================================================================

@swag_schema
class EnhancedSkuResponseSchema(BasePydanticAPISchema):
    """Response schema for SKU with department/profit center template references"""
    
    id: PositiveInt
    code: str
    name: str
    default_department_template_code: Optional[str] = None
    profit_center_template_code: Optional[str] = None
    auto_create_seller_sku: bool


@swag_schema
class EnhancedPropertySkuResponseSchema(BasePydanticAPISchema):
    """Response schema for property SKU with department assignment"""
    
    id: PositiveInt
    sku_id: PositiveInt
    property_id: str
    department_id: Optional[PositiveInt] = None


@swag_schema
class EnhancedSellerResponseSchema(BasePydanticAPISchema):
    """Response schema for seller with profit center enhancements"""
    
    id: PositiveInt
    name: str
    property_id: str
    department_id: Optional[PositiveInt] = None
    created_from_template_code: Optional[str] = None
    system_interface: Optional[str] = None
    is_auto_created: bool


@swag_schema
class EnhancedSellerSkuResponseSchema(BasePydanticAPISchema):
    """Response schema for seller SKU with department assignment"""
    
    id: PositiveInt
    seller_id: PositiveInt
    sku_id: PositiveInt
    department_id: Optional[PositiveInt] = None


# ============================================================================
# Bulk Operation Schemas
# ============================================================================

@swag_schema
class BulkDepartmentCreateSchema(BasePydanticAPISchema):
    """Schema for bulk creating departments from templates"""
    
    property_id: str = Field(..., description="Property ID")
    template_codes: list[str] = Field(..., description="List of department template codes to create")
    auto_create_profit_centers: bool = Field(True, description="Also create associated profit centers")


@swag_schema
class BulkOperationResponseSchema(BasePydanticAPISchema):
    """Response schema for bulk operations"""

    success_count: int = Field(..., description="Number of successful operations")
    error_count: int = Field(..., description="Number of failed operations")
    errors: list[Dict[str, Any]] = Field(default_factory=list, description="List of errors encountered")
    created_ids: list[PositiveInt] = Field(default_factory=list, description="List of created entity IDs")


# ============================================================================
# Query Parameter Schemas
# ============================================================================

@swag_schema
class DepartmentTemplateQuerySchema(BasePydanticAPISchema):
    """Query parameters for department template endpoints"""

    brand_id: PositiveInt = Field(..., description="Brand ID to filter by")
    active_only: bool = Field(True, description="Filter only active templates")


@swag_schema
class ProfitCenterTemplateQuerySchema(BasePydanticAPISchema):
    """Query parameters for profit center template endpoints"""

    brand_id: PositiveInt = Field(..., description="Brand ID to filter by")
    department_template_code: Optional[str] = Field(None, description="Department template code to filter by")
    active_only: bool = Field(True, description="Filter only active templates")


@swag_schema
class PropertyDepartmentQuerySchema(BasePydanticAPISchema):
    """Query parameters for property department endpoints"""

    active_only: bool = Field(True, description="Filter only active departments")
    root_only: bool = Field(False, description="Filter only root departments (no parent)")
