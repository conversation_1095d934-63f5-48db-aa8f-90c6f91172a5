"""
Property onboarding schemas for request/response validation
"""

from datetime import datetime
from typing import List, Dict, Any
from pydantic import Field, PositiveInt, NonNegativeInt

from cataloging_service.common.schema_registry import swag_schema
from cataloging_service.schemas.common import BasePydanticAPISchema


@swag_schema
class PropertyOnboardingRequestSchema(BasePydanticAPISchema):
    """Schema for property onboarding request - simplified and template-driven"""

    property_id: str = Field(..., description="Property ID (set from URL parameter)")
    brand_id: PositiveInt = Field(..., description="Brand ID for template resolution")
    auto_create_departments: bool = Field(
        True, description="Auto-create departments from templates where auto_create_on_property_launch=true"
    )
    auto_create_profit_centers: bool = Field(
        True, description="Auto-create profit centers from templates where auto_create_on_property_launch=true"
    )
    auto_create_property_skus: bool = Field(
        True, description="Auto-create property SKUs using existing property SKU creation logic"
    )
    auto_create_seller_skus: bool = Field(
        True, description="Auto-create seller SKUs for all created sellers/profit centers"
    )
    custom_config: Dict[str, Any] = Field(
        default_factory=dict, description="Custom configuration for template filtering"
    )



@swag_schema
class PropertyOnboardingResponseSchema(BasePydanticAPISchema):
    """Schema for property onboarding response"""

    property_id: str
    brand_id: PositiveInt
    departments_created: NonNegativeInt
    profit_centers_created: NonNegativeInt
    property_skus_created: NonNegativeInt = Field(default=0, description="Number of property SKUs created")
    seller_skus_created: NonNegativeInt = Field(default=0, description="Number of seller SKUs created")
    onboarding_status: str
    errors: List[str]
    warnings: List[str]
    onboarded_at: datetime