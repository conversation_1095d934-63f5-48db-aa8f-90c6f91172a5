from apispec.exceptions import DuplicateComponentNameError
from apispec_webframeworks.flask import FlaskPlugin
from flasgger import Swagger
from apispec import APISpec


from cataloging_service.common.api_spec_plugins import (
    GracefulMarshmallowAPISpecPlugin,
    GracefulPydanticAPISpecPlugin,
)
from cataloging_service.common.schema_registry import get_registered_schemas

def init_swagger_docs(app):
    spec = APISpec(
        title="Cataloging Service API",
        version="1.0.0",
        openapi_version="3.0.2",
        plugins=[
            FlaskPlugin(),
            GracefulMarshmallowAPISpecPlugin(),
            GracefulPydanticAPISpecPlugin(),
        ],
        info=dict(
            description="API documentation for Cataloging Service",
            contact=dict(name="Treebo Engineering", email="<EMAIL>"),
        ),
    )

    for schema_class in get_registered_schemas():
        try:
            spec.components.schema(schema_class.__name__, schema=schema_class)
        except DuplicateComponentNameError as e:
            continue

    template = spec.to_dict()

    swagger_config = {
        "headers": [],
        "specs": [
            {
                "endpoint": "apispec",
                "route": "/cataloging-service/swagger/",
                "rule_filter": lambda rule: True,
                "model_filter": lambda tag: True,
            }
        ],
        "static_url_path": "/flasgger_static",
        "swagger_ui": True,
        "specs_route": "/cataloging-service/apidocs/",
        "openapi": "3.0.2",
    }

    Swagger(app, config=swagger_config, template=template)