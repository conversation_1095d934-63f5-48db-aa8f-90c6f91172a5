class CurrencyConfig:
    """
    Temporary class to support currencies for few hotels, remove this once this is being picked from config service
    """
    HOTEL_WISE_SUPPORTED_CURRENCY_MAP = {'9908135': {'base_currency': dict(name='POUND',
                                                                           code='GBP'),
                                                     'payment_currencies': [dict(name='POUND',
                                                                                 code='GBP'),
                                                                            dict(name='Rupees',
                                                                                 code='INR'),
                                                                            dict(name='EURO',
                                                                                 code='EUR'),
                                                                            dict(name='Dollar',
                                                                                 code='USD'),
                                                                            ]},
                                         '9901530': {'base_currency': dict(name='POUND',
                                                                           code='GBP'),
                                                     'payment_currencies': [dict(name='POUND',
                                                                                 code='GBP'),
                                                                            dict(name='Rupees',
                                                                                 code='INR'),
                                                                            dict(name='EURO',
                                                                                 code='EUR'),
                                                                            dict(name='Dollar',
                                                                                 code='USD'),
                                                                            ]},
                                         '9909688': {'base_currency': dict(name='POUND',
                                                                           code='GBP'),
                                                     'payment_currencies': [dict(name='POUND',
                                                                                 code='GBP'),
                                                                            dict(name='Rupees',
                                                                                 code='INR'),
                                                                            dict(name='EURO',
                                                                                 code='EUR'),
                                                                            dict(name='Dollar',
                                                                                 code='USD'),
                                                                            ]},
                                         '9904620': {'base_currency': dict(name='POUND',
                                                                           code='GBP'),
                                                     'payment_currencies': [dict(name='POUND',
                                                                                 code='GBP'),
                                                                            dict(name='EURO',
                                                                                 code='EUR'),
                                                                            dict(name='Dollar',
                                                                                 code='USD'),
                                                                            ]}    
                                         }
