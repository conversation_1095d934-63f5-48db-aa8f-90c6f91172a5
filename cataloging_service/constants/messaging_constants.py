from enum import Enum

CS_EXCHANGE_NAME = 'cs_exchange'
PROPERTY_ROUTING_KEY = 'com.cs.property'
OPERATION_TYPE_UPDATE = 'UPDATE'
OPERATION_TYPE_CREATE = 'CREATE'
OPERATION_TYPE_DELETE = 'DELETE'
ROOM_ENTITY_TYPE = 'ROOM'
PROPERTY_ENTITY_TYPE = 'PROPERTY'
RESTAURANT_ENTITY_TYPE = 'RESTAURANT'
RESTAURANT_TABLE_ENTITY_TYPE = 'RESTAURANT_TABLE'
RESTAURANT_TABLE_ROUTING_KEY = 'com.cs.restaurant.table'
BAR_ENTITY_TYPE = 'BAR'
HALL_ENTITY_TYPE = 'BANQUET_HALL'
PROPERTY_AMENITY_ENTITY_TYPE = 'PROPERTY_AMENITY'
ROOM_AMENITY_ENTITY_TYPE = 'ROOM_AMENITY'
PROPERTY_IMAGE_ENTITY_TYPE = 'PROPERTY_IMAGE'
AMENITY_SUMMARY_ENTITY_TYPE = 'AMENITY_SUMMARY'
RUPTUB_LEGAL_ENTITY_DETAILS = 'RUPTUB_LEGAL_ENTITY_DETAILS'
AMENITY_SUMMARY_ROUTING_KEY = 'com.cs.property.amenity.summary'
ROOM_TYPE_CONFIG_ENTITY_TYPE = 'ROOM_TYPE_CONFIG'
ROOM_ROUTING_KEY = 'com.cs.property.room'
ROOM_TYPE_CONFIG_ROUTING_KEY = 'com.cs.property.room.type.config'
PROPERTY_AMENITY_ROUTING_KEY = 'com.cs.property.amenities'
ROOM_AMENITY_ROUTING_KEY = 'com.cs.property.room.amenities'
SKU_CATEGORY_ENTITY = 'SKU_CATEGORY'
SKU_CATEGORY_ROUTING_KEY = 'com.cs.sku.category'
CHANNEL_ENTITY = 'TREEBO_CHANNEL'
CHANNEL_ROUTING_KEY = 'com.cs.treebo.channel'
SUB_CHANNEL_ENTITY = 'TREEBO_SUBCHANNEL'
SUB_CHANNEL_ROUTING_KEY = 'com.cs.treebo.subchannel'
APPLICATION_ENTITY = 'TREEBO_APPLICATION'
APPLICATION_ROUTING_KEY = 'com.cs.treebo.application'
ROOM_TYPE_ENTITY = 'ROOM_TYPE'
ROOM_TYPE_ROUTING_KEY = 'com.cs.room.type'
CITY_ENTITY = 'CITY'
CITY_ROUTING_KEY = 'com.cs.city'
SKU_ENTITY = 'SKU'
PRICING_POLICY_ENTITY = 'PRICING_POLICY'
PRICING_POLICY_ROUTING_KEY = 'com.cs.treebo.policy.pricing'
SKU_ROUTING_KEY = 'com.cs.sku'
EXCHANGE_TYPE = 'topic'
PROPERTY_SKU_ENTITY = 'PROPERTY_SKU'
SELLER_DETAILS = 'SELLER'
SELLER_DETAILS_ROUTING_KEY = 'com.cs.seller'
SELLER_SKU_ROUTING_KEY = 'com.cs.sellersku'
PROPERTY_SKU_ROUTING_KEY = 'com.cs.treebo.propertysku'
RUPTUB_ROUTING_KEY = 'com.cs.ruptubdetails'
ROOM_RACK_RATE_ROUTING_KEY = 'com.cs.roomrackrate'
ROOM_RACK_RATE_ENTITY_TYPE = 'ROOM_RACK_RATE'
SKU_UNDER_PROPERTY_ROUTING_KEY = 'com.cs.skuunderproperty'
SKU_UNDER_PROPERTY_ENTITY = 'SKU_UNDER_PROPERTY'
MENU_ENTITY = "menu"
MENU_ROUTING_KEY = "com.cs.pos.menu"
COMBO_ENTITY = "combo"
COMBO_ROUTING_KEY = "com.cs.pos.combo"
ITEM_ENTITY = "item"
ITEM_ROUTING_KEY = "com.cs.pos.item"
PROPERTY_VIDEO_ENTITY = 'PROPERTY_VIDEO'
PROPERTY_MEDIA_ROUTING_KEY = 'com.cs.property.media'


class PropertyMessageActions(Enum):
    BUSINESS_DATE_ROLLOVER = 'business.date.rollover'
    BULK_UPDATE = 'bulk_update'
    ADMIN_UPDATE = 'admin.update'
    ADMIN_CREATE = 'admin.create'
    ADMIN_DELETE = 'admin.delete'
    CRS_MIGRATE = 'crs.migrate'
