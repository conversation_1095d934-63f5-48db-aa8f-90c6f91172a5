from  cataloging_service.constants.base_enum import BaseEnum


class OfferingType(BaseEnum):
    ROOM = 'per_room'
    GUEST = 'per_guest'
    BOOKING = 'per_booking'
    CHILD = 'per_child'
    ADULT = 'per_adult'
    FIX = 'fix'


class SkuFrequencyType(BaseEnum):
    DAILY = 'daily'
    DURING_THE_FULL_STAY = 'during_the_full_stay'
    EVERY_NIGHT_EXCEPT_CHECKIN = 'every_night_except_checkin'
    ONCE = 'once'
    DAYS_OF_WEEK = 'days_of_week'


class DayOfServingSku(BaseEnum):
    CHECKIN = 'Check-In'
    CHECKOUT = 'Check-Out'
    GUEST_PREFERENCE = 'As Per Guest Preference'
    LAST_NIGHT = 'Last Night'
    POSTING_RULE_BASED = 'Posting Rule Based'
