import enum


class PropertyChoices:
    STATUS_NEAR_CONFIRMED = 'NEAR_CONFIRMED'
    STATUS_NOT_SIGNING = 'NOT_SIGNING'
    STATUS_SIGNED = 'SIGNED'
    STATUS_DROPPED = 'DROPPED_POST_SIGNING'
    STATUS_LIVE = 'LIVE'
    STATUS_CHURNED = 'CHURNED'
    STATUS_CHOICES = [STATUS_NEAR_CONFIRMED, STATUS_NOT_SIGNING, STATUS_SIGNED, STATUS_DROPPED, STATUS_LIVE,
                      STATUS_CHURNED]


class BankDetailChoices:
    SAVINGS_ACCOUNT = 'SAVINGS'
    CURRENT_ACCOUNT = 'CURRENT'
    ACCOUNT_TYPE_CHOICES = [SAVINGS_ACCOUNT, CURRENT_ACCOUNT]


class SellerTypeModelChoices(enum.Enum):
    MARKETPLACE = 'MARKETPLACE'
    RESELLER = 'RESELLER'


class PropertyDetailChoices:
    NEIGHBOURHOOD_QUIET = 'QUIET'
    NEIGHBOURHOOD_RESIDENTIAL = 'RESIDENTIAL'
    NEIGHBOURHOOD_GREEN = 'GREEN'
    NEIGHBOURHOOD_BUSY_MARKET = 'BUSY_MARKET'
    NEIGHBOURHOOD_MAIN_ROAD = 'MAIN_ROAD'
    NEIGHBOURHOOD_OTHERS = 'OTHERS'
    PROPERTY_TYPE_HOTEL = 'HOTEL'
    PROPERTY_TYPE_SERVICE_APARTMENT = 'SERVICE_APARTMENT'
    PROPERTY_RESORT = 'RESORT'
    PROPERTY_HOMESTAY_COTTAGE_VILLA = 'HOMESTAY_COTTAGE_VILLA'
    PROPERTY_HOMESTAY = 'HOMESTAY'
    PROPERTY_STYLE_HERITAGE = 'HERITAGE'
    PROPERTY_STYLE_MODERN = 'MODERN'
    PROPERTY_STYLE_GUEST_HOUSE = 'GUEST_HOUSE'
    PROPERTY_STYLE_PLAIN_VANILLA = 'PLAIN_VANILLA'
    PROPERTY_STYLE_OTHERS = 'OTHERS'
    BUILDING_INDEPENDENT_SINGLE = 'INDEPENDENT_SINGLE'
    BUILDING_INDEPENDENT_MULTIPLE = 'INDEPENDENT_MULTIPLE'
    BUILDING_PART_OF_SINGLE_BUILDING = 'PART_OF_SINGLE_BUILDING'
    BUILDING_PART_OF_MULTIPLE_BUILDING = 'PART_OF_MULTIPLE_BUILDING'
    BUILDING_FLOORS_IN_EACH_BUILDING = 'FLOORS_IN_EACH_BUILDING'
    NEIGHBOURHOOD_CHOICES = [NEIGHBOURHOOD_QUIET, NEIGHBOURHOOD_RESIDENTIAL, NEIGHBOURHOOD_GREEN,
                             NEIGHBOURHOOD_BUSY_MARKET, NEIGHBOURHOOD_MAIN_ROAD, NEIGHBOURHOOD_OTHERS]
    PROPERTY_TYPE_CHOICES = [PROPERTY_TYPE_HOTEL, PROPERTY_TYPE_SERVICE_APARTMENT, PROPERTY_RESORT,
                             PROPERTY_HOMESTAY_COTTAGE_VILLA, PROPERTY_HOMESTAY]
    PROPERTY_STYLE_CHOICES = [PROPERTY_STYLE_HERITAGE, PROPERTY_STYLE_MODERN, PROPERTY_STYLE_GUEST_HOUSE,
                              PROPERTY_STYLE_PLAIN_VANILLA, PROPERTY_STYLE_OTHERS]
    BUILDING_CHOICES = [BUILDING_INDEPENDENT_SINGLE, BUILDING_INDEPENDENT_MULTIPLE, BUILDING_PART_OF_SINGLE_BUILDING,
                        BUILDING_PART_OF_MULTIPLE_BUILDING, BUILDING_FLOORS_IN_EACH_BUILDING]


class GoogleDriveFileTypeChoices:
    FILE_TYPE_COMPLETE_AGREEMENT = 'COMPLETE_AGREEMENT'
    FILE_TYPE_AGREEMENT_FIRST_PAGE = 'AGREEMENT_FIRST_PAGE'
    FILE_TYPE_AGREEMENT_EXPIRY = 'AGREEMENT_EXPIRY'
    FILE_TYPE_AGREEMENT_DETAILS = 'AGREEMENT_DETAILS'
    FILE_TYPE_SERVICE_TAX_ONE = 'SERVICE_TAX_ONE'
    FILE_TYPE_SERVICE_TAX_TWO = 'SERVICE_TAX_TWO'
    FILE_TYPE_LUXURY_TAX_ONE = 'LUXURY_TAX_ONE'
    FILE_TYPE_LUXURY_TAX_TWO = 'LUXURY_TAX_TWO'
    FILE_TYPE_OTA_NOC = 'OTA_NOC'
    FILE_TYPE_GI_NOC = 'GI_NOC'
    FILE_TYPE_LEASE_DOCUMENT = 'LEASE_DOCUMENT'
    FILE_TYPE_ELECTRIC_BILL = 'ELECTRIC_BILL'
    FILE_TYPE_PARTNER_CANCELLED_CHEQUE = 'PARTNER_CANCELLED_CHEQUE'
    FILE_TYPE_HOTEL_REGISTRATION_DOCUMENT = 'HOTEL_REGISTRATION_DOCUMENT'
    FILE_TYPE_PAN_CARD = 'PAN_CARD'
    FILE_TYPE_OLD_FRANCHISE_TERMINATION_DOCUMENT = 'OLD_FRANCHISE_TERMINATION_DOCUMENT'
    FILE_TYPE_PARTNER_OLD_PHOTO = 'PARTNER_OLD_PHOTO'
    FILE_TYPE_PARTNER_PROFESSIONAL_PHOTO = 'PARTNER_PROFESSIONAL_PHOTO'
    FILE_TYPE_FACADE_IMAGE = 'FACADE_IMAGE'
    FILE_TYPE_HIGH_RES_IMAGE = 'HIGH_RES_IMAGE'
    FILE_TYPE_MID_RES_IMAGE = 'MID_RES_IMAGE'
    FILE_TYPE_LOW_RES_IMAGE = 'LOW_RES_IMAGE'
    FILE_TYPE_TREEBO_SERVICE_TAX = 'TREEBO_SERVICE_TAX'
    FILE_TYPE_TREEBO_CANCELLED_CHEQUE = 'TREEBO_CANCELLED_CHEQUE'
    FILE_TYPE_GENERIC_IMAGE = 'GENERIC_IMAGE'
    FILE_TYPE_C_FORM = 'FILE_TYPE_C_FORM'
    FILE_TYPE_CHOICES = [FILE_TYPE_COMPLETE_AGREEMENT, FILE_TYPE_AGREEMENT_FIRST_PAGE, FILE_TYPE_AGREEMENT_EXPIRY,
                         FILE_TYPE_AGREEMENT_DETAILS, FILE_TYPE_SERVICE_TAX_ONE, FILE_TYPE_SERVICE_TAX_TWO,
                         FILE_TYPE_LUXURY_TAX_ONE, FILE_TYPE_LUXURY_TAX_TWO, FILE_TYPE_OTA_NOC, FILE_TYPE_GI_NOC,
                         FILE_TYPE_LEASE_DOCUMENT, FILE_TYPE_ELECTRIC_BILL, FILE_TYPE_PARTNER_CANCELLED_CHEQUE,
                         FILE_TYPE_HOTEL_REGISTRATION_DOCUMENT, FILE_TYPE_PAN_CARD,
                         FILE_TYPE_OLD_FRANCHISE_TERMINATION_DOCUMENT, FILE_TYPE_PARTNER_OLD_PHOTO,
                         FILE_TYPE_PARTNER_PROFESSIONAL_PHOTO, FILE_TYPE_FACADE_IMAGE, FILE_TYPE_HIGH_RES_IMAGE,
                         FILE_TYPE_MID_RES_IMAGE, FILE_TYPE_LOW_RES_IMAGE, FILE_TYPE_TREEBO_SERVICE_TAX,
                         FILE_TYPE_TREEBO_CANCELLED_CHEQUE, FILE_TYPE_GENERIC_IMAGE, FILE_TYPE_C_FORM]


class RoomTypeChoices:
    ACACIA = 'ACACIA'
    OAK = 'OAK'
    MAPLE = 'MAPLE'
    MAHOGANY = 'MAHOGANY'
    OFFLINE = 'OFFLINE'
    TYPE_CHOICES = [ACACIA, OAK, MAPLE, MAHOGANY, OFFLINE]


class RoomTypeConfigurationChoices:
    EXTRA_BED_MATTRESS = 'MATTRESS'
    EXTRA_BED_FOLDING_BED = 'FOLDING_BED'
    EXTRA_BED_SINGLE_BED = 'SINGLE_BED'
    EXTRA_BED_CHOICES = [EXTRA_BED_MATTRESS, EXTRA_BED_FOLDING_BED, EXTRA_BED_SINGLE_BED]


class OwnerChoices:
    GENDER_MALE = 'M'
    GENDER_FEMALE = 'F'
    CHOICES_GENDER = [GENDER_MALE, GENDER_FEMALE]


class ParkingChoices:
    LOCATION_INDOORS_BASEMENT = 'INDOORS_BASEMENT'
    LOCATION_OUTDOORS_UNCOVERED = 'OUTDOORS_UNCOVERED'
    LOCATION_CHOICES = [LOCATION_INDOORS_BASEMENT, LOCATION_OUTDOORS_UNCOVERED]


class SwimmingPoolChoices:
    LOCATION_INDOORS_COVERED = 'INDOORS_COVERED'
    LOCATION_OUTDOORS_UNCOVERED = 'OUTDOORS_UNCOVERED'
    LOCATION_CHOICES = [LOCATION_INDOORS_COVERED, LOCATION_OUTDOORS_UNCOVERED]


class BreakfastChoices:
    BREAKFAST_A_LA_CARTE = 'A_LA_CARTE'
    BREAKFAST_BUFFET = 'BUFFET'
    BREAKFAST_FIXED = 'FIXED'
    SERVICE_AREA_DINING_AREA = 'DINING_AREA'
    SERVICE_AREA_RESTAURANT = 'RESTAURANT'
    SERVICE_AREA_ROOM = 'ROOM'
    BREAKFAST_TYPE_CHOICES = [BREAKFAST_A_LA_CARTE, BREAKFAST_BUFFET, BREAKFAST_FIXED]
    SERVICE_AREA_CHOICES = [SERVICE_AREA_DINING_AREA, SERVICE_AREA_RESTAURANT, SERVICE_AREA_ROOM]


class TvChoices:
    TV_LCD = 'LCD'
    TV_CRT = 'CRT'
    CONNECTION_CABLE = 'CABLE'
    CONNECTION_DTH = 'DTH'
    TV_CHOICES = [TV_LCD, TV_CRT]
    CONNECTION_CHOICES = [CONNECTION_CABLE, CONNECTION_DTH]


class AcChoices:
    AC_SPLIT = 'SPLIT'
    AC_WINDOWS = 'WINDOW'
    AC_CENTRALISED = 'CENTRALISED'
    AC_CHOICES = [AC_SPLIT, AC_WINDOWS, AC_CENTRALISED]


class StoveChoices:
    STOVE_ALL_ROOMS = 'ALL_ROOMS'
    STOVE_ON_REQUEST = 'ON_REQUEST'
    STOVE_LPG = 'LPG'
    STOVE_INDUCTION = 'INDUCTION'
    STOVE_AVAILABILITY_CHOICES = [STOVE_ALL_ROOMS, STOVE_ON_REQUEST]
    STOVE_TYPE_CHOICES = [STOVE_LPG, STOVE_INDUCTION]


class RoomAmenityChoices:
    FAN_CEILING = 'CEILING'
    FAN_STANDING = 'STANDING'
    FAN_CHOICES = [FAN_CEILING, FAN_STANDING]
    LOCK_ELECTRONIC = 'ELECTRONIC'
    LOCK_MANUAL = 'MANUAL'
    LOCK_CHOICES = [LOCK_ELECTRONIC, LOCK_MANUAL]
    ON_REQUEST = 'ON_REQUEST'
    ALL_ROOMS = 'ALL_ROOMS'
    AVAILABILITY_CHOICES = [ON_REQUEST, ALL_ROOMS]


class LandmarkChoices:
    LANDMARK_AIRPORT = 'AIRPORT'
    LANDMARK_RAILWAY_STATION = 'RAILWAY_STATION'
    LANDMARK_INTER_CITY_BUS_STAND = 'INTER_CITY_BUS_STAND'
    LANDMARK_LOCAL_BUS_STAND = 'LOCAL_BUS_STAND'
    LANDMARK_METRO_STATION = 'METRO_STATION'
    LANDMARK_OTHER = 'OTHER'
    LANDMARK_CHOICES = [LANDMARK_AIRPORT, LANDMARK_RAILWAY_STATION, LANDMARK_INTER_CITY_BUS_STAND,
                        LANDMARK_LOCAL_BUS_STAND, LANDMARK_METRO_STATION, LANDMARK_OTHER]


class LandmarkChoices:
    LANDMARK_AIRPORT = 'AIRPORT'
    LANDMARK_RAILWAY_STATION = 'RAILWAY_STATION'
    LANDMARK_INTER_CITY_BUS_STAND = 'INTER_CITY_BUS_STAND'
    LANDMARK_LOCAL_BUS_STAND = 'LOCAL_BUS_STAND'
    LANDMARK_METRO_STATION = 'METRO_STATION'
    LANDMARK_OTHER = 'OTHER'
    LANDMARK_CHOICES = [LANDMARK_AIRPORT, LANDMARK_RAILWAY_STATION, LANDMARK_INTER_CITY_BUS_STAND,
                        LANDMARK_LOCAL_BUS_STAND, LANDMARK_METRO_STATION, LANDMARK_OTHER]


class TransportStationChoices:
    TRANSPORT_STATION_AIRPORT = 'AIRPORT'
    TRANSPORT_STATION_RAILWAY_STATION = 'RAILWAY_STATION'
    TRANSPORT_STATION_INTER_CITY_BUS_STAND = 'INTER_CITY_BUS_STAND'
    TRANSPORT_STATION_LOCAL_BUS_STAND = 'LOCAL_BUS_STAND'
    TRANSPORT_STATION_METRO_STATION = 'METRO_STATION'
    TRANSPORT_STATION_LOCAL_TRAIN_STATION = 'LOCAL_TRAIN_STATION'
    TRANSPORT_STATION_CHOICES = [TRANSPORT_STATION_AIRPORT, TRANSPORT_STATION_RAILWAY_STATION,
                                 TRANSPORT_STATION_INTER_CITY_BUS_STAND,
                                 TRANSPORT_STATION_LOCAL_BUS_STAND, TRANSPORT_STATION_METRO_STATION,
                                 TRANSPORT_STATION_LOCAL_TRAIN_STATION]


class StandardStatusChoices:
    ACTIVE = 'ACTIVE'
    INACTIVE = 'INACTIVE'
    STATUSES = [ACTIVE, INACTIVE]


class SkuTypeChoices:
    sku = 'sku'
    bundle = 'bundle'
    SKU_CHOICES = [sku, bundle]


class TaxTypeChoices:
    unit = 'unit'
    composite = 'composite'
    derived = 'derived'
    TAX_CHOICES = [unit, composite, derived]


class SuperHeroPriceSlabChoices(enum.Enum):
    PLATINUM = 'PLATINUM'
    SILVER = 'SILVER'
    GOLD_OPS = 'GOLD_OPS'
    GOLD_SALES = 'GOLD_SALES'


class HygieneShieldNameChoices(enum.Enum):
    GOLD = 'GOLD'
    PLATINUM = 'PLATINUM'
    BRONZE = 'BRONZE'

class RestaurantTableStatusChoices(enum.Enum):
    AVAILABLE = 'AVAILABLE'
    OCCUPIED = 'OCCUPIED'
    DIRTY = 'DIRTY'
    UNAVAILABLE = 'UNAVAILABLE'

    STATUSES = [AVAILABLE, OCCUPIED, DIRTY, UNAVAILABLE]


DS_PRICING_ENABLED_PRICE_SLABS = [SuperHeroPriceSlabChoices.PLATINUM, SuperHeroPriceSlabChoices.GOLD_SALES]


def get_choices():
    choices = {'transport_station_type_choices': TransportStationChoices.TRANSPORT_STATION_CHOICES,
               'room_amenity_availability_choices': RoomAmenityChoices.AVAILABILITY_CHOICES,
               'room_lock_type': RoomAmenityChoices.LOCK_CHOICES, 'fan_choices': RoomAmenityChoices.FAN_CHOICES,
               'stove_type_choices': StoveChoices.STOVE_TYPE_CHOICES,
               'stove_availability_choices': StoveChoices.STOVE_AVAILABILITY_CHOICES,
               'ac_type_choices': AcChoices.AC_CHOICES, 'tv_type_choices': TvChoices.TV_CHOICES,
               'breakfast_service_area_choices': BreakfastChoices.SERVICE_AREA_CHOICES,
               'breakfast_type_choices': BreakfastChoices.BREAKFAST_TYPE_CHOICES,
               'swimming_pool_type_choices': SwimmingPoolChoices.LOCATION_CHOICES,
               'parking_type_choices': ParkingChoices.LOCATION_CHOICES,
               'owner_gender_choices': OwnerChoices.CHOICES_GENDER,
               'extra_bed_choices': RoomTypeConfigurationChoices.EXTRA_BED_CHOICES,
               'room_type_choices': RoomTypeChoices.TYPE_CHOICES,
               'building_type_choices': PropertyDetailChoices.BUILDING_CHOICES,
               'neighbourhood_type_choices': PropertyDetailChoices.NEIGHBOURHOOD_CHOICES,
               'property_style_choices': PropertyDetailChoices.PROPERTY_STYLE_CHOICES,
               'property_type_choices': PropertyDetailChoices.PROPERTY_TYPE_CHOICES,
               'bank_account_type_choices': BankDetailChoices.ACCOUNT_TYPE_CHOICES,
               'property_status_choices': PropertyChoices.STATUS_CHOICES}
    return choices


class MenuTypeChoices(enum.Enum):
    RESTAURANT = 'restaurant'
    TAKE_AWAY = 'take_away'
    ROOM_SERVICE = 'room_service'
    DELIVERY = 'delivery'


MENU_TYPE_CHOICES = ['restaurant', 'take_away', 'room_service', 'delivery']


class FoodTypeChoices(enum.Enum):
    VEGETARIAN = "vegetarian"
    NON_VEGETARIAN = "non_vegetarian"
    VEGAN = "vegan"


FOOD_TYPE_CHOICES = ["vegetarian", "non_vegetarian", "vegan"]
