from cataloging_service.api.amenity_apis import bp as amenity_bp
from cataloging_service.api.channel_apis import bp as channel_bp
from cataloging_service.api.health_check import bp as health_check_api_bp
from cataloging_service.api.hotel_owner_apis import bp as hotel_owner_api_bp
from cataloging_service.api.location_apis import bp as location_api_bp
from cataloging_service.api.meta_apis import bp as meta_api_bp
from cataloging_service.api.ota_apis import bp as ota_bp
from cataloging_service.api.pos.restaurant_api import bp as pos_restaurant_bp
from cataloging_service.api.pos.seller_sku_api import bp as pos_sku_bp
from cataloging_service.api.pos.sellers_api import bp as pos_seller_bp
from cataloging_service.api.property_apis import bp as property_bp
from cataloging_service.api.provider_apis import bp as provider_bp
from cataloging_service.api.rate_plan import bp as rate_plan_bp
from cataloging_service.api.read_ui import bp as ui_bp
from cataloging_service.api.room_apis import bp as room_bp
from cataloging_service.api.ruptub_legal_entity_apis import bp as ruptub_legal_entity_bp
from cataloging_service.api.scripts import bp as scripts_bp
from cataloging_service.api.seller_type_apis import bp as seller_type_bp
from cataloging_service.api.sku_apis import bp as category_bp
from cataloging_service.api.room_rack_rate_apis import bp as room_rack_rate_bp
from cataloging_service.api.v3 import v3_bp
from cataloging_service.api.sku_pricing import bp as sku_pricing_bp
from cataloging_service.api.menu_apis import bp as menu_bp
from cataloging_service.api.item_apis import bp as items_bp
from cataloging_service.api.combo_apis import bp as combo_bp
from cataloging_service.api.restaurant_area_apis import bp as restaurant_area_bp
from cataloging_service.api.kitchen_apis import bp as kitchen_bp

# Phase 2: Department/Profit Center blueprints
from cataloging_service.api.templates.departments import bp as department_template_bp
from cataloging_service.api.templates.profit_centers import bp as profit_center_template_bp
from cataloging_service.api.property.departments import bp as property_department_bp
from cataloging_service.api.property.onboarding import bp as property_onboarding_bp

# Phase 2: Transaction blueprints
from cataloging_service.api.transactions.default_mappings import bp as transaction_default_mapping_bp
from cataloging_service.api.transactions.transaction_master import bp as transaction_master_bp

all_old_bps = [health_check_api_bp, meta_api_bp, location_api_bp, property_bp,
               room_bp, ui_bp, amenity_bp, ota_bp]

all_old_api_bps = [meta_api_bp, location_api_bp, property_bp, room_bp, amenity_bp,
                   category_bp, channel_bp, provider_bp, seller_type_bp, ruptub_legal_entity_bp, hotel_owner_api_bp,
                   rate_plan_bp, sku_pricing_bp, room_rack_rate_bp]

all_old_script_bps = [scripts_bp]

all_new_api_bps = [pos_seller_bp, v3_bp, pos_sku_bp, pos_restaurant_bp, menu_bp, items_bp, combo_bp, \
    restaurant_area_bp, kitchen_bp, department_template_bp, profit_center_template_bp, property_department_bp, \
    property_onboarding_bp, transaction_default_mapping_bp, transaction_master_bp]
