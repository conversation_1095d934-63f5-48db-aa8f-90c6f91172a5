from sqlalchemy.orm import joinedload
from sqlalchemy import or_
from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import ItemCustomisation, Item, ItemVariant, ItemVariantVariantAssociation, MenuItem,\
    Variant, VariantGroup, Menu


class ItemRepository(BaseRepository):

    def get_all_items(self, seller_id, name=None, food_type=None, item_ids=None, is_menu_item=False, is_side=None):
        name = name or ''
        query = self.pre_filtered_query(Item).filter_by(seller_id=seller_id, is_deleted=False) \
            .filter(or_(Item.name.ilike('%' + name + '%'), Item.display_name.ilike('%' + name + '%'),
                        Item.code.ilike('%' + name + '%')))

        if is_menu_item:
            query = query.join(Item.menu_items).join(MenuItem.menu).filter(
                MenuItem.is_deleted == False).filter(Menu.is_deleted == False)

        if is_side is not None:
            query = query.filter(Item.use_as_side == is_side)

        if food_type is not None:
            query = query.filter_by(food_type=food_type)
        if item_ids is not None:
            query = query.filter(Item.id.in_(item_ids))

        query = query.order_by(Item.modified_at.desc())
        return query.all()

    def get_all_sides(self, seller_id, name):
        return self.pre_filtered_query(Item).filter_by(seller_id=seller_id, is_deleted=False, use_as_side=True)\
            .filter(or_(Item.name.ilike('%' + name + '%'), Item.display_name.ilike('%' + name + '%'),
                        Item.code.ilike('%' + name + '%'))).all()

    def get_item_customisations(self, seller_id, item_id):
        return self.pre_filtered_query(
            ItemCustomisation).filter_by(item_id=item_id, is_deleted=False).join(ItemCustomisation.variant)\
            .join(Variant.variant_group).filter(Variant.is_deleted == False).filter(VariantGroup.is_deleted == False)\
            .all()

    def get_item_variants(self, seller_id, item_id):
        return self.pre_filtered_query(ItemVariant).filter_by(item_id=item_id, is_deleted=False)\
            .join(ItemVariant.variants).join(ItemVariantVariantAssociation.variant).join(Variant.variant_group)\
            .filter(ItemVariantVariantAssociation.is_deleted == False).filter(Variant.is_deleted == False)\
            .filter(VariantGroup.is_deleted == False).all()

    def get_all_items_and_children(self, seller_id, item_ids):
        return self.pre_filtered_query(Item).filter_by(seller_id=seller_id, is_deleted=False).filter(Item.id.in_(item_ids)) \
            .options(joinedload(Item.side_items)) \
            .options(joinedload(Item.item_variants)) \
            .options(joinedload(Item.item_customisations)) \
            .all()

    def get_item(self, seller_id, item_id):
        item = self.pre_filtered_query(Item).filter_by(id=item_id, seller_id=seller_id, is_deleted=False).first()
        item.variant_group_ids = self.pre_filtered_query(VariantGroup.id).join(VariantGroup.variants).join(Variant.item_variants).join(
            ItemVariantVariantAssociation.item_variant).join(ItemVariant.item).filter(Item.id == item_id).distinct().all()
        item.customisation_group_ids = self.pre_filtered_query(VariantGroup.id).join(VariantGroup.variants).join(
            Variant.item_customisations).join(ItemCustomisation.item_variant).join(ItemVariant.item).filter(Item.id == item_id).distinct().all()
        item.customisation_group_ids += self.pre_filtered_query(VariantGroup.id).join(VariantGroup.variants).join(
            Variant.item_customisations).join(ItemCustomisation.item).filter(Item.id == item_id).distinct().all()
        return item

    def get_items_in_property(self, property_id, code, item_id):
        queryset = self.pre_filtered_query(Item).filter_by(
            code=code).join(Item.seller).filter_by(property_id=property_id)
        if item_id:
            queryset = queryset.filter(Item.id != item_id)
        return queryset.all()

    def check_all_side_items_exists(self, seller_id, item_ids, item_variant_ids):
        count_of_items = self.pre_filtered_query(Item).filter_by(seller_id=seller_id, is_deleted=False,
                                                                 use_as_side=True)\
            .filter(Item.id.in_(item_ids)).count()
        count_of_item_variants = self.pre_filtered_query(ItemVariant).filter_by(is_deleted=False)\
            .filter(ItemVariant.id.in_(item_variant_ids)).count()
        return count_of_items == len(set(item_ids)) and count_of_item_variants == len(set(item_variant_ids))

    def load_item_for_update(self, seller_id, item_id):
        item = self.get_for_update(model=Item, id=item_id, seller_id=seller_id) \
            .options(joinedload(Item.side_items)) \
            .options(joinedload(Item.item_variants)) \
            .options(joinedload(Item.item_customisations)) \
            .first()

        associations = []
        item_variant_item_customisations = []

        for item_variant in item.item_variants:
            associations.extend(self.get_for_update(model=ItemVariantVariantAssociation,
                                                    item_variant_id=item_variant.id).all())
            item_variant_item_customisations.extend(self.get_for_update(
                model=ItemCustomisation, item_variant_id=item_variant.id).all())

        return item, associations, item_variant_item_customisations

    def get_all_variant_groups(self, seller_id):
        return self.pre_filtered_query(VariantGroup).filter_by(seller_id=seller_id, is_deleted=False).all()

    def search_variant_groups(self, seller_id, is_customisation, name, variant_group_ids):

        query = self.pre_filtered_query(VariantGroup).filter_by(
            seller_id=seller_id, is_deleted=False).filter(or_(VariantGroup.name.ilike('%' + name + '%'), VariantGroup.display_name.ilike('%' + name + '%')))

        if is_customisation is not None:
            query = query.filter(VariantGroup.is_customisation == is_customisation)
        if variant_group_ids:
            query = query.filter(VariantGroup.id.in_(variant_group_ids))
        return query.all()

    def load_variant_group_for_update(self, variant_group_id, seller_id):
        return self.get_for_update(model=VariantGroup, id=variant_group_id, is_deleted=False, seller_id=seller_id).one()
