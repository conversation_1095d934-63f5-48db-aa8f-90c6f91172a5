from typing import Optional, List
from cataloging_service.models import BrandDepartmentTemplateMap
from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.adapters.brand_department_template_adapter import BrandDepartmentTemplateAdapter
from cataloging_service.domain.entities.templates.department_template import BrandDepartmentTemplateMappingEntity

@register_instance()
class BrandDepartmentTemplateRepository(BaseEntityRepository):
    """Repository for brand department template operations"""

    adaptor = BrandDepartmentTemplateAdapter()

    def create(self, entity: BrandDepartmentTemplateMappingEntity) -> BrandDepartmentTemplateMappingEntity:
        """Create a new property department"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_brand_id(self, brand_id: int) -> Optional[List[BrandDepartmentTemplateMappingEntity]]:
        """Get brand department template mapping by brand ID"""
        model = self.rget_by_attr(BrandDepartmentTemplateMap, brand_id=brand_id)
        return [self.adaptor.to_entity(model) for model in model] if model else None
