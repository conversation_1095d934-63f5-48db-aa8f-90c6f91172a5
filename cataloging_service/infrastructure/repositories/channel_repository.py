from sqlalchemy.orm import joinedload, selectinload

from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import Channel, SubChannel, Application, PricingPolicy, PricingMapping


class ChannelRepository(BaseRepository):

    def get_channel(self, id, eager_load=False, with_applications=False, with_subchannels=False):
        query = self.session().query(Channel)
        if eager_load:
            if with_subchannels:
                query = query.options(
                    selectinload(Channel.sub_channels).joinedload(
                        SubChannel.pricing_mapping).joinedload(PricingMapping.pricing, innerjoin=True)
                )

            if with_applications:
                query = query.options(joinedload(Channel.applications))

        channels = query.filter(Channel.id == id).all()
        if channels:
            return channels[0]
        else:
            return None

    def get_all_channels(self, ids=None):
        query = self.session().query(Channel)

        query = query.options(
            joinedload(Channel.applications),
            selectinload(Channel.sub_channels).joinedload(SubChannel.pricing_mapping).joinedload(
                PricingMapping.pricing, innerjoin=True)
        )

        if ids and hasattr(ids, "__iter__"):
            query = query.filter(Channel.id.in_(ids))

        query = query.order_by(Channel.priority)

        return query.all()

    @staticmethod
    def get_channel_for_sub_channel(sub_channel_id):
        return SubChannel.query.get(sub_channel_id).channel

    @staticmethod
    def get_all_sub_channels(ids=None):
        query = SubChannel.query

        if ids and hasattr(ids, "__iter__"):
            query = query.filter(SubChannel.id.in_(ids))

        query = query.order_by(SubChannel.priority)

        return query.all()

    @staticmethod
    def get_all_applications(ids=None):
        query = Application.query

        if ids and hasattr(ids, "__iter__"):
            query = query.filter(Application.id.in_(ids))

        return query.all()

    @staticmethod
    def rget_all_pricing_policies(ids=None):
        query = PricingPolicy.query

        if ids and hasattr(ids, "__iter__"):
            query = query.filter(PricingPolicy.id.in_(ids))
        return query.all()

    @staticmethod
    def rget_default_pricing_policies():
        return PricingPolicy.query.filter_by(is_default=True).all()

    @staticmethod
    def rget_default_pricing_policy():
        return PricingPolicy.query.filter_by(is_default=True).first()
