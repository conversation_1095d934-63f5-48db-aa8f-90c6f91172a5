import simplejson

from cataloging_service.infrastructure.helper import SkuHelper
from cataloging_service.infrastructure.repositories.RedisRepository import RedisRepository


class SkuRedisRepository(RedisRepository):
    """
    'nt' : signifies named tuple
    """

    def __init__(self):
        self.sku_hash = "SKU"

    def save_all_sku(self, all_sku, room_type_dict):
        for sku in all_sku:
            self.save_sku(sku=sku, room_type_dict=room_type_dict)
        return all_sku

    def save_sku(self, sku, room_type_dict):
        nt_sku = SkuHelper.get_named_tuple_sku(sku=sku, room_type_dict=room_type_dict)
        RedisRepository.redis_save_one(nt_sku.code, simplejson.dumps(nt_sku._asdict()), timeout=-1)
        return sku

    def get_sku(self, sku_code):
        RedisRepository.redis_get_one(sku_code)

    def get_given_sku(self, sku_codes):
        return RedisRepository.redis_get_in(*sku_codes)
