from sqlalchemy import func

from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import Owner


class OwnerRepository(BaseRepository):
    def get_owner(self, first_name, last_name, email):
        if email:
            return Owner.query.filter_by(email=email).first()

        if first_name and last_name:
            return Owner.query.filter(func.lower(Owner.first_name) == func.lower(first_name),
                                      func.lower(Owner.last_name) == func.lower(last_name)).first()
        return None

    def get_owner_by_owner_id(self, owner_ids):
        if owner_ids:
            return Owner.query.filter(Owner.id.in_(owner_ids)).all()

        return None

    def delete_owners(self, owner_ids):
        if owner_ids:
            return Owner.query.filter(Owner.id.in_(owner_ids)).delete(synchronize_session='fetch')

        return None