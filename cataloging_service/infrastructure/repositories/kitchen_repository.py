from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import Kitchen


class KitchenRepository(BaseRepository):
    def get_kitchen(self, kitchen_id):
        return self.pre_filtered_query(Kitchen).filter_by(id=kitchen_id).first()

    def get_kitchens(self, property_id=None):
        queryset = self.pre_filtered_query(Kitchen)
        
        if property_id:
            queryset = queryset.filter_by(property_id=property_id)

        return queryset.filter_by(is_deleted=False).all()

    def load_for_update(self, kitchen_id=None, property_id=None):
        return self.get_for_update(model=Kitchen, id=kitchen_id, property_id=property_id, is_deleted=False).first()
