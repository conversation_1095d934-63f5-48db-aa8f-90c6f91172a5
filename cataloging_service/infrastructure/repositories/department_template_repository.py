from typing import List, Optional
from object_registry import register_instance
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.adapters.department_template_adapter import DepartmentTemplateAdapter
from cataloging_service.domain.entities.templates.department_template import DepartmentTemplateEntity
from cataloging_service.models import DepartmentTemplate, BrandDepartmentTemplateMap


@register_instance()
class DepartmentTemplateRepository(BaseEntityRepository):
    """Repository for department template operations"""

    adaptor = DepartmentTemplateAdapter()

    def create(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Create a new department template"""
        model = self.adaptor.to_db_model(entity)
        self.persist(model)
        return self.adaptor.to_entity(model)

    def get_by_id(self, template_id: int) -> Optional[DepartmentTemplateEntity]:
        """Get department template by ID"""
        model = self.rget_by_attr(DepartmentTemplate, limit_one=True, id=template_id)
        return self.adaptor.to_entity(model) if model else None

    def get_by_brand_and_code(self, brand_id: int, code: str) -> Optional[DepartmentTemplateEntity]:
        """Get department template by brand ID and code (M2M relationship)"""
        query = (
            self.session()
            .query(DepartmentTemplate)
            .join(BrandDepartmentTemplateMap,
                  DepartmentTemplate.id == BrandDepartmentTemplateMap.department_template_id)
            .filter(
                BrandDepartmentTemplateMap.brand_id == brand_id,
                DepartmentTemplate.code == code
            )
            .limit(1)
        )
        model = query.one_or_none()
        return self.adaptor.to_entity(model) if model else None


    def get_by_brand(self, brand_id: int, active_only: bool = True) -> List[DepartmentTemplateEntity]:
        """Get all department templates for a given brand (via M2M)"""
        query = self.session().query(DepartmentTemplate).join(
            BrandDepartmentTemplateMap,
            BrandDepartmentTemplateMap.department_template_id == DepartmentTemplate.id
        ).filter(
            BrandDepartmentTemplateMap.brand_id == brand_id
        )

        if active_only:
            query = query.filter(DepartmentTemplate.is_active == True)

        models = query.all()
        return [self.adaptor.to_entity(model) for model in models]

    def update(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Update department template"""
        existing_model = self.rget_by_attr(DepartmentTemplate, limit_one=True, id=entity.id)
        if not existing_model:
            raise ValueError(f"Department template with ID {entity.id} not found")
        updated_model = self.adaptor.update_model_from_entity(existing_model, entity)
        self._update(updated_model)
        return self.adaptor.to_entity(updated_model)

    def delete(self, template_id: int) -> bool:
        """Delete department template"""
        model = self.rget_by_attr(DepartmentTemplate, limit_one=True, id=template_id)
        if not model:
            return False
        super().delete(model)
        return True

    def get_auto_create_templates(self, brand_id: int) -> List[DepartmentTemplateEntity]:
        """Get templates that should be auto-created on property launch"""
        models = self.rget_by_attr(
            DepartmentTemplate,
            brand_id=brand_id,
            is_active=True,
            auto_create_on_property_launch=True,
        )
        return [self.adaptor.to_entity(model) for model in models]

    def exists_by_brand_and_code(self, brand_id: int, code: str, exclude_id: Optional[int] = None) -> bool:
        """Check if a department template with the given code exists for a brand"""
        query = (
            self.session().query(DepartmentTemplate)
            .join(BrandDepartmentTemplateMap,
                  DepartmentTemplate.id == BrandDepartmentTemplateMap.department_template_id)
            .filter(
                BrandDepartmentTemplateMap.brand_id == brand_id,
                DepartmentTemplate.code == code
            )
        )
        if exclude_id:
            query = query.filter(DepartmentTemplate.id != exclude_id)

        return query.first() is not None
