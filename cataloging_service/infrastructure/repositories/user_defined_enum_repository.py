from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.models import UserDefinedEnumModel


class UserDefinedEnumRepository(BaseEntityRepository):
    def load(self, property_id, enum_names=None, role=None):
        global_enums = self.session().query(UserDefinedEnumModel).filter(
            UserDefinedEnumModel.property_id.is_(None)).filter(UserDefinedEnumModel.role.is_(None))

        if property_id:
            property_enums = self.session().query(UserDefinedEnumModel).filter(
                UserDefinedEnumModel.property_id == property_id)
            if enum_names:
                property_enums = property_enums.filter(UserDefinedEnumModel.enum_name.in_(enum_names))
            if role:
                property_enums = property_enums.filter(UserDefinedEnumModel.role == role)
            property_enums = property_enums.all()

            if not property_enums:
                role_enums = self.session().query(UserDefinedEnumModel).filter(UserDefinedEnumModel.role == role).all()
                if role_enums:
                    return role_enums
                else:
                    return global_enums.all()

            return property_enums

        if role and not property_id:
            role_enums = self.session().query(UserDefinedEnumModel).filter(UserDefinedEnumModel.role == role).filter(
                UserDefinedEnumModel.property_id.is_(None))
            if enum_names:
                role_enums = role_enums.filter(UserDefinedEnumModel.enum_name.in_(enum_names))
            role_enums = role_enums.all()
            if not role_enums:
                global_enums = global_enums.all()
                return global_enums
            return role_enums

        if enum_names:
            global_enums = global_enums.filter(UserDefinedEnumModel.enum_name.in_(enum_names))
        return global_enums
