from collections import defaultdict
from sqlalchemy import or_

from cataloging_service.infrastructure.repositories.available_config_repository import AvailableConfigRepository
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.repositories.tenant_config_adaptor import TenantConfigAdaptor
from cataloging_service.models import TenantConfigModel


class TenantConfigRepository(BaseEntityRepository):
    adaptor = TenantConfigAdaptor()
    available_config_repository = AvailableConfigRepository()

    def load(self, property_id=None, seller_id=None):
        available_configs = self.available_config_repository.load_all()
        grouped_available_configs = {config.name: config for config in available_configs}
        global_configs = self.session().query(TenantConfigModel).filter(TenantConfigModel.property_id == None,
                                                                        TenantConfigModel.active == True).all()
        if not property_id:
            return [self.adaptor.to_domain_entity(config, grouped_available_configs) for config in global_configs]

        property_configs = self._load_property_config(property_id)

        property_config_names = {property_config.config_name for property_config in property_configs}
        property_configs.extend([global_config for global_config in global_configs if
                                 global_config.config_name not in property_config_names])
        if not seller_id:
            return [self.adaptor.to_domain_entity(config, grouped_available_configs) for config in property_configs]

        seller_configs = self._load_seller_config(seller_id)
        seller_config_names = {seller_config.config_name for seller_config in seller_configs}
        seller_configs.extend([property_config for property_config in property_configs if
                               property_config.config_name not in seller_config_names])
        return [self.adaptor.to_domain_entity(config, grouped_available_configs) for config in seller_configs]

    def _load_seller_config(self, seller_id):
        return self.session().query(TenantConfigModel).filter(TenantConfigModel.seller_id == seller_id,
                                                              TenantConfigModel.active == True).all()

    def _load_property_config(self, property_id):
        return self.session().query(TenantConfigModel).filter(TenantConfigModel.property_id == property_id,
                                                              TenantConfigModel.active == True).all()

    def create_or_update_tenant_config(self, parsed_request, property_id=None):
        available_config = self.available_config_repository.get_or_create_config(parsed_request['config_name'],
                                                                                 parsed_request['value_type'])
        model = self.session().query(TenantConfigModel).filter(TenantConfigModel.property_id == property_id,
                                                               TenantConfigModel.config_name == parsed_request[
                                                                   'config_name']).first()
        if not model:
            model = TenantConfigModel()
            model.config_name = parsed_request.get('config_name')
            model.property_id = property_id
            model.config_value = parsed_request.get('config_value')
            model.active = parsed_request['active'] if parsed_request.get('active') else True
            self.persist(model)
            return self.adaptor.to_domain_entity(model,
                                                 {parsed_request.get('config_name'): available_config})
        model.active = parsed_request['active'] if parsed_request.get('active') else True
        model.config_value = parsed_request['config_value']
        self.persist(model)
        return self.adaptor.to_domain_entity(model,
                                             {parsed_request.get('config_name'): available_config})

    def create_tenant_config(self, parsed_request, property_id=None):
        available_config = self.available_config_repository.get_or_create_config(parsed_request['config_name'],
                                                                                 parsed_request['value_type'])
        model = TenantConfigModel()
        model.config_name = parsed_request.get('config_name')
        model.property_id = property_id
        model.config_value = parsed_request.get('config_value')
        model.active = parsed_request['active'] if parsed_request.get('active') else True
        self.persist(model)
        return self.adaptor.to_domain_entity(model,
                                             {parsed_request.get('config_name'): available_config})

    def update_tenant_config(self, parsed_request, property_id=None):
        available_config = self.available_config_repository.get_or_create_config(parsed_request['config_name'],
                                                                                 parsed_request['value_type'])
        model = self.session().query(TenantConfigModel).filter(TenantConfigModel.property_id == property_id,
                                                               TenantConfigModel.config_name == parsed_request[
                                                                   'config_name']).first()
        if not model:
            raise ValueError('model with given request does not exist')
        model.active = parsed_request['active'] if parsed_request.get('active') else True
        model.config_value = parsed_request['config_value']
        self.persist(model)
        return self.adaptor.to_domain_entity(model,
                                             {parsed_request.get('config_name'): available_config})

    def load_v2(self, property_id=None, seller_id=None, config_name=None):
        """
        Returns configs which are applicable with the given query params.
        Note: It returns multiple configs having same name therefore need more filtering.
        """
        query = self.session().query(TenantConfigModel).filter(TenantConfigModel.active == True)
        if not (property_id or seller_id or config_name):
            query = query.filter(
                TenantConfigModel.property_id == None, TenantConfigModel.seller_id == None
            )
        if property_id:
            query = query.filter(
                or_(TenantConfigModel.property_id == property_id, TenantConfigModel.property_id == None)
            )
        if seller_id:
            query = query.filter(
                or_(TenantConfigModel.seller_id == seller_id, TenantConfigModel.seller_id == None)
            )
        if config_name:
            query = query.filter(TenantConfigModel.config_name == config_name)
        configs = query.all()

        available_configs = self.available_config_repository.load_all()
        grouped_available_configs = {config.name: config for config in available_configs}
        grouped_configs = defaultdict(list)
        for config in configs:
            grouped_configs[config.config_name].append(config)

        def sort_key(config):
            """
            update score based on more priority attribute
            """
            score = 0
            if property_id and config.property_id == property_id:
                score += 2
            if seller_id and config.seller_id == seller_id:
                score += 1
            # Note: If configs are requested for only property then try to return config where seller is none if same config is present with seller and without seller
            if not seller_id and config.seller_id:
                score -= 1
            return score

        applicable_configs = []
        for config_name, config_list in grouped_configs.items():
            sorted_list = sorted(config_list, key=sort_key, reverse=True)
            applicable_configs.append(sorted_list[0])

        return [self.adaptor.to_domain_entity(config, grouped_available_configs) for config in applicable_configs]
