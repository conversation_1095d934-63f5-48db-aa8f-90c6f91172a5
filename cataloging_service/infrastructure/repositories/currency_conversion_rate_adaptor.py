from cataloging_service.domain.entities.currency_conversion_rate import CurrencyConversionRate
from cataloging_service.models import CurrencyConversionRateModel


class CurrencyConversionRateAdaptor(object):

    def to_db_model(self, currency_conversion_rate: CurrencyConversionRate):
        return CurrencyConversionRateModel(to_currency=currency_conversion_rate.to_currency,
                                           from_currency=currency_conversion_rate.from_currency,
                                           conversion_rate=currency_conversion_rate.conversion_rate,
                                           start_date=currency_conversion_rate.start_date,
                                           end_date=currency_conversion_rate.end_date,
                                           property_id=currency_conversion_rate.property_id)

    def to_domain_entity(self, currency_conversion_rate_model: CurrencyConversionRateModel):
        return CurrencyConversionRate(to_currency=currency_conversion_rate_model.to_currency,
                                      from_currency=currency_conversion_rate_model.from_currency,
                                      conversion_rate=currency_conversion_rate_model.conversion_rate,
                                      start_date=currency_conversion_rate_model.start_date,
                                      end_date=currency_conversion_rate_model.end_date,
                                      property_id=currency_conversion_rate_model.property_id)
