import sqlalchemy
from sqlalchemy.orm import lazyload
from sqlalchemy.orm.exc import NoResultFound, MultipleResultsFound, FlushError

from treebo_commons.multitenancy.sqlalchemy import db_engine

from cataloging_service.exceptions import ValidationException, PrimaryKeyCollision, DatabaseError, DatabaseLockError
from cataloging_service.thread_locals import app_context


def handle_integrity_error(exception):
    raise PrimaryKeyCollision(description=exception.__str__())


def handle_database_exception(exception):
    if isinstance(exception, sqlalchemy.exc.IntegrityError) or isinstance(exception, FlushError):
        handle_integrity_error(exception)

    if isinstance(exception, sqlalchemy.exc.OperationalError):
        if "could not obtain lock".upper() in exception.__str__().upper():
            raise DatabaseLockError(description=exception.__str__())

    raise DatabaseError(exception.__str__())


class BaseRepository:
    @staticmethod
    def _include_test():
        return app_context.include_test

    def session(self):
        return db_engine.get_session()

    def pre_filtered_query(self, model, include_test=True):
        if not include_test:
            include_test = self._include_test()

        query = self.session().query(model)
        if not hasattr(model, 'is_test'):
            return query

        if include_test:
            return query
        else:
            return query.filter_by(is_test=False)

    def rget_by_attr(self, model, limit_one=False, **queries):
        """
        :param limit_one:
        :param model:
        :param queries:
        :return:
        """
        queryset = self.session().query(model)

        for attr, value in queries.items():
            if value and isinstance(value, (list,)):
                queryset = queryset.filter(getattr(model, attr).in_(value))
            elif value:
                value = "%s" % value
                queryset = queryset.filter(getattr(model, attr) == value)

        if limit_one:
            return queryset.first()

        return queryset.all()

    def persist(self, unsaved_object):
        self.session().add(unsaved_object)
        self.session().flush()

        return unsaved_object

    def _update(self, unsaved_object):
        saved_object = self.session().merge(unsaved_object)
        self.session().flush()
        return saved_object

    def _update_all(self, unsaved_objects):
        saved_objects = [self.session().merge(unsaved_object) for unsaved_object in unsaved_objects]
        self.session().flush()
        return saved_objects

    def persist_all(self, model_object_list):
        self.session().add_all(model_object_list)
        self.session().flush()
        return model_object_list

    def delete(self, item):
        self.session().delete(item)
        self.session().flush()

    def delete_all(self, items):
        [self.session().delete(item) for item in items]
        self.session().flush()

    def get_for_update(self, model, nowait=True, **queries):
        """
        The query will lock the row that is returned.
        If the transaction cannot lock the row (which will happen when some other transactions have obtained the lock),
        then:
            - If `nowait=True`, the query will fail with error
            - If `nowait=False`, the query will wait for the lock to get released.

        :param model:
        :param nowait:
        :param queries:
        :return:
        """
        queryset = self.session().query(model)
        for attr, value in queries.items():
            if value and isinstance(value, (list,)):
                queryset = queryset.filter(getattr(model, attr).in_(value))
            elif value:
                value = "%s" % value
                queryset = queryset.filter(getattr(model, attr) == value)
            elif value == False:
                queryset = queryset.filter(getattr(model, attr) == value)

        # Forcing lazy load here because
        # https://www.postgresql.org/message-id/<EMAIL>
        queryset = queryset.options(lazyload("*"))
        try:
            return queryset.with_for_update(of=model, nowait=nowait)
        except Exception as exp:
            handle_database_exception(exp)
