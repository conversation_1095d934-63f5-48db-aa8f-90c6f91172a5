from sqlalchemy import func, or_
from sqlalchemy.orm import joinedload
from typing import List


from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import Combo, ComboItem


class ComboRepository(BaseRepository):
    def get_combo(self, combo_id):
        return self.pre_filtered_query(Combo).filter_by(id=combo_id, is_deleted=False) \
            .options(joinedload(Combo.combo_items)) \
            .first()

    def load_for_update(self, combo_id, seller_id):
        return self.get_for_update(model=Combo, id=combo_id, seller_id=seller_id) \
            .options(joinedload(Combo.combo_items)) \
            .first()

    def get_all_combos(self, seller_id, name, food_type):
        query = self.pre_filtered_query(Combo).filter_by(seller_id=seller_id, is_deleted=False)\
            .filter(or_(Combo.name.ilike('%' + name + '%'), Combo.display_name.ilike('%' + name + '%'),
                        Combo.code.ilike('%' + name + '%')))
        if food_type is not None:
            query = query.filter_by(food_type=food_type)
        query = query.order_by(Combo.modified_at.desc())
        return query.all()

    def get_combos_via_item_id(self, item_id):
        return self.get_for_update(model=Combo)\
            .join(ComboItem, Combo.combo_items)\
            .filter(ComboItem.item_id == item_id)\
            .all()

    def get_combos_in_property(self, property_id, code, combo_id):
        queryset = self.pre_filtered_query(Combo).filter_by(
            code=code).join(Combo.seller).filter_by(property_id=property_id)
        if combo_id:
            queryset = queryset.filter(Combo.id != combo_id)
        return queryset.all()
