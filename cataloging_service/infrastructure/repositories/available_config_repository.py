from cataloging_service.infrastructure.repositories.available_config_adaptor import AvailableConfigAdaptor
from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.models import AvailableConfigModel, AllowedConfigValueModel


class AvailableConfigRepository(BaseEntityRepository):
    adaptor = AvailableConfigAdaptor()

    def load_all(self):
        models = self.session().query(AvailableConfigModel).all()
        return [self.adaptor.to_domain_entity(model) for model in models]

    def load(self, config_name):
        model = self.session().query(AvailableConfigModel).filter(AvailableConfigModel.name == config_name).first()
        allowed_config_values = self.session().query(AllowedConfigValueModel).filter(
            AllowedConfigValueModel.config_name == config_name).all()
        if not model:
            return None
        return self.adaptor.to_domain_entity(model, allowed_config_values)

    def get_or_create_config(self, config_name, value_type, description=None):
        model = self.session().query(AvailableConfigModel).filter(AvailableConfigModel.name == config_name).first()
        if not model:
            model = AvailableConfigModel()
            model.name = config_name
            model.value_type = value_type
            model.description = description
            self.persist(model)
        return self.adaptor.to_domain_entity(model)
