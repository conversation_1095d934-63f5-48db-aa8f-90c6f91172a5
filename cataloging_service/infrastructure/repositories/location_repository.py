from os import stat
from sqlalchemy import func, or_
from sqlalchemy.orm import joinedload

from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import (
    Locality,
    MicroMarket,
    Country,
    State,
    City,
    Cluster,
    Region,
    Location,
    CityAlias,
)


class LocationRepository(BaseRepository):
    def get_locations(self, property_ids):
        return (
            Location.query.filter(Location.property_id.in_(property_ids))
            .options(joinedload(Location.micro_market))
            .options(joinedload(Location.city).joinedload(City.state))
            .options(joinedload(Location.city).joinedload(City.aliases))
            .options(joinedload(Location.legal_city).joinedload(City.aliases))
            .options(joinedload(Location.legal_city).joinedload(City.state))
            .options(joinedload(Location.locality))
            .all()
        )

    def get_all_localities(self):
        return Locality.query.all()

    def get_all_related_localities(self, filter):
        return (
            Locality.query.join(MicroMarket)
            .join(City)
            .filter(
                or_(
                    Locality.name.ilike(filter),
                    MicroMarket.name.ilike(filter),
                    City.name.ilike(filter),
                )
            )
        )

    def get_all_micro_markets(self):
        return MicroMarket.query.all()

    def get_all_countries(self):
        return Country.query.all()

    def get_country(self, country_id):
        return Country.query.get(country_id)

    def get_country_by_name(self, country_name):
        return Country.query.filter(func.lower(Country.name) == func.lower(country_name)).first()

    def get_state_by_name(self, state_name, country_id):
        return State.query.filter(
            State.name.ilike("%" + state_name.lower() + "%"), State.country_id == country_id
        ).first()

    def get_city_by_name(self, city_name, state_id):
        return City.query.filter(
            func.lower(City.name) == func.lower(city_name), City.state_id == state_id
        ).first()

    def get_cluster_by_name(self, cluster_name):
        return Cluster.query.filter(func.lower(Cluster.name) == func.lower(cluster_name)).first()

    def get_locality_by_name(self, locality_name, city_id):
        return Locality.query.filter(
            func.lower(Locality.name) == func.lower(locality_name), Locality.city_id == city_id
        ).first()

    def get_micro_market_by_name(self, micro_market_name, city_id):
        return MicroMarket.query.filter(
            func.lower(MicroMarket.name) == func.lower(micro_market_name),
            MicroMarket.city_id == city_id,
        ).first()

    def get_all_states(self):
        return State.query.all()

    def get_state(self, state_id):
        return State.query.filter(State.id == state_id).first()

    def get_city(self, city_id):
        return City.query.get(city_id)

    def get_city_by_name_strict(self, city_name):
        return City.query.filter(func.lower(City.name) == func.lower(city_name)).first()

    def get_all_cities(self, ids=None):
        query = City.query
        if ids and hasattr(ids, "__iter__"):
            query = query.filter(City.id.in_(ids))

        return query.all()

    def get_all_related_cities(self, filter=None):
        return City.query.outerjoin(CityAlias).filter(
            or_(
                City.name.ilike(filter),
                CityAlias.name.ilike(filter),
            )
        )

    def get_all_regions(self):
        return Region.query.all()

    def get_region(self, region_id):
        return Region.query.get(region_id)

    def get_cluster(self, cluster_id):
        return Cluster.query.get(cluster_id)

    def get_city_by_cluster_and_state(self, cluster_id, state_id):
        return (
            City.query.filter(City.cluster_id == cluster_id).filter(City.state_id == state_id).all()
        )

    def save_city(self, city_model):
        self.persist(city_model)
