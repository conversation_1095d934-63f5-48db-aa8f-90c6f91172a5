from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import GoogleDriveBaseFolder, GoogleDriveFile


class GoogleDriveRepository(BaseRepository):
    def get_property_folder(self, property_id):
        return GoogleDriveBaseFolder.query.filter_by(property_id=property_id).first()

    def get_folder_by_name(self, folder_name):
        return GoogleDriveBaseFolder.query.filter_by(folder_name=folder_name).first()

    def get_property_files(self, property_id, file_type):
        return GoogleDriveFile.query.filter_by(property_id=property_id, file_type=file_type)

    def get_distinct_property_file_types(self, property_id):
        file_types = set()
        for file in GoogleDriveFile.query.filter_by(property_id=property_id).distinct(GoogleDriveFile.file_type):
            file_types.add(file.file_type)

        return file_types

    def get_property_base_folder(self, property_id):
        return GoogleDriveBaseFolder.query.filter_by(property_id=property_id).first()
