from sqlalchemy import func, text
from sqlalchemy.orm import subqueryload, contains_eager, joinedload, load_only, attributes

from cataloging_service.constants.constants import NON_INCLUSION_SKU_CATEGORIES
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.infrastructure.repositories.pagination import Pagination
from cataloging_service.models import SkuCategory, S<PERSON>, PropertyS<PERSON>, <PERSON><PERSON>, SellerSkuCategory
from cataloging_service.thread_locals import app_context


class SkuRepository(BaseRepository):

    @staticmethod
    def rget_sku_category(id):
        return SkuCategory.query.get(id)

    @staticmethod
    def rget_sku_categories():
        return SkuCategory.query.all()

    @staticmethod
    def rget_sku_category_by_code(code):
        return SkuCategory.query.filter(SkuCategory.code == code).first()

    @staticmethod
    def rget_all_sku_categories(ids=None):
        query = SkuCategory.query

        if ids and hasattr(ids, "__iter__"):
            query = query.filter(SkuCategory.id.in_(ids))

        return query.all()

    @staticmethod
    def rget_all_sku_categories_by_codes(codes=None):
        query = SkuCategory.query

        if codes and hasattr(codes, "__iter__"):
            query = query.filter(SkuCategory.code.in_(codes))

        return query.all()

    @staticmethod
    def rget_sku_by_codes(codes=None):
        query = Sku.query

        if codes and hasattr(codes, "__iter__"):
            query = query.filter(Sku.code.in_(codes))

        return query.all()

    @classmethod
    def rget_all_sku_by_status_and_codes(cls, page, items_per_page, codes=None):
        query = Sku.query.options(subqueryload(Sku.bundle))

        # If codes have been asked for
        if codes:
            query = query.filter(Sku.code.in_(codes))

        # If pagination requested
        if page and page > 0:
            return Pagination.paginate(query, page=page, per_page=items_per_page, error_out=False).items

        return query.all()

    @staticmethod
    def rget_all_sku_charged_per_occupant(codes=None):
        query = Sku.query

        if codes and hasattr(codes, "__iter__"):
            query = query.filter(Sku.chargeable_per_occupant.is_(True),
                                 Sku.is_modular.is_(True),
                                 Sku.code.in_(codes))

        return query.all()

    @staticmethod
    def rget_sku_by_name(name):
        query = Sku.query.filter(Sku.name == name)
        if name:
            return query.first()
        return None

    @staticmethod
    def rget_sku_by_id(ids=None):
        query = Sku.query.options(subqueryload(Sku.bundle))

        if ids and hasattr(ids, "__iter__"):
            query = query.filter(Sku.id.in_(ids))

        return query.all()

    @staticmethod
    def rget_sku_by_code(code):
        return Sku.query.filter(Sku.code == code).first()

    @staticmethod
    def rget_sku_by_identifier(identifier):
        return Sku.query.filter(Sku.identifier == identifier).first()

    @staticmethod
    def rget_sku_for_date_range_greater_than(date):
        query = Sku.query.filter(Sku.created_at >= date)
        return query.all()

    @staticmethod
    def get_property_sku_by_ids(ids):
        return PropertySku.query.filter(PropertySku.id.in_(ids)).all()

    @staticmethod
    def get_property_sku(property_id, sku_id):
        return PropertySku.query.filter(PropertySku.property_id == property_id, PropertySku.sku_id == sku_id).first()

    @staticmethod
    def rget_property_sku_for_date_range_greater_than(date):
        query = PropertySku.query.filter(PropertySku.created_at >= date)
        return query.all()

    def rcheck_if_sku_identifier_exists(self, identifier):
        """
        scalar() will produce MultipleResultsFound exception if multiple results are found,
        Since identifier is pre-calculated and checked against existing ones, so it might not produce
        the error, if at all an error is produced, it will be handled by the global exception handler
        and dev has to look into it.
        :param identifier:
        :return:
        """
        return self.session().query(Sku.code).filter_by(identifier=identifier).scalar() is not None

    @staticmethod
    def rget_skus_not_present_in_codes(codes=None):
        query = Sku.query

        if codes and hasattr(codes, "__iter__"):
            query = query.filter(Sku.code.notin_(codes))

        return query.all()

    def rget_newly_created_sku(self):
        query = self.session().query(Sku).from_statement(text(
            "select * from sku where is_modular=False and sku.sku_count > "
            "(select count(*) from sku_bundle where bundle_id=sku.id)"))
        return query.all()

    def rget_sku_by_name_category_sku_type_and_is_modular(self, sku_name, sku_cat_code, is_modular):
        return Sku.query.filter(func.lower(Sku.name) == sku_name, SkuCategory.code == sku_cat_code,
                                Sku.is_modular.is_(is_modular)).first()

    @cache.clear_cache_on_model_change(
        model_classes_with_cache_key_getter={
            Sku: lambda x: x.category.code,
            SkuCategory: lambda x: x.code
        })
    @cache.memoize_unhashed_key(attribute_for_cache_key='sku_cat_code', timeout=7200,
                                unless=lambda: app_context.include_test)
    def get_modular_skus_for_category(self, sku_cat_code):
        query = self.session().query(Sku)
        query = query.options(
            load_only('id', 'code', 'name'),
            joinedload(Sku.category, innerjoin=True)
        )
        query = query.join(Sku.category).options(contains_eager(Sku.category)).filter(SkuCategory.code == sku_cat_code,
                                                                                      Sku.is_modular.is_(True))
        return query.all()

    def rget_all_stay_sku_by_codes(self, codes=None):
        query = self.session().query(Sku).filter(Sku.code.in_(codes),
                                                 Sku.is_modular.is_(True),
                                                 func.lower(SkuCategory.code) == 'stay')
        return query.all()

    def rget_skus_by_tags(self, tags):
        return Sku.query.filter(func.lower(Sku.tag).in_(tags)).all()

    def save_sku(self, sku_model):
        self.persist(sku_model)

    def save_property_sku(self, property_sku):
        attributes.flag_modified(property_sku, 'extra_information')
        self.persist(property_sku)

    def save_seller_sku(self, seller_sku):
        self.persist(seller_sku)

    def get_sku_categories_for_seller(self, seller_id):
        return self.pre_filtered_query(SkuCategory).join(SkuCategory.seller_sku_categories)\
            .filter(SellerSkuCategory.seller_id == seller_id).all()

    def get_property_skus(self, property_id, sku_codes=None, sku_category_codes=None, for_inclusions=None):
        query = self.session().query(PropertySku).options(joinedload(PropertySku.sku).joinedload(Sku.category)) \
            .filter(PropertySku.property_id == property_id)

        query = query.options(contains_eager(PropertySku.sku).contains_eager(Sku.category)).join(Sku).join(
            SkuCategory)

        if sku_codes and isinstance(sku_codes, list):
            query = query.filter(Sku.code.in_(sku_codes))

        if sku_category_codes:
            query = query.filter(SkuCategory.code.in_(sku_category_codes))

        if for_inclusions:
            query = query.filter(Sku.is_property_inclusion.is_(True))

        return query.order_by(PropertySku.sku_id).all()
    
    def get_property_default_skus(self):
        query = Sku.query.filter(Sku.is_default_sku_for_property_launch == True)
        return query.all()
