from sqlalchemy import func, or_
from sqlalchemy.orm import joinedload
from typing import List


from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import ComboItem, Menu, MenuItem, Item, MenuCombo, MenuItemCategory, \
    RestaurantMenuCategory, Combo


class MenuRepository(BaseRepository):
    def get_menu(self, menu_id):
        query = self.pre_filtered_query(Menu).filter_by(id=menu_id, is_deleted=False) \
            .options(joinedload(Menu.menu_timings)) \
            .options(joinedload(Menu.menu_categories)) \
            .options(joinedload(Menu.menu_items).joinedload(MenuItem.menu_item_categories)) \
            .options(joinedload(Menu.menu_items).joinedload(MenuItem.item)) \
            .options(joinedload(Menu.menu_items).joinedload(MenuItem.menu_item_item_variants))\
            .options(joinedload(Menu.menu_items).joinedload(MenuItem.menu_item_item_customisations))\
            .options(joinedload(Menu.menu_items).joinedload(MenuItem.menu_item_side_items))\
            .options(joinedload(Menu.menu_combos).joinedload(MenuCombo.menu_combo_categories)) \
            .options(joinedload(Menu.menu_combos).joinedload(MenuCombo.combo))
        return query.first()

    def get_menu_items(self, seller_id, name, menu_type):
        queryset = self.pre_filtered_query(MenuItem).filter_by(is_deleted=False)\
            .join(MenuItem.menu).join(Menu.menu_timings, isouter=True).join(MenuItem.item)\
            .filter(Menu.seller_id == seller_id).filter(Item.is_deleted == False)\
            .filter(Menu.is_deleted == False)

        if name:
            queryset = queryset.filter(or_(Item.name.ilike('%' + name + '%'),
                                           Item.display_name.ilike('%' + name + '%'),
                                           Item.code.ilike('%' + name + '%')))
        if menu_type:
            queryset = queryset.filter(Menu.menu_types.any(menu_type))

        return queryset.all()

    def load_for_update(self, menu_id, seller_id):
        return self.get_for_update(model=Menu, id=menu_id, seller_id=seller_id, is_deleted=False) \
            .options(joinedload(Menu.menu_timings)) \
            .options(joinedload(Menu.menu_categories)) \
            .options(joinedload(Menu.menu_items, MenuItem.menu_item_categories)) \
            .options(joinedload(Menu.menu_items, MenuItem.menu_item_item_variants)) \
            .options(joinedload(Menu.menu_items, MenuItem.menu_item_side_items)) \
            .options(joinedload(Menu.menu_combos, MenuCombo.menu_combo_categories)) \
            .first()

    def get_all_menus(self, seller_id, name, menu_type, menu_ids=None):
        queryset = self.pre_filtered_query(Menu).filter_by(seller_id=seller_id, is_deleted=False)
        if name:
            queryset = queryset.filter(or_(Menu.name.ilike('%' + name + '%'),
                                           Menu.display_name.ilike('%' + name + '%'),
                                           Menu.code.ilike('%' + name + '%')))

        if menu_ids:
            queryset = queryset.filter(Menu.id.in_(menu_ids))
        if menu_type:
            queryset = queryset.filter(Menu.menu_types.any(menu_type))
        queryset = queryset.order_by(Menu.modified_at.desc())
        return queryset.options(joinedload(Menu.menu_timings)).all()

    def get_menus_with_categories_and_item_counts(self, seller_id, menu_ids=None):
        queryset = self.pre_filtered_query(Menu).filter_by(seller_id=seller_id, is_deleted=False)\
            .options(joinedload(Menu.menu_categories, RestaurantMenuCategory.menu_item_categories,
                                MenuItemCategory.menu_item))\
            .options(joinedload(Menu.menu_timings)) \
            .options(joinedload(Menu.menu_combos, MenuCombo.combo, Combo.combo_items, ComboItem.item))\
            .options(joinedload(Menu.menu_items, MenuItem.item))\
            .options(joinedload(Menu.menu_items, MenuItem.menu_item_categories)) \
            .options(joinedload(Menu.menu_items, MenuItem.menu_item_item_variants)) \
            .options(joinedload(Menu.menu_items, MenuItem.menu_item_side_items))

        if menu_ids:
            queryset = queryset.filter(Menu.id.in_(menu_ids))
        return queryset.all()

    def load_menu_combo_for_update(self, menu_combo_id, menu_id):
        return self.get_for_update(model=Menu, id=menu_id, is_deleted=False) \
            .options(joinedload(Menu.menu_combos, MenuCombo.menu_combo_categories)) \
            .first()

    def load_menu_item(self, menu_item_id):
        return self.pre_filtered_query(MenuItem).filter_by(id=menu_item_id, is_deleted=False) \
            .options(joinedload(MenuItem.item)) \
            .options(joinedload(MenuItem.menu_item_item_variants)) \
            .options(joinedload(MenuItem.menu_item_side_items)) \
            .options(joinedload(MenuItem.menu_item_item_customisations)) \
            .first()

    def load_menu_item_for_update(self, menu_item_id):
        return self.get_for_update(model=MenuItem, id=menu_item_id, is_deleted=False) \
            .options(joinedload(MenuItem.menu_item_item_variants)) \
            .options(joinedload(MenuItem.menu_item_side_items)) \
            .options(joinedload(MenuItem.menu_item_item_customisations)) \
            .first()

    def get_menus_in_property(self, menu_id, property_id, code):
        queryset = self.pre_filtered_query(Menu).filter_by(code=code).join(Menu.seller)\
            .filter_by(property_id=property_id)
        if menu_id:
            queryset = queryset.filter(Menu.id != menu_id)
        return queryset.all()
