from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import Provider, RoomTypeConfiguration


class ProviderRepository(BaseRepository):

    def rget_provider_by_code(self, code):
        return Provider.query.filter(Provider.code == code).first()

    def rget_provider_by_name(self, name):
        return Provider.query.filter(Provider.name == name).first()

    def rget_all_providers(self, codes=None):
        query = Provider.query

        if codes and hasattr(codes, "__iter__"):
            query = query.filter(Provider.code.in_(codes))

        return query.filter(Provider.status == 'ACTIVE').all()

    def rget_all_ext_room_configs_property(self, provider=None):
        query = RoomTypeConfiguration.query

        if type(provider) is Provider and provider.id:
            query = query.filter(RoomTypeConfiguration.ext_id == provider.id)

        return query.distinct(RoomTypeConfiguration.ext_room_code, RoomTypeConfiguration.ext_id).all()

