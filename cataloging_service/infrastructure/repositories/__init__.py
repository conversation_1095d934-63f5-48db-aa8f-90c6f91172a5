from cataloging_service.infrastructure.repositories.kitchen_repository import KitchenRepository
from cataloging_service.client.crs_client import CR<PERSON>lient
from cataloging_service.infrastructure.repositories.available_config_repository import AvailableConfigRepository
from cataloging_service.infrastructure.repositories.channel_repository import ChannelRepository
from cataloging_service.infrastructure.repositories.combo_repository import ComboRepository
from cataloging_service.infrastructure.repositories.combo_repository import ComboRepository
from cataloging_service.infrastructure.repositories.currency_conversion_rate_repository import \
    CurrencyConversionRateRepository
from cataloging_service.infrastructure.repositories.file_repository import GoogleDriveRepository
from cataloging_service.infrastructure.repositories.hotel_repository import PropertyRepository
from cataloging_service.infrastructure.repositories.item_repository import ItemRepository
from cataloging_service.infrastructure.repositories.item_repository import ItemRepository
from cataloging_service.infrastructure.repositories.location_repository import LocationRepository
from cataloging_service.infrastructure.repositories.menu_repository import MenuRepository
from cataloging_service.infrastructure.repositories.menu_repository import MenuRepository
from cataloging_service.infrastructure.repositories.owner_repository import OwnerRepository
from cataloging_service.infrastructure.repositories.ownership_repository import OwnershipRepository
from cataloging_service.infrastructure.repositories.property_policy_map_repository import PropertyPolicyMapRepository
from cataloging_service.infrastructure.repositories.provider_repository import ProviderRepository
from cataloging_service.infrastructure.repositories.rate_plan_addon import RatePlanAddonRepository
from cataloging_service.infrastructure.repositories.rate_plan_config_repository import NewRatePlanConfigRepository
from cataloging_service.infrastructure.repositories.rate_plan_repository import NewRatePlanRepository
from cataloging_service.infrastructure.repositories.repository import MetaRepository
from cataloging_service.infrastructure.repositories.restaurant_area_repository import RestaurantAreaRepository
from cataloging_service.infrastructure.repositories.restaurant_table_repository import RestaurantTableRepository
from cataloging_service.infrastructure.repositories.room_rack_rate_repository import RoomRackRateRepository
from cataloging_service.infrastructure.repositories.ruptub_legal_entity_repository import LegalEntityDetailsRepository
from cataloging_service.infrastructure.repositories.seller_repository import SellerRepository
from cataloging_service.infrastructure.repositories.seller_sku_repository import SellerSkuRepository, \
    MenuCategoryRepository
from cataloging_service.infrastructure.repositories.seller_type_history_repository import SellerTypeHistoryRepository
from cataloging_service.infrastructure.repositories.sku_pricing_repository import SkuPricingRepository
from cataloging_service.infrastructure.repositories.sku_redis_repository import SkuRedisRepository
from cataloging_service.infrastructure.repositories.sku_repository import SkuRepository
from cataloging_service.infrastructure.repositories.tenant_config_repository import TenantConfigRepository
from cataloging_service.infrastructure.repositories.menu_repository import MenuRepository
from cataloging_service.infrastructure.repositories.item_repository import ItemRepository
from cataloging_service.infrastructure.repositories.combo_repository import ComboRepository
from cataloging_service.infrastructure.repositories.restaurant_area_repository import RestaurantAreaRepository
from cataloging_service.infrastructure.repositories.kitchen_repository import KitchenRepository


class RepoProvider:
    def __init__(self):
        self.google_drive_repository = GoogleDriveRepository()
        self.meta_repository = MetaRepository()
        self.property_repository = PropertyRepository()
        self.room_rack_rate_repository = RoomRackRateRepository()
        self.property_location_repository = LocationRepository()
        self.owner_repository = OwnerRepository()
        self.sku_repository = SkuRepository()
        self.channel_repository = ChannelRepository()
        self.provider_repository = ProviderRepository()
        self.sku_redis_repository = SkuRedisRepository()
        self.seller_type_history_repository = SellerTypeHistoryRepository()
        self.legal_entity_details_repository = LegalEntityDetailsRepository()
        self.property_policy_map_repository = PropertyPolicyMapRepository()
        self.ownership_repository = OwnershipRepository()
        self.seller_sku_repository = SellerSkuRepository()
        self.seller_repository = SellerRepository()
        self.menu_category_repository = MenuCategoryRepository()
        self.restaurant_table_repository = RestaurantTableRepository()
        self.rate_plan_repository = NewRatePlanRepository()
        self.rate_plan_config_repository = NewRatePlanConfigRepository()
        self.rate_plan_addon_repository = RatePlanAddonRepository()
        self.sku_pricing_repository = SkuPricingRepository()
        self.menu_repository = MenuRepository()
        self.item_repository = ItemRepository()
        self.combo_repository = ComboRepository()
        self.restaurant_area_repository = RestaurantAreaRepository()
        self.kitchen_repository = KitchenRepository()
        self.crs_client = CRSClient()
        self.currency_conversion_rate_repository = CurrencyConversionRateRepository()


repo_provider = RepoProvider()
