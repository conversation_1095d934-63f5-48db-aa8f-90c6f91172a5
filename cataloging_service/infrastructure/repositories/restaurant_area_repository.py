from sqlalchemy.orm import joinedload
from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import RestaurantArea, RestaurantTable



class RestaurantAreaRepository(BaseRepository):
    def get_restaurant_area(self, seller_id, area_id):
        return self.pre_filtered_query(RestaurantArea).filter_by(id=area_id, seller_id=seller_id, is_deleted=False) \
            .options(joinedload(RestaurantArea.tables)) \
            .options(joinedload(RestaurantArea.tables, RestaurantTable.seats)) \
            .first()

    def get_restaurant_areas(self, seller_id):
        queryset = self.pre_filtered_query(RestaurantArea).filter_by(seller_id=seller_id, is_deleted=False)
        return queryset.options(joinedload(RestaurantArea.tables)) \
            .options(joinedload(RestaurantArea.tables, RestaurantTable.seats))\
                .order_by(RestaurantArea.modified_at.desc())

    def delete_all_tables_and_seats_in_area(self, restaurant_area):
        for restaurant_table in restaurant_area.tables:
            for table_seat in restaurant_table.seats:
                table_seat.delete()
            restaurant_table.delete()
        return restaurant_area
