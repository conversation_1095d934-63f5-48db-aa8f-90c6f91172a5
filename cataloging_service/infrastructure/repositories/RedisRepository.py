from cataloging_service.extensions import cache


class RedisRepository:

    @staticmethod
    def redis_save_one(key, value, timeout):
        """
        :param stored_hash:
        :param key:
        :param value:
        :param timeout:
        :return:
        """
        return cache.set(key, value, timeout=timeout)

    @staticmethod
    def redis_get_one(key):
        return cache.get(key=key)

    @staticmethod
    def redis_get_in(*key_list):
        return cache.get_many(*key_list)
