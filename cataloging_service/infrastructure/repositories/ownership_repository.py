from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import Ownership


class OwnershipRepository(BaseRepository):
    def get_ownership(self, property_id):
        if property_id:
            return Ownership.query.filter_by(property_id=property_id).all()

        return None

    def delete_ownership(self, property_id):
        if property_id:
            Ownership.query.filter_by(property_id=property_id).delete()
        return None
