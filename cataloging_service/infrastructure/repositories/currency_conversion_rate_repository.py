from sqlalchemy import func

from cataloging_service.infrastructure.repositories.base_entity_repository import BaseEntityRepository
from cataloging_service.infrastructure.repositories.currency_conversion_rate_adaptor import \
    CurrencyConversionRateAdaptor
from cataloging_service.models import CurrencyConversionRateModel


class CurrencyConversionRateRepository(BaseEntityRepository):
    adaptor = CurrencyConversionRateAdaptor()

    def load(self, property_id, transaction_date=None, from_currency=None):
        query = self.session().query(CurrencyConversionRateModel).filter(
            CurrencyConversionRateModel.property_id == property_id,
            CurrencyConversionRateModel.start_date <= transaction_date,
            CurrencyConversionRateModel.end_date >= transaction_date)

        sort_tuple = (CurrencyConversionRateModel.from_currency, CurrencyConversionRateModel.to_currency,
                      CurrencyConversionRateModel.modified_at.desc())
        distinct_tuple = (CurrencyConversionRateModel.to_currency, CurrencyConversionRateModel.from_currency)

        if from_currency:
            query = query.filter(func.lower(CurrencyConversionRateModel.from_currency) == func.lower(from_currency))

        models = query.order_by(*sort_tuple).distinct(*distinct_tuple).all()
        return [self.adaptor.to_domain_entity(model) for model in models]
