from cataloging_service.domain.entities.tenant_config.tenant_config import TenantConfig
from cataloging_service.models import TenantConfigModel


class TenantConfigAdaptor(object):
    def to_db_model(self, tenant_config: TenantConfig):
        return TenantConfigModel(config_name=tenant_config.config_name, config_value=tenant_config.config_value,
                                 property_id=tenant_config.property_id,
                                 active=tenant_config.active if tenant_config.active else False)

    def to_domain_entity(self, tenant_config_model: TenantConfigModel, grouped_available_configs):
        return TenantConfig(config=grouped_available_configs[tenant_config_model.config_name],
                            config_value=tenant_config_model.config_value, property_id=tenant_config_model.property_id)
