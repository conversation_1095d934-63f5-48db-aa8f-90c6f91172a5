from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import RestaurantTable, TableSeat


class RestaurantTableRepository(BaseRepository):

    def get_restaurant_tables(self, seller_id=None, table_id=None):
        query = RestaurantTable.query
        if table_id:
            query = query.filter(RestaurantTable.id == table_id)
        else:
            query = query.filter(RestaurantTable.is_deleted == False)
        if seller_id:
            query = query.filter(RestaurantTable.seller_id == seller_id)
        return query.all()

    def get_restaurant_table(self, seller_id=None, table_id=None):
        return self.pre_filtered_query(RestaurantTable).filter_by(seller_id=seller_id, id=table_id,
                                                                  is_deleted=False).first()

    def get_restaurant_tables_by_area_id(self, seller_id=None, area_id=None):
        return RestaurantTable.query.filter(
            RestaurantTable.area_id == area_id, RestaurantTable.seller_id == seller_id,
            RestaurantTable.is_deleted == False).all()

    def get_seats_by_table_id(self, table_id=None):
        return TableSeat.query.filter(
            TableSeat.table_id == table_id, TableSeat.is_deleted == False).all()
