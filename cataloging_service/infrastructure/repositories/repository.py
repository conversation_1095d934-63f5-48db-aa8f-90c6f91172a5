from sqlalchemy import func
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base

from cataloging_service.extensions import cache
from cataloging_service.infrastructure.repositories.base_repository import BaseRepository
from cataloging_service.models import <PERSON><PERSON>sine, GuestType, Notification, Param


class MetaRepository(BaseRepository):
    def get_all_cuisines(self):
        return Cuisine.query.all()

    def get_cuisine_by_name(self, name):
        return Cuisine.query.filter(func.lower(Cuisine.name) == func.lower(name)).first()

    @cache.memoize(timeout=3600 * 6)
    def get_all_guest_types(self):
        return GuestType.query.all()

    def get_guest_type_by_name(self, guest_type):
        return GuestType.query.filter(func.lower(GuestType.type) == func.lower(guest_type)).first()

    def get_notificaion_object(self, receiver_type):
        return Notification.query.filter_by(type=receiver_type).first()

    def get_db_time(self):
        return self.session().query(func.now()).scalar()

    def rget_all_entities(self):
        # TODO: Test this out, after moving from db.Model to Base
        return [cls.__name__ for cls in Base.registry._class_registry.values()
                if isinstance(cls, type) and issubclass(cls, Base)]

    def rget_entity_param(self, entity, field, validate):
        return Param.query.filter_by(entity=entity, field=field, validate=validate).all()

    def rget_entity_param_by_key_val(self, entity, field, value, validate):
        return Param.query.filter_by(entity=entity, field=field, value=value, validate=validate).first()

