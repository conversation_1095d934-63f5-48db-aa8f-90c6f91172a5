from cataloging_service.domain.entities.tenant_config.available_config import AvailableConfig, AllowedConfigValue
from cataloging_service.domain.enums import ConfigValueType
from cataloging_service.models import AvailableConfigModel


class AvailableConfigAdaptor(object):
    def to_db_model(self, available_config: AvailableConfig):
        return AvailableConfigModel(name=available_config.name, value_type=available_config.value_type.value)

    def to_domain_entity(self, model: AvailableConfigModel, allowed_config_values=None):
        if allowed_config_values:
            allowed_config_values = [AllowedConfigValue(acv.value, acv.label) for acv in allowed_config_values]
        return AvailableConfig(name=model.name, value_type=ConfigValueType(model.value_type),
                               allowed_values=allowed_config_values)
