import logging
import collections


class Helper:
    logger = logging.getLogger("Helper." + __name__)

    @staticmethod
    def _get_object(klass, **kwargs):
        class_object = klass()
        for attr, value in kwargs.items():
            if hasattr(class_object, attr):
                setattr(class_object, attr, value)
        return class_object

    @staticmethod
    def _get_object_from_named_tuple(klass, named_tuple):
        class_object = klass()
        for attr, value in named_tuple._asdict().items():
            if hasattr(class_object, attr):
                try:
                    setattr(class_object, attr, value)
                except TypeError:
                    Helper.logger.info(
                        "setattr failed for class {}, attr {}".format(klass, attr)
                    )
        return class_object

    @staticmethod
    def _get_named_tuple(named_tuple, **kwargs):
        intermediate_dict = dict()
        for attr, value in kwargs.items():
            if hasattr(named_tuple, attr):
                intermediate_dict[attr] = value
        return named_tuple(**intermediate_dict)


class SkuHelper(Helper):
    fields = ("id",
              "code",
              "name",
              "display_name",
              "is_modular",
              "tax_type",
              "sku_type",
              "hsn_sac",
              "saleable",
              "default_list_price",
              "default_sale_price",
              "category",
              "tag",
              "sku_count",
              "rt_sku_name",
              "rt_sku_count",
              "rt_adult_sku",
              "rt_adult_sku_count",
              "rt_child_sku",
              "rt_child_sku_count",
              "rt",
              "rt_code")

    tuple_sku = collections.namedtuple("Sku", fields)
    tuple_sku.__new__.__defaults__ = (None,) * len(tuple_sku._fields)

    @staticmethod
    def get_named_tuple_sku(sku, room_type_dict):
        """
        CAREFUL!! NamedTuple _replace() doesnt work
        :param sku:
        :param room_type_dict:
        :return:
        """

        sku_dict = sku.as_dict()
        if sku_dict.get('code', None) is None:
            sku_dict['code'] = str(sku_dict.get('id'))

        room_type_for_sku = room_type_dict.get(sku.name)
        if room_type_for_sku:
            sku_dict['rt'] = room_type_for_sku.type
            sku_dict['rt_code'] = room_type_for_sku.code

        # sku.bundle fires a query here
        for sku_bundle in sku.bundle:
            if sku_bundle.sku.name in room_type_dict:
                sku_dict['rt_sku_name'] = sku_bundle.sku.name
                sku_dict['rt_sku_count'] = sku_bundle.count
                room_type_for_sku = room_type_dict.get(sku_bundle.sku.name)
                sku_dict['rt'] = room_type_for_sku.type
                sku_dict['rt_code'] = room_type_for_sku.code
            elif 'adult' in sku_bundle.sku.name.lower():
                sku_dict['rt_adult_sku'] = sku_bundle.sku.name
                sku_dict['rt_adult_sku_count'] = sku_bundle.count + 1
            elif 'child' in sku_bundle.sku.name.lower():
                sku_dict['rt_child_sku'] = sku_bundle.sku.name
                sku_dict['rt_child_sku_count'] = sku_bundle.count

        sku_tuple = SkuHelper._get_named_tuple(named_tuple=SkuHelper.tuple_sku, **sku_dict)

        return sku_tuple
