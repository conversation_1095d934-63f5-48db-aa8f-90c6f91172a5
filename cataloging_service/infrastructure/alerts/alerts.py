import logging

import requests
from flask import current_app

logger = logging.getLogger(__name__)


def slack_alert(data=None, message=None):
    try:
        slack_text = create_slack_message(data, message)
        url = current_app.config.get('SLACKBOT_HOOK')

        # mrkdwn= {True:formatting enabled, False: formatting disabled}
        json_data = {
            "text": slack_text,
            "mrkdwn": True
        }
        requests.post(url, json=json_data)
    except:
        logger.exception("Failed to fire slack alert. Data: %s" % message)


def create_slack_message(data=None, message=None):
    environment = current_app.config.get('ENVIRONMENT')
    text = ""
    if message:
        text = "Service: `Cataloging`, Env: `%s`. Message: ```%s```\n" % (environment, message)
    if data:
        text = "%sData: ```%s```" % (text, str(data))
    return text
