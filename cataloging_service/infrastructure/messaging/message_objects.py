class MessageObjects:
    def __init__(self):
        self.new_properties = set()
        self.new_rooms = set()
        self.new_room_configs = set()
        self.new_restaurants = set()
        self.new_bars = set()
        self.new_halls = set()
        self.new_property_amenities = set()
        self.new_room_amenities = set()
        self.new_property_images = set()
        self.new_sku_categories = set()
        self.new_channels = set()
        self.new_sub_channels = set()
        self.new_applications = set()
        self.new_room_types = set()
        self.new_cities = set()
        self.new_sku = set()
        self.new_sellers = set()
        self.new_seller_sku = set()
        self.new_ruptub_details = set()
        self.new_property_videos = set()

        self.updated_properties = set()
        self.updated_rooms = set()
        self.updated_room_configs = set()
        self.updated_restaurants = set()
        self.updated_bars = set()
        self.updated_halls = set()
        self.updated_property_amenities = set()
        self.updated_room_amenities = set()
        self.updated_property_images = set()
        self.updated_sku_categories = set()
        self.updated_channels = set()
        self.updated_sub_channels = set()
        self.updated_applications = set()
        self.updated_room_types = set()
        self.updated_cities = set()
        self.updated_sku = set()
        self.updated_pricing_policy = set()
        self.updated_property_sku = set()
        self.updated_sellers = set()
        self.updated_seller_sku = set()
        self.updated_ruptub_details = set()
        self.updated_property_videos = set()
        self.updated_department_template = set()
        self.updated_profit_center_template = set()

        self.deleted_room_configs = set()
        self.deleted_restaurants = set()
        self.deleted_bars = set()
        self.deleted_halls = set()
        self.deleted_property_images = set()
        self.deleted_channels = set()
        self.deleted_sub_channels = set()
        self.deleted_applications = set()
        self.deleted_sellers = set()
        self.deleted_property_videos = set()


        self.amenity_summaries = set()

    def purge(self):
        self.new_properties.clear()
        self.new_rooms.clear()
        self.new_room_configs.clear()
        self.new_restaurants.clear()
        self.new_bars.clear()
        self.new_halls.clear()
        self.new_property_amenities.clear()
        self.new_room_amenities.clear()
        self.new_property_images.clear()
        self.new_sku_categories.clear()
        self.new_channels.clear()
        self.new_sub_channels.clear()
        self.new_applications.clear()
        self.new_room_types.clear()
        self.new_cities.clear()
        self.new_sellers.clear()
        self.new_seller_sku.clear()
        self.new_ruptub_details.clear()
        self.new_property_videos.clear()

        self.updated_properties.clear()
        self.updated_rooms.clear()
        self.updated_room_configs.clear()
        self.updated_restaurants.clear()
        self.updated_bars.clear()
        self.updated_halls.clear()
        self.updated_property_amenities.clear()
        self.updated_room_amenities.clear()
        self.updated_property_images.clear()
        self.updated_sku_categories.clear()
        self.updated_channels.clear()
        self.updated_sub_channels.clear()
        self.updated_applications.clear()
        self.updated_room_types.clear()
        self.updated_cities.clear()
        self.updated_pricing_policy.clear()
        self.updated_property_sku.clear()
        self.updated_sellers.clear()
        self.updated_seller_sku.clear()
        self.updated_ruptub_details.clear()
        self.updated_property_videos.clear()

        self.deleted_room_configs.clear()
        self.deleted_restaurants.clear()
        self.deleted_bars.clear()
        self.deleted_halls.clear()
        self.deleted_property_images.clear()
        self.deleted_channels.clear()
        self.deleted_sub_channels.clear()
        self.deleted_applications.clear()
        self.deleted_sellers.clear()
        self.deleted_property_videos.clear()
        self.updated_department_template.clear()
        self.updated_department_template.clear()

        self.amenity_summaries.clear()

    def register_new_property_image(self, property_image):
        if property_image:
            self.new_property_images.add(property_image)

    def register_updated_property_image(self, property_image):
        if property_image:
            self.updated_property_images.add(property_image)

    def register_deleted_property_image(self, property_image):
        if property_image:
            self.deleted_property_images.add(property_image)

    def register_new_property_video(self, property_video):
        if property_video:
            self.new_property_videos.add(property_video)

    def register_updated_property_video(self, property_video):
        if property_video:
            self.updated_property_videos.add(property_video)

    def register_deleted_property_video(self, property_video):
        if property_video:
            self.deleted_property_videos.add(property_video)

    def register_new_property(self, property):
        if property:
            self.new_properties.add(property)

    def register_new_room(self, room):
        if room:
            self.new_rooms.add(room)

    def register_new_room_config(self, room_config):
        if room_config:
            self.new_room_configs.add(room_config)

    def register_new_ruptub_details(self, ruptub_details):
        if ruptub_details:
            self.new_ruptub_details.add(ruptub_details)

    def register_updated_department_template(self, brand):
        if brand:
            self.updated_department_template.add(brand)

    def register_updated_profit_center_template(self, profit_center_template):
        if profit_center_template:
            self.updated_profit_center_template.add(profit_center_template)

    def register_updated_room(self, room):
        if room:
            self.updated_rooms.add(room)

    def register_updated_room_config(self, room_config):
        if room_config:
            self.updated_room_configs.add(room_config)

    def register_updated_ruptub_details(self, ruptub_details):
        if ruptub_details:
            self.updated_ruptub_details.add(ruptub_details)

    def register_deleted_room_config(self, room_config):
        if room_config:
            self.deleted_room_configs.add(room_config)

    def register_new_restaurant(self, restaurant):
        if restaurant:
            self.new_restaurants.add(restaurant)

    def register_updated_restaurant(self, restaurant):
        if restaurant:
            self.updated_restaurants.add(restaurant)

    def register_deleted_restaurant(self, restaurant):
        if restaurant:
            self.deleted_restaurants.add(restaurant)

    def register_new_bar(self, bar):
        if bar:
            self.new_bars.add(bar)

    def register_updated_bar(self, bar):
        if bar:
            self.updated_bars.add(bar)

    def register_deleted_bar(self, bar):
        if bar:
            self.deleted_bars.add(bar)

    def register_new_hall(self, hall):
        if hall:
            self.new_halls.add(hall)

    def register_updated_hall(self, hall):
        if hall:
            self.updated_halls.add(hall)

    def register_deleted_hall(self, hall):
        if hall:
            self.deleted_halls.add(hall)

    def register_new_property_amenity(self, amenity):
        if amenity:
            self.new_property_amenities.add(amenity)

    def register_updated_property_amenity(self, amenity):
        if amenity:
            self.updated_property_amenities.add(amenity)

    def register_new_room_amenities(self, amenity):
        if amenity:
            self.new_room_amenities.add(amenity)

    def register_updated_room_amenities(self, amenity):
        if amenity:
            self.updated_room_amenities.add(amenity)

    def register_amenity_summary(self, amenity_summary):
        if amenity_summary:
            self.amenity_summaries.add(amenity_summary)

    def register_sku_category(self, sku_category):
        if sku_category:
            self.new_sku_categories.add(sku_category)

    def register_updated_sku_category(self, sku_category):
        if sku_category:
            self.updated_sku_categories.add(sku_category)

    def register_new_channel(self, channel):
        if channel:
            self.new_channels.add(channel)

    def register_updated_channel(self, channel):
        if channel:
            self.updated_channels.add(channel)

    def register_deleted_channel(self, channel):
        if channel:
            self.deleted_channels.add(channel)

    def register_new_sub_channel(self, sub_channel):
        if sub_channel:
            self.new_sub_channels.add(sub_channel)

    def register_updated_sub_channel(self, sub_channel):
        if sub_channel:
            self.updated_sub_channels.add(sub_channel)

    def register_deleted_sub_channel(self, sub_channel):
        if sub_channel:
            self.deleted_sub_channels.add(sub_channel)

    def register_new_application(self, application):
        if application:
            self.new_applications.add(application)

    def register_updated_application(self, application):
        if application:
            self.updated_applications.add(application)

    def register_deleted_application(self, application):
        if application:
            self.deleted_applications.add(application)

    def register_new_room_type(self, room_type):
        if room_type:
            self.new_room_types.add(room_type)

    def register_updated_room_type(self, room_type):
        if room_type:
            self.updated_room_types.add(room_type)

    def register_new_city(self, city):
        if city:
            self.new_cities.add(city)

    def register_updated_city(self, city):
        if city:
            self.updated_cities.add(city)

    def register_new_sku(self, sku):
        if sku:
            self.new_sku.add(sku)

    def register_updated_sku(self, sku):
        if sku:
            self.updated_sku.add(sku)

    def register_updated_pricing_policy(self, policy):
        if policy:
            self.updated_pricing_policy.add(policy)

    def register_updated_property_sku(self, property_sku):
        if property_sku:
            self.updated_property_sku.add(property_sku)

    def register_updated_sellers(self, seller):
        if seller:
            self.updated_sellers.add(seller)

    def register_new_sellers(self, seller):
        if seller:
            self.new_sellers.add(seller)

    def register_new_seller_sku(self, seller_sku):
        if seller_sku:
            self.new_seller_sku.add(seller_sku)

    def get_message_objects(self):
        self.updated_properties -= self.new_properties
        self.updated_rooms -= self.new_rooms
        self.updated_room_configs -= self.new_room_configs
        self.updated_room_configs -= self.deleted_room_configs

        self.updated_property_images -= self.new_property_images
        self.updated_property_images -= self.deleted_property_images

        self.updated_restaurants -= self.new_restaurants
        self.updated_restaurants -= self.deleted_restaurants

        self.updated_bars -= self.new_bars
        self.updated_bars -= self.deleted_bars

        self.updated_halls -= self.new_halls
        self.updated_halls -= self.deleted_halls

        self.updated_property_amenities -= self.new_property_amenities
        self.updated_room_amenities -= self.new_room_amenities

        self.updated_sellers -= self.new_sellers

        self.updated_ruptub_details -=self.new_ruptub_details
        self.updated_property_videos -= self.new_property_videos

        new_objects = self.new_properties.union(self.new_room_configs).union(self.new_rooms).union(
            self.new_restaurants).union(self.new_bars).union(self.new_halls).union(self.new_property_amenities) \
            .union(self.new_room_amenities).union(self.new_property_images).union(self.new_sku_categories) \
            .union(self.new_channels).union(self.new_sub_channels).union(self.new_applications) \
            .union(self.new_room_types).union(self.new_cities).union(self.new_sku).union(self.new_sellers).union(
            self.new_seller_sku).union(self.new_ruptub_details).union(self.new_property_videos)
        modified_objects = self.updated_properties.union(self.updated_room_configs).union(self.updated_rooms).union(
            self.updated_restaurants).union(self.updated_bars).union(self.updated_halls).union(
            self.updated_property_amenities).union(self.updated_room_amenities).union(
            self.updated_property_images).union(self.amenity_summaries).union(self.updated_sku_categories) \
            .union(self.updated_channels).union(self.updated_sub_channels).union(self.updated_applications) \
            .union(self.updated_room_types).union(self.updated_cities).union(self.updated_sku) \
            .union(self.updated_pricing_policy).union(self.updated_property_sku).union(self.updated_sellers).union(
            self.updated_ruptub_details).union(self.updated_property_videos)
        deleted_objects = self.deleted_room_configs.union(self.deleted_halls).union(self.deleted_bars).union(
            self.deleted_restaurants).union(self.deleted_property_images).union(self.deleted_channels) \
            .union(self.deleted_sub_channels).union(self.deleted_applications).union(self.deleted_sellers).union(self.deleted_property_videos)

        return new_objects, modified_objects, deleted_objects
