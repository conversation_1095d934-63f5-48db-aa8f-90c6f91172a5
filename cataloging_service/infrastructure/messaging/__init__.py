from cataloging_service.infrastructure.messaging.message_objects import MessageObjects
from cataloging_service.infrastructure.messaging.message_producer import RMQMessagePublisher
from cataloging_service.infrastructure.messaging.messages import Messages


class MessagingProvider:
    def __init__(self):
        self.rmq_message_publisher = RMQMessagePublisher()

    def get_rmq_message_publisher(self):
        return self.rmq_message_publisher


messaging_provider = MessagingProvider()
message_objects = MessageObjects()
queue_messages = Messages()
