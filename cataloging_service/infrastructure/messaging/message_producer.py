import json
import logging

import os
from kombu import Connection
from kombu.pools import producers
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from cataloging_service.constants import error_codes, constants
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.alerts.alerts import slack_alert

logger = logging.getLogger(__name__)

# using the patched json
from kombu.serialization import register

register('json', json.dumps, json.loads, content_type='application/json')


class RMQMessagePublisher:
    """
    'confirm_publish': True => Produces channel.basic_publish now raises amqp.exceptions.NotConfirmed on basic.nack
    If confirm_publish is set, pyamqp blocks to wait for ack or nack. Publish confirm doesn't guarantee that the message
    is queued. The message might be confirmed and dropped on the broker side, if no route is found. In terms of business
    logic the only way to make sure a message is put in a queue on the broker side is to set mandatory flag, check for
    return and get an ack.
    """
    _connection = None

    def __init__(self):
        self._tenant_wise_rmq_connection = dict()
        for tenant in TenantClient.get_active_tenants():
            rmq_url = AwsSecretManager.get_rmq_url(tenant_id=tenant.tenant_id)
            self._tenant_wise_rmq_connection[tenant.tenant_id] = Connection(rmq_url, transport_options={
                'confirm_publish': True})
        self.environment = os.environ.get('APP_ENV', 'development')

    def publish(self, payload, exchange=None, routing_key="cs"):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        if self.environment not in {'staging', 'production'}:
            return
        try:
            connection = self._tenant_wise_rmq_connection[current_tenant_id]
            logger.debug("Publishing payload: %s to exchange: %s with routing_key: %s",
                         payload, exchange, routing_key)
            with producers[connection].acquire(block=True) as producer:
                producer.publish(payload, exchange=exchange, routing_key=routing_key, declare=[exchange],
                                 serializer='json', compression='zlib', retry=True,
                                 retry_policy={'interval_start': 0,  # First retry immediately,
                                               'interval_step': 2,  # then increase by 2s for every retry.
                                               'interval_max': 10,  # but don't exceed 10s between retries.
                                               'max_retries': 3,  # give up after 3 tries.
                                               })
        except Exception as exception:
            logger.error('Exception while publishing message: %s. Data: %s' % (repr(exception), str(payload)))
            slack_alert(message="Publisher error, %s" % repr(exception), data=payload)

    def test_connection(self):
        for tenant_id, connection in self._tenant_wise_rmq_connection.items():
            connection.connect()

            if not connection.connected:
                raise CatalogingServiceException(error_codes.RMQ_CONNECTION_ERROR)

            connection.release()
