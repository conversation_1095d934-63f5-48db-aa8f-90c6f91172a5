from datetime import datetime
from cataloging_service.domain.entities.templates.department_template import BrandDepartmentTemplateMappingEntity
from cataloging_service.models import BrandDepartmentTemplateMap


class BrandDepartmentTemplateAdapter:
    """Adapter for brand department template entity and model conversion"""

    @staticmethod
    def to_entity(model: BrandDepartmentTemplateMap) -> BrandDepartmentTemplateMappingEntity:
        """Convert database model to domain entity"""
        return BrandDepartmentTemplateMappingEntity(
            brand_id=model.brand_id,
            department_template_id=model.department_template_id,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: BrandDepartmentTemplateMappingEntity) -> BrandDepartmentTemplateMap:
        """Convert domain entity to database model"""
        model = BrandDepartmentTemplateMap()
        model.department_template = entity.department_template_id
        model.brand_id = entity.brand_id
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        return model

    @staticmethod
    def update_model_from_entity(model: BrandDepartmentTemplateMap, entity: BrandDepartmentTemplateMappingEntity) -> BrandDepartmentTemplateMap:
        model.department_template = entity.department_template_id
        model.brand_id = entity.brand_id
        model.modified_at = datetime.utcnow()
        return model
