from datetime import datetime

from cataloging_service.domain.entities.templates.department_template import DepartmentTemplateEntity
from cataloging_service.models import DepartmentTemplate, BrandDepartmentTemplateMap


class DepartmentTemplateAdapter:
    """Adapter for department template entity and model conversion"""

    @staticmethod
    def to_entity(model: DepartmentTemplate) -> DepartmentTemplateEntity:
        """Convert database model to domain entity"""
        brand_ids = list({link.brand_id for link in model.brand_department_template_links})

        return DepartmentTemplateEntity(
            id=model.id,
            brand_ids=brand_ids,
            code=model.code,
            name=model.name,
            parent_code=model.parent_code,
            description=model.description,
            financial_code=model.financial_code,
            is_active=model.is_active,
            auto_create_on_property_launch=model.auto_create_on_property_launch,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: DepartmentTemplateEntity) -> DepartmentTemplate:
        """Convert domain entity to database model"""
        model = DepartmentTemplate()
        if entity.id:
            model.id = entity.id
        model.code = entity.code
        model.name = entity.name
        model.parent_code = entity.parent_code
        model.description = entity.description
        model.financial_code = entity.financial_code
        model.is_active = entity.is_active
        model.auto_create_on_property_launch = entity.auto_create_on_property_launch
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at

        model.brand_department_template_links = set([
            BrandDepartmentTemplateMap(
                brand_id=brand_id,
                created_at=datetime.utcnow(),
                modified_at=datetime.utcnow()
            )
            for brand_id in entity.brand_ids
        ])

        return model

    @staticmethod
    def update_model_from_entity(model: DepartmentTemplate, entity: DepartmentTemplateEntity) -> DepartmentTemplate:
        """Update existing model with entity data"""
        model.code = entity.code
        model.name = entity.name
        model.parent_code = entity.parent_code
        model.description = entity.description
        model.financial_code = entity.financial_code
        model.is_active = entity.is_active
        model.auto_create_on_property_launch = entity.auto_create_on_property_launch
        model.modified_at = datetime.utcnow()

        model.brand_department_template_links.clear()
        for brand_id in set(entity.brand_ids or []):
            link = BrandDepartmentTemplateMap(
                brand_id=brand_id,
                department_template=model,
                created_at=datetime.utcnow(),
                modified_at=datetime.utcnow(),
            )
            model.brand_department_template_links.add(link)
        return model
