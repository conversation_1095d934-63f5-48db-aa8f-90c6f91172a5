from datetime import datetime
from typing import Optional
from cataloging_service.domain.entities.templates.profit_center_template import ProfitCenterTemplateEntity
from cataloging_service.models import ProfitCenterTemplate, BrandProfitCenterTemplateMap


class ProfitCenterTemplateAdapter:
    """Adapter for profit center template entity and model conversion"""

    @staticmethod
    def to_entity(model: ProfitCenterTemplate) -> ProfitCenterTemplateEntity:
        """Convert database model to domain entity"""
        brand_ids = list({link.brand_id for link in model.brand_profit_center_template_links})

        return ProfitCenterTemplateEntity(
            id=model.id,
            brand_ids=brand_ids,
            code=model.code,
            name=model.name,
            department_template_code=model.department_template_code,
            system_interface=model.system_interface,
            description=model.description,
            auto_create_on_property_launch=model.auto_create_on_property_launch,
            is_active=model.is_active,
            created_at=model.created_at,
            modified_at=model.modified_at,
        )

    @staticmethod
    def to_db_model(entity: ProfitCenterTemplateEntity) -> ProfitCenterTemplate:
        """Convert domain entity to database model"""
        model = ProfitCenterTemplate()
        if entity.id:
            model.id = entity.id
        model.code = entity.code
        model.name = entity.name
        model.department_template_code = entity.department_template_code
        model.system_interface = entity.system_interface
        model.description = entity.description
        model.auto_create_on_property_launch = entity.auto_create_on_property_launch
        model.is_active = entity.is_active
        if entity.created_at:
            model.created_at = entity.created_at
        if entity.modified_at:
            model.modified_at = entity.modified_at
        model.brand_profit_center_template_links = set([
            BrandProfitCenterTemplateMap(
                brand_id=brand_id,
                created_at=datetime.utcnow(),
                modified_at=datetime.utcnow()
            )
            for brand_id in entity.brand_ids
        ])

        return model

    @staticmethod
    def update_model_from_entity(model: ProfitCenterTemplate, entity: ProfitCenterTemplateEntity) -> ProfitCenterTemplate:
        """Update existing model with entity data"""
        model.code = entity.code
        model.name = entity.name
        model.department_template_code = entity.department_template_code
        model.system_interface = entity.system_interface
        model.description = entity.description
        model.auto_create_on_property_launch = entity.auto_create_on_property_launch
        model.is_active = entity.is_active
        model.modified_at = datetime.utcnow()

        model.brand_profit_center_template_links.clear()
        for brand_id in set(entity.brand_ids or []):
            link = BrandProfitCenterTemplateMap(
                brand_id=brand_id,
                profit_center_template=model,
                created_at=datetime.utcnow(),
                modified_at=datetime.utcnow(),
            )
            model.brand_profit_center_template_links.add(link)

        return model
