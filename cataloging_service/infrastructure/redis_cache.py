from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from cataloging_service.cache import CustomCache
from cataloging_service.constants import constants
from cataloging_service.exceptions import InvalidTenantException


def create_cache_configs(host, port, password, db):
    cache_configs = {
        'CACHE_TYPE': 'redis',
        'CACHE_OPTIONS': None,
        'CACHE_DEFAULT_TIMEOUT': None,
        'CACHE_KEY_PREFIX': None,
        'CACHE_REDIS_HOST': host,
        'CACHE_REDIS_PORT': port,
        'CACHE_REDIS_PASSWORD': password,
        'CACHE_REDIS_DB': db
    }
    return cache_configs


tenant_wise_caches = dict()


def get_redis_credentials():
    redis_configs = dict()
    active_tenants = TenantClient.get_active_tenants()

    for tenant in active_tenants:
        tenant_id = tenant.tenant_id
        redis_creds = AwsSecretManager.get_redis_credentials(tenant_id)
        redis_configs[tenant_id] = create_cache_configs(redis_creds['host'], redis_creds['port'],
                                                        redis_creds['password'], redis_creds['db'])

    return redis_configs


def setup_tenant_cache(tenant_id=None):
    """
    Setup a tenant wise CustomCache objects. The setup should be done only once, on application startup. So this
    method is called on at module level.

    To access tenant cache, get the custom_cache for that specific tenant from the created tenant_wise_caches,
    and use `cache` property from that `CustomCache` object

    :return:
    """
    redis_configs = get_redis_credentials()
    if tenant_id:
        redis_config = redis_configs.get(tenant_id)
        if not redis_config:
            raise InvalidTenantException()

        tenant_wise_caches[tenant_id] = CustomCache(with_jinja2_ext=False, config=redis_config)
    else:
        for tenant_id, redis_config in get_redis_credentials().items():
            if tenant_id in tenant_wise_caches:
                continue
            tenant_wise_caches[tenant_id] = CustomCache(with_jinja2_ext=False, config=redis_config)


def init_app(app):
    if not tenant_wise_caches:
        setup_tenant_cache()

    for tenant_id, cache in tenant_wise_caches.items():
        cache.init_app(app)


def get_cache(tenant_id=None) -> CustomCache:
    if not tenant_id:
        tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()

    cache = tenant_wise_caches.get(tenant_id)
    if not cache:
        setup_tenant_cache()
        cache = tenant_wise_caches.get(tenant_id)
        if not cache:
            raise InvalidTenantException()

    return cache
