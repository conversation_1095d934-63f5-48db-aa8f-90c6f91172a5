from flask_login import current_user
from flask import has_request_context, request
from flask_admin.contrib.sqla import ModelView
from treebo_commons.flask_audit.audit import (
    AuditContextResolvers,
    AuditManager,
    AuditManagerExtension,
    FlaskInternalSources,
)
from treebo_commons.request_tracing.context import get_current_request_id
from cataloging_service.models import User


def admin_user_resolver():
    user_email = current_user.email
    user_group = User.query.filter_by(email=user_email).first()
    return dict(
        user_id=user_group.id,
        user_type="Flask-Admin-User",
        roles=user_group.roles,
        user_name=user_email,
    )


def api_user_resolver():
    return dict(
        user_id=request.headers.get("X-Auth-Id"),
        user_type="SH-API-User",
        roles=request.headers.get("X-User-Type"),
        user_name=request.headers.get("X-User"),
    )


def application_source_resolver():
    if not has_request_context():
        return FlaskInternalSources.FLASK_CLI

    application = request.headers.get("X-Application")

    if application:
        return application

    if (
        request.url_rule and "admin" in request.url_rule.rule
    ):
        return FlaskInternalSources.FLASK_ADMIN_PANEL

    return request.headers.get("User-Agent", "Unknown-Source")


audit_context_resolvers = AuditContextResolvers(
    api_user_context_resolver=api_user_resolver,
    admin_user_context_resolver=admin_user_resolver,
    source_context_resolver=application_source_resolver,
    request_id_resolver=get_current_request_id,
)

audit_manager = AuditManager(resolvers=audit_context_resolvers)

audit_ext = AuditManagerExtension(manager=audit_manager)


def admin_action_resolver(operation):
    return lambda instance, *args, **kwargs: instance.infer_user_action(operation)


class AuditedModelView(ModelView):
    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("update"))
    def update_model(self, form, model):
        return super(AuditedModelView, self).update_model(form, model)

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("delete"))
    def delete_model(self, model):
        return super(AuditedModelView, self).delete_model(model)

    @audit_ext.manager.capture_trail(user_action=admin_action_resolver("create"))
    def create_model(self, form):
        return super(AuditedModelView, self).create_model(form)

    def infer_user_action(self, operation):
        model_name = self.model.__name__
        return "{0}".format(operation.upper())
