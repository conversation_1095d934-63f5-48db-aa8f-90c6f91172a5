import inspect
import logging
import flask
from flask_caching import Cache

logger = logging.getLogger(__name__)


class MultiKeyCacheManager(object):
    cache_key_prefix = None

    def __init__(self, attribute_for_cache_key, keys_to_cache_keys_dict, cache_key_id_getter, cache: Cache = None,
                 timeout=None):
        """

        :param attribute_for_cache_key:
        :param keys_to_cache_keys_dict:
            Cache Key Id -> Cache Key Prefix dict
        :param cache_key_id_getter:
            A function that returns the Identifier to be used with cache_key
        :param cache:
        :param timeout:
        """
        self.attribute_for_cache_key = attribute_for_cache_key
        self.keys_to_cache_keys_dict = keys_to_cache_keys_dict
        self.cache_key_id_getter = cache_key_id_getter
        self.cache = cache
        self.timeout = timeout

        self.prefix = ''
        self.cache_miss_keys = []

    @property
    def cache_keys(self):
        if self.keys_to_cache_keys_dict is None:
            raise RuntimeError('make_cache_keys has not been run yet. It should be called before accessing cache_keys')
        return self.keys_to_cache_keys_dict.values()

    def before_get_cache(self):
        """ Hook to do pre processing before setting cache for the multi key cache managers """
        pass

    def after_get_cache(self, result):
        """
        Used to populate cache miss keys and filter out None items in result which is due to key not in cache
        """
        keys_in_cache = [self.cache_key_id_getter(item) for item in result if item is not None]
        self.cache_miss_keys = [v for k, v in self.keys_to_cache_keys_dict.items() if k not in keys_in_cache]
        return [item for item in result if item is not None]  # filter out None as it means cache key was not present

    def get_cache(self):
        try:
            self.before_get_cache()
            logger.debug('Getting cache for: {c}'.format(c=self.cache_keys))
            rv = self.cache.get_many(*self.cache_keys)
            return self.after_get_cache(rv)
        except Exception:
            logger.exception("Exception possibly due to cache backend.")

    def before_set_cache(self, result):
        """
        returns a list of payloads mapped with their cache keys
        eg: [Payload1, Payload2] -> {cache_key1: Payload1, cache_key2: Payload2}
        """
        try:
            formatted_result = {self.keys_to_cache_keys_dict[self.cache_key_id_getter(item)]: item for item in result if
                                item}
            return formatted_result
        except KeyError as ex:
            logger.exception("Key Error. Keys to cache dict: %s", self.keys_to_cache_keys_dict)
            raise

    def after_set_cache(self, result):
        """ Hook to do post processing after setting cache for the multi key cache managers """
        return result

    def set_cache(self, result):
        try:
            new_result = self.before_set_cache(result)
            if new_result:
                cache_set_success = self.cache.cache.set_many(mapping=new_result, timeout=self.timeout)
                if not cache_set_success:
                    raise Exception('Setting in cache failed due to unknown reason')
            return self.after_set_cache(new_result)
        except Exception:
            request = flask.request
            request_url = request.url
            logger.exception("Exception possibly due to cache backend. Request URL: %s", request_url)

    def get_response_for_missed_keys(self, f, args, kwargs):
        logger.debug('Missed keys: {m}'.format(m=self.cache_miss_keys))
        missed_cache_key_ids = [k for k, v in self.keys_to_cache_keys_dict.items() if v in self.cache_miss_keys]
        spec = inspect.getfullargspec(f)
        new_args = []
        if args:
            if spec.args[0] == 'self':
                new_args.append(args[0])
                new_args.append(missed_cache_key_ids)
            else:
                new_args.append(missed_cache_key_ids)
        else:
            kwargs[self.attribute_for_cache_key] = missed_cache_key_ids
        new_args = tuple(new_args)
        return f(*new_args, **kwargs)
