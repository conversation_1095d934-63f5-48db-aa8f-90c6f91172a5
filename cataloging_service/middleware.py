import logging
import os

from flask import request
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context

from cataloging_service.extensions import admin
from cataloging_service.thread_locals import app_context

logger = logging.getLogger(__name__)


def is_request_from_admin():
    return bool(admin.url) and str(request.path).startswith(admin.url)


def before_request():
    include_test = request.headers.get('X-test', False)
    if 'ADMIN_TENANT_ID' in os.environ:
        logger.info("ADMIN_TENANT_ID: %s, found in environment. Overriding that in context.",
                    os.environ.get('ADMIN_TENANT_ID'))
        request_context.tenant_id = os.environ.get('ADMIN_TENANT_ID', TenantClient.get_default_tenant())

    if is_request_from_admin():
        include_test = True
    app_context.include_test = bool(include_test)


def after_request(response):
    app_context.clear()
    return response
