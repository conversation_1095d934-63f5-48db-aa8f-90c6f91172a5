import logging
import os
import flask
import sentry_sdk

from sentry_sdk.integrations.flask import FlaskIntegration

from cataloging_service.constants import constants
from treebo_commons.request_tracing.flask import before_request, after_request

from cataloging_service.middleware import before_request as catalog_before_request
from cataloging_service.middleware import after_request as catalog_after_request


class BaseConfig(object):
    DEBUG = True
    TESTING = True

    SQLALCHEMY_TRACK_MODIFICATIONS = True
    SQLALCHEMY_ECHO = False
    SECRET_KEY = "q_\xdd\x1c\xbd\x15\xeb\xdb\x8dD5\xc8\xfcR\x84\xd8?\xc5\x03rC=\x12\x98"

    LOG_FORMAT = "%(asctime)s:%(name)s:%(levelname)s:%(message)s"
    LOG_PATH = "cataloging_service.log"
    LOG_LEVEL = logging.DEBUG

    # APPLICATION_ROOT = '/cataloging-service'
    _log_root = "{d}/logs".format(d=os.path.dirname(os.path.dirname(__file__)))
    LOG_ROOT = os.environ.get("LOG_ROOT", _log_root)

    SECURITY_REGISTERABLE = True
    SECURITY_TOKEN_MAX_AGE = 1800
    SECURITY_POST_LOGIN_VIEW = constants.ADMIN_URL
    SECURITY_POST_LOGOUT_VIEW = constants.ADMIN_URL
    SECURITY_POST_REGISTER_VIEW = constants.ADMIN_URL
    SECURITY_RECOVERABLE = False
    SECURITY_CONFIRMABLE = False
    SECURITY_SEND_REGISTER_EMAIL = False
    SECURITY_PASSWORD_HASH = "bcrypt"
    SECURITY_PASSWORD_SALT = SECRET_KEY
    SECURITY_URL_PREFIX = constants.ADMIN_URL
    ERROR_MAIL_RECIPIENTS = ["<EMAIL>"]

    DEV_EMAIL_PERMITTED_RECIPIENT_SET = {"<EMAIL>"}

    PROPERTY_SIGNED_RECIPIENTS = ["<EMAIL>"]
    BASE_ZIP_DIRECTORY = "/var/tmp/zips/"
    IMAGE_BUCKET_NAME = "stage-s3-catalog"
    TREEBO_TENANT_CODE = "101"
    CDN_HOST = "https://cs-images.treebo.be"
    SLACKBOT_HOOK = os.environ.get(
        "SLACKBOT_HOOK",
        "*****************************************************************************",
    )

    # Middlewares
    WSGI_MIDDLEWARES = []
    BEFORE_REQUEST_MIDDLEWARES = [lambda: before_request(flask.request), catalog_before_request]
    AFTER_REQUEST_MIDDLEWARES = [
        lambda resp: after_request(resp, flask.request),
        catalog_after_request,
    ]
    GOOGLE_API_KEY = "AIzaSyB4P5nKNp4GjFZBG2U5nRN2Bo-fQzt9xtg"


class DevelopmentConfig(BaseConfig):
    ENVIRONMENT = "DEV"
    DEBUG = True
    LOG_PATH = "cataloging_service.log"
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    DB_PASSWORD = os.environ.get("DB_PASSWORD", "")
    DB_USER = os.environ.get("DB_USER", "")
    DB_HOST = os.environ.get("DB_HOST", "localhost")
    DB_PORT = os.environ.get("DB_PORT", "5432")
    DB = os.environ.get("DB_NAME", "catalog")
    SQLALCHEMY_DATABASE_URI = "postgresql://%s:%s@%s:%s/%s" % (
        DB_USER,
        DB_PASSWORD,
        DB_HOST,
        DB_PORT,
        DB,
    )
    GOOGLE_DRIVE_ROOT_FOLDER_ID = "0BwIj7LX1TgUJWGJ2TXI1RzJtQkE"
    # IMAGE_BUCKET_NAME = 'dev-s3-catalog'
    S3_ACCESS_KEY = "********************"
    S3_SECRET_KEY = "Ur+l07/tNGEeP1zGkgigmO+sOK4I98FpiS50Q8Pz"
    IMAGE_BUCKET_NAME = "stage-s3-catalog"


class TestingConfig(BaseConfig):
    ENVIRONMENT = "TEST"
    TESTING = True
    SQLALCHEMY_TRACK_MODIFICATIONS = True


class ProductionConfig(BaseConfig):
    DEBUG = False
    sentry_sdk.init(
        dsn="https://<EMAIL>/7",
        integrations=[FlaskIntegration()],
        release=os.environ.get("BUILD_NUMBER"),
        environment="production",
    )
    ENVIRONMENT = "PRODUCTION"
    LOG_LEVEL = logging.INFO
    LOG_ROOT = os.environ.get("LOG_ROOT", "./logs")
    LOG_PATH = "cataloging_service.log"
    GOOGLE_DRIVE_ROOT_FOLDER_ID = "0B6pjPvMd-DfkWXNVTFV4bW9nVVk"
    SQLALCHEMY_ECHO = False
    PROWL_GET_USERS_URL = "http://api.treebohotels.com/prowl/rest/v3/hotel-users"
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        "DB",
        "postgresql://cataloging:<EMAIL>:5432/cataloging_service",
    )

    IMAGE_BUCKET_NAME = "prod-s3-catalog-sgp"
    TREEBO_TENANT_CODE = "treebo"
    CDN_HOST = "https://cs-images.treebo.com"
    SLACKBOT_HOOK = os.environ.get(
        "SLACKBOT_HOOK",
        "*****************************************************************************",
    )


class StagingConfig(BaseConfig):
    ENVIRONMENT = "STAGE"
    LOG_ROOT = os.environ.get("LOG_ROOT", "./logs")
    LOG_PATH = "cataloging_service.log"
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        "DB",
        "postgresql://rms_admin:}fP97xcNP2^P]*<EMAIL>:6432/cataloging_service",
    )
    GOOGLE_DRIVE_ROOT_FOLDER_ID = "0BwoePrvZPnuuTkdBTW9RLXFxYjg"
    IMAGE_BUCKET_NAME = "stage-s3-catalog"
