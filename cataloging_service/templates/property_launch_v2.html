<!doctype html>

<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Property Launch</title>
    <meta name="description" content="Property Launch">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.3.1/dist/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.js"></script>
    <script src="{{ url_for('static', filename = 'js/property_launch_v2.js')|autoversion }}"></script>
</head>

<body>
<div class="ui container">

    <h1 class="ui center aligned header" style="margin-top: 10px;">
        <i class="rocket icon"></i>
        Property launch
    </h1>
    <form class="ui form" action="" method="post" id="property-launch-form" name="property_launch_form" autocomplete="new-password">

        <div class="two fields">
            <div class="required field">
                <label>Name</label>
                <input type="text" name="name" id="property_name" placeholder="Name" autocomplete="new-password"">
            </div>

            <div class="field">
                <label>Previous Name</label>
                <input type="text" name="previous_name" placeholder="Previous Name" autocomplete="new-password">
            </div>
        </div>
        <div class="two fields">
            <div class="required field">
                <label>Signed Date</label>
                <div class="ui calendar date_calendar">
                    <div class="ui input left icon">
                        <i class="calendar icon"></i>
                        <input type="text" name="signed_date" placeholder="Signed Date" autocomplete="new-password">
                    </div>
                </div>
            </div>
            <div class="field">
                <label>Contractual Launch Date</label>
                <div class="ui calendar date_calendar">
                    <div class="ui input left icon">
                        <i class="calendar icon"></i>
                        <input type="text" name="launch_date" placeholder="Contractual launch Date" autocomplete="new-password">

                    </div>
                </div>
            </div>

        </div>
        <div class="ui two column grid">
            <div class="row">
                <div class="column">
                    <div class="required field" autocomplete="new-password">
                        <label>Location details </label>
                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="latitude" placeholder="latitude" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" name="longitude" placeholder="longitude" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                    <div class="field" autocomplete="new-password">
                        <label>Contact Address</label>
                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="address_line1" placeholder="address line1" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" name="address_line2" placeholder="address line2" autocomplete="new-password">
                            </div>
                        </div>
                        <div class="two fields">
                            <div class="field">
                                <div class="ui search city-search" id="address_city">
                                    <input type="text" hidden name="address_city_id" autocomplete="new-password">
                                    <div class="ui icon input">
                                        <input class="prompt" type="text" placeholder="Search city..." name="address_city" autocomplete="new-password">
                                        <i class="search icon"></i>
                                    </div>
                                    <div class="results"></div>
                                </div>
                            </div>
                            <div class="field">
                                <input type="text" name="address_pincode" placeholder="pincode" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="column">

                    <div class="field">
                        <label>Legal details</label>

                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="legal_name" placeholder="Legal name" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" maxlength="15" minlength="15" name="gstin" placeholder="gstin" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                    <div class="field">


                        <label>Legal Address
                            <div class="ui slider checkbox">
                                <input type="checkbox" name="same_as_contact_address">
                                <label>same_as_contact_address</label>

                            </div>
                        </label>


                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="legal_address_line1" placeholder="legal address line1" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" name="legal_address_line2" placeholder="legal address line2" autocomplete="new-password">
                            </div>
                        </div>
                        <div class="two fields">
                            <div class="field">
                                <div class="ui search city-search" id="legal_address_city">
                                    <input type="text" hidden name="legal_address_city_id" autocomplete="new-password">
                                    <div class="ui icon input">
                                     <input class="prompt" type="text" placeholder="Search city..."
                                           name="legal_address_city" autocomplete="new-password">
                                        <i class="search icon"></i>
                                    </div>

                                    <div class="results"></div>
                                </div>
                            </div>

                            <div class="field">
                                <input type="text" name="legal_address_pincode" placeholder="pincode" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="field">
            <label>Contact details</label>
            <div class="three fields">
                <div class="field">
                    <input type="text" name="reception_mobile" placeholder="Reception primary" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="reception_landline" placeholder="Reception secondary" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="email" placeholder="Hotel email" autocomplete="new-password">
                </div>
            </div>
        </div>

        <div class="field">
            <label>Owner details</label>
            <div class="three fields">
                <div class="field">
                    <input type="text" name="owner_name" placeholder="Primary owner name" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="email" name="owner_email" placeholder="Primary owner email" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="owner_phone" placeholder="Primary owner mobile" autocomplete="new-password">
                </div>
            </div>
            <div class="three fields">
                <div class="field">
                    <input type="text" name="secondary_owner_name" placeholder="secondary owner name" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="email" name="secondary_owner_email" placeholder="secondary owner email" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="secondary_owner_phone" placeholder="secondary owner mobile" autocomplete="new-password">
                </div>
            </div>
        </div>


        <div class="field">
            <label>Registration details</label>
            <div class="ui two column grid fields">
                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_msme">
                        <label>is_msme</label>
                    </div>
                </div>
                <div class="field">
                    <input type="text" name="msme" placeholder="MSME Number">
                </div>
                <div class="field">
                    <input type="text" name="tan" placeholder="Tan Number">
                </div>
            </div>
        </div>

        <div class="field">
            <label>Facility details</label>
            <div class="ui four column grid fields">
                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_property_leased">
                        <label>is_property_leased</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_security_guard_available">
                        <label>is_security_guard_available</label>
                    </div>
                </div>
                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_public_washroom_available">
                        <label>is_public_washroom_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_elevator_available">
                        <label>is_elevator_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_parking_available">
                        <label>is_parking_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_disabled_friendly">
                        <label>is_disabled_friendly</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_bar_available">
                        <label>is_bar_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_restaurant_available">
                        <label>is_restaurant_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_ac_available">
                        <label>is_ac_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_unmarried_couple_allowed">
                        <label>is_unmarried_couple_allowed</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_local_ids_accepted">
                        <label>is_local_ids_accepted</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_private_intercom_available">
                        <label>is_private_intercom_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_tv_available">
                        <label>is_tv_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_laundry_available">
                        <label>is_laundry_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_hot_water_available">
                        <label>is_hot_water_available</label>
                    </div>
                </div>

            </div>
            <div class="ui four column grid fields">


                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_banquet_hall_available">
                        <label>is_banquet_hall_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_pool_available">
                        <label>is_pool_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_gym_available">
                        <label>is_gym_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_spa_available">
                        <label>is_spa_available</label>
                    </div>
                </div>


                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_amex_payment_available">
                        <label>is_amex_payment_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_roof_top_cafe_available">
                        <label>is_roof_top_cafe_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_pool_table_available">
                        <label>is_pool_table_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_private_cab_service_available">
                        <label>is_private_cab_service_available</label>
                    </div>
                </div>


                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_moquito_repellant_available">
                        <label>is_moquito_repellant_available</label>
                    </div>
                </div>

                <div class="field">
                    <div class="ui checkbox">
                        <input type="checkbox" name="is_room_heater_available">
                        <label>is_room_heater_available</label>
                    </div>
                </div>
            </div>


            <div class="fields">
                <div class="field">
                    <label>suited for</label>
                    <div class="ui clearable multiple selection dropdown">
                        <input type="hidden" name="suited_for" value="">
                        <i class="dropdown icon"></i>
                        <div class="default text">Select suited for</div>
                        <div class="menu">
                            <div class="item">Corporate</div>
                            <div class="item">Couples</div>
                            <div class="item">Families</div>
                            <div class="item">Foreigners</div>
                            <div class="item">Medical Tourists</div>
                            <div class="item">Others</div>
                            <div class="item">SME Owner</div>
                            <div class="item">Young Singles</div>
                        </div>
                    </div>
                </div>
                <div class="field">
                    <label>Star rating</label>
                    <input type="number" min="1" max="5" name="star_rating" placeholder="5" size="1" value="3">
                </div>
                <div class="field">
                    <label>Total floors</label>
                    <input type="number" min="1" max="99" name="total_floors" placeholder="5" size="1">
                </div>
                <div class="six wide field">
                    <label>Google maps link</label>
                    <input type="text" name="google_maps_link" placeholder="google maps link">
                </div>
                <div class="field">
                    <label>Super Hero Price Slab</label>
                <div class="ui selection dropdown">
                    <input type="hidden" name="super_hero_price_slab">
                    <i class="dropdown icon"></i>
                    <div class="default text">Super Hero Price Slab</div>
                    <div class="menu">
                        <div class="item" data-value="silver">Silver</div>
                        <div class="item" data-value="Platinum">Platinum</div>
                        <div class="item" data-value="gold_sales">Gold_Sales</div>
                        <div class="item" data-value="gold_ops">Gold_Ops</div>
                    </div>
                </div>
                </div>
                <div class="field">
                    <label>Hygiene Shield Name</label>
                <div class="ui selection dropdown">
                    <input type="hidden" name="hygiene_shield_name">
                    <i class="dropdown icon"></i>
                    <div class="default text">Name</div>
                    <div class="menu">
                        <div class="item" data-value="GOLD">GOLD</div>
                        <div class="item" data-value="PLATINUM">PLATINUM</div>
                        <div class="item" data-value="BRONZE">BRONZE</div>
                    </div>
                </div>
                </div>
            </div>

            <div class="equal width fields">

                <div class="field">
                    <label>laundry timings</label>
                    <input type="text" name="laundry_timings" placeholder="10:00 AM - 5:00 PM"
                           value="10:00 AM - 5:00 PM">
                </div>
                <div class="field">
                    <label>Room service timings</label>
                    <input type="text" name="room_service_timings" placeholder="10:00 AM - 5:00 PM"
                           value="10:00 AM - 5:00 PM">
                </div>
                <div class="field">
                    <label>Restaurant timings</label>
                    <input type="text" name="restaurant_timings" placeholder="10:00 AM - 5:00 PM"
                           value="10:00 AM - 5:00 PM">
                </div>
                <div class="field">
                    <label>Bar timings</label>
                    <input type="text" name="bar_timings" placeholder="10:00 AM - 5:00 PM" value="10:00 AM - 5:00 PM">
                </div>
                <div class="field">
                    <label>Base Currency Code</label>
                    <div class=" field" size="1">
                        <input type="text" name="base_currency_code" placeholder="base currency code (Ex. INR)" autocomplete="new-password">
                    </div>
                </div>
                <div class="required field">
                    <label>Time Zone</label>
                    <div class=" field" size="1">
                        <input type="text" name="timezone" placeholder="timezone">
                    </div>
                </div>
                <div class="field">
                    <label>Hotel Logo</label>
                    <div class=" field" size="1">
                        <input type="text" name="hotel_logo" placeholder="hotel_logo">
                    </div>
                </div>
                <div class="field">
                    <label>Country Code</label>
                    <div class=" field" size="1">
                        <input type="text" name="country_code" placeholder="2 digit IN, US">
                    </div>
                </div>
            </div>

            <div class="field">
                <div class="fields">
                    <div class="field">
                        <label> Checkin time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="standard_checkin_time" value="12:00 PM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Checkout time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="standard_checkout_time" value="11:00 AM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Free early checkin time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="free_early_checkin_time" value="10:00 AM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Free late checkout time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="free_late_checkout_time" value="2:00 PM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Early checkin fee</label>
                        <input type="number" name="early_checkin_fee" placeholder="in rupees" value="0.00" min="0"
                               step="0.10">
                    </div>
                    <div class="field">
                        <label> Late checkout fee</label>
                        <input type="number" name="late_checkout_fee" placeholder="in rupees" value="0.00" min="0"
                               step="0.10">
                    </div>
                </div>
            </div>


        </div>

        <h4 class="ui horizontal divider header">
            <i class="hotel icon"></i>
            Room level details
        </h4>

        <div class="field">
            <label>Room type details</label>
            <div class="ui horizontal segments">
                <div class="ui segment room_type_details">
                    <h5 class="ui header" data-value="acacia">
                        Acacia
                    </h5>
                    <div class="field">
                        <label>Is this room type available in property?</label>
                        <div class="ui selection dropdown">
                            <input type="hidden" name="is_room_type_available" value="0">
                            <i class="dropdown icon"></i>
                            <div class="default text">Is this room type available?</div>
                            <div class="menu">
                                <div class="item" data-value="1">Yes</div>
                                <div class="item" data-value="0">No</div>
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> max adults </label>
                        <input type="number" name="max_adults" placeholder="max_adults" value="0">
                    </div>
                    <div class="field">
                        <label> max children </label>
                        <input type="number" name="max_children" placeholder="max_children" value="0">
                    </div>
                    <div class="field">
                        <label> max total </label>
                        <input type="number" name="max_total" placeholder="max_total" value="0">
                    </div>
                    <div class="field">
                        <label> room size </label>
                        <input type="number" name="room_size" placeholder="room size in sq ft" value="0">
                    </div>
                    <div class="ui two column grid field">
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_wardrobe_available">
                                <label>is_wardrobe_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_luggage_shelf_available">
                                <label>is_luggage_shelf_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_coffee_table_available">
                                <label>is_coffee_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_smoking_permitted">
                                <label>is_smoking_permitted</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_study_table_available">
                                <label>is_study_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_mini_fridge_available">
                                <label>is_mini_fridge_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_kitchenette_available">
                                <label>is_kitchenette_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_microwave_oven_available">
                                <label>is_microwave_oven_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_sofa_chair_available">
                                <label>is_sofa_chair_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_dining_table_available">
                                <label>is_dining_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui selection dropdown">
                                <input type="hidden" name="bed_type">
                                <i class="dropdown icon"></i>
                                <div class="default text">Bed type</div>
                                <div class="menu">
                                    <div class="item" data-value="king_bed">King Beds</div>
                                    <div class="item" data-value="queen_bed">Queen Beds</div>
                                    <div class="item" data-value="single_bed">Single beds</div>
                                    <div class="item" data-value="non_joinable_twin_bed">Non Join-able twin beds</div>
                                    <div class="item" data-value="joinable_twin_bed">Join-able twin beds</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ui segment room_type_details">
                    <h5 class="ui header" data-value="oak">
                        Oak
                    </h5>
                    <div class="field">
                        <label>Is this room type available in property?</label>
                        <div class="ui selection dropdown">
                            <input type="hidden" name="is_room_type_available" value="0">
                            <i class="dropdown icon"></i>
                            <div class="default text">Is this room type available?</div>
                            <div class="menu">
                                <div class="item" data-value="1">Yes</div>
                                <div class="item" data-value="0">No</div>
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> max adults </label>
                        <input type="number" name="max_adults" placeholder="max_adults" value="0">
                    </div>
                    <div class="field">
                        <label> max children </label>
                        <input type="number" name="max_children" placeholder="max_children" value="0">
                    </div>
                    <div class="field">
                        <label> max total </label>
                        <input type="number" name="max_total" placeholder="max_total" value="0">
                    </div>
                    <div class="field">
                        <label> room size </label>
                        <input type="number" name="room_size" placeholder="room size in sq ft" value="0">
                    </div>
                    <div class="ui two column grid field">
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_wardrobe_available">
                                <label>is_wardrobe_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_luggage_shelf_available">
                                <label>is_luggage_shelf_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_coffee_table_available">
                                <label>is_coffee_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_smoking_permitted">
                                <label>is_smoking_permitted</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_study_table_available">
                                <label>is_study_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_mini_fridge_available">
                                <label>is_mini_fridge_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_kitchenette_available">
                                <label>is_kitchenette_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_microwave_oven_available">
                                <label>is_microwave_oven_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_sofa_chair_available">
                                <label>is_sofa_chair_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_dining_table_available">
                                <label>is_dining_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui selection dropdown">
                                <input type="hidden" name="bed_type">
                                <i class="dropdown icon"></i>
                                <div class="default text">Bed type</div>
                                <div class="menu">
                                    <div class="item" data-value="king_bed">King Beds</div>
                                    <div class="item" data-value="queen_bed">Queen Beds</div>
                                    <div class="item" data-value="single_bed">Single beds</div>
                                    <div class="item" data-value="non_joinable_twin_bed">Non Join-able twin beds</div>
                                    <div class="item" data-value="joinable_twin_bed">Join-able twin beds</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ui segment room_type_details">
                    <h5 class="ui header" data-value="maple">
                        Maple
                    </h5>
                    <div class="field">
                        <label>Is this room type available in property?</label>
                        <div class="ui selection dropdown">
                            <input type="hidden" name="is_room_type_available" value="0">
                            <i class="dropdown icon"></i>
                            <div class="default text">Is this room type available?</div>
                            <div class="menu">
                                <div class="item" data-value="1">Yes</div>
                                <div class="item" data-value="0">No</div>
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> max adults </label>
                        <input type="number" name="max_adults" placeholder="max_adults" value="0">
                    </div>
                    <div class="field">
                        <label> max children </label>
                        <input type="number" name="max_children" placeholder="max_children" value="0">
                    </div>
                    <div class="field">
                        <label> max total </label>
                        <input type="number" name="max_total" placeholder="max_total" value="0">
                    </div>
                    <div class="field">
                        <label> room size </label>
                        <input type="number" name="room_size" placeholder="room size in sq ft" value="0">
                    </div>
                    <div class="ui two column grid field">
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_wardrobe_available">
                                <label>is_wardrobe_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_luggage_shelf_available">
                                <label>is_luggage_shelf_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_coffee_table_available">
                                <label>is_coffee_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_smoking_permitted">
                                <label>is_smoking_permitted</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_study_table_available">
                                <label>is_study_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_mini_fridge_available">
                                <label>is_mini_fridge_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_kitchenette_available">
                                <label>is_kitchenette_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_microwave_oven_available">
                                <label>is_microwave_oven_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_sofa_chair_available">
                                <label>is_sofa_chair_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_dining_table_available">
                                <label>is_dining_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui selection dropdown">
                                <input type="hidden" name="bed_type">
                                <i class="dropdown icon"></i>
                                <div class="default text">Bed type</div>
                                <div class="menu">
                                    <div class="item" data-value="king_bed">King Beds</div>
                                    <div class="item" data-value="queen_bed">Queen Beds</div>
                                    <div class="item" data-value="single_bed">Single beds</div>
                                    <div class="item" data-value="non_joinable_twin_bed">Non Join-able twin beds</div>
                                    <div class="item" data-value="joinable_twin_bed">Join-able twin beds</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ui segment room_type_details">
                    <h5 class="ui header" data-value="mahogany">
                        Mahogany
                    </h5>
                    <div class="field">
                        <label>Is this room type available in property?</label>
                        <div class="ui selection dropdown">
                            <input type="hidden" name="is_room_type_available" value="0">
                            <i class="dropdown icon"></i>
                            <div class="default text">Is this room type available?</div>
                            <div class="menu">
                                <div class="item" data-value="1">Yes</div>
                                <div class="item" data-value="0">No</div>
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> max adults </label>
                        <input type="number" name="max_adults" placeholder="max_adults" value="0">
                    </div>
                    <div class="field">
                        <label> max children </label>
                        <input type="number" name="max_children" placeholder="max_children" value="0">
                    </div>
                    <div class="field">
                        <label> max total </label>
                        <input type="number" name="max_total" placeholder="max_total" value="0">
                    </div>
                    <div class="field">
                        <label> room size </label>
                        <input type="number" name="room_size" placeholder="room size in sq ft" value="0">
                    </div>
                    <div class="ui two column grid field">
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_wardrobe_available">
                                <label>is_wardrobe_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_luggage_shelf_available">
                                <label>is_luggage_shelf_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_coffee_table_available">
                                <label>is_coffee_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_smoking_permitted">
                                <label>is_smoking_permitted</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_study_table_available">
                                <label>is_study_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_mini_fridge_available">
                                <label>is_mini_fridge_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_kitchenette_available">
                                <label>is_kitchenette_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_microwave_oven_available">
                                <label>is_microwave_oven_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_sofa_chair_available">
                                <label>is_sofa_chair_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui checkbox">
                                <input type="checkbox" name="is_dining_table_available">
                                <label>is_dining_table_available</label>
                            </div>
                        </div>
                        <div class="field">
                            <div class="ui selection dropdown">
                                <input type="hidden" name="bed_type">
                                <i class="dropdown icon"></i>
                                <div class="default text">Bed type</div>
                                <div class="menu">
                                    <div class="item" data-value="king_bed">King Beds</div>
                                    <div class="item" data-value="queen_bed">Queen Beds</div>
                                    <div class="item" data-value="single_bed">Single beds</div>
                                    <div class="item" data-value="non_joinable_twin_bed">Non Join-able twin beds</div>
                                    <div class="item" data-value="joinable_twin_bed">Join-able twin beds</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="ui hidden divider"></div>
        <div class="field">
                    <h4 class="ui horizontal divider header">
            Room details
                                    <button class="ui right floated primary button" id="add-room-button">
                Add room
            </button>
        </h4>

            <div class="ui hidden divider"></div>
            <div class="ui segments">
                <div class="ui sixteen wide segment room_detail">
                        <div class="equal width fields">
                        <div class="field">
                            <label> room number </label>
                            <input type="text" name="room_number" placeholder="comma separated room no" value="" autocomplete="new-password">
                        </div>
                        <div class="field">
                            <label> building number </label>
                            <input type="text" name="building_number" placeholder="#building number" value="" autocomplete="new-password">
                        </div>
                        <div class="field">
                            <label> floor number </label>
                            <input type="number" name="floor_number" placeholder="floor number" value="0" autocomplete="new-password">
                        </div>
                        <div class="field">
                            <label for="room_type"> Room Type </label>
                            <div class="ui selection dropdown">
                                <input type="hidden" name="room_type">
                                <i class="dropdown icon"></i>
                                <div class="default text">Room type</div>
                                <div class="menu">
                                    <div class="item" data-value="rt01">acacia</div>
                                    <div class="item" data-value="rt02">oak</div>
                                    <div class="item" data-value="rt03">maple</div>
                                    <div class="item" data-value="rt04">mahogany</div>
                                </div>
                            </div>

                        </div>
                            <div class="field">
                                                        <button class="ui negative basic floated right circular button remove-room-button">x</button>

                            </div>
                        </div>







                </div>
            </div>
        </div>

        <button class="ui fluid positive massive button" type="submit" value="submit">Submit</button>
    </form>
</div>
</body>
</html>