<!doctype html>

<html lang="en">
<head>
    <meta charset="utf-8">
    <title>User Role Creation</title>
    <meta name="description" content="User Role Creation">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.3.1/dist/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.js"></script>
    <script src="{{ url_for('static', filename = 'js/user_role_creation.js')|autoversion }}"></script>
</head>

<body>
<div class="ui container" style="padding-top: 20px;">

    <div class="ui center aligned header" style="overflow: hidden; padding-bottom:50px;">
        <h1>User Role Creation Form</h1>
    </div>
    <div style="padding: 10px;margin: 0;">
    <form class="ui form" action="" method="post" id="user-role-creation-form" name="user_role_creation_form" autocomplete="new-password">
        <div class="field">
            <h4 class="ui horizontal divider header">
                <button class="ui right floated primary button" id="add-user">
                    Add User
                </button>
            </h4>

            <div class="ui segments">
                <div class="ui sixteen wide segment user_role_detail" style="border-radius: 5px; background-color: #f2f2f2; padding: 20px; margin:10px;">
                     <div class="three fields">
                        <div class="required field">
                            <label> First Name </label>
                            <input type="text" name="first_name" placeholder="Enter First Name" value="" autocomplete="new-password" required>
                        </div>
                        <div class="field">
                            <label> Last Name </label>
                            <input type="text" name="last_name" placeholder="Enter Last Name" value="" autocomplete="new-password">
                        </div>
                        <div class="required field">
                            <label> Email </label>
                            <input type="text" name="email" placeholder="Enter Email" autocomplete="new-password" required>
                        </div>
                     </div>
                     <div class="three fields">
                        <div class="required field">
                            <label for="app_name"> ApplicationName </label>
                            <div class="ui selection dropdown">
                                <input type="hidden" name="application_id" required>
                                <i class="dropdown icon"></i>
                                <div class="default text">ApplicationName</div>
                                <div class="menu">
                                    <div class="item" data-value="treebo-pms">Treebo PMS</div>
                                    <div class="item" data-value="treebo-junkyard">Treebo Junkyard</div>
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label> Hotel Id </label>
                            <input type="text" name="hotel_id" placeholder="Enter Hotel Id" value="" autocomplete="new-password">
                        </div>
                        <div class="required field">
                            <label for="role"> Role </label>
                            <div class="ui selection dropdown">
                                <input type="hidden" name="role" required>
                                <i class="dropdown icon"></i>
                                <div class="default text">Role</div>
                                <div class="menu">
                                    <div class="item" data-value="fdm">FDM</div>
                                    <div class="item" data-value="super-admin">SuperAdmin</div>
                                    <div class="item" data-value="backend-system">BackendSystem</div>
                                    <div class="item" data-value="view-only">ViewOnly</div>
                                    <div class="item" data-value="aom">AOM</div>
                                    <div class="item" data-value="gdc">GDC</div>
                                    <div class="item" data-value="cr-team">CRTeam</div>
                                </div>
                            </div>
                        </div>
                     </div>
                        <div class="field">
                            <button class="ui negative basic floated right circular button remove-user-button">x</button>
                        </div>
                </div>
            </div>
        </div>

        <button class="ui fluid positive massive button" type="submit" value="submit">Submit</button>
    </form>
    </div>
</div>
</body>
</html>