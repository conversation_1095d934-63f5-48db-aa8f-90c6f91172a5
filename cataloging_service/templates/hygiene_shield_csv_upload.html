<html>
<head>
    <meta charset="utf-8">
    <title>POS menu</title>
    <meta name="description" content="File Upload">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.3.1/dist/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.js"></script>
</head>
<body>
<div class="ui container">
    <h1 class="ui center aligned header" style="margin-top: 32px;">
        <i class="cloud upload icon"></i>
        Upload Hygiene Shield Details
    </h1>
    <form class="ui form " method=POST enctype=multipart/form-data action="{{ url_for('property_v3.hygiene_shield') }}">
        <input type=file name="data">
        <button class="ui grey basic button " type="submit" style="margin-top: 20px;margin-left: 47%">Submit</button>
    </form>

    {% if code == 400 %}
    <h4 class="ui horizontal divider header">
        <i class="close icon"></i>
        Failed
    </h4>
    <p style="margin-left: 40%">Fix below errors and try uploading again.</p>
    <table class="ui celled table">
        <thead>
        <tr>
            <th>Line_number</th>
            <th>Reason</th>
        </tr>
        </thead>
        <tbody>
        {% for error, value in errors.items() %}
        <tr>
            <td data-label="Line_number">{{error}}</td>
            <td data-label="Reason">{{value}}</td>
        </tr>
        {% endfor %}
        </tbody>
    </table>
    {% elif code == 200 %}
        <p style="margin-left: 40%">Successfully added hygiene shield details</p>
    {% endif%}
</div>


</body>
</html>