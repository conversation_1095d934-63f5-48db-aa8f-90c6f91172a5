<!DOCTYPE html>
<html style="background: black;">
<head>
	<title>Hotel Details</title>

    <script src="{{ url_for('static', filename = 'js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename = 'js/jquery-ui.min.js') }}"></script>
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.png') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'styles/jquery-ui.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'styles/jquery-ui.structure.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'styles/jquery-ui.theme.min.css') }}">
    <!--<link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">-->
    <script>
        $(function() {
            $( "#searchtext", this ).autocomplete({
               source: [
//                  { label: "Treebo Elmas", value: "CS_1492955857" },
//                  { label: "Treebo Cartier", value: "CS_1492955857" },
                   {% for prop in all_properties %}
                        {label: "{{prop.name}}", value: "{{prop.id}}"},
                   {% endfor %}
               ],
                autoFocus: true,
                delay: 800,
                select: function( event, ui ) {
                    window.location.replace('{{ url_for('ui_routes.show_property', property_id='') }}' + ui.item.value);
                }
            });
         });

        $(document).ready(function(){
            $("#landmark").hide();
            $("#room-amenities").hide();
            $("#description").hide(500);
            $("#loc-link").click(function(){
                $("#landmark").hide(500);
                $("#room-amenities").hide(500);
                $("#loc").show(500);
                $("#description").hide(500);
                $('html, body').animate({scrollTop: '0px'}, 0);
            });
            $("#lm-link").click(function(){
                $("#loc").hide(500);
                $("#room-amenities").hide(500);
                $("#landmark").show(500);
                $("#description").hide(500);
                $('html, body').animate({scrollTop: '0px'}, 0);
            });
            $("#ra-link").click(function(){
                $("#landmark").hide(500);
                $("#loc").hide(500);
                $("#room-amenities").show(500);
                $("#description").hide(500);
                $('html, body').animate({scrollTop: '0px'}, 0);
            });
            $("#de-link").click(function () {
                $("#landmark").hide(500);
                $("#loc").hide(500);
                $("#room-amenities").hide(500);
                $("#description").show(500);
                $('html, body').animate({scrollTop: '0px'}, 0);
            });
        });
    </script>
	<style>
        .topnav {
		    background-color: #F7F7F7;
		    overflow: hidden;
		}

		/* Style the links inside the navigation bar */
		.topnav a {
		    float: left;
		    display: block;
		    padding: 10px 10px;
		    text-align: center;
		    text-decoration-line: none;
		    color: 	#212121;
            font-family: Roboto;
            font-size: 14px;
            font-weight: bold;
		    line-height: 19px;
		    width: 150px;
		}

        .av-status {
            margin-left: 60px;
            margin-top: 5px;
            float: left;
        }

		/* Change the color of links on hover */
		.topnav a:hover {
		    background-color: #ddd;
		    color: #0EB550;
		}

		/* Add a color to the active/current link */
		.topnav a.active {
		    background-color: #4CAF50;
		    color: white;
		}

        #detail-separator {
		    border: 1px solid;
		    border-color: #F1F1F1;
		    margin-left: 10px;
		    margin-right: 10px;
		}

        .info-paragraph {
            color: #727171;
            font-family: "Lucida Grande";
            font-size: 12px;
            line-height: 24px;
            padding-left: 15px;
            padding-right: 5px;
            width: 650px;
            padding-bottom: 15px;
        }

		.left-details {
			width: 370px;
			height: 3800px;
			background-color: #4A4A4A;
			box-shadow: 0 0 2px 0 rgba(0,0,0,0.12), 0 2px 2px 0 rgba(0,0,0,0.12);
			float: left;
			display: inline-block;
            color: #9B9B9B;
            font-family: Roboto;
            font-size: 14px;
			line-height: 19px;
		}
		.right-details {
			width: 790px;
			height: 3800px;
			background-color: #FFFFFF;
			box-shadow: 0 0 2px 0 rgba(0,0,0,0.12), 0 2px 2px 0 rgba(0,0,0,0.12);
			display: inline-block;
			float: left;
		}

		.detail-block {
			margin-left: 40px;
			margin-top: 40px;
			float: left;
		}

		.detail-block-pair {
			display: inline-block;
		}

		.detail-header {
            color: #E0E0E0;
            font-family: Roboto;
            font-size: 12px;
            font-weight: bold;
			line-height: 16px;
		}

		.detail-value {
            color: #9B9B9B;
            font-family: Roboto;
            font-size: 14px;
			line-height: 19px;
            width: 100px;
		}

		#hotel-new-name {
            color: #FFFFFF;
            font-family: Roboto;
			font-size: 22px;
			font-weight: 800;
			line-height: 28px;
            width: 200px;
		}

		.main-details {
			background-color: #FFFFFF;
			padding-top: 40px;
			padding-bottom: 20px;
		}

		.main-details-alternate {
			background-color: #F7F7F7;
			padding-top: 40px;
			padding-bottom: 20px;
		}

		.md-detail-header {
			color: #212121;
            font-family: Roboto;
            font-size: 12px;
            font-weight: bold;
			line-height: 16px;
		}

		.md-detail-value {
			color: #727171;
            font-family: Roboto;
            font-size: 14px;
			line-height: 19px;
            width: 110px;
		}

		.md-detail-block {
			margin-left: 60px;
			margin-top: 40px;
			float: left;
		}

		.md-detail-block-next {
			margin-left: 60px;
			margin-top: 40px;
			float: left;
		}

		.md-header {
			height: 28px;
			width: 300px;
			color: #212121;
			font-family: Averta;
			font-weight: bold;
			font-size: 22px;
			font-weight: 800;
			line-height: 28px;
			margin-left: 60px;
		}

		.amenity {
			margin-top: 20px;
			padding-bottom: 10px;
		}

        #av-true {
			margin-left: 60px;
			margin-top: 5px;
			float: left;
		}

		.amenity-header {
			height: 25px;
			color: #212121;
			font-family: Averta;
			font-size: 20px;
			line-height: 25px;
			margin-left: 10px;
			display: inline-block;
			float: left;
			padding-bottom: 5px;
		}

		.amenity-detail-value {
			color: #727171;
            font-family: Roboto;
            font-size: 14px;
			line-height: 19px;
			clear: both;
			margin-left: 87px;
			margin-top: 5px;
		}

		#scrollable-content {
			/*height: 600px;
			overflow-y: auto;*/
		}

        .lm {
            margin-top: 20px;
            padding-bottom: 10px;
            margin-left: 50px;
        }

        .lm ul {
            list-style: none;
            margin-left: 0;
            padding-left: 10px;
            color: #727171;
            font-family: "Lucida Grande";
            font-size: 12px;
            line-height: 24px;
        }

        .lm li {
            padding-left: 1em;
            text-indent: -1em;
        }

        .lm li:before {
            content: "→";
            padding-right: 5px;
        }

        .lm-header {
            height: 25px;
            color: #212121;
            font-family: Averta;
            font-size: 20px;
            line-height: 25px;
            margin-left: 10px;
            display: inline-block;
            float: left;
            padding-bottom: 40px;
            width: 700px;
        }

        .dt-header {
            height: 25px;
            color: #212121;
            font-family: Averta;
            font-size: 20px;
            line-height: 25px;
            margin-left: 10px;
            display: inline-block;
            float: left;
            padding-bottom: 15px;
            padding-top: 15px;
            width: 700px;
        }

        .lm-detail-header {
            color: #212121;
            font-family: Roboto;
            font-size: 12px;
            font-weight: bold;
            line-height: 16px;
            margin-left: 10px;
            padding-top: 15px;
        }

        .lm-detail-value {
            height: 28px;
            width: 198px;
            color: #212121;
            font-family: Averta;
            font-size: 22px;
            line-height: 28px;
            margin-left: 10px;
            padding-bottom: 30px;
        }

        #lm-separator {
            border: 1px solid;
            border-color: #F1F1F1;
            margin-left: 10px;
            margin-right: 10px;
        }

        .transport-header {
            height: 25px;
            color: #212121;
            font-family: Averta;
            font-size: 20px;
            line-height: 25px;
            margin-left: 10px;
            display: inline-block;
            float: left;
            width: 700px;
        }

        .transport-address {
            color: #727171;
            font-family: Roboto;
            font-size: 14px;
            line-height: 19px;
            padding-bottom: 30px;
            margin-left: 10px;
        }

        a {
            color: #5768E9;
            font-family: Roboto;
            font-size: 14px;
            line-height: 19px;
            text-decoration: none;
        }

        .room-details {
            margin-left: 10px;
            margin-right: 10px;
            border: 1px solid;
        }

        table {
            border-collapse: collapse;
            border-radius: 4px 4px 0 0;
            width: 760px;
            margin-left: 10px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.13);
            border-radius: 4px;
        }

        th {
            border: 1px solid #E0E0E0;
            background-color: #3344A9;
            height: 18px;
            width: 150px;
            color: #FFFFFF;
            font-family: Roboto;
            font-size: 13px;
            font-weight: 500;
            line-height: 18px;
            padding: 10px;
        }

        tr {
            color: #727171;
            font-family: Roboto;
            font-size: 12px;
            line-height: 14px;
            text-align: center;
            padding: 20px;
        }

        td {
            border: 1px solid #E0E0E0;
        }

        .av-image {
            float: left;
            margin-left: 20px;
        }

        .av-details {
            float: left;
            margin-left: 10px;
            width: 80px;
        }

        .room-num {
            margin-top: 10px;
            float: left;
        {#            display: inline-block;#}
            margin-left: 40px;
            padding-bottom: 10px;
        }

        .amenity-sub-header {
            color: #727171;
            font-weight: bold;
            font-family: Averta;
            font-size: 14px;
            line-height: 19px;
            clear: both;
            margin-left: 87px;
            margin-top: 5px;
        }

        .amenity-sub-detail-value {
            color: #727171;
            font-family: Roboto;
            font-size: 12px;
            line-height: 19px;
            clear: both;
            margin-left: 95px;
            margin-top: 5px;
        }

        @font-face {
            font-family: Averta;
            font-style: normal;
            font-weight: 400;
            src:
               url({{ url_for('static', filename = 'fonts/Intelligent Design - Averta-Bold.otf') }}) format('opentype');
        }

        @font-face {
            font-family: Roboto;
            font-style: normal;
            src:
               url({{ url_for('static', filename = 'fonts/Roboto-Regular.ttf') }}) format('truetype');
        }

	</style>

</head>
<body>
	<div id="main" style="
            background-image: url('{{ url_for('static', filename = 'img/cat-bg1.png') }}'); background-repeat: no-repeat;">
		<div id="header">
			<div id="product-name" style="display: inline-block; height: 60px; width: 140px; vertical-align: bottom;">
				<div style="width: 151px;
							height: 29px;
							font-family: Averta;
							font-size: 24px;
							font-weight: 800;
							line-height: 29px;
							color: #0EB550;
							padding-left: 36px;">Catalog</div>
				<div style="width: 72.99px;
							height: 16px;
							font-family: Roboto;
							font-size: 12px;
							line-height: 16px;
							color: #A19FA1;
							padding-left: 36px;">By Treebo</div>
			</div>
			<div id="search-box" style="height: 60px;
										width: 85%;
										display: inline-block;">
				<img src="{{ url_for('static', filename = 'img/Shape.png') }}" style="height: 16px; width: 16px; margin-left: 10px; margin-top: 10px;">
				<input id="searchtext" name="hotel-name" style="width: 300px;
							height: 19px;
							font-family: Roboto;
							font-size: 14px;
							line-height: 19px;
							color: #727171;
							display: inline-block;
							border-width: 1px;
							border: none;
						    border-bottom: 1px solid #808080;
						    background: black;
						    padding: 8px;"/>

				<div style="display: inline-block; float: right; margin-right: 10px;
							width: 58px;
							height: 20px;
							font-family: Roboto;
							font-size: 14px;
							font-weight: 500;
							line-height: 20px;
							color: #5768E9;
							margin-top: 10px;">
                    <a href="{{ url_for_security('logout') }}"> LOGOUT </a>
				</div>

				<div style="display: inline-block;
							float: right;
							margin-right: 40px;
							margin-top: 10px;
							width: 100px;
							height: 20px;
							font-family: Roboto;
							font-size: 14px;
							font-weight: 500;
							line-height: 20px;
							color: #9B9B9B;">
                    {% if current_user.is_authenticated %}
                        HI {{ current_user.email.split('@')[0].strip().upper() }}
                    {% endif %}
				</div>
			</div>
			<div id="hotel-wrapper" style="
						margin: 0 auto;
						margin-top: 50px;
						padding-bottom: 40px;
						width: 1160px;">

				<div style="height: 39px;
							color: #E0E0E0;
							font-family: Averta;
							font-size: 32px;
							font-weight: 800;
							line-height: 39px;
							color: #E0E0E0;
							display: inline-block;">
                    {% if property.name %}{{ property.name.upper() }}{% endif %}
				</div>
				<div style="height: 39px;
							color: #9B9B9B;
							font-family: Averta;
							font-size: 32px;
							font-weight: 300;
							line-height: 39px;
							display: inline-block;
							margin-left: 20px;">
                    {% if property.location.locality %}
                        {{ property.location.locality.name }},
                    {% endif %}
                    {{ property.location.city.name }}
				</div>
			</div>

			<!-- Details Wrapper -->
			<div id="details-wrapper" style="width: 1160px; height: 1200px; margin: 0 auto;">
				<div class="left-details">
					<div class="detail-block">
						<div class="detail-header">New Hotel Name</div>
                        <div id="hotel-new-name">{% if property.name %}{{ property.name.title() }}{% endif %}</div>
					</div>
                    <div class="detail-block-pair">
                        <div class="detail-block">
                            <div class="detail-header">CS ID</div>
                            <div class="detail-value">{{ property.id }}</div>
                        </div>
                        <div class="detail-block">
                            <div class="detail-header">HX ID</div>
                            <div class="detail-value">{{ property.hx_id }}</div>
                        </div>
                    </div>
                    <div class="detail-block">
                        <div class="detail-header">Address</div>
                        <div class="detail-value" style="width: 200px;">
                            {{ property.location.postal_address }}
                        </div>
                    </div>
                    <div class="detail-block-pair">
						<div class="detail-block">
							<div class="detail-header">Locality</div>
                            <div class="detail-value">
                                {% if property.location.locality %}
                                    {{ property.location.locality.name }}
                                {% endif %}</div>
						</div>
						<div class="detail-block">
							<div class="detail-header">Pincode</div>
							<div class="detail-value">{{property.location.pincode}}</div>
						</div>
					</div>
					<div class="detail-block-pair">
						<div class="detail-block">
							<div class="detail-header">City</div>
							<div class="detail-value">{{property.location.city.name}}</div>
						</div>
						<div class="detail-block">
							<div class="detail-header">State</div>
                            <div class="detail-value">{{ property.location.city.state.name }}</div>
						</div>
					</div>
					<div class="detail-block-pair">
						<div class="detail-block">
							<div class="detail-header">Total Rooms</div>
                            <div class="detail-value">{{ property.rooms|selectattr("is_active", "equalto", True)|list|length }}</div>
						</div>
						<div class="detail-block">
							<div class="detail-header">Property Status</div>
                            <div class="detail-value">{{ property.status.replace('_', ' ').capitalize() }}</div>
						</div>
					</div>
					<div class="detail-block-pair">
						<div class="detail-block">
							<div class="detail-header">Property Type</div>
                            <div class="detail-value">{{ property.property_detail.property_type.replace('_', ' ').capitalize() }}</div>
						</div>
						<div class="detail-block">
							<div class="detail-header">Star Rating</div>
                            <div class="detail-value">{{ property.property_detail.star_rating }} star</div>
						</div>
					</div>
                    <div class="detail-block-pair">
						<div class="detail-block">
							<div class="detail-header">Reception Mobile</div>
                            <div class="detail-value">{{ property.property_detail.reception_mobile }}</div>
						</div>
						<div class="detail-block">
                            <div class="detail-header">Reception Landline</div>
                            <div class="detail-value">{{ property.property_detail.reception_landline }}</div>
						</div>
					</div>
				</div>
				<div class="right-details">
					<div class="topnav" id="myTopnav">
					  <a href="#" id="loc-link">Hotel Details</a>
					  <a href="#" id="lm-link">Direction & Landmarks</a>
					  <a href="#" id="ra-link">Room Amenities</a>
                        <a href="#" id="de-link">Description</a>
					</div>
                    <div id="loc">

						<div class="main-details" id="location">
							<div class="md-header">Location</div>
							<div class="md-content">
								<div class="md-detail-block">
									<div class="md-detail-header">Address</div>
									<div class="md-detail-value" style="width: 500px;">{{property.location.postal_address}}</div>
								</div>
								<div class="detail-block-pair">
									<div class="md-detail-block">
										<div class="md-detail-header">Locality</div>
                                        <div class="md-detail-value">
                                            {% if property.location.locality %}
                                                {{ property.location.locality.name }}
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Pincode</div>
                                        <div class="md-detail-value">
                                            {{ property.location.pincode }}
                                        </div>
                                    </div>
                                    <div class="md-detail-block-next">
                                        <div class="md-detail-header">Micromarket</div>
                                        <div class="md-detail-value">
                                            {% if property.location.micro_market %}
                                                {{ property.location.micro_market.name }}
                                            {% endif %}
                                        </div>
									</div>
                                    <div class="md-detail-block-next">
                                        <div class="md-detail-header">City</div>
                                        <div class="md-detail-value">{{ property.location.city.name }}</div>
                                    </div>
                                </div>
                                <div class="detail-block-pair">
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">State</div>
                                        <div class="md-detail-value">{{ property.location.city.state.name }}</div>
                                    </div>
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Cluster</div>
                                        <div class="md-detail-value">{{ property.location.city.cluster.name }}</div>
                                    </div>
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Country</div>
                                        <div class="md-detail-value">{{ property.location.city.state.country.name }}</div>
                                    </div>
                                    <div class="md-detail-block-next">
                                        <div class="md-detail-header">Google Map Location</div>
                                        <div class="md-detail-value">
                                            {% if property.location.maps_link and not property.location.maps_link.startswith('http') %}
                                                <a href="//{{ property.location.maps_link }}" target="_blank">
                                                    Google map link
                                                </a>
                                            {% elif property.location.maps_link %}
                                                <a href="{{ property.location.maps_link }}" target="_blank">
                                                    Google map link
                                                </a>
                                            {% endif %}
                                        </div>
									</div>
								</div>
                                <div class="detail-block-pair">
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Latitude</div>
                                        <div class="md-detail-value">{{ property.location.latitude }}</div>
                                    </div>
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Longitude</div>
                                        <div class="md-detail-value">{{ property.location.longitude }}</div>
                                    </div>
                                </div>
							</div>
						</div>
                        {% if users %}
                            <div class="main-details-alternate">
                                <div class="md-header">Managing Team</div>
                                {% for user_group in users %}
                                    <div class="md-content">
                                        <div class="detail-block-pair">
                                            {% for user in user_group %}
                                                <div class="md-detail-block">
                                                    <div class="md-detail-header">QAM</div>
                                                    <div class="md-detail-value">{{ user['first_name'].title() }} {{ user['last_name'].title() }}</div>
                                                    <div class="md-detail-value">{{ user['phone_number'] }}</div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}

						<div class="main-details">
							<div class="md-header">Other Details</div>
							<div class="md-content">
								<div class="detail-block-pair">
									<div class="md-detail-block">
										<div class="md-detail-header">Neighbourhood</div>
                                        <div class="md-detail-value">{{ property.property_detail.neighbourhood_type.replace('_', ' ').capitalize() }}</div>
									</div>
                                    <div class="md-detail-block-next">
                                        <div class="md-detail-header">Property Style</div>
                                        <div class="md-detail-value">{{ property.property_detail.property_style.replace('_', ' ').capitalize() }}</div>
									</div>
                                    <div class="md-detail-block-next">
										<div class="md-detail-header">Suited to Guest Type</div>
                                        <div class="md-detail-value">
                                            {% set all_guests = [] %}
                                            {% for guest_type in  property.guest_types %}
                                                {% do all_guests.append(guest_type.type) %}
                                            {% endfor %}
                                            {{ ', '.join(all_guests).title() }}

                                        </div>
									</div>
                                    <div class="md-detail-block">
										<div class="md-detail-header">Year of Construction</div>
                                        <div class="md-detail-value">{{ property.property_detail.construction_year }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="md-content">
                                <div class="detail-block-pair">
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Building Style</div>
                                        <div class="md-detail-value">{{ property.property_detail.building_style.replace('_', ' ').capitalize() }}</div>
                                    </div>
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Unmarried Couples</div>
                                        <div class="md-detail-value">
                                            {% if property.property_detail.unmarried_couple_allowed %}
                                                Allowed
                                            {% else %}
                                                Not Allowed
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="md-detail-block">
                                        <div class="md-detail-header">Local ID</div>
                                        <div class="md-detail-value">
                                            {% if property.property_detail.local_id_allowed %}
                                                Allowed
                                            {% else %}
                                                Not Allowed
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="md-detail-block-next">
                                        <div class="md-detail-header">Floors</div>
                                        <div class="md-detail-value">{{ property.property_detail.floor_count }}</div>
                                    </div>
                                </div>
                            </div>
						</div>

                        {% set present_image=url_for('static', filename = 'img/av-true.png') %}
                        {% set not_present_image=url_for('static', filename = 'img/Cross.svg') %}
                        <div class="main-details-alternate">
							<div class="md-header" id="hotel-amenities">Hotel Amenities</div>
							<div class="md-content">
                                <div class="amenity">
                                    {% set banquet_halls = property.banquet_halls %}
                                    {% set hall_available = banquet_halls|length %}

                                    <img src="{% if hall_available %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                         class="av-status">
                                    <div class="amenity-header">Banquet Halls</div>
                                    <div class="amenity-detail-value">Availability: {% if hall_available %}
                                        Yes {% else %}
                                        No{% endif %}</div>
                                    {% for hall in banquet_halls %}
                                        <div class="amenity-sub-header">Hall {{ loop.index }}</div>
                                        <div class="amenity-sub-detail-value">Name:
                                            {% if hall.name %}{{ hall.name.title() }}{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Floor:
                                            {{ hall.floor }}</div>
                                        <div class="amenity-sub-detail-value">Capacity:
                                            {{ hall.capacity }}</div>
                                        <div class="amenity-sub-detail-value">Size:
                                            {{ hall.size }}</div>
                                    {% endfor %}
                                </div>
                                <hr id="detail-separator"/>
                                <div class="amenity">
                                    {% set bars = property.bars %}
                                    {% set bar_available = bars|length %}

                                    <img src="{% if bar_available %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                         class="av-status">
                                    <div class="amenity-header">Bars</div>
                                    <div class="amenity-detail-value">Availability: {% if bar_available %}
                                        Yes {% else %}
                                        No{% endif %}</div>
                                    {% for bar in bars %}
                                        <div class="amenity-sub-header">Bar {{ loop.index }}</div>
                                        <div class="amenity-sub-detail-value">Name:
                                            {% if bar.name %}{{ bar.name.title() }}{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Open
                                            Time: {{ bar.open_time }}</div>
                                        <div class="amenity-sub-detail-value">Last Order
                                            Time: {{ bar.last_order_time }}</div>
                                        <div class="amenity-sub-detail-value">Close
                                            Time: {{ bar.close_time }}</div>
                                        <div class="amenity-sub-detail-value">Room Service Starts
                                            At: {{ bar.room_start_time }}</div>
                                        <div class="amenity-sub-detail-value">Room Service Ends
                                            At: {{ bar.room_end_time }}</div>
                                    {% endfor %}
                                </div>
                                <hr id="detail-separator"/>
                                {% if property.property_amenity %}
                                    <div class="amenity">
                                        {% set breakfast = property.property_amenity.breakfast %}

                                        <img src="{% if breakfast %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Breakfast</div>
                                        <div class="amenity-detail-value">Availability: {% if breakfast %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if breakfast %}
                                            <div class="amenity-detail-value">
                                                Type: {% if breakfast.type %}
                                                {{ breakfast.type.replace('_', ' ').capitalize() }}{% endif %}</div>
                                            <div class="amenity-detail-value">
                                                Service Area: {% if breakfast.service_area %}
                                                {{ breakfast.service_area.replace('_', ' ').capitalize() }}{% endif %}</div>
                                            <div class="amenity-detail-value">Non-Veg: {% if breakfast.non_veg %}
                                                Yes {% else %}No{% endif %}</div>
                                            <div class="amenity-detail-value">Rotational: {% if breakfast.rotational %}
                                                Yes {% else %}No{% endif %}</div>
                                            <div class="amenity-detail-value">Cuisines:
                                                {% set all_breakfast_cuisines = [] %}
                                                {% for cuisine in  breakfast.cuisines %}
                                                    {% do all_breakfast_cuisines.append(cuisine.name) %}
                                                {% endfor %}
                                                {{ ', '.join(all_breakfast_cuisines).title() }}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set cloak_room = property.property_amenity.cloak_room %}

                                        <img src="{% if cloak_room %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Cloak Room</div>
                                        <div class="amenity-detail-value">Availability: {% if cloak_room %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set disable_friendly = property.property_amenity.disable_friendly %}

                                        <img src="{% if (disable_friendly.ramp_available or (disable_friendly.wheelchair_count and disable_friendly.wheelchair_count > 0) or disable_friendly.disable_friendly_room_available) %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Disable Friendly</div>
                                        {% if disable_friendly %}
                                            <div class="amenity-detail-value">
                                                Wheelchair Ramp Available: {% if disable_friendly.ramp_available %}
                                                Yes {% else %}No{% endif %}</div>
                                            <div class="amenity-detail-value">
                                                Wheelchair Count: {% if disable_friendly.wheelchair_count %}
                                                {{ disable_friendly.wheelchair_count }}{% else %}N/A{% endif %}</div>
                                            <div class="amenity-detail-value">
                                                Disable Friendly Rooms:
                                                {% if disable_friendly.disable_friendly_rooms %}
                                                    {{ disable_friendly.disable_friendly_rooms }} {% else %}
                                                    N/A{% endif %}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set driver_quarters_count = property.property_amenity.driver_quarters_count %}

                                        <img src="{% if driver_quarters_count and (driver_quarters_count > 0) %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Driver Quarters</div>
                                        <div class="amenity-detail-value">Availability:
                                            {% if driver_quarters_count and (driver_quarters_count > 0) %}
                                                Yes {% else %}No{% endif %}</div>
                                        {% if driver_quarters_count and (driver_quarters_count > 0) %}
                                            <div class="amenity-detail-value">
                                                How Many: {{ driver_quarters_count }}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set elevator = property.property_amenity.elevator %}

                                        <img src="{% if elevator %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Elevator</div>
                                        <div class="amenity-detail-value">Availability: {% if elevator %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if elevator %}
                                            <div class="amenity-detail-value">Floors Accessible:
                                                {{ elevator.floors_accessible }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set gym = property.property_amenity.gym %}

                                        <img src="{% if gym %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Gym</div>
                                        <div class="amenity-detail-value">Availability: {% if gym %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if gym %}
                                            <div class="amenity-detail-value">
                                                Open Time: {{ gym.open_time }}</div>
                                            <div class="amenity-detail-value">
                                                Close Time: {{ gym.close_time }}</div>
                                            <div class="amenity-detail-value">
                                                Equipments: {% if gym.equipments_available %}
                                                {{ gym.equipments_available.title() }} {% endif %}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set iron_boards = property.property_amenity.iron_board_count %}

                                        <img src="{% if iron_boards and (iron_boards > 0) %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Iron Boards</div>
                                        <div class="amenity-detail-value">Availability:
                                            {% if iron_boards and (iron_boards > 0) %}
                                                Yes {% else %}No{% endif %}</div>
                                        {% if iron_boards and (iron_boards) > 0 %}
                                            <div class="amenity-detail-value">
                                                How Many: {{ iron_boards }}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set laundry = property.property_amenity.laundry %}

                                        <img src="{% if laundry %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">{% if laundry.is_external %}
                                            Laundry Service (Third Party)
                                        {% else %}Laundry Service (In-House)
                                        {% endif %}</div>
                                        <div class="amenity-detail-value">Availability: {% if laundry %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if laundry %}
                                            <div class="amenity-detail-value">
                                                Pickup Time: {{ laundry.pickup_time }}</div>
                                            <div class="amenity-detail-value">
                                                Drop Time: {{ laundry.drop_time }}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set lobby_ac = property.property_amenity.lobby_ac %}

                                        <img src="{% if lobby_ac %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Lobby AC</div>
                                        <div class="amenity-detail-value">Availability: {% if lobby_ac %}Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set lobby_furniture = property.property_amenity.lobby_furniture %}

                                        <img src="{% if lobby_furniture %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Lobby Furniture</div>
                                        <div class="amenity-detail-value">Availability: {% if lobby_furniture %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set lobby_smoke_alarm = property.property_amenity.lobby_smoke_alarm %}

                                        <img src="{% if lobby_smoke_alarm %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Lobby Smoke Alarm</div>
                                        <div class="amenity-detail-value">Availability: {% if lobby_smoke_alarm %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set pantry = property.property_amenity.pantry %}

                                        <img src="{% if pantry %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Pantry</div>
                                        <div class="amenity-detail-value">Availability: {% if pantry %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set parking = property.property_amenity.parking %}

                                        <img src="{% if parking %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Parking</div>
                                        <div class="amenity-detail-value">Availability: {% if parking %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if parking %}
                                            <div class="amenity-detail-value">
                                                Location:{{ parking.location.replace('_', '/').title() }}</div>
                                            <div class="amenity-detail-value">
                                                Max Two Wheelers: {{ parking.max_two_wheelers }}</div>
                                            <div class="amenity-detail-value">
                                                Max Four Wheelers: {{ parking.max_four_wheelers }}</div>
                                            <div class="amenity-detail-value">
                                                Charges: {{ parking.charges }}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set payment = property.property_amenity.payment %}

                                        <img src="{% if payment and (payment.amex_accepted or payment.wallet_accepted) %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Payment</div>
                                        {% if payment %}
                                            <div class="amenity-detail-value">
                                                Amex Accepted: {% if payment.amex_accepted %}
                                                Yes {% else %}No{% endif %}</div>
                                            <div class="amenity-detail-value">
                                                Wallet Accepted: {% if payment.wallet_accepted %}
                                                Yes {% else %}No{% endif %}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set pets = property.property_amenity.pets_allowed %}

                                        <img src="{% if pets %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Pets</div>
                                        <div class="amenity-detail-value">Allowed: {% if pets %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set pool_table = property.property_amenity.pool_table %}

                                        <img src="{% if pool_table %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Pool Table</div>
                                        <div class="amenity-detail-value">Availability: {% if pool_table %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set private_cab = property.property_amenity.private_cab %}

                                        <img src="{% if private_cab %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Private Cab</div>
                                        <div class="amenity-detail-value">Availability: {% if private_cab %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if private_cab %}
                                            <div class="amenity-detail-value">
                                                Charges: {% if  private_cab.charges %}
                                                {{ private_cab.charges.capitalize() }}{% endif %}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set public_washroom = property.property_amenity.public_washroom %}

                                        <img src="{% if public_washroom %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Public Washroom</div>
                                        <div class="amenity-detail-value">Availability: {% if public_washroom %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if public_washroom %}
                                            <div class="amenity-detail-value">Different bathroom for men & women:
                                                {% if public_washroom.gender_segregated %} Yes {% else %}
                                                    No{% endif %}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                {% endif %}
                                <div class="amenity">
                                    {% set restaurants = property.restaurants %}
                                    {% set restaurant_available = restaurants|length %}

                                    <img src="{% if restaurant_available %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                         class="av-status">
                                    <div class="amenity-header">Restaurants</div>
                                    <div class="amenity-detail-value">Availability: {% if restaurant_available %}
                                        Yes {% else %}
                                        No{% endif %}</div>
                                    {% for restaurant in restaurants %}
                                        <div class="amenity-sub-header">Restaurant {{ loop.index }}</div>
                                        <div class="amenity-sub-detail-value">Name:
                                            {% if restaurant.name %}{{ restaurant.name.title() }}{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Non-Veg:
                                            {% if restaurant.non_veg %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">à la carte:
                                            {% if restaurant.a_la_carte %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Buffet:
                                            {% if restaurant.buffet %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Outside Food Allowed:
                                            {% if restaurant.outside_food_allowed %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Baby Milk Served:
                                            {% if restaurant.baby_milk_served %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Handwash Present:
                                            {% if restaurant.handwash_present %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Washroom Present:
                                            {% if restaurant.washroom_present %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Eggs Served:
                                            {% if restaurant.egg_served %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Jain Food Served:
                                            {% if restaurant.jain_food_served %}Yes{% else %}No{% endif %} </div>
                                        <div class="amenity-sub-detail-value">Room Service Starts
                                            At: {{ restaurant.room_service_start_time }}</div>
                                        <div class="amenity-sub-detail-value">Room Service Ends
                                            At: {{ restaurant.room_service_end_time }}</div>
                                        <div class="amenity-sub-detail-value">
                                            Cuisines: {% set restaurant_cuisines = [] %}
                                            {% for cuisine in  restaurant.cuisines %}
                                                {% do restaurant_cuisines.append(cuisine.name) %}
                                            {% endfor %}
                                            {{ ', '.join(restaurant_cuisines).title() }}</div>
                                        <div class="amenity-sub-detail-value">Open
                                            Time: {{ restaurant.open_time }}</div>
                                        <div class="amenity-sub-detail-value">Last Order
                                            Time: {{ restaurant.last_order_time }}</div>
                                        <div class="amenity-sub-detail-value">Close
                                            Time: {{ restaurant.close_time }}</div>
                                    {% endfor %}

                                </div>

                                {% if property.property_amenity %}
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set roof_cafe = property.property_amenity.roof_top_cafe %}

                                        <img src="{% if roof_cafe %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Roof Top Café</div>
                                        <div class="amenity-detail-value">Availability: {% if roof_cafe %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set room_service = property.property_amenity.room_service %}

                                        <img src="{% if room_service %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Room Service</div>
                                        <div class="amenity-detail-value">Availability: {% if room_service %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set security = property.property_amenity.security %}

                                        <img src="{% if security %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Security</div>
                                        <div class="amenity-detail-value">Availability: {% if security %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set spa = property.property_amenity.spa %}

                                        <img src="{% if spa %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Spa</div>
                                        <div class="amenity-detail-value">Availability: {% if spa %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if spa %}
                                            <div class="amenity-detail-value">
                                                Open Time: {{ spa.open_time }}</div>
                                            <div class="amenity-detail-value">
                                                Close Time: {{ spa.close_time }}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set swimming_pool = property.property_amenity.swimming_pool %}

                                        <img src="{% if swimming_pool %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Swimming Pool</div>
                                        <div class="amenity-detail-value">Availability: {% if swimming_pool %}
                                            Yes {% else %}No{% endif %}</div>
                                        {% if swimming_pool %}
                                            <div class="amenity-detail-value">
                                                Location: {{ swimming_pool.location.replace('_','/').title() }}</div>
                                            <div class="amenity-detail-value">
                                                Size: {% if  swimming_pool.pool_size %}
                                                {{ swimming_pool.pool_size.capitalize() }}{% endif %}</div>
                                            <div class="amenity-detail-value">
                                                Open Time: {{ swimming_pool.open_time }}</div>
                                            <div class="amenity-detail-value">
                                                Close Time: {{ swimming_pool.close_time }}</div>
                                        {% endif %}
                                    </div>
                                    <hr id="detail-separator"/>
                                    <div class="amenity">
                                        {% set travel_desk = property.property_amenity.travel_desk %}

                                        <img src="{% if travel_desk %} {{ present_image }} {% else %}{{ not_present_image }}{% endif %}"
                                             class="av-status">
                                        <div class="amenity-header">Travel Desk</div>
                                        <div class="amenity-detail-value">Availability: {% if travel_desk %}
                                            Yes {% else %}
                                            No{% endif %}</div>
                                    </div>
                                    <hr id="detail-separator"/>
                                {% endif %}

						</div>
					    </div>
                    </div>

                    <div id="landmark">
                        {% if property.property_landmarks|length > 0 %}
                        <div class="main-details">
                            <div class="md-header">LANDMARKS</div>
                            <div class="md-content">
                                {% for landmark in property.property_landmarks %}
                                    <div class="lm">
                                        <div class="lm-header">{% if landmark.landmark.name %}
                                            {{ landmark.type.title() }} -
                                            {{ landmark.landmark.name.title() }}  {% else %}
                                            {{ landmark.type.title() }}{% endif %}</div>

                                        <div class="lm-detail-header">Distance from hotel</div>
                                        <div class="lm-detail-value">{{ landmark.distance_from_property }} KMs</div>

                                        <div class="lm-detail-header">Directions</div>
                                        <ul>
                                            {% if landmark.property_direction %}
                                                {% for landmark_point in landmark.property_direction.split(';') %}
                                                    <li>{{ landmark_point.strip().capitalize() }}</li>
                                                {% endfor %}
                                            {% endif %}
                                        </ul>
                                        <div class="detail-block-pair">
                                            <div style="float: left; width: 200px;">
                                                <div class="lm-detail-header">Hatchback Cab Fare</div>
                                                <div class="lm-detail-value">{% if landmark.hatchback_cab_fare %}
                                                    {{ landmark.hatchback_cab_fare }} Rs{% endif %}</div>
                                            </div>
                                            <div style="float: left; width: 200px;">
                                                <div class="lm-detail-header">Sedan Cab Fare</div>
                                                <div class="lm-detail-value">
                                                    {% if landmark.sedan_cab_fare %}{{ landmark.sedan_cab_fare }}
                                                        Rs{% endif %}</div>
                                            </div>
                                        </div>
                                        <div class="detail-block-pair">
                                            <div style="float: left; width: 200px;">
                                                <div class="lm-detail-header">Latitude</div>
                                                <div class="lm-detail-value">{{ landmark.landmark.latitude }}
                                                </div>
                                            </div>
                                            <div style="float: left; width: 200px;">
                                                <div class="lm-detail-header">Longitude</div>
                                                <div class="lm-detail-value">{{ landmark.landmark.longitude }}</div>
                                            </div>
                                        </div>

                                        <hr id="lm-separator">
                                    </div>
                                {% endfor %}

                            </div>
                        </div>
                        {% endif %}
                        {% if station_map %}
                        <div class="main-details-alternate">
                            {% for type, station_list in station_map.items() %}
                                <div class="md-header" id="hotel-amenities">{{ type.replace('_', ' ').title() }}</div>
                                {% for station in station_list %}
                                    <div class="md-content">
                                        <div class="lm">
                                            <div class="transport-header">{{ station.station.name.title() }}</div>

                                            <div class="detail-block-pair">
                                                <div style="float: left; width: 200px;">
                                                    <div class="lm-detail-header">Distance from hotel</div>
                                                    <div class="lm-detail-value">{{ station.mapping.distance_from_property }}
                                                        KMs
                                                    </div>
                                                </div>
                                                <div style="float: left; width: 200px;">
                                                    <div class="lm-detail-header">Direction to hotel</div>
                                                    <div class="lm-detail-value">
                                                        {% if station.mapping.property_direction and not station.mapping.property_direction.startswith('http') %}
                                                            <a href="//{{ station.mapping.property_direction }}"
                                                               target="_blank">
                                                                See it on google
                                                            </a>
                                                        {% elif station.mapping.property_direction %}
                                                            <a href="{{ station.mapping.property_direction }}"
                                                               target="_blank">
                                                                See it on google
                                                            </a>
                                                        {% endif %}</div>
                                                </div>
                                            </div>
                                            <div class="detail-block-pair">
                                                <div style="float: left; width: 200px;">
                                                    <div class="lm-detail-header">Latitude</div>
                                                    <div class="lm-detail-value">{{ station.station.latitude }}
                                                    </div>
                                                </div>
                                                <div style="float: left; width: 200px;">
                                                    <div class="lm-detail-header">Longitude</div>
                                                    <div class="lm-detail-value">{{ station.station.longitude }}</div>
                                                </div>
                                            </div>
                                            <div class="detail-block-pair">
                                                <div style="float: left; width: 200px;">
                                                    <div class="lm-detail-header">Hatchback Cab Fare</div>
                                                    <div class="lm-detail-value">
                                                        {% if station.mapping.hatchback_cab_fare %}
                                                            {{ station.mapping.hatchback_cab_fare }}
                                                            Rs {% endif %}</div>
                                                </div>
                                                <div style="float: left; width: 200px;">
                                                    <div class="lm-detail-header">Sedan Cab Fare</div>
                                                    <div class="lm-detail-value">
                                                        {% if station.mapping.sedan_cab_fare %}
                                                            {{ station.mapping.sedan_cab_fare }} Rs {% endif %}</div>
                                                </div>
                                            </div>

                                            <hr id="lm-separator">

                                        </div>
                                    </div>
                                {% endfor %}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="main-details">
                            <div class="md-header">Neighbourhood Details</div>
                            <div class="md-content">
                                <div class="lm">
                                    {% if property.neighbouring_place.nearest_hospital %}
                                        <div class="dt-header">Nearest Hospital</div>
                                        {% for paragraph in property.neighbouring_place.nearest_hospital.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}

                                    {% if property.neighbouring_place.utility_shops %}
                                        <div class="dt-header">Utility Shops</div>
                                        {% for paragraph in property.neighbouring_place.utility_shops.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}

                                    {% if property.neighbouring_place.restaurants %}
                                        <div class="dt-header">Restaurants</div>
                                        {% for paragraph in property.neighbouring_place.restaurants.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}

                                    {% if property.neighbouring_place.tourist_spots %}
                                        <div class="dt-header">Tourist Spots</div>
                                        {% for paragraph in property.neighbouring_place.tourist_spots.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}
                                    {% if property.neighbouring_place.corporate_offices %}
                                        <div class="dt-header">Corporate Offices</div>
                                        {% for paragraph in property.neighbouring_place.corporate_offices.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}
                                    {% if property.neighbouring_place.popular_malls %}
                                        <div class="dt-header">Popular Malls</div>
                                        {% for paragraph in property.neighbouring_place.popular_malls.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}
                                    {% if property.neighbouring_place.shopping_streets %}
                                        <div class="dt-header">Shopping Streets</div>
                                        {% for paragraph in property.neighbouring_place.shopping_streets.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}
                                    {% if property.neighbouring_place.city_centre %}
                                        <div class="dt-header">City Centre</div>
                                        {% for paragraph in property.neighbouring_place.city_centre.split(';') %}
                                            <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                        {% endfor %}

                                        <hr id="lm-separator">
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    {% macro show_room_numbers(room_numbers, is_amenity_room) %}
                        {% for i in range(0,room_numbers|length,2) %}
                            {% if not is_amenity_room %}
                                <div style="display: inline-block;width: 150px;">
                                    {% if i < room_numbers|length %}
                                        <div class="room-num">{{ room_numbers[i].strip() }}</div>
                                    {% endif %}
                                    {% if i + 1< room_numbers|length %}
                                        <div class="room-num">{{ room_numbers[i + 1] }}</div>
                                    {% endif %}
                                </div>
                            {% else %}
                                {% if i < room_numbers|length %}
                                    {{ room_numbers[i] }}&#9;
                                {% endif %}
                                {% if i + 1< room_numbers|length %}
                                    {{ room_numbers[i + 1] }}&#9;
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                    {% endmacro %}
                    {% macro show_room_amenity(all_rooms, amenity_details) %}
                        {% set amenity_rooms = amenity_details['rooms'] %}
                        {% set on_request = ('on_request' in amenity_details) and amenity_details['on_request'] %}
                        <td>
                            {% if not amenity_rooms %}
                                <img class="av-image"
                                     src="{{ url_for('static', filename = 'img/Cross.svg') }}"/>
                                <div class="av-details">Not available in any rooms</div>
                            {% elif on_request %}
                                <img class="av-image"
                                     src="{{ url_for('static', filename = 'img/partial-complete.png') }}"/>
                                <div class="av-details">
                                    Available on request
                                </div>
                            {% elif all_rooms|length != amenity_rooms|length %}
                                <img class="av-image"
                                     src="{{ url_for('static', filename = 'img/partial-complete.png') }}"/>
                                <div class="av-details">
                                    Available in<br/>
                                    {{ show_room_numbers(amenity_rooms, True) }}
                                </div>
                            {% else %}
                                <img class="av-image"
                                     src="{{ url_for('static', filename = 'img/av-true.png') }}"/>
                                <div class="av-details">Available in all rooms</div>
                            {% endif %}
                        </td>
                    {% endmacro %}
                    {% macro show_room_amenities(accacia, oak, maple, mahogany, name, key) %}
                        <tr>
                            <th>{{ name }}</th>
                            {% if accacia %}
                                {{ show_room_amenity(accacia.rooms, accacia[key]) }}
                            {% endif %}
                            {% if oak %}
                                {{ show_room_amenity(oak.rooms, oak[key]) }}
                            {% endif %}
                            {% if maple %}
                                {{ show_room_amenity(maple.rooms, maple[key]) }}
                            {% endif %}
                            {% if mahogany %}
                                {{ show_room_amenity(mahogany.rooms, mahogany[key]) }}
                            {% endif %}
                        </tr>
                    {% endmacro %}


                    {% set oak_details = room_details['OAK'] %}
                    {% set accacia_details = room_details['ACACIA'] %}
                    {% set maple_details = room_details['MAPLE'] %}
                    {% set mahogany_details = room_details['MAHOGANY'] %}
                    <div id="room-amenities">
                        {% if room_details %}
                        <div class="main-details">
                            <div class="rooms-details">
                                <table>
                                    <tr>
                                        <th>FEATURES</th>
                                        {% if accacia_details %}
                                            <th>ACACIA</th>{% endif %}
                                        {% if oak_details %}
                                            <th>OAK</th>{% endif %}
                                        {% if maple_details %}
                                            <th>MAPLE</th>{% endif %}
                                        {% if mahogany_details %}
                                            <th>MAHOGANY</th>{% endif %}
                                    </tr>
                                    <tr>
                                        <th class="row-header">Total Rooms</th>

                                        {% if accacia_details %}
                                            <td>{{ accacia_details.rooms|length }}</td>{% endif %}
                                        {% if oak_details %}
                                            <td>{{ oak_details.rooms|length }}</td>{% endif %}
                                        {% if maple_details %}
                                            <td>{{ maple_details.rooms|length }}</td>{% endif %}
                                        {% if mahogany_details %}
                                            <td>{{ mahogany_details.rooms|length }}</td>{% endif %}
                                    </tr>
                                    <tr>
                                        <th>Minimum Occupancy</th>
                                        {% if accacia_details %}
                                            <td>
                                                {% if 'config' in accacia_details %}
                                                    {{ accacia_details.config.min_occupancy }}{% endif %}</td>{% endif %}
                                        {% if oak_details %}
                                            <td>{% if 'config' in oak_details %}
                                                {{ oak_details.config.min_occupancy }}{% endif %}</td>{% endif %}
                                        {% if maple_details %}
                                            <td>{% if 'config' in maple_details %}
                                                {{ maple_details.config.min_occupancy }}{% endif %}</td>{% endif %}
                                        {% if mahogany_details %}
                                            <td>{% if 'config' in mahogany_details %}
                                                {{ mahogany_details.config.min_occupancy }}{% endif %}</td>{% endif %}
                                    </tr>
                                    <tr>
                                        <th>Maximum Occupancy</th>
                                        {% if accacia_details %}
                                            <td>
                                                {% if 'config' in accacia_details %}
                                                    {{ accacia_details.config.max_occupancy }}{% endif %}</td>{% endif %}
                                        {% if oak_details %}
                                            <td>{% if 'config' in oak_details %}
                                                {{ oak_details.config.max_occupancy }}{% endif %}</td>{% endif %}
                                        {% if maple_details %}
                                            <td>{% if 'config' in maple_details %}
                                                {{ maple_details.config.max_occupancy }}{% endif %}</td>{% endif %}
                                        {% if mahogany_details %}
                                            <td>{% if 'config' in mahogany_details %}
                                                {{ mahogany_details.config.max_occupancy }}{% endif %}</td>{% endif %}
                                    </tr>
                                    <tr>
                                        <th>Extra Bed</th>
                                        {% if accacia_details %}
                                            <td>
                                                {% if 'config' in accacia_details %}
                                                    {{ accacia_details.config.extra_bed }}{% endif %}</td>{% endif %}
                                        {% if oak_details %}
                                            <td>{% if 'config' in oak_details %}
                                                {{ oak_details.config.extra_bed }}{% endif %}</td>{% endif %}
                                        {% if maple_details %}
                                            <td>{% if 'config' in maple_details %}
                                                {{ maple_details.config.extra_bed }}{% endif %}</td>{% endif %}
                                        {% if mahogany_details %}
                                            <td>{% if 'config' in mahogany_details %}
                                                {{ mahogany_details.config.extra_bed }}{% endif %}</td>{% endif %}
                                    </tr>
                                    <tr>
                                        <th>Room Numbers</th>
                                        {% if accacia_details %}
                                            <td valign="top">
                                                {{ show_room_numbers(accacia_details.rooms, False) }}
                                            </td>
                                        {% endif %}
                                        {% if oak_details %}
                                            <td valign="top">
                                                {{ show_room_numbers(oak_details.rooms, False) }}
                                            </td>
                                        {% endif %}
                                        {% if maple_details %}
                                            <td valign="top">
                                                {{ show_room_numbers(maple_details.rooms, False) }}
                                            </td>
                                        {% endif %}
                                        {% if mahogany_details %}
                                            <td valign="top">
                                                {{ show_room_numbers(mahogany_details.rooms, False) }}
                                            </td>
                                        {% endif %}
                                    </tr>
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Mini Fridge', 'mini_fridge') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Balcony', 'balcony') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Kitchenette', 'kitchenette') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Kitchenette Utensils', 'kitchenette_utensils') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'King Bed', 'king_sized_beds') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Queen Bed', 'queen_sized_beds') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Single Beds', 'single_beds') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Wardrobe', 'wardrobe') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Locker', 'locker_available') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Microwave', 'microwave') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Luggage Shelf', 'luggage_shelf') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Study Table/Chair', 'study_table_chair') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Sofa Chair', 'sofa_chair') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Coffee Table', 'coffee_table') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Other Furniture', 'other_furniture') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Smoking Room', 'smoking_room') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Bath Tub', 'bath_tub') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Shower Curtain', 'shower_curtain') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Smoke Alarm', 'smoke_alarm') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Shower Cabinets', 'shower_cabinets') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Living Room', 'living_room') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Dining Table', 'dining_table') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Windows', 'windows') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Heater', 'heater') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Intercom', 'intercom') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Hot Water', 'hot_water') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'TV', 'tv') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'AC', 'ac') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Joinable Twin Beds', 'joinable_twin_beds') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Non Joinable Twin Beds', 'non_joinable_twin_beds') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Bucket/Mug', 'bucket_mug') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Fan', 'fan') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Room Lock', 'room_lock') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Mosquito Repellent', 'mosquito_repellent') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Stove', 'stove') }}
                                    {{ show_room_amenities(accacia_details, oak_details, maple_details, mahogany_details, 'Treebo Toiletries', 'treebo_toiletries') }}
                                </table>
                            </div>
                        </div>
                        {% endif %}

                    </div>

                    <div id="description">
                        {% if property.description %}
                            <div class="main-details">
                                <div class="md-header">Hotel Description</div>
                                <div class="md-content">
                                    <div class="lm">
                                        {% if property.description.property_description %}
                                            {% for paragraph in property.description.property_description.split(';') %}
                                                <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="main-details-alternate">
                                <div class="md-header">Room Category Descriptions</div>
                                <div class="md-content">
                                    <div class="lm">
                                        {% if property.description.acacia_description %}
                                            <div class="dt-header">Acacia</div>
                                            {% for paragraph in property.description.acacia_description.split(';') %}
                                                <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                            {% endfor %}

                                            <hr id="lm-separator">
                                        {% endif %}

                                        {% if property.description.oak_description %}
                                            <div class="dt-header">Oak</div>
                                            {% for paragraph in property.description.oak_description.split(';') %}
                                                <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                            {% endfor %}

                                            <hr id="lm-separator">
                                        {% endif %}

                                        {% if property.description.maple_description %}
                                            <div class="dt-header">Maple</div>
                                            {% for paragraph in property.description.maple_description.split(';') %}
                                                <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                            {% endfor %}

                                            <hr id="lm-separator">
                                        {% endif %}

                                        {% if property.description.mahogany_description %}
                                            <div class="dt-header">Mahogany</div>
                                            {% for paragraph in property.description.mahogany_description.split(';') %}
                                                <div class="info-paragraph">{{ paragraph.strip() }}</div>
                                            {% endfor %}

                                            <hr id="lm-separator">
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="main-details">
                                <div class="md-header">Trilights</div>
                                <div class="md-content">
                                    <div class="lm">
                                        <ul>
                                            {% if property.description.trilight_one %}
                                                <li>{{ property.description.trilight_one.strip() }}</li>
                                            {% endif %}
                                            {% if property.description.trilight_two %}
                                                <li>{{ property.description.trilight_two.strip() }}</li>
                                            {% endif %}
                                            {% if property.description.trilight_three %}
                                                <li>{{ property.description.trilight_three.strip() }}</li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
			</div>
		</div>
	</div>

</body>
</html>
