<!doctype html>

<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Property Launch</title>
    <meta name="description" content="Property Launch">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.3.1/dist/jquery.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/fomantic-ui@2.7.6/dist/semantic.min.js"></script>
    <script src="{{ url_for('static', filename = 'js/superhero_property_launch.js')|autoversion }}"></script>
</head>

<body onload="get_all_room_types(), get_all_rate_plans(), get_all_currencies()">
<div class="ui container">

    <h1 class="ui center aligned header" style="margin-top: 10px;">
        <i class="rocket icon"></i>
        Property launch
    </h1>
    <form class="ui form" action="" method="post" id="property-launch-form" name="property_launch_form" autocomplete="new-password">

        <div class="two fields">
            <div class="required field">
                <label>Name</label>
                <input type="text" name="name" id="property_name" placeholder="Name" autocomplete="new-password"">
            </div>

            <div class="field">
                <label>Previous Name</label>
                <input type="text" name="previous_name" placeholder="Previous Name" autocomplete="new-password">
            </div>
        </div>
        <div class="two fields">
            <div class="required field">
                <label>Signed Date</label>
                <div class="ui calendar date_calendar">
                    <div class="ui input left icon">
                        <i class="calendar icon"></i>
                        <input type="text" name="signed_date" placeholder="Signed Date" autocomplete="new-password">
                    </div>
                </div>
            </div>
            <div class="field">
                <label>Contractual Launch Date</label>
                <div class="ui calendar date_calendar">
                    <div class="ui input left icon">
                        <i class="calendar icon"></i>
                        <input type="text" name="launch_date" placeholder="Contractual launch Date" autocomplete="new-password">

                    </div>
                </div>
            </div>
        </div>
        <div class="two fields">
            <div class="required field">
                <label for="brands">Brand</label>
                <select id="brands" name="brand">
                    {% for item in brands %}
                    <option value="{{ item }}">{{ item }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="required field">
                <label for="regions">Region</label>
                <select id="regions" name="region">
                    {% for item in regions %}
                    <option value="{{ item }}">{{ item }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="ui two column grid">
            <div class="row">
                <div class="column">
                    <div class="required field" autocomplete="new-password">
                        <label>Location details </label>
                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="latitude" placeholder="latitude" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" name="longitude" placeholder="longitude" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                    <div class="field" autocomplete="new-password">
                        <label>Contact Address</label>
                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="address_line1" placeholder="address line1" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" name="address_line2" placeholder="address line2" autocomplete="new-password">
                            </div>
                        </div>
                        <div class="two fields">
                            <div class="field">
                                <div class="ui search city-search" id="address_city">
                                    <input type="text" hidden name="address_city_id" autocomplete="new-password">
                                    <div class="ui icon input">
                                        <input class="prompt" type="text" placeholder="Search city..." name="address_city" autocomplete="new-password">
                                        <i class="search icon"></i>
                                    </div>
                                    <div class="results"></div>
                                </div>
                            </div>
                            <div class="field">
                                <input type="text" name="address_pincode" placeholder="pincode" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="column">

                    <div class="field">
                        <label>Legal details</label>

                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="legal_name" placeholder="Legal name" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" name="gstin" placeholder="gstin" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                    <div class="field">


                        <label>Legal Address
                            <div class="ui slider checkbox">
                                <input type="checkbox" name="same_as_contact_address">
                                <label>same_as_contact_address</label>

                            </div>
                        </label>


                        <div class="two fields">
                            <div class="field">
                                <input type="text" name="legal_address_line1" placeholder="legal address line1" autocomplete="new-password">
                            </div>
                            <div class="field">
                                <input type="text" name="legal_address_line2" placeholder="legal address line2" autocomplete="new-password">
                            </div>
                        </div>
                        <div class="two fields">
                            <div class="field">
                                <div class="ui search city-search" id="legal_address_city">
                                    <input type="text" hidden name="legal_address_city_id" autocomplete="new-password">
                                    <div class="ui icon input">
                                     <input class="prompt" type="text" placeholder="Search city..."
                                           name="legal_address_city" autocomplete="new-password">
                                        <i class="search icon"></i>
                                    </div>

                                    <div class="results"></div>
                                </div>
                            </div>

                            <div class="field">
                                <input type="text" name="legal_address_pincode" placeholder="pincode" autocomplete="new-password">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="field">
            <label>Contact details</label>
            <div class="three fields">
                <div class="field">
                    <input type="text" name="reception_mobile" placeholder="Reception primary" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="reception_landline" placeholder="Reception secondary" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="email" placeholder="Hotel email" autocomplete="new-password">
                </div>
            </div>
        </div>

        <div class="field">
            <label>Owner details</label>
            <div class="three fields">
                <div class="field">
                    <input type="text" name="owner_name" placeholder="Primary owner name" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="email" name="owner_email" placeholder="Primary owner email" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="owner_phone" placeholder="Primary owner mobile" autocomplete="new-password">
                </div>
            </div>
            <div class="three fields">
                <div class="field">
                    <input type="text" name="secondary_owner_name" placeholder="secondary owner name" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="email" name="secondary_owner_email" placeholder="secondary owner email" autocomplete="new-password">
                </div>
                <div class="field">
                    <input type="text" name="secondary_owner_phone" placeholder="secondary owner mobile" autocomplete="new-password">
                </div>
            </div>
        </div>


        <div class="field">
            <div class="equal width fields">
                <div class="field">
                    <label>Star rating</label>
                    <input type="number" min="1" max="5" name="star_rating" placeholder="5" size="1" value="3">
                </div>
                <div class="field">
                    <label>Total floors</label>
                    <input type="number" min="1" max="99" name="total_floors" placeholder="5" size="1">
                </div>
                <div class="six wide field">
                    <label>Google maps link</label>
                    <input type="text" name="google_maps_link" placeholder="google maps link">
                </div>
            </div>

            <div class="equal width fields">

                <div class="field">
                    <label>Base Currency Code</label>
                    <div class=" field" size="1">
                        <input type="text" name="base_currency_code" placeholder="base currency code (Ex. INR)" autocomplete="new-password">
                    </div>
                </div>
                <div class="required field">
                    <label>Time Zone</label>
                    <div class=" field" size="1">
                        <input type="text" name="timezone" placeholder="timezone">
                    </div>
                </div>
                <div class="field">
                    <label>Hotel Logo</label>
                    <div class=" field" size="1">
                        <input type="text" name="hotel_logo" placeholder="hotel_logo">
                    </div>
                </div>
                <div class="field">
                    <label>Country Code</label>
                    <div class=" field" size="1">
                        <input type="text" name="country_code" placeholder="2 digit IN, US">
                    </div>
                </div>
            </div>

            <div class="field">
                <div class="fields">
                    <div class="field">
                        <label> Checkin time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="standard_checkin_time" value="12:00 PM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Checkout time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="standard_checkout_time" value="11:00 AM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Free early checkin time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="free_early_checkin_time" value="10:00 AM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Free late checkout time</label>
                        <div class="ui calendar time_calendar">
                            <div class="ui input left icon">
                                <i class="time icon"></i>
                                <input type="text" placeholder="time" name="free_late_checkout_time" value="2:00 PM">
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> Early checkin fee</label>
                        <input type="number" name="early_checkin_fee" placeholder="in rupees" value="0.00" min="0"
                               step="0.10">
                    </div>
                    <div class="field">
                        <label> Late checkout fee</label>
                        <input type="number" name="late_checkout_fee" placeholder="in rupees" value="0.00" min="0"
                               step="0.10">
                    </div>
                </div>
            </div>
        </div>
        <h4 class="ui horizontal divider header">
            <i class="hotel icon"></i>
            Room level details
        </h4>

        <div class="field">
            <h4 class="ui horizontal divider header">
                <button class="ui right floated primary button" id="add-room-type-button">
                Add room type
                </button>
            </h4>
            <div class="ui horizontal segments">
                <div class="ui segment room_type_details">
                    <div class="field">
                    <div class="ui selection dropdown room_types" onclick="get_all_room_types()">
                        <input type="hidden" name="room_type" class="pickRoomType" value="" required>
                        <i class="dropdown icon"></i>
                        <div class="default text">Room type</div>
                        <select class="selectFromGivenRoomType"></select>
                    </div>
                    </div>
                    <div class="field">
                        <label>Is this room type available in property?</label>
                        <div class="ui selection dropdown">
                            <input type="hidden" name="is_room_type_available" value="0">
                            <i class="dropdown icon"></i>
                            <div class="default text">Is this room type available?</div>
                            <div class="menu">
                                <div class="item" data-value="1">Yes</div>
                                <div class="item" data-value="0">No</div>
                            </div>
                        </div>
                    </div>
                    <div class="field">
                        <label> max adults </label>
                        <input type="number" name="max_adults" placeholder="max_adults" value="0">
                    </div>
                    <div class="field">
                        <label> max children </label>
                        <input type="number" name="max_children" placeholder="max_children" value="0">
                    </div>
                    <div class="field">
                        <label> max total </label>
                        <input type="number" name="max_total" placeholder="max_total" value="0">
                    </div>
                    <div class="field">
                        <label> room size </label>
                        <input type="number" name="room_size" placeholder="room size in sq ft" value="0">
                    </div>
                    <div class="field">
                        <div class="ui selection dropdown">
                            <input type="hidden" name="bed_type">
                            <i class="dropdown icon"></i>
                            <div class="default text">Bed type</div>
                            <div class="menu">
                                <div class="item" data-value="king_bed">King Beds</div>
                                <div class="item" data-value="queen_bed">Queen Beds</div>
                                <div class="item" data-value="single_bed">Single beds</div>
                                <div class="item" data-value="non_joinable_twin_bed">Non Join-able twin beds</div>
                                <div class="item" data-value="joinable_twin_bed">Join-able twin beds</div>
                            </div>
                        </div>
                    </div>
                    <div class="container selectRatePlans">
                        <h4> Rate Plans </h4>
                    </div>
                    <div class="field">
                        <button class="ui negative basic floated right circular button remove-room-type-button">x</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="ui hidden divider"></div>
        <div class="field">
            <h4 class="ui horizontal divider header">
                Room details
                <button class="ui right floated primary button" id="add-room-button">
                    Add room
                </button>
            </h4>

            <div class="ui hidden divider"></div>
            <div class="ui segments">
                <div class="ui sixteen wide segment room_detail">
                        <div class="equal width fields">
                        <div class="field">
                            <label> room number </label>
                            <input type="text" name="room_number" placeholder="comma separated room no" value="" autocomplete="new-password">
                        </div>
                        <div class="field">
                            <label> building number </label>
                            <input type="text" name="building_number" placeholder="#building number" value="" autocomplete="new-password">
                        </div>
                        <div class="field">
                            <label> floor number </label>
                            <input type="number" name="floor_number" placeholder="floor number" value="0" autocomplete="new-password">
                        </div>
                        <div class="field">
                            <label for="room_type"> Room Type </label>
                            <div class="ui selection dropdown" onclick="add_room_type()">
                                <input type="hidden" name="room_type" class="selected_room_type">
                                <i class="dropdown icon"></i>
                                <div class="default text">Room type</div>
                                <select class="selectRoomType"></select>
                            </div>

                        </div>
                        <div class="field">
                            <button class="ui negative basic floated right circular button remove-room-button">x</button>
                        </div>
                        </div>
                </div>
            </div>
        </div>

        <div class="ui hidden divider"></div>
        <div class="field">
            <h4 class="ui horizontal divider header">
                Additional Info
            </h4>
            <div class="ui hidden divider"></div>
            <div class="ui segments">
            <div class="ui sixteen wide segment ">
            <div class="equal width fields">
                <div class="field">
                        <label for="supported_currencies"> Supported Currencies </label>
                        <select class="ui fluid search dropdown" id="supported_payment_currencies" multiple="">
                        </select>
                </div>
                <div class="field">
                        <label for="ereg_config"> EReg Config </label>
                        <div class="ui selection dropdown">
                            <input type="hidden" name="e_reg_card" value=null>
                            <i class="dropdown icon"></i>
                            <div class="default text">EReg Config</div>
                            <div class="menu">
                                <div class="item" data-value="{'enabled':true, 'required':false,'level':'room'}">{"enabled":true, "required":false,"level":"room"}</div>
                                <div class="item" data-value="{'enabled':false, 'required':true,'level':'room'}"> {"enabled":false, "required":true,"level":"room"}</div>
                                <div class="item" data-value="{'enabled':false, 'required':false,'level':'room'}"> {"enabled":false, "required":false,"level":"room"}</div>
                                <div class="item" data-value="{'enabled':true, 'required':true,'level':'room'}">{"enabled":true, "required":true,"level":"room"}</div>
                                <div class="item" data-value="{'enabled':true, 'required':false,'level':'guest'}">{"enabled":true, "required":false,"level":"guest"}</div>
                                <div class="item" data-value="{'enabled':false, 'required':true,'level':'guest'}"> {"enabled":false, "required":true,"level":"guest"}</div>
                                <div class="item" data-value="{'enabled':false, 'required':false,'level':'guest'}">{"enabled":false, "required":false,"level":"guest"}</div>
                                <div class="item" data-value="{'enabled':true, 'required':true,'level':'guest'}">{"enabled":true, "required":true,"level":"guest"}</div>
                                <div class="item" data-value="{'enabled':true, 'required':false,'level':'booking'}">{"enabled":true, "required":false,"level":"booking"}</div>
                                <div class="item" data-value="{'enabled':false, 'required':true,'level':'booking'}">{"enabled":false, "required":true,"level":"booking"}</div>
                                <div class="item" data-value="{'enabled':false, 'required':false,'level':'booking'}">{"enabled":false, "required":false,"level":"booking"}</div>
                                <div class="item" data-value="{'enabled':true, 'required':true,'level':'booking'}">{"enabled":true, "required":true,"level":"booking"}</div>
                                <div class="item" data-value=null>None</div>
                            </div>
                        </div>
                </div>
            </div>
            <div class="equal width fields">
                <div class="field">
                        <label for="external_user_id"> External User Id </label>
                        <div class="ui selection dropdown">
                            <input type="hidden" name="external_user_id" value=null>
                            <i class="dropdown icon"></i>
                            <div class="default text">External User Id</div>
                            <div class="menu">
                                <div class="item" data-value=true>True</div>
                                <div class="item" data-value=false>False</div>
                                <div class="item" data-value=null>None</div>
                            </div>
                        </div>
                </div>
                <div class="field">
                    <label for="cashiering"> Enable Cashiering </label>
                    <div class="ui selection dropdown">
                        <input type="hidden" name="cashiering_enabled" value=null>
                        <i class="dropdown icon"></i>
                        <div class="default text">Enable Cashiering</div>
                        <div class="menu">
                            <div class="item" data-value=true>True</div>
                            <div class="item" data-value=false>False</div>
                            <div class="item" data-value=null>None</div>
                        </div>
                    </div>
                </div>
                <div class="field">
                    <label for="ups"> Enable UPS </label>
                    <div class="ui selection dropdown">
                        <input type="hidden" name="ups_enabled" value=null>
                        <i class="dropdown icon"></i>
                        <div class="default text">Enable UPS</div>
                        <div class="menu">
                            <div class="item" data-value=true>True</div>
                            <div class="item" data-value=false>False</div>
                            <div class="item" data-value=null>None</div>
                        </div>
                    </div>
                </div>
            </div>
            <h3 style="padding-top: 10px;">
                AR Module
            </h3>
            <div class="equal width fields">
                <div class="field">
                    <label for="hotel_level_accounts_receivable"> Hotel Level Accounts Receivable </label>
                    <div class="ui selection dropdown">
                        <input type="hidden" name="hotel_level_accounts_receivable" value=null>
                        <i class="dropdown icon"></i>
                        <div class="default text">Hotel Level Accounts Receivable</div>
                        <div class="menu">
                            <div class="item" data-value=true>True</div>
                            <div class="item" data-value=false>False</div>
                            <div class="item" data-value=null>None</div>
                        </div>
                    </div>
                </div>
                <div class="field">
                    <label for="is_tds_override_enabled"> Enable TDS Override </label>
                    <div class="ui selection dropdown">
                        <input type="hidden" name="is_tds_override_enabled" value=null>
                        <i class="dropdown icon"></i>
                        <div class="default text">Enable TDS Override</div>
                        <div class="menu">
                            <div class="item" data-value=true>True</div>
                            <div class="item" data-value=false>False</div>
                            <div class="item" data-value=null>None</div>
                        </div>
                    </div>
                </div>
                <div class="field">
                    <label for="is_tds_settlement_enabled"> Enable TDS Settlement </label>
                    <div class="ui selection dropdown">
                        <input type="hidden" name="is_tds_settlement_enabled" value=null>
                        <i class="dropdown icon"></i>
                        <div class="default text">Enable TDS Settlement</div>
                        <div class="menu">
                            <div class="item" data-value=true>True</div>
                            <div class="item" data-value=false>False</div>
                            <div class="item" data-value=null>None</div>
                        </div>
                    </div>
                </div>
                <div class="field">
                    <label> TDS Settlement Percent </label>
                    <input type="number" name="tds_settlement_percent" placeholder="tds_settlement_percent" value=null>
                </div>
            </div>
            </div>
            </div>

        </div>

        <button class="ui fluid positive massive button" type="submit" value="submit">Submit</button>
    </form>
</div>

</body>
</html>