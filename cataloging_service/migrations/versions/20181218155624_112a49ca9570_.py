"""empty message

Revision ID: 112a49ca9570
Revises: cfae0db089a0
Create Date: 2018-12-18 15:56:24.480861

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '112a49ca9570'
down_revision = 'cfae0db089a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('_unique_pricing_policy_mapping_key', 'pricing_mapping', type_='unique')
    op.create_unique_constraint('_unique_pricing_policy_mapping_key', 'pricing_mapping', ['pricing_policy_id', 'channel_id', 'sub_channel_id'])
    op.add_column('sku', sa.Column('flat_count_for_creation', sa.INTEGER(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sku', 'flat_count_for_creation')
    op.drop_constraint('_unique_pricing_policy_mapping_key', 'pricing_mapping', type_='unique')
    op.create_unique_constraint('_unique_pricing_policy_mapping_key', 'pricing_mapping', ['channel_id', 'sub_channel_id'])
    # ### end Alembic commands ###
