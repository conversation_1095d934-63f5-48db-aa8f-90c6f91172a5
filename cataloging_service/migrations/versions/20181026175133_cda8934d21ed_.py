"""empty message

Revision ID: cda8934d21ed
Revises: f352a209ec5a
Create Date: 2018-10-26 17:51:33.752848

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cda8934d21ed'
down_revision = 'f352a209ec5a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pricing_policy',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='pricing_policy_status'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('display_name'),
    sa.UniqueConstraint('name')
    )
    op.create_table('pricing_mapping',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('pricing_policy_id', sa.INTEGER(), nullable=False),
    sa.Column('channel_id', sa.String(length=100), nullable=False),
    sa.Column('sub_channel_id', sa.String(length=100), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='pricing_mapping_status'), nullable=True),
    sa.ForeignKeyConstraint(['channel_id'], ['channel.id'], ),
    sa.ForeignKeyConstraint(['pricing_policy_id'], ['pricing_policy.id'], ),
    sa.ForeignKeyConstraint(['sub_channel_id'], ['sub_channel.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('channel_id', 'sub_channel_id', name='_unique_pricing_policy_mapping_key')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('pricing_mapping')
    op.drop_table('pricing_policy')
    # ### end Alembic commands ###
