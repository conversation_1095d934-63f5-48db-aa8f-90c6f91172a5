"""empty message

Revision ID: f686936bc2b7
Revises: 2fc675490395
Create Date: 2017-05-08 17:34:45.432253

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f686936bc2b7'
down_revision = '2fc675490395'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('migration_detail',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('migration_sheet_name', sa.String(length=200), nullable=False),
    sa.Column('is_success', sa.BOOLEAN(), nullable=True),
    sa.Column('old_hotel', sa.BOOLEAN(), nullable=True),
    sa.Column('error_message', sa.String(length=1000), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.alter_column('amenity_laundry', 'drop_time',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('amenity_laundry', 'pickup_time',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('restaurant', 'baby_milk_timing',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('room_type_configuration', 'max_occupancy',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=20),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('room_type_configuration', 'max_occupancy',
               existing_type=sa.String(length=20),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('restaurant', 'baby_milk_timing',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('amenity_laundry', 'pickup_time',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('amenity_laundry', 'drop_time',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.drop_table('migration_detail')
    # ### end Alembic commands ###
