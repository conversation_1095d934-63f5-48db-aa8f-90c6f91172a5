"""empty message

Revision ID: a1439512119e
Revises: 3cfb02a3b5e9
Create Date: 2019-01-21 16:39:57.411570

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1439512119e'
down_revision = '3cfb02a3b5e9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('seller_type_history',
    sa.<PERSON>umn('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('from_seller_model', sa.Enum('MARKETPLACE', 'RESELLER', name='seller_type_choices'), nullable=False),
    sa.Column('to_seller_model', sa.<PERSON>('MARKETPLACE', 'RESELLER', name='seller_type_choices'), nullable=False),
    sa.Column('date', sa.DATE(), nullable=False),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('seller_type_history')
    # ### end Alembic commands ###
