"""empty message

Revision ID: 3479f636db39
Revises: 2712d88c6b4b
Create Date: 2018-04-20 14:26:31.173468

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3479f636db39'
down_revision = '2712d88c6b4b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('application', sa.Column('description', sa.String(length=400), nullable=True))
    op.add_column('channel', sa.Column('description', sa.String(length=400), nullable=True))
    op.add_column('sub_channel', sa.Column('description', sa.String(length=400), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sub_channel', 'description')
    op.drop_column('channel', 'description')
    op.drop_column('application', 'description')
    # ### end Alembic commands ###
