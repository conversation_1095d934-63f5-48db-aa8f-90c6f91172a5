"""empty message

Revision ID: 8d31c60df653
Revises: b25a3fa33abc
Create Date: 2018-09-03 15:48:13.919512

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8d31c60df653'
down_revision = 'b25a3fa33abc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_type_configuration', sa.Column('description', sa.TEXT(), nullable=True))
    op.drop_column('room_type_configuration', 'ext_description')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_type_configuration', sa.Column('ext_description', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_column('room_type_configuration', 'description')
    # ### end Alembic commands ###
