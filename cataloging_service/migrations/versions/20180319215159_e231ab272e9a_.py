"""empty message

Revision ID: e231ab272e9a
Revises: d8403884fe86
Create Date: 2018-03-19 21:51:59.607812

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e231ab272e9a'
down_revision = 'd8403884fe86'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('facility_category',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('category')
    )
    op.create_table('amenity_summary',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('summary', sa.TEXT(), nullable=False),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('facility_category_mapping',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('category_id', sa.INTEGER(), nullable=False),
    sa.Column('facility_name', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['category_id'], ['facility_category.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('facility_name')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('facility_category_mapping')
    op.drop_table('amenity_summary')
    op.drop_table('facility_category')
    # ### end Alembic commands ###
