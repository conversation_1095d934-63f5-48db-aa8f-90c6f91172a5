"""empty message

Revision ID: 2712d88c6b4b
Revises: d2c6dfb501f2
Create Date: 2018-04-20 12:56:03.060592

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2712d88c6b4b'
down_revision = 'd2c6dfb501f2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_detail', sa.Column('gstin', sa.String(length=30), nullable=True))
    op.add_column('property_detail', sa.Column('legal_address', sa.String(length=1000), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_detail', 'legal_address')
    op.drop_column('property_detail', 'gstin')
    # ### end Alembic commands ###
