"""empty message

Revision ID: 7ab5de096ad4
Revises: 19dd9f029d34
Create Date: 2018-08-21 21:43:10.669643

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7ab5de096ad4'
down_revision = '19dd9f029d34'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('provider_room_mapping', sa.Column('display_name', sa.String(length=50), nullable=False))
    op.add_column('provider_room_mapping', sa.Column('ext_description', sa.TEXT(), nullable=True))
    op.add_column('provider_room_mapping', sa.Column('property_id', sa.String(), nullable=False))
    op.alter_column('provider_room_mapping', 'room_type_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.create_unique_constraint('_unique_property_external_room_mapping_key', 'provider_room_mapping', ['property_id', 'ext_id', 'room_type_id', 'ext_room_code'])
    op.drop_constraint('_unique_external_room_mapping_key', 'provider_room_mapping', type_='unique')
    op.create_foreign_key(None, 'provider_room_mapping', 'property', ['property_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'provider_room_mapping', type_='foreignkey')
    op.create_unique_constraint('_unique_external_room_mapping_key', 'provider_room_mapping', ['ext_id', 'room_type_id', 'ext_room_code'])
    op.drop_constraint('_unique_property_external_room_mapping_key', 'provider_room_mapping', type_='unique')
    op.alter_column('provider_room_mapping', 'room_type_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('provider_room_mapping', 'property_id')
    op.drop_column('provider_room_mapping', 'ext_description')
    op.drop_column('provider_room_mapping', 'display_name')
    # ### end Alembic commands ###
