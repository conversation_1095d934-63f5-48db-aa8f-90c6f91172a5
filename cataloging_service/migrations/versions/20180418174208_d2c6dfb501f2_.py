"""empty message

Revision ID: d2c6dfb501f2
Revises: 89f0ab06d02d
Create Date: 2018-04-18 17:42:08.880849

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd2c6dfb501f2'
down_revision = '89f0ab06d02d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_type', sa.Column('code', sa.String(), nullable=True))
    op.create_unique_constraint(None, 'room_type', ['code'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'room_type', type_='unique')
    op.drop_column('room_type', 'code')
    # ### end Alembic commands ###
