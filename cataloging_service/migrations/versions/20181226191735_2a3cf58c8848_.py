"""empty message

Revision ID: 2a3cf58c8848
Revises: 883fbd626e7f
Create Date: 2018-12-26 19:17:35.392324

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2a3cf58c8848'
down_revision = '883fbd626e7f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_property_sku_property_id'), 'property_sku', ['property_id'], unique=False)
    op.create_index(op.f('ix_property_sku_sku_id'), 'property_sku', ['sku_id'], unique=False)
    op.create_index(op.f('ix_sku_bundle_rule_id'), 'sku', ['bundle_rule_id'], unique=False)
    op.create_index(op.f('ix_sku_category_id'), 'sku', ['category_id'], unique=False)
    op.create_index(op.f('ix_sku_sku_type_id'), 'sku', ['sku_type_id'], unique=False)
    op.create_index(op.f('ix_sku_tax_type_id'), 'sku', ['tax_type_id'], unique=False)
    op.create_index(op.f('ix_sku_bundle_bundle_id'), 'sku_bundle', ['bundle_id'], unique=False)
    op.create_index(op.f('ix_sku_bundle_sku_id'), 'sku_bundle', ['sku_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sku_bundle_sku_id'), table_name='sku_bundle')
    op.drop_index(op.f('ix_sku_bundle_bundle_id'), table_name='sku_bundle')
    op.drop_index(op.f('ix_sku_tax_type_id'), table_name='sku')
    op.drop_index(op.f('ix_sku_sku_type_id'), table_name='sku')
    op.drop_index(op.f('ix_sku_category_id'), table_name='sku')
    op.drop_index(op.f('ix_sku_bundle_rule_id'), table_name='sku')
    op.drop_index(op.f('ix_property_sku_sku_id'), table_name='property_sku')
    op.drop_index(op.f('ix_property_sku_property_id'), table_name='property_sku')
    # ### end Alembic commands ###
