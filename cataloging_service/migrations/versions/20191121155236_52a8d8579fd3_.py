"""empty message

Revision ID: 52a8d8579fd3
Revises: ca828e2e25cc
Create Date: 2019-11-21 15:52:36.813097

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '52a8d8579fd3'
down_revision = '77e318b4d616'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('seller', sa.Column('legal_signature', sa.String(length=500), nullable=True))
    op.add_column('seller', sa.Column('pincode', sa.String(length=20), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('seller', 'pincode')
    op.drop_column('seller', 'legal_signature')
    # ### end Alembic commands ###
