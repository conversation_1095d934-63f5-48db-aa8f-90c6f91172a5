"""empty message

Revision ID: f48075373f42
Revises: 959eb2573aaf
Create Date: 2018-09-24 15:32:28.525433

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f48075373f42'
down_revision = '959eb2573aaf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('param', 'value',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=700),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('param', 'value',
               existing_type=sa.String(length=700),
               type_=sa.VARCHAR(length=100),
               existing_nullable=False)
    # ### end Alembic commands ###
