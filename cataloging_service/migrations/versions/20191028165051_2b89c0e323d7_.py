"""empty message

Revision ID: 2b89c0e323d7
Revises: 05171097dec5
Create Date: 2019-10-28 16:50:51.255462

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2b89c0e323d7'
down_revision = '05171097dec5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('room', 'room_number',
               existing_type=sa.VARCHAR(length=10),
               type_=sa.String(length=30),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('room', 'room_number',
               existing_type=sa.String(length=30),
               type_=sa.VARCHAR(length=10),
               existing_nullable=False)
    # ### end Alembic commands ###
