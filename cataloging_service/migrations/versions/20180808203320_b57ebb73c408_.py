"""empty message

Revision ID: b57ebb73c408
Revises: 3eede9d77ddf
Create Date: 2018-08-08 20:33:20.339893

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b57ebb73c408'
down_revision = '3eede9d77ddf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('provider',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='provider_status'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('name')
    )
    op.create_table('provider_room_mapping',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('ext_id', sa.INTEGER(), nullable=False),
    sa.Column('room_type_id', sa.INTEGER(), nullable=False),
    sa.Column('ext_room_code', sa.String(length=100), nullable=False),
    sa.Column('ext_room_name', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['ext_id'], ['provider.id'], ),
    sa.ForeignKeyConstraint(['room_type_id'], ['room_type.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('ext_id', 'room_type_id', 'ext_room_code', name='_unique_external_room_mapping_key')
    )
    op.add_column('property_detail', sa.Column('ext_id', sa.INTEGER(), nullable=True))
    op.create_foreign_key(None, 'property_detail', 'provider', ['ext_id'], ['id'])
    op.add_column('rate_plan', sa.Column('ext_id', sa.INTEGER(), nullable=True))
    op.create_foreign_key(None, 'rate_plan', 'provider', ['ext_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'rate_plan', type_='foreignkey')
    op.drop_column('rate_plan', 'ext_id')
    op.drop_constraint(None, 'property_detail', type_='foreignkey')
    op.drop_column('property_detail', 'ext_id')
    op.drop_table('provider_room_mapping')
    op.drop_table('provider')
    # ### end Alembic commands ###
