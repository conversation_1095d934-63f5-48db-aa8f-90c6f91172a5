"""empty message

Revision ID: 42c293097284
Revises: 82642efa6ec0
Create Date: 2018-11-05 17:32:47.700672

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '42c293097284'
down_revision = '82642efa6ec0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_sku', sa.Column('display_name', sa.String(length=100), nullable=True))
    op.add_column('sku', sa.Column('display_name', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sku', 'display_name')
    op.drop_column('property_sku', 'display_name')
    # ### end Alembic commands ###
