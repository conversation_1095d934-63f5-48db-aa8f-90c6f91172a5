"""empty message

Revision ID: 05171097dec5
Revises: f0169a967712
Create Date: 2019-10-28 16:07:40.483981

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
from sqlalchemy.dialects import postgresql

revision = '05171097dec5'
down_revision = 'f0169a967712'
branch_labels = None
depends_on = None

superhero_price_slab_choices = postgresql.ENUM('PLATINUM', 'SILVER', 'GOLD_OPS', 'GOLD_SALES', name='superheropriceslabchoices')

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    superhero_price_slab_choices.create(op.get_bind())
    op.add_column('property_detail', sa.Column('is_ds_pricing_enabled', sa.BOOLEAN(), server_default=sa.text('false'), nullable=False))
    op.add_column('property_detail', sa.Column('superhero_price_slab', sa.<PERSON>um('PLATINUM', 'SILVER', 'GOLD_OPS', 'GOLD_SALES', name='superheropriceslabchoices'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_detail', 'superhero_price_slab')
    op.drop_column('property_detail', 'is_ds_pricing_enabled')
    superhero_price_slab_choices.drop(op.get_bind())
    # ### end Alembic commands ###
