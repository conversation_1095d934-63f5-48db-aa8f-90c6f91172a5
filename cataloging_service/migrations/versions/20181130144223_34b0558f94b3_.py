"""empty message

Revision ID: 34b0558f94b3
Revises: 65d5a5cbae61
Create Date: 2018-11-30 14:42:23.372571

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '34b0558f94b3'
down_revision = '65d5a5cbae61'
branch_labels = None
depends_on = None

name = 'room_type_choices'
tmp_name = 'tmp_' + name

old_options = ('ACACIA', 'OAK', 'MAPLE', 'MAHOGANY')
new_options = sorted(old_options + ('OFFLINE',))

new_type = sa.Enum(*new_options, name=name)

room_type = sa.sql.table('room_type', sa.Column('type', new_type, nullable=False, unique=True))


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TYPE ' + name + ' RENAME TO ' + tmp_name)

    new_type.create(op.get_bind())
    op.execute('ALTER TABLE room_type ALTER COLUMN type ' +
               'TYPE ' + name + ' USING type::text::' + name)
    op.execute('DROP TYPE ' + tmp_name)
    # ### end Alembic commands ###


def downgrade():
    # Cannot downgrade since existing type will go missing
    pass
