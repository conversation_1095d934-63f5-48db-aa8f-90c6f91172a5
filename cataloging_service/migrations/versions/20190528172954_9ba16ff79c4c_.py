"""empty message

Revision ID: 9ba16ff79c4c
Revises: d4a40c6bd52e
Create Date: 2019-05-28 17:29:54.074807

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9ba16ff79c4c'
down_revision = '16029c54d881'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('global_policy',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('policy_type', sa.String(length=80), nullable=False),
    sa.Column('title', sa.String(length=80), nullable=False),
    sa.Column('description', sa.String(length=800), nullable=False),
    sa.Column('display_in_need_to_know', sa.<PERSON>(), nullable=True),
    sa.Column('display_in_policy', sa.<PERSON>(), nullable=True),
    sa.Primary<PERSON>eyConstraint('id')
    )
    op.create_table('property_policy',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('policy_type', sa.String(length=80), nullable=False),
    sa.Column('title', sa.String(length=80), nullable=False),
    sa.Column('description', sa.String(length=800), nullable=False),
    sa.Column('display_in_need_to_know', sa.Boolean(), nullable=True),
    sa.Column('display_in_policy', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('property_policy_map',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('property_detail_id', sa.INTEGER(), nullable=True),
    sa.Column('property_policy_id', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['property_detail_id'], ['property_detail.id'], ),
    sa.ForeignKeyConstraint(['property_policy_id'], ['property_policy.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('property_policy_map')
    op.drop_table('property_policy')
    op.drop_table('global_policy')
    # ### end Alembic commands ###
