"""empty message

Revision ID: 6786e8e21726
Revises: 8d31c60df653
Create Date: 2018-09-12 12:51:53.164557

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6786e8e21726'
down_revision = '1ac3b829d865'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('param',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('entity', sa.String(length=100), nullable=False),
    sa.Column('field', sa.String(length=100), nullable=False),
    sa.Column('value', sa.String(length=100), nullable=False),
    sa.Column('definition', sa.TEXT(), nullable=True),
    sa.Column('validate', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('entity', 'field', 'value', 'validate', name='unique_entity_param_index')
    )
    op.create_table('sku',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('is_modular', sa.Boolean(), nullable=True),
    sa.Column('tax_type_id', sa.INTEGER(), nullable=False),
    sa.Column('sku_type_id', sa.INTEGER(), nullable=False),
    sa.Column('hsn_sac', sa.String(length=100), nullable=True),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('saleable', sa.Boolean(), nullable=True),
    sa.Column('default_price', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('category_id', sa.INTEGER(), nullable=False),
    sa.Column('chargeable_per_occupant', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['sku_category.id'], ),
    sa.ForeignKeyConstraint(['sku_type_id'], ['param.id'], ),
    sa.ForeignKeyConstraint(['tax_type_id'], ['param.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('property_sku',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('sku_id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='property_sku_status'), nullable=True),
    sa.Column('saleable', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('sku_id', 'property_id', name='_unique_property_sku')
    )
    op.create_table('sku_attribute',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('sku_id', sa.INTEGER(), nullable=False),
    sa.Column('key', sa.String(length=100), nullable=False),
    sa.Column('value', sa.String(length=200), nullable=False),
    sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ondelete='cascade'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('sku_bundle',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('bundle_id', sa.INTEGER(), nullable=False),
    sa.Column('sku_id', sa.INTEGER(), nullable=False),
    sa.Column('count', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['bundle_id'], ['sku.id'], ondelete='cascade'),
    sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ondelete='cascade'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('bundle_id', 'sku_id', name='unique_bundle_index')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sku_bundle')
    op.drop_table('sku_attribute')
    op.drop_table('property_sku')
    op.drop_table('sku')
    op.drop_table('param')
    op.execute('DROP TYPE property_sku_status')
    # ### end Alembic commands ###
