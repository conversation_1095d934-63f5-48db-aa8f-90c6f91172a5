"""empty message

Revision ID: f352a209ec5a
Revises: f48075373f42
Create Date: 2018-09-24 16:10:46.902857

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f352a209ec5a'
down_revision = 'f48075373f42'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sku', sa.Column('bundle_rule_id', sa.INTEGER(), nullable=True))
    op.create_foreign_key(None, 'sku', 'param', ['bundle_rule_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'sku', type_='foreignkey')
    op.drop_column('sku', 'bundle_rule_id')
    # ### end Alembic commands ###
