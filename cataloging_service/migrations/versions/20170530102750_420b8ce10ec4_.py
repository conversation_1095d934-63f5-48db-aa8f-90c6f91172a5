"""empty message

Revision ID: 420b8ce10ec4
Revises: f686936bc2b7
Create Date: 2017-05-30 10:27:50.008173

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '420b8ce10ec4'
down_revision = 'f686936bc2b7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('amenity_disable_friendly', 'disable_friendly_rooms',
               existing_type=sa.VARCHAR(length=200),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('amenity_gym', 'equipments_available',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('amenity_heater', 'charges',
               existing_type=sa.VARCHAR(length=30),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('amenity_parking', 'charges',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('amenity_private_cab', 'charges',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('amenity_swimming_pool', 'pool_size',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('amenity_tv', 'size',
               existing_type=sa.VARCHAR(length=10),
               type_=sa.String(length=50),
               existing_nullable=True)
    op.alter_column('amenity_tv', 'vendor',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('banquet_hall', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('banquet_hall', 'size',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('bar', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('city', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('cluster', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('country', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('cuisine', 'name',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.String(length=50),
               existing_nullable=True)
    op.alter_column('description', 'acacia_description',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=5000),
               existing_nullable=True)
    op.alter_column('description', 'mahogany_description',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=5000),
               existing_nullable=True)
    op.alter_column('description', 'maple_description',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=5000),
               existing_nullable=True)
    op.alter_column('description', 'oak_description',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=5000),
               existing_nullable=True)
    op.alter_column('description', 'property_description',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=5000),
               existing_nullable=True)
    op.alter_column('description', 'trilight_one',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('description', 'trilight_three',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('description', 'trilight_two',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('google_drive_base_folder', 'folder_link',
               existing_type=sa.VARCHAR(length=150),
               type_=sa.String(length=500),
               existing_nullable=False)
    op.alter_column('google_drive_base_folder', 'property_documents_link',
               existing_type=sa.VARCHAR(length=150),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('google_drive_base_folder', 'property_images_link',
               existing_type=sa.VARCHAR(length=150),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('guest_facing_process', 'early_checkin_fee',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('guest_facing_process', 'late_checkout_fee',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('landmark', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('landmark', 'property_direction',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('locality', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('location', 'maps_link',
               existing_type=sa.VARCHAR(length=200),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('location', 'postal_address',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('micro_market', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('neighbouring_place', 'city_centre',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'corporate_offices',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'nearest_hospital',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'popular_malls',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'restaurants',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'shopping_streets',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'tourist_spots',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'utility_shops',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=1000),
               existing_nullable=True)
    op.alter_column('property_detail', 'neighbourhood_detail',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('property_detail', 'style_detail',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=500),
               existing_nullable=True)
    op.alter_column('restaurant', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('room', 'size',
               existing_type=sa.VARCHAR(length=10),
               type_=sa.String(length=100),
               existing_nullable=True)
    op.alter_column('state', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('transport_station', 'name',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('transport_station_property', 'property_direction',
               existing_type=sa.VARCHAR(length=500),
               type_=sa.String(length=1000),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('transport_station_property', 'property_direction',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('transport_station', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('state', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('room', 'size',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=10),
               existing_nullable=True)
    op.alter_column('restaurant', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('property_detail', 'style_detail',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('property_detail', 'neighbourhood_detail',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'utility_shops',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'tourist_spots',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'shopping_streets',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'restaurants',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'popular_malls',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'nearest_hospital',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'corporate_offices',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('neighbouring_place', 'city_centre',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('micro_market', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('location', 'postal_address',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('location', 'maps_link',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=200),
               existing_nullable=True)
    op.alter_column('locality', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('landmark', 'property_direction',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('landmark', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('guest_facing_process', 'late_checkout_fee',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('guest_facing_process', 'early_checkin_fee',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('google_drive_base_folder', 'property_images_link',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=150),
               existing_nullable=True)
    op.alter_column('google_drive_base_folder', 'property_documents_link',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=150),
               existing_nullable=True)
    op.alter_column('google_drive_base_folder', 'folder_link',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=150),
               existing_nullable=False)
    op.alter_column('description', 'trilight_two',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('description', 'trilight_three',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('description', 'trilight_one',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=100),
               existing_nullable=True)
    op.alter_column('description', 'property_description',
               existing_type=sa.String(length=5000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('description', 'oak_description',
               existing_type=sa.String(length=5000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('description', 'maple_description',
               existing_type=sa.String(length=5000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('description', 'mahogany_description',
               existing_type=sa.String(length=5000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('description', 'acacia_description',
               existing_type=sa.String(length=5000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('cuisine', 'name',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.alter_column('country', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('cluster', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('city', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.alter_column('bar', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('banquet_hall', 'size',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.alter_column('banquet_hall', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('amenity_tv', 'vendor',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.alter_column('amenity_tv', 'size',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=10),
               existing_nullable=True)
    op.alter_column('amenity_swimming_pool', 'pool_size',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.alter_column('amenity_private_cab', 'charges',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('amenity_parking', 'charges',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.alter_column('amenity_heater', 'charges',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=30),
               existing_nullable=True)
    op.alter_column('amenity_gym', 'equipments_available',
               existing_type=sa.String(length=1000),
               type_=sa.VARCHAR(length=500),
               existing_nullable=True)
    op.alter_column('amenity_disable_friendly', 'disable_friendly_rooms',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=200),
               existing_nullable=True)
    # ### end Alembic commands ###
