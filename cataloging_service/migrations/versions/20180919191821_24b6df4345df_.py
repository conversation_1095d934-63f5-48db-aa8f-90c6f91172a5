"""empty message

Revision ID: 24b6df4345df
Revises: 10095543573a
Create Date: 2018-09-19 19:18:21.283725

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '24b6df4345df'
down_revision = '10095543573a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sku_activation',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('sku_id', sa.INTEGER(), nullable=False),
    sa.Column('service_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ondelete='cascade'),
    sa.ForeignKeyConstraint(['service_id'], ['param.id'], ),
    sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('property_id', 'sku_id', 'service_id', name='_unique_sku_activation_service_key')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sku_activation')
    # ### end Alembic commands ###
