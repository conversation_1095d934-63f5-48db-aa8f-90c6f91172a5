"""empty message

Revision ID: a2a838ec44a0
Revises: ca828e2e25cc
Create Date: 2019-12-03 11:15:55.905137

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a2a838ec44a0'
down_revision = 'ca828e2e25cc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('restaurant_table',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('seller_id', sa.String(), nullable=False),
    sa.Column('table_number', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['seller_id'], ['seller.seller_id'], ),
    sa.PrimaryKeyConstraint('seller_id', 'table_number'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('restaurant_table')
    # ### end Alembic commands ###
