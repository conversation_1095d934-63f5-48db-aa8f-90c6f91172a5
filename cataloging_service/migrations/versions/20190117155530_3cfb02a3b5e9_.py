"""empty message

Revision ID: 3cfb02a3b5e9
Revises: 8343b1e1b595
Create Date: 2019-01-17 15:55:30.190053

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3cfb02a3b5e9'
down_revision = '8343b1e1b595'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('location', sa.Column('legal_address', sa.TEXT(), nullable=True))
    op.add_column('location', sa.Column('legal_city_id', sa.INTEGER(), nullable=True))
    op.create_foreign_key(None, 'location', 'city', ['legal_city_id'], ['id'])
    op.drop_column('property_detail', 'legal_address')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_detail', sa.Column('legal_address', sa.VARCHAR(length=1000), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'location', type_='foreignkey')
    op.drop_column('location', 'legal_city_id')
    op.drop_column('location', 'legal_address')
    # ### end Alembic commands ###
