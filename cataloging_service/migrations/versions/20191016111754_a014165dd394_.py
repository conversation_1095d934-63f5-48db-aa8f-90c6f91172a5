"""empty message

Revision ID: a014165dd394
Revises: 4f1867fdc1ac
Create Date: 2019-10-16 11:17:54.383160

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a014165dd394'
down_revision = '3c7750b7cd08'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_detail', sa.Column('churn_initiation_date', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_detail', 'churn_initiation_date')
    # ### end Alembic commands ###
