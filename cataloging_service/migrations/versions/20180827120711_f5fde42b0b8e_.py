"""empty message

Revision ID: f5fde42b0b8e
Revises: 1cb46b3aaa5d
Create Date: 2018-08-27 12:07:11.182270

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f5fde42b0b8e'
down_revision = '1cb46b3aaa5d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_type_configuration', sa.Column('ext_rate_plan_code', sa.String(length=100), nullable=True))
    op.add_column('room_type_configuration', sa.Column('ext_rate_plan_name', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('room_type_configuration', 'ext_rate_plan_name')
    op.drop_column('room_type_configuration', 'ext_rate_plan_code')
    # ### end Alembic commands ###
