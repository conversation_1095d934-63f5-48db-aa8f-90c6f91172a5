"""empty message

Revision ID: 6e5ac9bce918
Revises: eb4dedb16194
Create Date: 2017-12-08 14:16:27.400029

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6e5ac9bce918'
down_revision = 'eb4dedb16194'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('property_image',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('path', sa.Text(), nullable=True),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('room_type_config_id', sa.INTEGER(), nullable=True),
    sa.Column('tag_description', sa.Text(), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['room_type_config_id'], ['room_type_configuration.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('property_image')
    # ### end Alembic commands ###
