"""empty message

Revision ID: 81525c1c5b11
Revises: ed470ddb1bef
Create Date: 2018-05-15 19:50:55.693680

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
from sqlalchemy import text
from sqlalchemy.orm import Session

from cataloging_service.models import PropertyLandmark

revision = '81525c1c5b11'
down_revision = 'ed470ddb1bef'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = Session(bind=bind)

    for property_landmark in session.query(PropertyLandmark):
        if not property_landmark.distance_from_property:
            property_landmark.distance_from_property = 0.00

    session.commit()
    op.alter_column('property_landmark', 'distance_from_property',
               existing_type=sa.NUMERIC(precision=5, scale=2),
               nullable=False, server_default=text('0.00'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('property_landmark', 'distance_from_property',
               existing_type=sa.NUMERIC(precision=5, scale=2),
               nullable=True)
    # ### end Alembic commands ###
