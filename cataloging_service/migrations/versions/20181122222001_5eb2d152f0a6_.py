"""empty message

Revision ID: 5eb2d152f0a6
Revises: 27d061aabdbc
Create Date: 2018-11-22 22:20:01.224371

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5eb2d152f0a6'
down_revision = '27d061aabdbc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_sku_tag'), 'sku', ['tag'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sku_tag'), table_name='sku')
    # ### end Alembic commands ###
