"""empty message

Revision ID: d8403884fe86
Revises: 29a2ae632537
Create Date: 2018-03-13 21:31:26.406440

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd8403884fe86'
down_revision = '29a2ae632537'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ota',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('ota_code', sa.String(), nullable=False),
    sa.Column('mm_ota_code', sa.String(), nullable=False),
    sa.Column('unirate_ota_code', sa.String(), nullable=False),
    sa.Column('rate_push_enabled', sa.Boolean(), nullable=False),
    sa.Column('promo_push_enabled', sa.Boolean(), nullable=False),
    sa.Column('inventory_push_enabled', sa.Boolean(), nullable=False),
    sa.Column('promo_push_api', sa.Boolean(), nullable=False),
    sa.Column('promo_disable_api', sa.Boolean(), nullable=False),
    sa.Column('rcs_push_complete', sa.Boolean(), nullable=False),
    sa.Column('rcs_callback_complete', sa.Boolean(), nullable=False),
    sa.Column('unirate_push_complete', sa.Boolean(), nullable=False),
    sa.Column('rcs_push_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('rcs_callback_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('unirate_push_time', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    sa.UniqueConstraint('ota_code'),
    sa.UniqueConstraint('unirate_ota_code')
    )
    op.create_table('rate_plan',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('plan', sa.String(), nullable=False),
    sa.Column('unirate_rate_plan_code', sa.String(), nullable=True),
    sa.Column('crs_rate_plan_code', sa.String(), nullable=True),
    sa.Column('bb_rate_plan_code', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('bb_rate_plan_code'),
    sa.UniqueConstraint('crs_rate_plan_code'),
    sa.UniqueConstraint('plan'),
    sa.UniqueConstraint('unirate_rate_plan_code')
    )
    op.create_table('ota_property',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('ota_id', sa.INTEGER(), nullable=False),
    sa.Column('rate_push_complete', sa.Boolean(), nullable=True),
    sa.Column('promo_push_complete', sa.Boolean(), nullable=True),
    sa.Column('inventory_push_complete', sa.Boolean(), nullable=True),
    sa.Column('rcs_push_complete', sa.Boolean(), nullable=False),
    sa.Column('rcs_callback_complete', sa.Boolean(), nullable=False),
    sa.Column('unirate_push_complete', sa.Boolean(), nullable=False),
    sa.Column('rcs_push_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('rcs_callback_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('unirate_push_time', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['ota_id'], ['ota.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('property_id', 'ota_id', name='_unique_property_ota')
    )
    op.create_table('rate_plan_configuration',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('rate_plan_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['rate_plan_id'], ['rate_plan.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('property_id', 'rate_plan_id', name='_unique_property_rate_plan')
    )
    op.create_table('ota_property_mapping',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('ota_property_id', sa.INTEGER(), nullable=False),
    sa.Column('ota_hotel_code', sa.String(), nullable=False),
    sa.Column('username', sa.String(), nullable=True),
    sa.Column('access_token', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['ota_property_id'], ['ota_property.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('ota_property_id')
    )
    op.create_table('ota_rate_plan_mapping',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('ota_property_id', sa.INTEGER(), nullable=False),
    sa.Column('room_type', sa.INTEGER(), nullable=False),
    sa.Column('rate_plan', sa.INTEGER(), nullable=False),
    sa.Column('ota_rate_plan_code', sa.String(), nullable=False),
    sa.Column('ota_rate_plan_name', sa.String(), nullable=True),
    sa.Column('rate_push_enabled', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['ota_property_id'], ['ota_property.id'], ),
    sa.ForeignKeyConstraint(['rate_plan'], ['rate_plan.id'], ),
    sa.ForeignKeyConstraint(['room_type'], ['room_type.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ota_room_mapping',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('ota_property_id', sa.INTEGER(), nullable=False),
    sa.Column('room_type_id', sa.INTEGER(), nullable=False),
    sa.Column('ota_room_code', sa.String(), nullable=False),
    sa.Column('ota_room_name', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['ota_property_id'], ['ota_property.id'], ),
    sa.ForeignKeyConstraint(['room_type_id'], ['room_type.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('ota_property_id', 'room_type_id', name='_unique_property_ota_room')
    )
    op.add_column('room_type', sa.Column('bb_room_type_code', sa.String(), nullable=True))
    op.add_column('room_type', sa.Column('crs_room_type_code', sa.String(), nullable=True))
    op.add_column('room_type', sa.Column('unirate_room_type_code', sa.String(), nullable=True))
    op.create_unique_constraint('_unique_bb_room', 'room_type', ['bb_room_type_code'])
    op.create_unique_constraint('_unique_cm_room', 'room_type', ['unirate_room_type_code'])
    op.create_unique_constraint('_unique_crs_room', 'room_type', ['crs_room_type_code'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('_unique_bb_room', 'room_type', type_='unique')
    op.drop_constraint('_unique_cm_room', 'room_type', type_='unique')
    op.drop_constraint('_unique_crs_room', 'room_type', type_='unique')
    op.drop_column('room_type', 'unirate_room_type_code')
    op.drop_column('room_type', 'crs_room_type_code')
    op.drop_column('room_type', 'bb_room_type_code')
    op.drop_table('ota_room_mapping')
    op.drop_table('ota_rate_plan_mapping')
    op.drop_table('ota_property_mapping')
    op.drop_table('rate_plan_configuration')
    op.drop_table('ota_property')
    op.drop_table('rate_plan')
    op.drop_table('ota')
    # ### end Alembic commands ###
