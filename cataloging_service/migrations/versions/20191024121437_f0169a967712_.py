"""empty message

Revision ID: f0169a967712
Revises: a014165dd394
Create Date: 2019-10-24 12:14:37.745982

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f0169a967712'
down_revision = 'a014165dd394'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_detail', sa.Column('is_partner_pricing_enabled', sa.BOOLEAN(), server_default=sa.text('false'), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_detail', 'is_partner_pricing_enabled')
    # ### end Alembic commands ###
