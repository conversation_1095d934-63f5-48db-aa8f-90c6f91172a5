"""empty message

Revision ID: 1a184e90de8b
Revises: 760182db17a9
Create Date: 2018-04-13 12:10:07.543007

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1a184e90de8b'
down_revision = '760182db17a9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sku_category',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('hsn_sac', sa.String(length=100), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('properties_sku_categories',
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('sku_category_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['sku_category_id'], ['sku_category.id'], ),
    sa.PrimaryKeyConstraint('property_id', 'sku_category_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('properties_sku_categories')
    op.drop_table('sku_category')
    # ### end Alembic commands ###
