"""empty message

Revision ID: 3823e1ec0165
Revises: 6786e8e21726
Create Date: 2018-09-16 23:05:59.945728

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3823e1ec0165'
down_revision = '6786e8e21726'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sku', sa.Column('identifier', sa.String(length=400), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sku', 'identifier')
    # ### end Alembic commands ###
