"""empty message

Revision ID: 6b0ce4c990b9
Revises: 6e5ac9bce918
Create Date: 2018-02-07 14:20:39.314587

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6b0ce4c990b9'
down_revision = '6e5ac9bce918'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('city_alias',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('city_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['city_id'], ['city.id'], ),
    sa.<PERSON>eyConstraint('id'),
    sa.UniqueConstraint('name', 'city_id', name='_unique_city_alias')
    )
    op.add_column('locality', sa.Column('latitude', sa.DECIMAL(precision=9, scale=6), nullable=True))
    op.add_column('locality', sa.Column('longitude', sa.DECIMAL(precision=9, scale=6), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('locality', 'longitude')
    op.drop_column('locality', 'latitude')
    op.drop_table('city_alias')
    # ### end Alembic commands ###
