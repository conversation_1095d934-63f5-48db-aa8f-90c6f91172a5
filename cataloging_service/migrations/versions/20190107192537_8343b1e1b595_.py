"""empty message

Revision ID: 8343b1e1b595
Revises: 403f332a10fb
Create Date: 2019-01-07 19:25:37.670835

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8343b1e1b595'
down_revision = '403f332a10fb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('sku', 'sku_type',
               existing_type=postgresql.ENUM('SKU', 'BUNDLE', name='sku_choice'),
               nullable=False)
    op.alter_column('sku', 'tax_type',
               existing_type=postgresql.ENUM('UNIT', 'COMPOSITE', 'DERIVED', name='tax_choice'),
               nullable=False)
    op.drop_index('ix_sku_sku_type_id', table_name='sku')
    op.drop_index('ix_sku_tax_type_id', table_name='sku')
    op.drop_constraint('sku_sku_type_id_fkey', 'sku', type_='foreignkey')
    op.drop_constraint('sku_tax_type_id_fkey', 'sku', type_='foreignkey')
    op.drop_column('sku', 'tax_type_id')
    op.drop_column('sku', 'sku_type_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sku', sa.Column('sku_type_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('sku', sa.Column('tax_type_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('sku_tax_type_id_fkey', 'sku', 'param', ['tax_type_id'], ['id'])
    op.create_foreign_key('sku_sku_type_id_fkey', 'sku', 'param', ['sku_type_id'], ['id'])
    op.create_index('ix_sku_tax_type_id', 'sku', ['tax_type_id'], unique=False)
    op.create_index('ix_sku_sku_type_id', 'sku', ['sku_type_id'], unique=False)
    op.alter_column('sku', 'tax_type',
               existing_type=postgresql.ENUM('UNIT', 'COMPOSITE', 'DERIVED', name='tax_choice'),
               nullable=True)
    op.alter_column('sku', 'sku_type',
               existing_type=postgresql.ENUM('SKU', 'BUNDLE', name='sku_choice'),
               nullable=True)
    # ### end Alembic commands ###
