"""empty message

Revision ID: 27d061aabdbc
Revises: 42c293097284
Create Date: 2018-11-21 17:41:25.156698

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '27d061aabdbc'
down_revision = '42c293097284'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sku', sa.Column('default_list_price', sa.DECIMAL(precision=10, scale=2), nullable=True))
    op.add_column('sku', sa.Column('default_sale_price', sa.DECIMAL(precision=10, scale=2), nullable=True))
    op.add_column('sku', sa.Column('tag', sa.String(length=100), nullable=True))
    op.alter_column('sku', 'code',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.drop_constraint('sku_name_key', 'sku', type_='unique')
    op.drop_column('sku', 'default_price')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sku', sa.Column('default_price', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True))
    op.create_unique_constraint('sku_name_key', 'sku', ['name'])
    op.alter_column('sku', 'code',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.drop_column('sku', 'tag')
    op.drop_column('sku', 'default_sale_price')
    op.drop_column('sku', 'default_list_price')
    # ### end Alembic commands ###
