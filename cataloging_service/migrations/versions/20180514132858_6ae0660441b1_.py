"""empty message

Revision ID: 6ae0660441b1
Revises: 3479f636db39
Create Date: 2018-05-14 13:28:58.935009

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6ae0660441b1'
down_revision = '3479f636db39'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sku_category', sa.Column('code', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sku_category', 'code')
    # ### end Alembic commands ###
