"""empty message

Revision ID: 1cb46b3aaa5d
Revises: 612b8daa05cb
Create Date: 2018-08-23 15:49:19.146215

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1cb46b3aaa5d'
down_revision = '612b8daa05cb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('provider_room_mapping', 'display_name',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.create_unique_constraint('_unique_external_room_mapping_key', 'provider_room_mapping', ['ext_id', 'room_type_id', 'ext_room_code'])
    op.drop_constraint('_unique_property_external_room_mapping_key', 'provider_room_mapping', type_='unique')
    op.drop_constraint('provider_room_mapping_property_id_fkey', 'provider_room_mapping', type_='foreignkey')
    op.drop_column('provider_room_mapping', 'property_id')
    op.add_column('room_type_configuration', sa.Column('display_name', sa.String(length=50), nullable=True))
    op.add_column('room_type_configuration', sa.Column('ext_description', sa.TEXT(), nullable=True))
    op.add_column('room_type_configuration', sa.Column('ext_id', sa.INTEGER(), nullable=True))
    op.add_column('room_type_configuration', sa.Column('ext_room_code', sa.String(length=100), nullable=True))
    op.add_column('room_type_configuration', sa.Column('ext_room_name', sa.String(length=50), nullable=True))
    op.create_foreign_key(None, 'room_type_configuration', 'provider', ['ext_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'room_type_configuration', type_='foreignkey')
    op.drop_column('room_type_configuration', 'ext_room_name')
    op.drop_column('room_type_configuration', 'ext_room_code')
    op.drop_column('room_type_configuration', 'ext_id')
    op.drop_column('room_type_configuration', 'ext_description')
    op.drop_column('room_type_configuration', 'display_name')
    op.add_column('provider_room_mapping', sa.Column('property_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.create_foreign_key('provider_room_mapping_property_id_fkey', 'provider_room_mapping', 'property', ['property_id'], ['id'])
    op.create_unique_constraint('_unique_property_external_room_mapping_key', 'provider_room_mapping', ['property_id', 'ext_id', 'room_type_id', 'ext_room_code'])
    op.drop_constraint('_unique_external_room_mapping_key', 'provider_room_mapping', type_='unique')
    op.alter_column('provider_room_mapping', 'display_name',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    # ### end Alembic commands ###
