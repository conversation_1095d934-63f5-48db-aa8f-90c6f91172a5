"""empty message

Revision ID: 959eb2573aaf
Revises: 24b6df4345df
Create Date: 2018-09-21 17:13:12.542849

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '959eb2573aaf'
down_revision = '24b6df4345df'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'sku', ['name'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'sku', type_='unique')
    # ### end Alembic commands ###
