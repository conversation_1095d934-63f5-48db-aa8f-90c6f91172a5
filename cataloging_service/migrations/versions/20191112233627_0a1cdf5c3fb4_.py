"""empty message

Revision ID: 0a1cdf5c3fb4
Revises: 2b89c0e323d7
Create Date: 2019-11-12 23:36:27.777394

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0a1cdf5c3fb4'
down_revision = '2b89c0e323d7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('seller_category',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('code', sa.String(length=20), nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('name')
    )
    op.create_table('pos_menu_category',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('seller_category_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['seller_category_id'], ['seller_category.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('seller',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('seller_id', sa.String(), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('property_id', sa.String(), nullable=True),
    sa.Column('seller_category_id', sa.INTEGER(), nullable=False),
    sa.Column('city_id', sa.INTEGER(), nullable=False),
    sa.Column('legal_city_id', sa.INTEGER(), nullable=True),
    sa.Column('gstin', sa.String(length=20), nullable=True),
    sa.Column('legal_name', sa.String(length=100), nullable=True),
    sa.Column('legal_address', sa.TEXT(), nullable=True),
    sa.Column('legal_pincode', sa.String(length=20), nullable=True),
    sa.Column('phone_number', sa.String(length=30), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.ForeignKeyConstraint(['city_id'], ['city.id'], ),
    sa.ForeignKeyConstraint(['legal_city_id'], ['city.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['seller_category_id'], ['seller_category.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('seller_id')
    )
    op.create_table('seller_sku',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('sku_id', sa.INTEGER(), nullable=False),
    sa.Column('seller_id', sa.String(), nullable=False),
    sa.Column('sku_category_code', sa.String(length=30), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('is_sellable', sa.Boolean(), nullable=True),
    sa.Column('menu_category_id', sa.Integer(), nullable=True),
    sa.Column('pretax_price', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['menu_category_id'], ['pos_menu_category.id'], ),
    sa.ForeignKeyConstraint(['seller_id'], ['seller.seller_id'], ),
    sa.ForeignKeyConstraint(['sku_id'], ['sku.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_seller_sku_seller_id'), 'seller_sku', ['seller_id'], unique=False)
    op.create_index(op.f('ix_seller_sku_sku_id'), 'seller_sku', ['sku_id'], unique=False)
    op.add_column('sku', sa.Column('is_active', sa.Boolean(), nullable=True))
    op.add_column('sku', sa.Column('sku_details', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sku', 'sku_details')
    op.drop_column('sku', 'is_active')
    op.drop_index(op.f('ix_seller_sku_sku_id'), table_name='seller_sku')
    op.drop_index(op.f('ix_seller_sku_seller_id'), table_name='seller_sku')
    op.drop_table('seller_sku')
    op.drop_table('seller')
    op.drop_table('pos_menu_category')
    op.drop_table('seller_category')
    # ### end Alembic commands ###
