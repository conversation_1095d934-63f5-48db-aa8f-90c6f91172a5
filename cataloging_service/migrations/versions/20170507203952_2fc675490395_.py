"""empty message

Revision ID: 2fc675490395
Revises: 
Create Date: 2017-05-07 20:39:52.974642

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2fc675490395'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('amenity_ac',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('ac_type', sa.Enum('SPLIT', 'WINDOW', name='ac_choices'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_breakfast',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('type', sa.Enum('A_LA_CARTE', 'BUFFET', 'FIXED', name='breakfast_type_choices'), nullable=True),
    sa.Column('service_area', sa.Enum('DINING_AREA', 'RESTAURANT', 'ROOM', name='service_area_choices'), nullable=True),
    sa.Column('non_veg', sa.BOOLEAN(), nullable=True),
    sa.Column('rotational', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_disable_friendly',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('ramp_available', sa.BOOLEAN(), nullable=True),
    sa.Column('wheelchair_count', sa.INTEGER(), nullable=True),
    sa.Column('disable_friendly_room_available', sa.BOOLEAN(), nullable=True),
    sa.Column('disable_friendly_rooms', sa.String(length=200), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_elevator',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('floors_accessible', sa.String(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_gym',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('open_time', sa.TIME(), nullable=True),
    sa.Column('close_time', sa.TIME(), nullable=True),
    sa.Column('equipments_available', sa.String(length=500), nullable=True),
    sa.Column('active', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_heater',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('availability', sa.Enum('ON_REQUEST', 'ALL_ROOMS', name='heater_choices'), nullable=True),
    sa.Column('charges', sa.String(length=30), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_hot_water',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('central_geyser', sa.BOOLEAN(), nullable=True),
    sa.Column('room_geyser', sa.BOOLEAN(), nullable=True),
    sa.Column('from_time', sa.TIME(), nullable=True),
    sa.Column('to_time', sa.TIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_intercom',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('all_rooms_connected', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_laundry',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('pickup_time', sa.String(length=50), nullable=True),
    sa.Column('drop_time', sa.String(length=50), nullable=True),
    sa.Column('is_external', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_parking',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('location', sa.Enum('INDOORS_BASEMENT', 'OUTDOORS_UNCOVERED', name='parking_location_choices'), nullable=True),
    sa.Column('max_two_wheelers', sa.INTEGER(), nullable=True),
    sa.Column('max_four_wheelers', sa.INTEGER(), nullable=True),
    sa.Column('charges', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_payment',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('amex_accepted', sa.BOOLEAN(), nullable=True),
    sa.Column('wallet_accepted', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_private_cab',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('charges', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_public_washroom',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('gender_segregated', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_spa',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('open_time', sa.TIME(), nullable=True),
    sa.Column('close_time', sa.TIME(), nullable=True),
    sa.Column('active', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_stove',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('stove_type', sa.Enum('LPG', 'INDUCTION', name='stove_type_choices'), nullable=True),
    sa.Column('availability', sa.Enum('ALL_ROOMS', 'ON_REQUEST', name='stove_availability_choices'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_swimming_pool',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('location', sa.Enum('INDOORS_COVERED', 'OUTDOORS_UNCOVERED', name='pool_location_choices'), nullable=True),
    sa.Column('pool_size', sa.String(length=20), nullable=True),
    sa.Column('open_time', sa.TIME(), nullable=True),
    sa.Column('close_time', sa.TIME(), nullable=True),
    sa.Column('active', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_tv',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('vendor', sa.String(length=20), nullable=True),
    sa.Column('tv_type', sa.Enum('LCD', 'CRT', name='tv_choices'), nullable=True),
    sa.Column('connection_type', sa.Enum('CABLE', 'DTH', name='connection_choices'), nullable=True),
    sa.Column('size', sa.String(length=10), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('amenity_twin_bed',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('joinable', sa.BOOLEAN(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('bank_detail',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('account_name', sa.String(length=100), nullable=False),
    sa.Column('account_number', sa.String(length=50), nullable=False),
    sa.Column('account_type', sa.Enum('SAVINGS', 'CURRENT', name='account_type_choices'), nullable=True),
    sa.Column('ifsc_code', sa.String(length=100), nullable=False),
    sa.Column('bank', sa.String(length=100), nullable=False),
    sa.Column('branch', sa.String(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('cluster',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('country',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('cs_user',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('password', sa.String(length=255), nullable=False),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email')
    )
    op.create_table('cuisine',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=20), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('guest_type',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('type', sa.String(length=20), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('type')
    )
    op.create_table('notification',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('type', sa.String(length=100), nullable=True),
    sa.Column('receivers', sa.String(length=1000), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('type')
    )
    op.create_table('owner',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('first_name', sa.String(length=50), nullable=False),
    sa.Column('middle_name', sa.String(length=50), nullable=True),
    sa.Column('last_name', sa.String(length=50), nullable=True),
    sa.Column('gender', sa.Enum('M', 'F', name='gender_choices'), nullable=True),
    sa.Column('email', sa.String(length=50), nullable=True),
    sa.Column('phone_number', sa.String(length=50), nullable=True),
    sa.Column('date_of_birth', sa.DATE(), nullable=True),
    sa.Column('occupation', sa.String(length=50), nullable=True),
    sa.Column('education', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('property',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('hx_id', sa.String(length=10), nullable=True),
    sa.Column('status', sa.Enum('NEAR_CONFIRMED', 'NOT_SIGNING', 'SIGNED', 'DROPPED_POST_SIGNING', 'LIVE', 'CHURNED', name='status_choices'), nullable=True),
    sa.Column('name', sa.String(length=100), nullable=True),
    sa.Column('old_name', sa.String(length=100), nullable=False),
    sa.Column('legal_name', sa.String(length=100), nullable=True),
    sa.Column('signed_date', sa.DATE(), nullable=True),
    sa.Column('contractual_launch_date', sa.DATE(), nullable=True),
    sa.Column('launched_date', sa.DATE(), nullable=True),
    sa.Column('churned_date', sa.DATE(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('hx_id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('role',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=80), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('room_type',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('type', sa.Enum('ACACIA', 'OAK', 'MAPLE', 'MAHOGANY', name='room_type_choices'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('type')
    )
    op.create_table('system_property',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('value', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('transport_station',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('type', sa.Enum('AIRPORT', 'RAILWAY_STATION', 'INTER_CITY_BUS_STAND', 'LOCAL_BUS_STAND', 'METRO_STATION', 'LOCAL_TRAIN_STATION', name='station_choices'), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('latitude', sa.DECIMAL(precision=9, scale=6), nullable=True),
    sa.Column('longitude', sa.DECIMAL(precision=9, scale=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('banquet_hall',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=True),
    sa.Column('floor', sa.INTEGER(), nullable=True),
    sa.Column('capacity', sa.INTEGER(), nullable=True),
    sa.Column('size', sa.String(length=20), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('bar',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=True),
    sa.Column('open_time', sa.TIME(), nullable=True),
    sa.Column('last_order_time', sa.TIME(), nullable=True),
    sa.Column('close_time', sa.TIME(), nullable=True),
    sa.Column('room_start_time', sa.TIME(), nullable=True),
    sa.Column('room_end_time', sa.TIME(), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('breakfast_cuisine',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('cuisine_id', sa.INTEGER(), nullable=False),
    sa.Column('breakfast_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['breakfast_id'], ['amenity_breakfast.id'], ),
    sa.ForeignKeyConstraint(['cuisine_id'], ['cuisine.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('description',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('property_description', sa.String(length=500), nullable=True),
    sa.Column('acacia_description', sa.String(length=500), nullable=True),
    sa.Column('oak_description', sa.String(length=500), nullable=True),
    sa.Column('maple_description', sa.String(length=500), nullable=True),
    sa.Column('mahogany_description', sa.String(length=500), nullable=True),
    sa.Column('trilight_one', sa.String(length=100), nullable=True),
    sa.Column('trilight_two', sa.String(length=100), nullable=True),
    sa.Column('trilight_three', sa.String(length=100), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('google_drive_base_folder',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=True),
    sa.Column('folder_name', sa.String(length=50), nullable=False),
    sa.Column('folder_id', sa.String(length=50), nullable=False),
    sa.Column('folder_link', sa.String(length=150), nullable=False),
    sa.Column('property_documents_file_id', sa.String(length=50), nullable=True),
    sa.Column('property_documents_link', sa.String(length=150), nullable=True),
    sa.Column('property_images_file_id', sa.String(length=50), nullable=True),
    sa.Column('property_images_link', sa.String(length=150), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('folder_name')
    )
    op.create_table('google_drive_file',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=True),
    sa.Column('file_id', sa.String(length=50), nullable=True),
    sa.Column('file_name', sa.String(length=100), nullable=True),
    sa.Column('file_type', sa.Enum('COMPLETE_AGREEMENT', 'AGREEMENT_FIRST_PAGE', 'AGREEMENT_EXPIRY', 'AGREEMENT_DETAILS', 'SERVICE_TAX_ONE', 'SERVICE_TAX_TWO', 'LUXURY_TAX_ONE', 'LUXURY_TAX_TWO', 'OTA_NOC', 'GI_NOC', 'LEASE_DOCUMENT', 'ELECTRIC_BILL', 'PARTNER_CANCELLED_CHEQUE', 'HOTEL_REGISTRATION_DOCUMENT', 'PAN_CARD', 'OLD_FRANCHISE_TERMINATION_DOCUMENT', 'PARTNER_OLD_PHOTO', 'PARTNER_PROFESSIONAL_PHOTO', 'FACADE_IMAGE', 'HIGH_RES_IMAGE', 'MID_RES_IMAGE', 'LOW_RES_IMAGE', 'TREEBO_SERVICE_TAX', 'TREEBO_CANCELLED_CHEQUE', 'GENERIC_IMAGE', 'FILE_TYPE_C_FORM', name='file_type_choices'), nullable=False),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('guest_facing_process',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('checkin_time', sa.TIME(), nullable=True),
    sa.Column('checkout_time', sa.TIME(), nullable=True),
    sa.Column('free_early_checkin', sa.TIME(), nullable=True),
    sa.Column('free_late_checkout', sa.TIME(), nullable=True),
    sa.Column('early_checkin_fee', sa.String(length=50), nullable=False),
    sa.Column('late_checkout_fee', sa.String(length=50), nullable=False),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('guest_type_property',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('guest_type_id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['guest_type_id'], ['guest_type.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('landmark',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=True),
    sa.Column('latitude', sa.DECIMAL(precision=9, scale=6), nullable=True),
    sa.Column('longitude', sa.DECIMAL(precision=9, scale=6), nullable=True),
    sa.Column('distance_from_property', sa.DECIMAL(precision=5, scale=2), nullable=True),
    sa.Column('property_direction', sa.String(length=500), nullable=True),
    sa.Column('hatchback_cab_fare', sa.DECIMAL(precision=7, scale=2), nullable=True),
    sa.Column('sedan_cab_fare', sa.DECIMAL(precision=7, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('neighbouring_place',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('nearest_hospital', sa.String(length=100), nullable=True),
    sa.Column('utility_shops', sa.String(length=100), nullable=True),
    sa.Column('restaurants', sa.String(length=100), nullable=True),
    sa.Column('tourist_spots', sa.String(length=100), nullable=True),
    sa.Column('corporate_offices', sa.String(length=100), nullable=True),
    sa.Column('popular_malls', sa.String(length=100), nullable=True),
    sa.Column('shopping_streets', sa.String(length=100), nullable=True),
    sa.Column('city_centre', sa.String(length=100), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ownership',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('owner_id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('primary', sa.BOOLEAN(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['owner.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('owner_id', 'property_id', name='_unique_property_owner')
    )
    op.create_table('property_amenity',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('public_washroom_id', sa.INTEGER(), nullable=True),
    sa.Column('elevator_id', sa.INTEGER(), nullable=True),
    sa.Column('lobby_ac', sa.BOOLEAN(), nullable=True),
    sa.Column('lobby_furniture', sa.BOOLEAN(), nullable=True),
    sa.Column('lobby_smoke_alarm', sa.BOOLEAN(), nullable=True),
    sa.Column('security', sa.BOOLEAN(), nullable=True),
    sa.Column('pantry', sa.BOOLEAN(), nullable=True),
    sa.Column('cloak_room', sa.BOOLEAN(), nullable=True),
    sa.Column('travel_desk', sa.BOOLEAN(), nullable=True),
    sa.Column('room_service', sa.BOOLEAN(), nullable=True),
    sa.Column('roof_top_cafe', sa.BOOLEAN(), nullable=True),
    sa.Column('pool_table', sa.BOOLEAN(), nullable=True),
    sa.Column('pets_allowed', sa.BOOLEAN(), nullable=True),
    sa.Column('parking_id', sa.INTEGER(), nullable=True),
    sa.Column('private_cab_id', sa.INTEGER(), nullable=True),
    sa.Column('iron_board_count', sa.INTEGER(), nullable=True),
    sa.Column('driver_quarters_count', sa.INTEGER(), nullable=True),
    sa.Column('disable_friendly_id', sa.INTEGER(), nullable=True),
    sa.Column('swimming_pool_id', sa.INTEGER(), nullable=True),
    sa.Column('gym_id', sa.INTEGER(), nullable=True),
    sa.Column('spa_id', sa.INTEGER(), nullable=True),
    sa.Column('laundry_id', sa.INTEGER(), nullable=True),
    sa.Column('breakfast_id', sa.INTEGER(), nullable=True),
    sa.Column('payment_id', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['breakfast_id'], ['amenity_breakfast.id'], ),
    sa.ForeignKeyConstraint(['disable_friendly_id'], ['amenity_disable_friendly.id'], ),
    sa.ForeignKeyConstraint(['elevator_id'], ['amenity_elevator.id'], ),
    sa.ForeignKeyConstraint(['gym_id'], ['amenity_gym.id'], ),
    sa.ForeignKeyConstraint(['laundry_id'], ['amenity_laundry.id'], ),
    sa.ForeignKeyConstraint(['parking_id'], ['amenity_parking.id'], ),
    sa.ForeignKeyConstraint(['payment_id'], ['amenity_payment.id'], ),
    sa.ForeignKeyConstraint(['private_cab_id'], ['amenity_private_cab.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['public_washroom_id'], ['amenity_public_washroom.id'], ),
    sa.ForeignKeyConstraint(['spa_id'], ['amenity_spa.id'], ),
    sa.ForeignKeyConstraint(['swimming_pool_id'], ['amenity_swimming_pool.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('property_detail',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('neighbourhood_type', sa.Enum('QUIET', 'RESIDENTIAL', 'GREEN', 'BUSY_MARKET', 'MAIN_ROAD', 'OTHERS', name='neighbourhood_choices'), nullable=False),
    sa.Column('neighbourhood_detail', sa.String(length=100), nullable=True),
    sa.Column('property_type', sa.Enum('HOTEL', 'SERVICE_APARTMENT', 'RESORT', 'HOMESTAY_COTTAGE_VILLA', name='property_type_choices'), nullable=False),
    sa.Column('property_style', sa.Enum('HERITAGE', 'MODERN', 'GUEST_HOUSE', 'PLAIN_VANILLA', 'OTHERS', name='property_style_choices'), nullable=False),
    sa.Column('style_detail', sa.String(length=100), nullable=True),
    sa.Column('construction_year', sa.INTEGER(), nullable=True),
    sa.Column('building_style', sa.Enum('INDEPENDENT_SINGLE', 'INDEPENDENT_MULTIPLE', 'PART_OF_SINGLE_BUILDING', 'PART_OF_MULTIPLE_BUILDING', 'FLOORS_IN_EACH_BUILDING', name='building_style_choices'), nullable=False),
    sa.Column('unmarried_couple_allowed', sa.BOOLEAN(), nullable=True),
    sa.Column('local_id_allowed', sa.BOOLEAN(), nullable=True),
    sa.Column('floor_count', sa.INTEGER(), nullable=True),
    sa.Column('star_rating', sa.INTEGER(), nullable=True),
    sa.Column('previous_franchise', sa.BOOLEAN(), nullable=True),
    sa.Column('reception_landline', sa.String(length=200), nullable=True),
    sa.Column('reception_mobile', sa.String(length=200), nullable=True),
    sa.Column('bank_detail_id', sa.INTEGER(), nullable=True),
    sa.Column('vat_number', sa.String(length=15), nullable=True),
    sa.Column('tin', sa.String(length=15), nullable=True),
    sa.Column('pan', sa.String(length=15), nullable=True),
    sa.Column('service_tax_number', sa.String(length=15), nullable=True),
    sa.Column('luxury_tax_number', sa.String(length=15), nullable=True),
    sa.Column('is_leased', sa.BOOLEAN(), nullable=True),
    sa.ForeignKeyConstraint(['bank_detail_id'], ['bank_detail.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('restaurant',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=True),
    sa.Column('non_veg', sa.BOOLEAN(), nullable=True),
    sa.Column('open_time', sa.TIME(), nullable=True),
    sa.Column('last_order_time', sa.TIME(), nullable=True),
    sa.Column('close_time', sa.TIME(), nullable=True),
    sa.Column('a_la_carte', sa.BOOLEAN(), nullable=True),
    sa.Column('buffet', sa.BOOLEAN(), nullable=True),
    sa.Column('outside_food_allowed', sa.BOOLEAN(), nullable=True),
    sa.Column('baby_milk_served', sa.BOOLEAN(), nullable=True),
    sa.Column('baby_milk_timing', sa.String(length=100), nullable=True),
    sa.Column('handwash_present', sa.BOOLEAN(), nullable=True),
    sa.Column('washroom_present', sa.BOOLEAN(), nullable=True),
    sa.Column('egg_served', sa.BOOLEAN(), nullable=True),
    sa.Column('jain_food_served', sa.BOOLEAN(), nullable=True),
    sa.Column('room_service_start_time', sa.TIME(), nullable=True),
    sa.Column('room_service_end_time', sa.TIME(), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('roles_users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['role_id'], ['role.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['cs_user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('room',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('room_number', sa.String(length=10), nullable=False),
    sa.Column('room_type_id', sa.INTEGER(), nullable=False),
    sa.Column('building_number', sa.String(), nullable=True),
    sa.Column('floor_number', sa.INTEGER(), nullable=True),
    sa.Column('size', sa.String(length=10), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['room_type_id'], ['room_type.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('property_id', 'room_number', name='_unique_property_room_number')
    )
    op.create_table('room_type_configuration',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('room_type_id', sa.INTEGER(), nullable=False),
    sa.Column('extra_bed', sa.Enum('MATTRESS', 'FOLDING_BED', 'SINGLE_BED', name='extra_bed_choices'), nullable=True),
    sa.Column('min_occupancy', sa.INTEGER(), nullable=True),
    sa.Column('max_occupancy', sa.INTEGER(), nullable=True),
    sa.Column('adults', sa.INTEGER(), nullable=True),
    sa.Column('mm_id', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['room_type_id'], ['room_type.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('mm_id'),
    sa.UniqueConstraint('property_id', 'room_type_id', name='_unique_property_room_type')
    )
    op.create_table('state',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('country_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('country_id', 'name', name='_unique_state_country')
    )
    op.create_table('transport_station_property',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('transport_station_id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('distance_from_property', sa.DECIMAL(precision=5, scale=2), nullable=True),
    sa.Column('property_direction', sa.String(length=500), nullable=True),
    sa.Column('hatchback_cab_fare', sa.DECIMAL(precision=7, scale=2), nullable=True),
    sa.Column('sedan_cab_fare', sa.DECIMAL(precision=7, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.ForeignKeyConstraint(['transport_station_id'], ['transport_station.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('property_id', 'transport_station_id', name='_unique_property_station')
    )
    op.create_table('city',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('state_id', sa.INTEGER(), nullable=False),
    sa.Column('cluster_id', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['cluster_id'], ['cluster.id'], ),
    sa.ForeignKeyConstraint(['state_id'], ['state.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('state_id', 'name', name='_unique_state_city')
    )
    op.create_table('restaurant_cuisine',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('cuisine_id', sa.INTEGER(), nullable=False),
    sa.Column('restaurant_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['cuisine_id'], ['cuisine.id'], ),
    sa.ForeignKeyConstraint(['restaurant_id'], ['restaurant.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('room_amenity',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('room_id', sa.INTEGER(), nullable=False),
    sa.Column('mini_fridge', sa.BOOLEAN(), nullable=True),
    sa.Column('balcony', sa.BOOLEAN(), nullable=True),
    sa.Column('kitchenette', sa.BOOLEAN(), nullable=True),
    sa.Column('kitchenette_utensils', sa.BOOLEAN(), nullable=True),
    sa.Column('king_sized_beds', sa.BOOLEAN(), nullable=True),
    sa.Column('queen_sized_beds', sa.BOOLEAN(), nullable=True),
    sa.Column('single_beds', sa.BOOLEAN(), nullable=True),
    sa.Column('wardrobe', sa.BOOLEAN(), nullable=True),
    sa.Column('locker_available', sa.BOOLEAN(), nullable=True),
    sa.Column('microwave', sa.BOOLEAN(), nullable=True),
    sa.Column('luggage_shelf', sa.BOOLEAN(), nullable=True),
    sa.Column('study_table_chair', sa.BOOLEAN(), nullable=True),
    sa.Column('sofa_chair', sa.BOOLEAN(), nullable=True),
    sa.Column('coffee_table', sa.BOOLEAN(), nullable=True),
    sa.Column('other_furniture', sa.BOOLEAN(), nullable=True),
    sa.Column('smoking_room', sa.BOOLEAN(), nullable=True),
    sa.Column('bath_tub', sa.BOOLEAN(), nullable=True),
    sa.Column('shower_curtain', sa.BOOLEAN(), nullable=True),
    sa.Column('smoke_alarm', sa.BOOLEAN(), nullable=True),
    sa.Column('shower_cabinets', sa.BOOLEAN(), nullable=True),
    sa.Column('living_room', sa.BOOLEAN(), nullable=True),
    sa.Column('dining_table', sa.BOOLEAN(), nullable=True),
    sa.Column('windows', sa.BOOLEAN(), nullable=True),
    sa.Column('treebo_toiletries', sa.BOOLEAN(), nullable=True),
    sa.Column('fan_type', sa.Enum('CEILING', 'STANDING', name='fan_type_choices'), nullable=True),
    sa.Column('lock_type', sa.Enum('ELECTRONIC', 'MANUAL', name='lock_type_choices'), nullable=True),
    sa.Column('bucket_mug', sa.Enum('ON_REQUEST', 'ALL_ROOMS', name='bucket_choices'), nullable=True),
    sa.Column('mosquito_repellent', sa.Enum('ON_REQUEST', 'ALL_ROOMS', name='mosquito_repellent_choices'), nullable=True),
    sa.Column('heater_id', sa.INTEGER(), nullable=True),
    sa.Column('twin_bed_id', sa.INTEGER(), nullable=True),
    sa.Column('intercom_id', sa.INTEGER(), nullable=True),
    sa.Column('hot_water_id', sa.INTEGER(), nullable=True),
    sa.Column('tv_id', sa.INTEGER(), nullable=True),
    sa.Column('ac_id', sa.INTEGER(), nullable=True),
    sa.Column('stove_id', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['ac_id'], ['amenity_ac.id'], ),
    sa.ForeignKeyConstraint(['heater_id'], ['amenity_heater.id'], ),
    sa.ForeignKeyConstraint(['hot_water_id'], ['amenity_hot_water.id'], ),
    sa.ForeignKeyConstraint(['intercom_id'], ['amenity_intercom.id'], ),
    sa.ForeignKeyConstraint(['room_id'], ['room.id'], ),
    sa.ForeignKeyConstraint(['stove_id'], ['amenity_stove.id'], ),
    sa.ForeignKeyConstraint(['tv_id'], ['amenity_tv.id'], ),
    sa.ForeignKeyConstraint(['twin_bed_id'], ['amenity_twin_bed.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('micro_market',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('city_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['city_id'], ['city.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('locality',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('city_id', sa.INTEGER(), nullable=False),
    sa.Column('micro_market_id', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['city_id'], ['city.id'], ),
    sa.ForeignKeyConstraint(['micro_market_id'], ['micro_market.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('city_id', 'name', name='_unique_locality_city')
    )
    op.create_table('location',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('latitude', sa.DECIMAL(precision=9, scale=6), nullable=True),
    sa.Column('longitude', sa.DECIMAL(precision=9, scale=6), nullable=True),
    sa.Column('pincode', sa.INTEGER(), nullable=True),
    sa.Column('postal_address', sa.String(length=500), nullable=True),
    sa.Column('maps_link', sa.String(length=200), nullable=True),
    sa.Column('micro_market_id', sa.INTEGER(), nullable=True),
    sa.Column('locality_id', sa.INTEGER(), nullable=True),
    sa.Column('city_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['city_id'], ['city.id'], ),
    sa.ForeignKeyConstraint(['locality_id'], ['locality.id'], ),
    sa.ForeignKeyConstraint(['micro_market_id'], ['micro_market.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('location')
    op.drop_table('locality')
    op.drop_table('micro_market')
    op.drop_table('room_amenity')
    op.drop_table('restaurant_cuisine')
    op.drop_table('city')
    op.drop_table('transport_station_property')
    op.drop_table('state')
    op.drop_table('room_type_configuration')
    op.drop_table('room')
    op.drop_table('roles_users')
    op.drop_table('restaurant')
    op.drop_table('property_detail')
    op.drop_table('property_amenity')
    op.drop_table('ownership')
    op.drop_table('neighbouring_place')
    op.drop_table('landmark')
    op.drop_table('guest_type_property')
    op.drop_table('guest_facing_process')
    op.drop_table('google_drive_file')
    op.drop_table('google_drive_base_folder')
    op.drop_table('description')
    op.drop_table('breakfast_cuisine')
    op.drop_table('bar')
    op.drop_table('banquet_hall')
    op.drop_table('transport_station')
    op.drop_table('system_property')
    op.drop_table('room_type')
    op.drop_table('role')
    op.drop_table('property')
    op.drop_table('owner')
    op.drop_table('notification')
    op.drop_table('guest_type')
    op.drop_table('cuisine')
    op.drop_table('cs_user')
    op.drop_table('country')
    op.drop_table('cluster')
    op.drop_table('bank_detail')
    op.drop_table('amenity_twin_bed')
    op.drop_table('amenity_tv')
    op.drop_table('amenity_swimming_pool')
    op.drop_table('amenity_stove')
    op.drop_table('amenity_spa')
    op.drop_table('amenity_public_washroom')
    op.drop_table('amenity_private_cab')
    op.drop_table('amenity_payment')
    op.drop_table('amenity_parking')
    op.drop_table('amenity_laundry')
    op.drop_table('amenity_intercom')
    op.drop_table('amenity_hot_water')
    op.drop_table('amenity_heater')
    op.drop_table('amenity_gym')
    op.drop_table('amenity_elevator')
    op.drop_table('amenity_disable_friendly')
    op.drop_table('amenity_breakfast')
    op.drop_table('amenity_ac')
    # ### end Alembic commands ###
