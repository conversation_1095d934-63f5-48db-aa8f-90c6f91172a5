"""empty message

Revision ID: ed470ddb1bef
Revises: 6ae0660441b1
Create Date: 2018-05-14 13:50:32.659275

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
from sqlalchemy.orm import Session

from cataloging_service.models import SkuCategory
from cataloging_service.utils import Utils

revision = 'ed470ddb1bef'
down_revision = '6ae0660441b1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    bind = op.get_bind()
    session = Session(bind=bind)

    for category in session.query(SkuCategory):
        category.code = Utils.slugify(category.name)

    session.commit()

    op.alter_column('sku_category', 'code',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.create_unique_constraint(None, 'sku_category', ['code'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'sku_category', type_='unique')
    op.alter_column('sku_category', 'code',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    # ### end Alembic commands ###
