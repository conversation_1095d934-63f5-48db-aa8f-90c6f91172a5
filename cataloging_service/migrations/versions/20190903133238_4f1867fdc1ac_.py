"""empty message

Revision ID: 4f1867fdc1ac
Revises: 4e22cd840a37
Create Date: 2019-09-03 13:32:38.886619

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4f1867fdc1ac'
down_revision = '4e22cd840a37'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_detail', sa.Column('is_housekeeping_enabled', sa.BOOLEAN(), server_default=sa.text('false'), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_detail', 'is_housekeeping_enabled')
    # ### end Alembic commands ###
