"""empty message

Revision ID: eb4dedb16194
Revises: 255c03f5b1c3
Create Date: 2017-11-12 14:00:36.139878

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'eb4dedb16194'
down_revision = '255c03f5b1c3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('room_type_configuration', sa.Column('children', sa.INTEGER(), nullable=True))
    op.add_column('room_type_configuration', sa.Column('max_total', sa.INTEGER(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('room_type_configuration', 'max_total')
    op.drop_column('room_type_configuration', 'children')
    # ### end Alembic commands ###
