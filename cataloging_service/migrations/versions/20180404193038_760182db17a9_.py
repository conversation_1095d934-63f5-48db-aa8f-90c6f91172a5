"""empty message

Revision ID: 760182db17a9
Revises: 519b90ed40d3
Create Date: 2018-04-04 19:30:38.676561

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '760182db17a9'
down_revision = '519b90ed40d3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('property_image', 'path',
               existing_type=sa.TEXT(),
               nullable=False)
    op.create_unique_constraint(None, 'property_image', ['path'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'property_image', type_='unique')
    op.alter_column('property_image', 'path',
               existing_type=sa.TEXT(),
               nullable=True)
    # ### end Alembic commands ###
