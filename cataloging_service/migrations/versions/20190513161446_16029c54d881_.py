"""empty message

Revision ID: 16029c54d881
Revises: d4a40c6bd52e
Create Date: 2019-05-13 16:14:46.347241

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '16029c54d881'
down_revision = 'd4a40c6bd52e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property', sa.Column('is_test', sa.<PERSON>(), server_default=sa.text('false'), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property', 'is_test')
    # ### end Alembic commands ###
