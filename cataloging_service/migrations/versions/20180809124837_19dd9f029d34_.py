"""empty message

Revision ID: 19dd9f029d34
Revises: b57ebb73c408
Create Date: 2018-08-09 12:48:37.551118

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '19dd9f029d34'
down_revision = 'b57ebb73c408'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rate_plan', sa.Column('treebo_plan_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'rate_plan', 'rate_plan', ['treebo_plan_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'rate_plan', type_='foreignkey')
    op.drop_column('rate_plan', 'treebo_plan_id')
    # ### end Alembic commands ###
