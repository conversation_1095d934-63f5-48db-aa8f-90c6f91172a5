"""empty message

Revision ID: 255c03f5b1c3
Revises: 7eb8f5b23134
Create Date: 2017-06-05 11:11:54.818993

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '255c03f5b1c3'
down_revision = '7eb8f5b23134'
branch_labels = None
depends_on = None

old_options = ('SPLIT', 'WINDOW')
new_options = sorted(old_options + ('CENTRALISED',))

old_type = sa.Enum(*old_options, name='ac_choices')
new_type = sa.Enum(*new_options, name='ac_choices')
tmp_type = sa.Enum(*new_options, name='_ac_choices')

tcr = sa.sql.table('amenity_ac',
                   sa.Column('ac_type', new_type, nullable=True))


def upgrade():
    tmp_type.create(op.get_bind(), checkfirst=False)
    op.execute('ALTER TABLE amenity_ac ALTER COLUMN ac_type TYPE _ac_choices'
               ' USING ac_type::text::_ac_choices')
    old_type.drop(op.get_bind(), checkfirst=False)
    new_type.create(op.get_bind(), checkfirst=False)
    op.execute('ALTER TABLE amenity_ac ALTER COLUMN ac_type TYPE ac_choices'
               ' USING ac_type::text::ac_choices')
    tmp_type.drop(op.get_bind(), checkfirst=False)


def downgrade():
    pass
    # Cannot downgrade since existing type will go missing
