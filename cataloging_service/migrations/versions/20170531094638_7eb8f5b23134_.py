"""empty message

Revision ID: 7eb8f5b23134
Revises: 420b8ce10ec4
Create Date: 2017-05-31 09:46:38.818600

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7eb8f5b23134'
down_revision = '420b8ce10ec4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('region',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.add_column('cluster', sa.Column('region_id', sa.INTEGER(), nullable=True))
    op.create_foreign_key(None, 'cluster', 'region', ['region_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'cluster', type_='foreignkey')
    op.drop_column('cluster', 'region_id')
    op.drop_table('region')
    # ### end Alembic commands ###
