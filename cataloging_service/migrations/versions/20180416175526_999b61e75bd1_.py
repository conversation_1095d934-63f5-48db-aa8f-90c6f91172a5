"""empty message

Revision ID: 999b61e75bd1
Revises: 1a184e90de8b
Create Date: 2018-04-16 17:55:26.482841

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '999b61e75bd1'
down_revision = '1a184e90de8b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('channel',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='channel_status'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('sub_channel',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='sub_channel_status'), nullable=True),
    sa.Column('channel_id', sa.String(length=100), nullable=False),
    sa.ForeignKeyConstraint(['channel_id'], ['channel.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.add_column('guest_facing_process', sa.Column('checkin_grace_time', sa.INTEGER(), nullable=True))
    op.add_column('guest_facing_process', sa.Column('checkout_grace_time', sa.INTEGER(), nullable=True))
    op.add_column('guest_facing_process', sa.Column('switch_over_time', sa.TIME(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('guest_facing_process', 'switch_over_time')
    op.drop_column('guest_facing_process', 'checkout_grace_time')
    op.drop_column('guest_facing_process', 'checkin_grace_time')
    op.drop_table('sub_channel')
    op.drop_table('channel')
    # ### end Alembic commands ###
