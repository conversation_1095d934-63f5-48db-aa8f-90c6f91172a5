"""empty message

Revision ID: 3c7750b7cd08
Revises: 4f1867fdc1ac
Create Date: 2019-09-24 15:47:18.754283

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3c7750b7cd08'
down_revision = '4f1867fdc1ac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('property_detail', sa.Column('has_lut', sa.BOOLEAN(), server_default=sa.text('false'), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('property_detail', 'has_lut')
    # ### end Alembic commands ###
