"""empty message

Revision ID: d4a40c6bd52e
Revises: 9f28b7b1cb7b
Create Date: 2019-03-14 16:57:53.998016

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd4a40c6bd52e'
down_revision = '9f28b7b1cb7b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ruptub_legal_entities',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('state_id', sa.INTEGER(), nullable=False),
    sa.Column('gstin', sa.String(length=15), nullable=False),
    sa.Column('date_of_registration', sa.DATE(), nullable=False),
    sa.Column('address_line_1', sa.String(length=100), nullable=False),
    sa.Column('address_line_2', sa.String(length=100), nullable=False),
    sa.Column('address_city', sa.String(length=100), nullable=False),
    sa.Column('address_pincode', sa.String(length=6), nullable=False),
    sa.Column('legal_name', sa.String(length=32), nullable=False),
    sa.ForeignKeyConstraint(['state_id'], ['state.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('gstin'),
    sa.UniqueConstraint('state_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('ruptub_legal_entities')
    # ### end Alembic commands ###
