"""empty message

Revision ID: 519b90ed40d3
Revises: e231ab272e9a
Create Date: 2018-04-04 15:33:08.831714

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '519b90ed40d3'
down_revision = 'e231ab272e9a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('property_landmark',
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('modified_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('landmark_id', sa.INTEGER(), nullable=False),
    sa.Column('property_id', sa.String(), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.Column('distance_from_property', sa.DECIMAL(precision=5, scale=2), nullable=True),
    sa.Column('property_direction', sa.String(length=1000), nullable=True),
    sa.Column('hatchback_cab_fare', sa.DECIMAL(precision=7, scale=2), nullable=True),
    sa.Column('sedan_cab_fare', sa.DECIMAL(precision=7, scale=2), nullable=True),
    sa.ForeignKeyConstraint(['landmark_id'], ['landmark.id'], ),
    sa.ForeignKeyConstraint(['property_id'], ['property.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('landmark_id', 'property_id', name='_unique_property_landmark')
    )
    op.alter_column('landmark', 'name',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('landmark', 'property_id',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.alter_column('landmark', 'type',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('landmark', 'type',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('landmark', 'property_id',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('landmark', 'name',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.drop_table('property_landmark')
    # ### end Alembic commands ###
