import logging
from collections import defaultdict

import simplej<PERSON>


from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.models import RoomRackRateModel, Property, RoomType

logger = logging.getLogger(__name__)


class RoomRackRateService:
    def __init__(self, room_rack_rate_repository):
        self.__room_rack_rate_repository = room_rack_rate_repository

    def get_room_rack_rates(self, property_id):
        return self.__room_rack_rate_repository.get_room_rack_rates(property_id)

    @atomic_operation
    def update_room_rack_rate(self, room_rack_rate_id, payload):
        room_rack_rate = self._get_room_rack_rate_by_id(room_rack_rate_id)

        room_rack_rate.rack_rate = payload['rack_rate']
        self.__room_rack_rate_repository.save_room_rack_rate(room_rack_rate)

        return room_rack_rate

    def _get_room_rack_rate_by_id(self, room_rack_rate_id):
        room_rack_rate = self.__room_rack_rate_repository.get_room_rack_rate_by_id(room_rack_rate_id)

        if not room_rack_rate:
            raise CatalogingServiceException(error_codes.ROOM_RACK_RATE_NOT_FOUND)

        return room_rack_rate
