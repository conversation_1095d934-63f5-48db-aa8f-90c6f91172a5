import logging
from datetime import timedelta

import requests
from flask.globals import current_app

from cataloging_service.constants import error_codes, constants
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.alerts.alerts import slack_alert
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.utils import Utils
from cataloging_service.client.service_registry_client import ServiceRegistryClient

logger = logging.getLogger(__name__)


class OTAService:
    def __init__(self, property_repository, meta_repository, email_client):
        self.__property_repository = property_repository
        self.__meta_repository = meta_repository
        self.__email_client = email_client

    def get_ota(self, ota_code):
        ota_object = self.__property_repository.get_ota(ota_code)
        if not ota_object:
            raise CatalogingServiceException(error_codes.OTA_NOT_FOUND, context='OTA with code %s not found' % ota_code)

        return ota_object

    def get_ota_property(self, property_id, ota_code):
        ota_property = self.__property_repository.get_ota_property(property_id, ota_code)

        if not ota_property:
            raise CatalogingServiceException(error_codes.OTA_PROPERTY_NOT_FOUND,
                                             context='OTA Property %s-%s not found' % (property_id, ota_code))

        return ota_property

    def _get_unirate_map(self):
        room_types = self.__property_repository.get_all_room_types()
        rate_plans = self.__property_repository.get_all_rate_plans()

        room_type_map = {room_type.type: room_type.unirate_room_type_code for room_type in room_types}
        rate_plan_map = {rate_plan.plan: rate_plan.unirate_rate_plan_code for rate_plan in rate_plans}

        return room_type_map, rate_plan_map

    def validate_ota_property(self, ota_property):
        self._validate_hotel(ota_property)
        self._validate_rooms(ota_property)
        self._validate_rate_plans(ota_property)

    def _validate_hotel(self, ota_property):
        # if ota_property.property.status != PropertyChoices.STATUS_LIVE:
        #     raise CatalogingServiceException(error_codes.PROPERTY_NOT_LIVE, context='The property is not LIVE')

        if not ota_property.hotel_mappings:
            raise CatalogingServiceException(error_codes.INVALID_OTA_PROPERTY_CONFIG, context='Hotel mapping needed')

    def _validate_rooms(self, ota_property):
        room_type_configurations = self.__property_repository.get_property_room_type_configurations(
            ota_property.property.id)

        property_room_types = {room_config.room_type.type for room_config in room_type_configurations}

        for mapping in ota_property.room_mappings:

            if mapping.room_type.type not in property_room_types:
                raise CatalogingServiceException(error_codes.INVALID_OTA_PROPERTY_CONFIG,
                                                 context='%s not applicable for hotel %s' % (
                                                     mapping.room_type.type, ota_property.property))

                # if len(property_room_types) != len(ota_property.room_mappings):
                #     raise CatalogingServiceException(error_codes.INVALID_OTA_PROPERTY_CONFIG,
                #                                      context='Not all room types have been mapped')

    def _validate_rate_plans(self, ota_property):
        room_type_configurations = self.__property_repository.get_property_room_type_configurations(
            ota_property.property.id)
        rate_plan_configurations = self.__property_repository.get_property_rate_plan_configurations(
            ota_property.property.id)

        property_room_types = {room_config.room_type.type for room_config in room_type_configurations}
        property_rate_plans = {rate_config.rate_plan.plan for rate_config in rate_plan_configurations}
        standard_rate_plan_mappings = set()

        for mapping in ota_property.rate_plan_mappings:
            rate_plan = mapping.rate_plan.plan
            room_type = mapping.room_type.type
            if rate_plan not in property_rate_plans:
                raise CatalogingServiceException(error_codes.INVALID_OTA_PROPERTY_CONFIG,
                                                 context='%s not applicable for hotel %s' % (
                                                     rate_plan, ota_property.property))

            if room_type not in property_room_types:
                raise CatalogingServiceException(error_codes.INVALID_OTA_PROPERTY_CONFIG,
                                                 context='%s not applicable for hotel %s' % (
                                                     room_type, ota_property.property))

            if mapping.rate_push_enabled:
                if (room_type, rate_plan) in standard_rate_plan_mappings:
                    raise CatalogingServiceException(error_codes.INVALID_OTA_PROPERTY_CONFIG,
                                                     context='Duplicate standard rate plan mapping %s, %s' % (
                                                         room_type, rate_plan))

                standard_rate_plan_mappings.add((room_type, rate_plan))

                # if len(standard_rate_plan_mappings) != (len(property_room_types) * len(property_rate_plans)):
                #     raise CatalogingServiceException(error_codes.INVALID_OTA_PROPERTY_CONFIG,
                #                                      context='Not all standard rate plans mapped')

    @atomic_operation
    def process_rcs_ota_push(self, ota_object):
        ota_object.rcs_callback_complete = True
        ota_object.rcs_callback_time = Utils.get_current_time_utc()
        self.__property_repository.persist(ota_object)
        self.push_ota_details(ota_object, False, True)

    @atomic_operation
    def push_ota_details(self, ota_object, push_to_its=True, push_to_unirate=True):
        if push_to_its:
            self._push_ota_its(ota_object)

        if push_to_unirate:
            self._push_ota_unirate(ota_object)

    def _push_ota_its(self, ota_object):
        try:
            payload = dict(OTA_name=ota_object.name, treebo_ota_code=ota_object.ota_code,
                           maximojo_ota_code=ota_object.mm_ota_code, unirate_ota_code=ota_object.unirate_ota_code)

            logger.info('Sending ota details to ITS. %s' % payload)
            its_host_url = ServiceRegistryClient.get_its_service_url() + "/its/api/v1/cm_automation"
            response = requests.put(its_host_url + constants.ITS_CREATE_OR_UPDATE_OTA_URI,
                                    json=payload)
            ota_object.rcs_push_complete = True
            ota_object.rcs_push_time = Utils.get_current_time_utc()
            if response.status_code != 200:
                raise Exception('Error while pushing data to ITS: %s' % response.text)
        except:
            ota_object.rcs_push_complete = False
            logger.exception('Error while pushing OTA details to ITS')
            raise CatalogingServiceException(error_codes.RCS_ERROR, context='Error while pushing OTA details to ITS',
                                             force_commit=True)
        finally:
            self.__property_repository.persist(ota_object)

    def _push_ota_unirate(self, ota_object):
        try:
            payload = dict(code=ota_object.unirate_ota_code, name=ota_object.name,
                           promo_push_enabled=ota_object.promo_push_enabled,
                           inventory_push_enabled=ota_object.inventory_push_enabled,
                           rate_push_enabled=ota_object.rate_push_enabled,
                           promo_push_api_present=ota_object.promo_push_api,
                           promo_disable_api_present=ota_object.promo_disable_api)

            logger.info('Sending ota details to ITS. %s' % payload)

            ota_object.unirate_push_complete = True
            ota_object.unirate_push_time = Utils.get_current_time_utc()
            response = requests.put(ServiceRegistryClient.get_unirate_service_url() +
                                    constants.UNIRATE_CREATE_OTA_URI, json=payload)
            if response.status_code != 200:
                raise Exception('Error while pushing data to Unirate: %s' % response.text)
        except:
            ota_object.unirate_push_complete = False
            logger.exception('Error while pushing OTA details to Unirate')
            raise CatalogingServiceException(error_codes.UNIRATE_ERROR,
                                             context='Error while pushing OTA details to Unirate')
        finally:
            self.__property_repository.persist(ota_object)

    @atomic_operation
    def process_its_ota_mappings_push(self, ota_property):
        # notification_success = self.__meta_repository.get_notificaion_object(
        #     constants.REVENUE_OPS_NOTIFICATION_TYPE_SUCCESS)
        logger.info('Process its ota mapping started for ota_property %s', str(ota_property))
        notification_failure = self.__meta_repository.get_notificaion_object(
            constants.REVENUE_OPS_NOTIFICATION_TYPE_FAILURE)
        # success_receivers = []
        failure_receivers = []
        # if notification_success:
        #     success_receivers = notification_success.receivers.split(',')

        if notification_failure:
            failure_receivers = notification_failure.receivers.split(',')

        try:
            ota_property.rcs_callback_complete = True
            ota_property.rcs_callback_time = Utils.get_current_time_utc()
            self.__property_repository.persist(ota_property)
            # self.push_ota_mappings(ota_property, False, True)
            # self._trigger_promo_push(ota_property)
            # self._trigger_rate_push(ota_property)
            # self._trigger_inventory_push(ota_property)
            logger.info('Process its ota mapping ended for ota_property %s', str(ota_property))
        except CatalogingServiceException as e:
            logger.exception('Error while processing its mappings callback')
            subject = 'Error in %s - %s configuration' % (ota_property.ota, ota_property.property.name)
            Utils.send_email(subject, failure_receivers, e.context)
            logger.info('Email sent for failure of process its ota mapping for ota_property %s', str(ota_property))
        # else:
        #     logger.info('Successfully completed all processes for %s' % ota_property)
        #     subject = 'Successfully completed: %s - %s configuration' % (ota_property.ota, ota_property.property.name)
        #     Utils.send_email(subject, success_receivers)

    @atomic_operation
    def push_unirate_properties(self):
        notification_success = self.__meta_repository.get_notificaion_object(
            constants.REVENUE_OPS_NOTIFICATION_TYPE_SUCCESS)
        notification_failure = self.__meta_repository.get_notificaion_object(
            constants.REVENUE_OPS_NOTIFICATION_TYPE_FAILURE)
        success_receivers = []
        failure_receivers = []
        if notification_success:
            success_receivers = notification_success.receivers.split(',')

        if notification_failure:
            failure_receivers = notification_failure.receivers.split(',')

        unpushed_properties = self.__property_repository.get_unpushed_unirate_properties()
        for ota_property in unpushed_properties:
            try:
                if not ota_property.unirate_push_complete:
                    self.push_ota_mappings(ota_property, False, True)
                if not ota_property.promo_push_complete:
                    self._trigger_promo_push(ota_property)
                if not ota_property.rate_push_complete:
                    self._trigger_rate_push(ota_property)
                if not ota_property.inventory_push_complete:
                    self._trigger_inventory_push(ota_property)
            except CatalogingServiceException as e:
                logger.exception('Error while processing rcs mappings callback')
                subject = 'Error in %s - %s configuration' % (ota_property.ota, ota_property.property.name)
                Utils.send_email(subject, failure_receivers, e.context)
            else:
                logger.info('Successfully completed all processes for %s' % ota_property)
                subject = 'Successfully completed: %s - %s configuration' % (
                    ota_property.ota, ota_property.property.name)
                Utils.send_email(subject, success_receivers)

    def check_if_property_is_provided_by_treebo(self, ota_property):
        """
        # ...HARDCODED now!!
        if property provider is null or treebo, then its treated as provided by treebo
        also assuming if property_detail is None, it can be a treebo property
        :param ota_property:
        :return:
        """
        return not ota_property.property.property_detail or not ota_property.property.property_detail.provider or \
               (str(ota_property.property.property_detail.provider.name).lower() == 'treebo')

    @atomic_operation
    def push_ota_mappings(self, ota_property, its_push=True, unirate_push=True):
        if its_push:
            self._push_ota_mappings_its(ota_property)

        if unirate_push:
            self._push_ota_mappings_unirate(ota_property)

    def _push_ota_mappings_its(self, ota_property):
        try:
            property_otas = self.__property_repository.get_ota_properties(ota_property.property.id)
            ota_codes = ','.join([property_ota.ota.ota_code for property_ota in property_otas])

            payload = dict(hotel_name=ota_property.property.name, catalog_id=ota_property.property.id,
                           unirate_id='CM-' + ota_property.property.id, treebo_ota_code_list=ota_codes)

            logger.info('Sending Ota Mapping to ITS: %s', payload)
            its_host_url = ServiceRegistryClient.get_its_service_url() + "/its/api/v1/cm_automation"
            response = requests.put(its_host_url + constants.ITS_CREATE_OR_UPDATE_OTA_MAPPINGS,
                                    json=payload)
            ota_property.rcs_push_time = Utils.get_current_time_utc()
            ota_property.rcs_push_complete = True
            if response.status_code != 200:
                logger.error('Error in ITS Push: %s', response.text)
                raise Exception('Error while pushing data to ITS: %s' % response.text)
        except Exception as e:
            logger.exception('Error while pushing mappings to ITS', e)
            ota_property.rcs_push_complete = False
            slack_alert(message="ITS push error, %s" % repr(e),
                        data=str.format('cs_id: %s - ota_id: %s' % (ota_property.property_id, ota_property.ota_id)))
            raise CatalogingServiceException(error_codes.RCS_ERROR, context='Error while pushing mappings to ITS',
                                             force_commit=True)
        finally:
            self.__property_repository.persist(ota_property)

    def _push_ota_mappings_unirate(self, ota_property):
        try:
            room_type_map, rate_plan_map = self._get_unirate_map()

            room_type_configs = self.__property_repository.get_property_room_type_configurations(
                ota_property.property.id)
            rate_plan_configs = self.__property_repository.get_property_rate_plan_configurations(
                ota_property.property.id)

            room_types = [
                dict(
                    code=room_type_map[room_type_config.room_type.type],
                    name=room_type_config.room_type.type,
                    bb_room_type_code=room_type_config.room_type.bb_room_type_code,
                    crs_room_type_code=room_type_config.room_type.crs_room_type_code
                ) for room_type_config in room_type_configs
            ]
            rate_plans = [
                dict(
                    code=rate_plan_map[rate_plan_config.rate_plan.plan],
                    name=rate_plan_config.rate_plan.plan,
                    bb_rate_plan_code=rate_plan_config.rate_plan.bb_rate_plan_code,
                    crs_rate_plan_code=rate_plan_config.rate_plan.crs_rate_plan_code
                ) for rate_plan_config in rate_plan_configs
            ]

            hotel_mapping = ota_property.hotel_mappings[0]
            hotel_mapping = dict(
                ota_hotel_code=hotel_mapping.ota_hotel_code,
                username=hotel_mapping.username,
                access_token=hotel_mapping.access_token
            )
            room_mappings = [
                dict(
                    room_code=room_type_map[mapping.room_type.type],
                    ota_room_code=mapping.ota_room_code,
                    ota_room_name=mapping.ota_room_name
                ) for mapping in ota_property.room_mappings
            ]
            rate_plan_mappings = [
                dict(room_code=room_type_map[mapping.room_type.type],
                     rate_plan_code=rate_plan_map[mapping.rate_plan.plan],
                     ota_rate_plan_code=mapping.ota_rate_plan_code,
                     ota_rate_plan_name=mapping.ota_rate_plan_name,
                     rate_push_enabled=mapping.rate_push_enabled
                     ) for mapping in ota_property.rate_plan_mappings
            ]

            payload = dict(
                property_id='CM-' + ota_property.property.id,
                property_name=ota_property.property.name,
                hx_id=ota_property.property.hx_id,
                ota_code=ota_property.ota.unirate_ota_code,
                room_types=room_types,
                rate_plans=rate_plans,
                mappings=dict(
                    hotel_mapping=hotel_mapping,
                    room_mappings=room_mappings,
                    rate_plan_mappings=rate_plan_mappings
                )
            )

            logger.info('sending unirate mappings: %s', payload)

            url = (ServiceRegistryClient.get_unirate_service_url() + constants.UNIRATE_CREATE_MAPPINGS_URI) % \
                  current_app.config[constants.CONFIG_TREEBO_TENANT_CODE]

            response = requests.put(url, json=payload)

            ota_property.unirate_push_complete = True
            ota_property.unirate_push_time = Utils.get_current_time_utc()

            if response.status_code != 200:
                logger.error('Error in unirate push: %s', response.text)
                raise Exception('Error while pushing data to Unirate: %s' % response.text)
        except Exception as e:
            logger.exception('Error while pushing mappings to Unirate', e)
            ota_property.unirate_push_complete = False
            # slack_alert(message="Unirate push error, %s" % repr(e),
            #             data=str.format('cs_id: %s - ota_id: %s' % (ota_property.property_id, ota_property.ota_id)))
            raise CatalogingServiceException(error_codes.UNIRATE_ERROR, context='Error while configuring unirate',
                                             force_commit=True)
        finally:
            self.__property_repository.persist(ota_property)

    def _trigger_promo_push(self, ota_property):
        try:
            # query_params = dict(hotel_ids=ota_property.property.hx_id, batch_size=1, sleep_time_between_batches=1)
            # logger.info('Triggering promo push: %s', query_params)
            #
            # response = requests.get(current_app.config[constants.CONFIG_PROMO_HOST] + constants.PROMO_TRIGGER_URI,
            #                         params=query_params)
            ota_property.promo_push_complete = True
            # if response.status_code != 200:
            #     logger.error('Error in PU Push: %s', response.text)
            #     raise Exception('Error while triggering promo push: %s' % response.text)
        except:
            ota_property.promo_push_complete = False
            raise CatalogingServiceException(error_codes.PROMO_UTILITY_ERROR,
                                             context='Error while triggering promo push', force_commit=True)
        finally:
            self.__property_repository.persist(ota_property)

    def _trigger_rate_push(self, ota_property):
        try:
            start_date = Utils.get_current_time_utc()
            end_date = start_date + timedelta(days=365)
            payload = {
                'property_codes': [ota_property.property.id],
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat()
            }
            logger.info('Triggering rate push: %s', payload)

            cm_sync_rates_url = '{rr_host}/external/rate_push/'.format(rr_host=ServiceRegistryClient.get_rate_manager_service_url())

            response = requests.post(url=cm_sync_rates_url, json=payload, headers={'Content-Type': "application/json"})
            ota_property.rate_push_complete = True
            if response.status_code != 201:
                logger.error('Error in rate push: %s', response.text)
                raise Exception('Error while triggering rate push. %s' % response.text)
        except:
            logger.exception('Error while triggering rate push')
            ota_property.rate_push_complete = False
            raise CatalogingServiceException(error_codes.PRICING_ERROR,
                                             context='Error while triggering rate push', force_commit=True)
        finally:
            self.__property_repository.persist(ota_property)

    def _trigger_inventory_push(self, ota_property):
        try:
            start_date = Utils.get_current_date_ist()
            end_date = start_date + timedelta(days=365)
            start_date = Utils.format_date_time(start_date)
            end_date = Utils.format_date_time(end_date)
            query_params = dict(hotel_ids=ota_property.property.id, start_date=start_date, end_date=end_date,
                                channel=ota_property.ota.name)
            logger.info('Triggering inventory push: %s', query_params)
            response = requests.get(ServiceRegistryClient.get_its_service_url() + "/its/v1/inventory/bulk-sync-cm/",
                                    params=query_params)
            ota_property.inventory_push_complete = True
            if response.status_code != 200:
                logger.error('Error in ITS push: %s', response.text)
                raise Exception('Error while triggering inventory push. %s' % response.text)
        except:
            logger.exception('Error while triggering inventory push')
            ota_property.inventory_push_complete = False
            raise CatalogingServiceException(error_codes.ITS_ERROR,
                                             context='Error while triggering inventory push', force_commit=True)
        finally:
            self.__property_repository.persist(ota_property)

    def alert_incomplete_mappings(self):
        since = Utils.get_current_time_utc() - timedelta(minutes=10)
        ota_properties = self.__property_repository.get_incomplete_ota_properties(since)
        notification = self.__meta_repository.get_notificaion_object(constants.REVENUE_OPS_NOTIFICATION_TYPE_FAILURE)
        receivers = current_app.config[constants.CONFIG_ERROR_MAIL_RECIPIENTS]
        if notification:
            receivers = notification.receivers.split(',')
        for ota_property in ota_properties:
            subject = 'Did not receive callback from RCS for config: %s-%s' % (
                ota_property.ota, ota_property.property.name)
            body = 'Please check the problem'
            self.__email_client.send_email(subject, receivers, body, '')

    def alert_incomplete_ota(self):
        since = Utils.get_current_time_utc() - timedelta(minutes=10)
        otas = self.__property_repository.get_incomplete_otas(since)
        notification = self.__meta_repository.get_notificaion_object(constants.REVENUE_OPS_NOTIFICATION_TYPE_FAILURE)
        receivers = current_app.config[constants.CONFIG_ERROR_MAIL_RECIPIENTS]
        if notification:
            receivers = notification.receivers.split(',')
        for ota in otas:
            subject = 'Did not receive callback from RCS for OTA: %s' % ota
            body = 'Please check the problem'
            self.__email_client.send_email(subject, receivers, body, '')
