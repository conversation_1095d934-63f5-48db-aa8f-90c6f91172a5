import logging
from flask.helpers import flash

from cataloging_service.api.request_objects import RoomRequest
from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.messaging.messaging_wrappers import Room<PERSON>essaging<PERSON>rapper, \
    RoomTypeConfigMessagingWrapper, RoomAmenitiesWrapper, RoomTypeWrapper, RoomRackRateMessagingWrapper
from cataloging_service.models import RoomTypeConfiguration, Room, RoomAmenity, AmenityTV, AmenityAC, AmenityIntercom, \
    AmenityTwinBed, AmenityHotWater, AmenityHeater, AmenityStove
from cataloging_service.utils import Utils, group_list

logger = logging.getLogger(__name__)


class RoomService:
    def __init__(self, property_repository, messaging_service, sku_repository, room_rack_rate_repository):
        self.__property_repository = property_repository
        self.__messaging_service = messaging_service
        self.__sku_repository = sku_repository
        self.__room_rack_rate_repository = room_rack_rate_repository

    @atomic_operation
    def add_property_room_type_config(self, property_object, add_room_config_request):
        return self._insert_room_type_config(property_object, add_room_config_request)

    @atomic_operation
    def update_property_room_type_config(self, room_type_config_object, update_room_config_request):
        if room_type_config_object.room_type_id != update_room_config_request.room_type_id:
            logger.error('Cannot change the room type of config %s', room_type_config_object)
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_CANNOT_CHANGE)
        return self._update_room_type_config(room_type_config_object, update_room_config_request)

    @atomic_operation
    def add_property_room(self, property_object, add_room_request):
        return self._insert_property_room(property_object, add_room_request)

    def add_room_amenity(self, add_room_amenity_request):
        return self._insert_room_amenity(add_room_amenity_request)

    def add_ac(self, add_ac_request):
        return self._insert_ac(add_ac_request)

    def add_intercom(self, add_intercom_request):
        return self._insert_intercom(add_intercom_request)

    def add_heater(self, add_heater_request):
        return self._insert_heater(add_heater_request)

    def add_hot_water(self, add_hot_water_request):
        return self._insert_hot_water(add_hot_water_request)

    def add_tv(self, add_tv_request):
        return self._insert_tv(add_tv_request)

    def add_stove(self, add_stove_request):
        return self._insert_stove(add_stove_request)

    def add_twin_bed(self, add_twin_bed_request):
        return self._insert_twin_bed(add_twin_bed_request)

    @atomic_operation
    def update_property_room(self, room_object, update_room_request):
        return self._update_property_room(room_object, update_room_request)

    def get_room_type_configurations(self, property_id, room_count=True):
        room_configs = self.__property_repository.get_room_type_configs([property_id])

        associated_modular_skus_for_all_room_types = self.__sku_repository.get_modular_skus_for_category('stay')
        associated_modular_skus_for_all_room_types = {sku.name.lower(): sku for sku in
                                                      associated_modular_skus_for_all_room_types}
        if room_count:
            room_count_map = self.__property_repository.get_property_room_count_map(property_id)
        else:
            room_count_map = dict()

        new_room_configs = []

        for room_config in room_configs:
            setattr(room_config, 'room_count', room_count_map.get(room_config.room_type_id, 0))
            associated_modular_sku = associated_modular_skus_for_all_room_types.get(
                room_config.room_type.type.lower())
            room_config.room_type.associated_modular_sku_code = associated_modular_sku.code if associated_modular_sku\
                else None
            new_room_configs.append(room_config)
        return new_room_configs

    def get_property_rooms(self, property_object):
        associated_modular_skus_for_all_room_types = self.__sku_repository.get_modular_skus_for_category('stay')
        associated_modular_skus_for_all_room_types = {sku.name.lower(): sku for sku in
                                                      associated_modular_skus_for_all_room_types}
        rooms = self.__property_repository.get_property_rooms(property_object.id)
        for room in rooms:
            associated_modular_sku = associated_modular_skus_for_all_room_types.get(
                room.room_type.type.lower())
            room.room_type.associated_modular_sku_code = associated_modular_sku.code if associated_modular_sku else None
        return rooms

    def get_property_room_type_config(self, property_id, id):
        room_config = self.__property_repository.get_property_room_type_configuration(property_id, id)

        if not room_config:
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_NOT_FOUND)

        return room_config

    def get_property_room(self, property_id, id):
        room = self.__property_repository.get_property_room(property_id, id)

        if not room:
            raise CatalogingServiceException(error_codes.ROOM_NOT_FOUND)

        return room

    def get_room_type(self, room_type_id):
        room_type = self.__property_repository.get_room_type(room_type_id)
        if not room_type:
            raise CatalogingServiceException(error_codes.ROOM_TYPE_NOT_FOUND)
        return room_type

    def _check_duplicate_room_type_configuration(self, property_id, room_type_id, mm_id, id=None):
        room_type_config = self.__property_repository.get_property_room_type_configuration_by_room_type(property_id,
                                                                                                        room_type_id)

        if room_type_config and id != room_type_config.id:
            raise CatalogingServiceException(error_codes.DUPLICATE_ROOM_TYPE_CONFIGURATION)

        room_type_config = self.__property_repository.get_property_room_type_configuration_by_mm_id(mm_id)

        if room_type_config and id != room_type_config.id:
            raise CatalogingServiceException(error_codes.DUPLICATE_ROOM_TYPE_CONFIGURATION)

    def _check_duplicate_room(self, property_id, room_number, id=None):

        room = self.__property_repository.get_property_room_by_number(property_id, room_number)

        if room and id != room.id:
            raise CatalogingServiceException(error_codes.DUPLICATE_ROOM)

    def _insert_room_type_config(self, property_object, add_room_config_request):
        self._check_duplicate_room_type_configuration(property_object.id, add_room_config_request.room_type_id,
                                                      add_room_config_request.mm_id)
        self.get_room_type(add_room_config_request.room_type_id)

        room_config_object = RoomTypeConfiguration()
        room_config_object.property_id = property_object.id
        room_config_object.room_type_id = add_room_config_request.room_type_id
        room_config_object.extra_bed = add_room_config_request.extra_bed_form
        room_config_object.min_occupancy = add_room_config_request.minimum_occupancy
        room_config_object.max_occupancy = add_room_config_request.maximum_occupancy
        room_config_object.adults = add_room_config_request.adults
        room_config_object.mm_id = add_room_config_request.mm_id
        room_config_object.children = add_room_config_request.children
        room_config_object.max_total = add_room_config_request.max_total
        self.validate_room_type_config(room_config_object.adults, room_config_object.children,
                                       room_config_object.max_total, room_config_object.min_occupancy)

        return self.__property_repository.persist(room_config_object)

    def validate_room_type_config(self, adults, children, max_total, min_occupancy):
        self.__validate_positive(adults)
        self.__validate_positive(children)
        self.__validate_positive(max_total)
        self.__validate_positive(min_occupancy)
        # empty values to be allowed for min_occupancy just that it should not be 0 or greater than max(adult, children)
        if min_occupancy == 0:
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_INVALID)
        if min_occupancy and (min_occupancy > max((adults or 0), (children or 0))):
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_INVALID)
        # max_total cannot be empty or 0 or max_total > than adult + children
        if not max_total or (max_total > ((adults or 0) + (children or 0))):
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_INVALID)

    def validate_not_null(self, value):
        if value is None:
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_FIELD_NULL)

    def __validate_positive(self, value):
        if not value:
            return

        if value < 0:
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_INVALID)

    def _insert_ac(self, add_ac_request):
        ac_object = AmenityAC()
        ac_object.ac_type = add_ac_request.ac_type
        return self.__property_repository.persist(ac_object)

    def _insert_intercom(self, add_intercom_request):
        intercom_object = AmenityIntercom()
        intercom_object.all_rooms_connected = add_intercom_request.all_rooms_connected
        return self.__property_repository.persist(intercom_object)

    def _insert_heater(self, add_heater_request):
        heater_object = AmenityHeater()
        heater_object.availability = add_heater_request.availability
        return self.__property_repository.persist(heater_object)

    def _insert_hot_water(self, add_hot_water_request):
        hot_water_object = AmenityHotWater()
        hot_water_object.central_geyser = add_hot_water_request.central_geyser
        hot_water_object.room_geyser = add_hot_water_request.room_geyser
        hot_water_object.from_time = add_hot_water_request.from_time
        hot_water_object.to_time = add_hot_water_request.to_time
        return self.__property_repository.persist(hot_water_object)

    def _insert_stove(self, add_stove_request):
        stove_object = AmenityStove()
        stove_object.stove_type = add_stove_request.stove_type
        stove_object.availability = add_stove_request.availability
        return self.__property_repository.persist(stove_object)

    def _insert_tv(self, add_tv_request):
        tv_object = AmenityTV()
        tv_object.vendor = add_tv_request.vendor
        tv_object.tv_type = add_tv_request.tv_type
        tv_object.connection_type = add_tv_request.connection_type
        tv_object.size = add_tv_request.size
        return self.__property_repository.persist(tv_object)

    def _insert_twin_bed(self, add_twin_bed_request):
        twin_bed_object = AmenityTwinBed()
        twin_bed_object.joinable = add_twin_bed_request.joinable
        return self.__property_repository.persist(twin_bed_object)

    def _insert_property_room(self, property_object, add_room_request):
        room_type_object = self.get_room_type(add_room_request.room_type_id)
        self._check_duplicate_room(property_object.id, add_room_request.room_number)

        room_object = Room()
        room_object.property_id = property_object.id
        room_object.room_number = add_room_request.room_number
        room_object.room_type = room_type_object
        room_object.building_number = add_room_request.building_number
        room_object.floor_number = add_room_request.floor_number
        room_object.size = add_room_request.size
        room_object.is_active = add_room_request.is_active
        room_object.room_size = add_room_request.room_size

        room_config = self.__property_repository.get_property_room_type_configuration_by_room_type(property_object.id,
                                                                                                   room_type_object.id)

        if not room_config:
            logger.error('Room type %s not valid for hotel %s', room_type_object, property_object)
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_NOT_FOUND)

        room_object.room_type_config = room_config

        room_object = self.__property_repository.persist(room_object)

        room_config.modified_at = Utils.get_current_time_utc()
        min_size_map = self.__property_repository.get_property_min_room_size_map(property_object.id)
        room_config.min_room_size = min_size_map.get(room_type_object.id, None)

        self.__property_repository.persist(room_config)

        return room_object

    def _insert_room_amenity(self, add_room_amenity_request):

        room_amenity_object = RoomAmenity()
        room_amenity_object.room_id = add_room_amenity_request.room_id
        room_amenity_object.ac_id = add_room_amenity_request.ac_id
        room_amenity_object.intercom_id = add_room_amenity_request.intercom_id
        room_amenity_object.heater_id = add_room_amenity_request.heater_id
        room_amenity_object.hot_water_id = add_room_amenity_request.hot_water_id
        room_amenity_object.tv_id = add_room_amenity_request.tv_id
        room_amenity_object.twin_bed_id = add_room_amenity_request.twin_bed_id
        room_amenity_object.mini_fridge = add_room_amenity_request.mini_fridge
        room_amenity_object.balcony = add_room_amenity_request.balcony
        room_amenity_object.kitchenette = add_room_amenity_request.kitchenette
        room_amenity_object.kitchenette_utensils = add_room_amenity_request.kitchenette_utensils
        room_amenity_object.king_sized_beds = add_room_amenity_request.king_sized_beds
        room_amenity_object.queen_sized_beds = add_room_amenity_request.queen_sized_beds
        room_amenity_object.single_beds = add_room_amenity_request.single_beds
        room_amenity_object.wardrobe = add_room_amenity_request.wardrobe
        room_amenity_object.locker_available = add_room_amenity_request.locker_available
        room_amenity_object.microwave = add_room_amenity_request.microwave
        room_amenity_object.luggage_shelf = add_room_amenity_request.luggage_shelf
        room_amenity_object.study_table_chair = add_room_amenity_request.study_table_chair
        room_amenity_object.sofa_chair = add_room_amenity_request.sofa_chair
        room_amenity_object.coffee_table = add_room_amenity_request.coffee_table
        room_amenity_object.other_furniture = add_room_amenity_request.other_furniture
        room_amenity_object.smoking_room = add_room_amenity_request.smoking_room
        room_amenity_object.bath_tub = add_room_amenity_request.bath_tub
        room_amenity_object.shower_curtain = add_room_amenity_request.shower_curtain
        room_amenity_object.smoke_alarm = add_room_amenity_request.smoke_alarm
        room_amenity_object.shower_cabinets = add_room_amenity_request.shower_cabinets
        room_amenity_object.living_room = add_room_amenity_request.living_room
        room_amenity_object.dining_table = add_room_amenity_request.dining_table
        room_amenity_object.windows = add_room_amenity_request.windows
        room_amenity_object.treebo_toiletries = add_room_amenity_request.treebo_toiletries
        room_amenity_object.fan_type = add_room_amenity_request.fan_type
        room_amenity_object.lock_type = add_room_amenity_request.lock_type
        room_amenity_object.bucket_mug = add_room_amenity_request.bucket_mug
        room_amenity_object.mosquito_repellent = add_room_amenity_request.mosquito_repellent
        return self.__property_repository.persist(room_amenity_object)

    def _update_property_room(self, room_object, update_room_request):
        self.get_room_type(update_room_request.room_type_id)
        self._check_duplicate_room(room_object.property_id, update_room_request.room_number,
                                   room_object.id)

        room_config = self.__property_repository.get_property_room_type_configuration_by_room_type(
            room_object.property_id, update_room_request.room_type_id)

        if not room_config:
            logger.error('Room type %s not valid for hotel %s', room_object.room_type_id, room_object.property_id)
            raise CatalogingServiceException(error_codes.ROOM_TYPE_CONFIG_NOT_FOUND)

        room_object.room_type_id = update_room_request.room_type_id
        room_object.room_number = update_room_request.room_number
        room_object.building_number = update_room_request.building_number
        room_object.floor_number = update_room_request.floor_number
        room_object.is_active = update_room_request.is_active
        room_object.size = update_room_request.size
        room_object.room_type_config = room_config

        room_object = self.__property_repository.persist(room_object)
        min_size_map = self.__property_repository.get_property_min_room_size_map(room_object.property_id)

        room_config.modified_at = Utils.get_current_time_utc()
        old_room_config = room_object.room_type_config
        room_config.min_room_size = min_size_map.get(room_object.room_type_id, None)
        self.__property_repository.persist(room_config)

        if old_room_config:
            old_room_config.modified_at = Utils.get_current_time_utc()
            old_room_config.min_room_size = min_size_map.get(old_room_config.room_type_id, None)
            self.__property_repository.persist(room_config)

        return room_object

    def _update_room_type_config(self, room_type_config_object, update_room_config_request):
        self._check_duplicate_room_type_configuration(room_type_config_object.property_id,
                                                      update_room_config_request.room_type_id,
                                                      update_room_config_request.mm_id, room_type_config_object.id)
        self.get_room_type(update_room_config_request.room_type_id)

        room_type_config_object.room_type_id = update_room_config_request.room_type_id
        room_type_config_object.extra_bed = update_room_config_request.extra_bed_form
        room_type_config_object.min_occupancy = update_room_config_request.minimum_occupancy
        room_type_config_object.max_occupancy = update_room_config_request.maximum_occupancy
        room_type_config_object.adults = update_room_config_request.adults
        room_type_config_object.mm_id = update_room_config_request.mm_id
        room_type_config_object.max_total = update_room_config_request.max_total
        room_type_config_object.children = update_room_config_request.children

        self.validate_room_type_config(room_type_config_object.adults, room_type_config_object.children,
                                       room_type_config_object.max_total, room_type_config_object.min_occupancy)

        return self.__property_repository.persist(room_type_config_object)

    @atomic_operation
    def clone_room(self, room_id):
        room_details = self.__property_repository.get_room(room_id)
        room_amenity = room_details.amenity
        add_room_request = RoomRequest({
            'room_number': room_details.room_number + ' Clone',
            'room_type_id': room_details.room_type_id,
            'building_number': room_details.building_number,
            'floor_number': room_details.floor_number,
            'size': room_details.size,
            'is_active': room_details.is_active,
            'room_size': room_details.room_size,
        })
        new_room = self.add_property_room(room_details.property, add_room_request)
        flash("Room cloned successfully. New Room number: %s" % new_room.room_number)
        if room_amenity:
            room_amenity.room_id = new_room.id

            if room_amenity.ac_id:
                ac_object = self.add_ac(room_amenity.ac)
                room_amenity.ac_id = ac_object.id

            if room_amenity.intercom_id:
                intercom_object = self.add_intercom(room_amenity.intercom)
                room_amenity.intercom_id = intercom_object.id

            if room_amenity.heater_id:
                heater_object = self.add_heater(room_amenity.heater)
                room_amenity.heater_id = heater_object.id

            if room_amenity.hot_water_id:
                hot_water_object = self.add_hot_water(room_amenity.hot_water)
                room_amenity.hot_water_id = hot_water_object.id

            if room_amenity.tv_id:
                tv_object = self.add_tv(room_amenity.tv)
                room_amenity.tv_id = tv_object.id

            if room_amenity.twin_bed_id:
                twin_bed_object = self.add_twin_bed(room_amenity.twin_bed)
                room_amenity.twin_bed_id = twin_bed_object.id

            if room_amenity.stove_id:
                stove_object = self.add_stove(room_amenity.stove)
                room_amenity.stove_id = stove_object.id

            self.add_room_amenity(room_amenity)
            flash("Room Amenities cloned successfully.")

    def get_all_room_types_by_codes(self, codes=None):
        associated_modular_skus_for_all_room_types = self.__sku_repository.get_modular_skus_for_category('stay')
        associated_modular_skus_for_all_room_types = {sku.name.lower(): sku for sku in
                                                      associated_modular_skus_for_all_room_types}
        room_types = self.__property_repository.get_all_room_types_by_codes(codes)
        for room_type in room_types:
            associated_modular_sku = associated_modular_skus_for_all_room_types.get(
                room_type.type.lower())
            # NOTE: Modifying the model like this marks it as expired in SQLAlchemy
            # Problem with this is, if we've have local in-memory cache of room_type in a dict, they are detached
            # from session. And then, when SQLAlchemy tries to refresh the attribute (since model object is expired),
            # it fails with DetachedInstanceError
            # Ideally, we shouldn't add new attributes to existing model object, if they are not to be saved in DB
            room_type.associated_modular_sku_code = associated_modular_sku.code if associated_modular_sku else None
        return room_types

    def publish_rooms(self, ids):
        rooms = self.__property_repository.get_all_rooms(ids)
        messages = [RoomMessagingWrapper(room, False, False).get_json() for room in rooms]
        for message in messages:
            self.__messaging_service.publish_room_message(message)

    def publish_room_rack_rates(self, room_rack_rate_ids):
        room_rack_rates = self.__room_rack_rate_repository.get_room_rack_rates_by_ids(room_rack_rate_ids)
        messages = [RoomRackRateMessagingWrapper(room_rack_rate, False, False).get_json() for room_rack_rate in room_rack_rates]
        for message in messages:
            self.__messaging_service.publish_room_rack_rate_message(message)

    def publish_room_configs(self, ids):
        rooms = self.__property_repository.get_all_room_configs(ids)
        messages = [RoomTypeConfigMessagingWrapper(room, False, False).get_json() for room in rooms]
        for message in messages:
            self.__messaging_service.publish_room_type_config_message(message)

    def publish_room_amenities(self, ids):
        amenities = self.__property_repository.get_all_room_amenities(ids)
        messages = [RoomAmenitiesWrapper(amenity, False, False).get_json() for amenity in amenities]
        for message in messages:
            self.__messaging_service.publish_room_amenity_message(message)

    def publish_room_types(self, ids):
        room_types = self.__property_repository.get_all_room_types(ids)
        messages = [RoomTypeWrapper(room_type, False, False).get_json() for room_type in room_types]
        for message in messages:
            self.__messaging_service.publish_room_message(message)

    def get_all_room_types(self):
        room_types = self.__property_repository.get_all_room_types()
        return room_types
