from flask import current_app as app

from cataloging_service.constants import constants
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.models import GoogleDriveBaseFolder


class FileUploadService:
    def __init__(self, google_drive_client, google_drive_repository):
        self.__google_drive_client = google_drive_client
        self.__google_drive_repository = google_drive_repository

    @atomic_operation
    def save_property_file(self, file_model, file_data):
        if not file_model.property:
            uploaded_file = self.__upload_common_file(file_model, file_data)
        else:
            uploaded_file = self.__upload_property_file(file_model, file_data)

        file_model.file_id = uploaded_file.file_id

    def delete_file(self, file_id):
        self.__google_drive_client.delete_file(file_id)

    def __upload_common_file(self, file_model, file_data):
        common_folder = self.__google_drive_repository.get_folder_by_name(constants.COMMON_FOLDER_NAME)

        if not common_folder:
            common_folder = self.__create_common_folder()

        return self.__google_drive_client.upload_file(file_data.stream, file_data.mimetype, common_folder.folder_id,
                                                      file_model.file_name)

    def __create_common_folder(self):
        base_folder = self.__google_drive_client.create_folder(app.config[constants.CONFIG_GOOGLE_DRIVE_ROOT_FOLDER_ID],
                                                               constants.COMMON_FOLDER_NAME)
        folder = GoogleDriveBaseFolder()
        folder.folder_name = base_folder.name
        folder.folder_id = base_folder.file_id
        folder.folder_link = base_folder.link
        return self.__google_drive_repository.persist(folder)

    def __upload_property_file(self, file_model, file_data):
        property_folder = self.__google_drive_repository.get_property_folder(file_model.property.id)
        if not property_folder:
            property_folder = self.__create_property_folder(file_model, file_data)

        if constants.MIME_TYPE_FOLDER_MAPPING.get(file_data.mimetype, None) == constants.IMAGE_FOLDER_NAME:
            return self.__google_drive_client.upload_file(file_data.stream, file_data.mimetype,
                                                          property_folder.property_images_file_id,
                                                          file_model.file_name)
        return self.__google_drive_client.upload_file(file_data.stream, file_data.mimetype,
                                                      property_folder.property_documents_file_id,
                                                      file_model.file_name)

    def __create_property_folder(self, file_model, file_data):
        base_folder = self.__google_drive_client.create_folder(app.config[constants.CONFIG_GOOGLE_DRIVE_ROOT_FOLDER_ID],
                                                               'property_%s' % file_model.property.id)

        document_folder = self.__google_drive_client.create_folder(base_folder.file_id, constants.DOCUMENT_FOLDER_NAME)
        image_folder = self.__google_drive_client.create_folder(base_folder.file_id, constants.IMAGE_FOLDER_NAME)

        folder = GoogleDriveBaseFolder()
        folder.property_id = file_model.property.id
        folder.folder_name = base_folder.name
        folder.folder_id = base_folder.file_id
        folder.folder_link = base_folder.link
        folder.property_documents_file_id = document_folder.file_id
        folder.property_documents_link = document_folder.link
        folder.property_images_file_id = image_folder.file_id
        folder.property_images_link = image_folder.link

        return self.__google_drive_repository.persist(folder)
