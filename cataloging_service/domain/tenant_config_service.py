from collections import defaultdict

from cataloging_service.api.validators import TenantConfigRequestValidator
from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import PropertyRepository, SellerRepository
from cataloging_service.infrastructure.repositories.available_config_repository import AvailableConfigRepository
from cataloging_service.infrastructure.repositories.currency_conversion_rate_repository import \
    CurrencyConversionRateRepository
from cataloging_service.infrastructure.repositories.tenant_config_repository import TenantConfigRepository


class TenantConfigService(object):
    def __init__(self):
        self.tenant_config_repository = TenantConfigRepository()
        self.currency_conversion_rate_repository = CurrencyConversionRateRepository()
        self.property_repository = PropertyRepository()
        self.available_config_repository = AvailableConfigRepository()
        self.seller_repository = SellerRepository()

    def get_tenant_configs(self, property_id=None, seller_id=None):
        if property_id:
            property = self.property_repository.get_property(property_id)
            if not property:
                raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                                 context='property id: %s' % property_id,
                                                 send_error_mail=False)
        if seller_id:
            seller = self.seller_repository.get_seller_by_id(seller_id)
            if not seller:
                raise CatalogingServiceException(error_codes.SELLER_NOT_FOUND,
                                                 context='seller_id id: %s' % seller_id,
                                                 send_error_mail=False)
            property_id = seller[0].property_id

        tenant_configs = self.tenant_config_repository.load(property_id=property_id, seller_id=seller_id)
        return tenant_configs

    def get_tenant_configs_v2(self, property_id=None, seller_id=None, config_name=None):
        if property_id:
            property = self.property_repository.get_property(property_id)
            if not property:
                raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                                 context='property id: %s' % property_id,
                                                 send_error_mail=False)
        if seller_id:
            seller = self.seller_repository.get_seller_by_id(seller_id)
            if not seller:
                raise CatalogingServiceException(error_codes.SELLER_NOT_FOUND,
                                                 context='seller_id id: %s' % seller_id,
                                                 send_error_mail=False)
            if property_id and seller[0].property_id != property_id:
                raise CatalogingServiceException(error_codes.PROPERTY_AND_SELLER_PROPERTY_MISMATCH,
                                                 context='seller_id id: %s property_id: %s' % (seller_id, property_id),
                                                 send_error_mail=False)
            property_id = property_id or seller[0].property_id

        tenant_configs = self.tenant_config_repository.load_v2(
            property_id=property_id, seller_id=seller_id, config_name=config_name
        )
        return tenant_configs

    @atomic_operation
    def create_or_update_tenant_config(self, parsed_request: TenantConfigRequestValidator, property_id=None):
        if property_id:
            property = self.property_repository.get_property(property_id)
            if not property:
                raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                                 context='property id: %s' % property_id,
                                                 send_error_mail=False)
        tenant_config = self.tenant_config_repository.create_or_update_tenant_config(parsed_request, property_id)
        return tenant_config

    @atomic_operation
    def create_tenant_config(self, parsed_request: TenantConfigRequestValidator, property_id=None):
        if property_id:
            property = self.property_repository.get_property(property_id)
            if not property:
                raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                                 context='property id: %s' % property_id,
                                                 send_error_mail=False)
        tenant_config = self.tenant_config_repository.create_tenant_config(parsed_request, property_id)
        return tenant_config

    @atomic_operation
    def update_tenant_config(self, parsed_request: TenantConfigRequestValidator, property_id=None):
        if property_id:
            property = self.property_repository.get_property(property_id)
            if not property:
                raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                                 context='property id: %s' % property_id,
                                                 send_error_mail=False)
        tenant_config = self.tenant_config_repository.update_tenant_config(parsed_request, property_id)
        return tenant_config
