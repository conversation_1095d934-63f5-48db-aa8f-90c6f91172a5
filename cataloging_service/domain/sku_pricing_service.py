import logging

from treebo_commons.money import Money
from treebo_commons.money.constants import CurrencyType

logger = logging.getLogger(__name__)


class SkuPricingService:
    def __init__(self, sku_pricing_repository, seller_repository):
        self.sku_pricing_repository = sku_pricing_repository
        self.seller_repository = seller_repository

    def get_pretax_prices(self, seller_id, sku_codes):
        seller_skus = self.sku_pricing_repository.get_all_seller_skus(seller_id=seller_id, sku_codes=sku_codes)
        base_currency = self.seller_repository.get_seller_base_currency(seller_id=seller_id)
        base_currency = CurrencyType.INR if not base_currency else CurrencyType(base_currency)
        sku_prices = []
        for sku in seller_skus:
            sku_price_with_code = {'pre_tax_price': Money(sku.pretax_price, base_currency), 'sku_code': sku.sku_id}
            sku_prices.append(sku_price_with_code)
        data = dict(seller_id=seller_id, sku_prices=sku_prices)
        return data

