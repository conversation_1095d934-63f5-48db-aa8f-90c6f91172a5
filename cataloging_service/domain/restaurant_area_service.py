from treebo_commons.utils import dateutils

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.messaging.messaging_wrappers import RestaurantTableMessagingWrapper
from cataloging_service.models import RestaurantArea, RestaurantTable, TableSeat


class RestaurantAreaService:
    def __init__(self, restaurant_area_repository, restaurant_table_repository, seller_service, messaging_service):
        self.__restaurant_area_repository = restaurant_area_repository
        self.__restaurant_table_repository = restaurant_table_repository
        self._seller_service = seller_service
        self._messaging_service = messaging_service

    def _get_table_name(self, area_name, table_number):
        return ''.join([s[0] for s in area_name.title().split()]) + '-' + str(table_number)

    @atomic_operation
    def create_area(self, create_area_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=create_area_request.seller_id)
        area = RestaurantArea(name=create_area_request.name, seller_id=create_area_request.seller_id,
                              tables=self._create_tables(create_area_request.tables,
                                                         seller_id=create_area_request.seller_id,
                                                         area_name=create_area_request.name))
        saved_area = self.__restaurant_table_repository.persist(area)
        self.create_and_publish_restaurant_table_events(
            saved_area.tables, create_area_request.seller_id, created=True, deleted=False)
        return saved_area

    @atomic_operation
    def add_tables_to_area(self, seller_id, area_id, create_table_requests):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        area = self.__restaurant_area_repository.get_restaurant_area(seller_id=seller_id, area_id=area_id)
        if not area:
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context={
                "name": ["Area ID is not valid"]})
        last_table_number = 0
        if area.tables:
            last_table_number = max(int(table.table_number) for table in area.tables)
        tables = self._create_tables(create_table_requests=create_table_requests.tables, seller_id=seller_id,
                                     area_name=area.name, table_number=last_table_number)
        area.tables.extend(tables)
        updated_area = self.__restaurant_table_repository._update(area)
        self.create_and_publish_restaurant_table_events(updated_area.tables, seller_id, created=False, deleted=False)
        return updated_area

    def _create_tables(self, create_table_requests, seller_id, area_name, table_number=0):
        tables = []
        for request in create_table_requests:
            for i in range(request.number_of_tables):
                table_number += 1
                if not area_name:
                    table_name = table_number
                else:
                    table_name = self._get_table_name(area_name=area_name, table_number=table_number)
                tables.append(RestaurantTable(current_status=None, status_updated_at=None, seller_id=seller_id,
                                              x_coordinate=None, y_coordinate=None, width=None, height=None,
                                              table_number=table_number, name=table_name,
                                              seats=[TableSeat(seat_number=j+1)
                                                     for j in range(request.seats_count)]))
        return tables

    def get_restaurant_area(self, seller_id, area_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        return self.__restaurant_area_repository.get_restaurant_area(seller_id=seller_id, area_id=area_id)

    def get_restaurant_areas(self, seller_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        return self.__restaurant_area_repository.get_restaurant_areas(seller_id=seller_id)

    @atomic_operation
    def update_restaurant_area(self, seller_id, area_update_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        area = self.__restaurant_area_repository.get_restaurant_area(
            seller_id=seller_id, area_id=area_update_request.area_id)

        if not area:
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context={
                "name": ["Area ID is not valid"]})

        area.name = getattr(area_update_request, "name", area.name)

        if hasattr(area_update_request, "tables"):
            updated_tables = self._update_tables(area_name=area.name, existing_tables=area.tables,
                                                 table_update_request=area_update_request.tables)
            area.tables = updated_tables

        updated_area = self.__restaurant_area_repository._update(area)
        self.create_and_publish_restaurant_table_events(updated_area.tables, seller_id, created=False, deleted=False)
        return updated_area

    def _update_tables(self, area_name, existing_tables, table_update_request):
        table_dict = {table.table_id: table for table in table_update_request}
        index = 1
        for table in existing_tables:
            table_request = table_dict.get(table.id)

            if table_request:
                table.table_number = index
                table.name = self._get_table_name(area_name=area_name, table_number=index)
                table.x_coordinate = table_request.x_coordinate
                table.y_coordinate = table_request.y_coordinate
                table.width = table_request.width
                table.height = table_request.height
                index += 1
            else:
                table.delete()

        return existing_tables

    def get_tables(self, seller_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        return self.__restaurant_table_repository.get_restaurant_tables(seller_id=seller_id)

    def get_table(self, seller_id, table_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        return self.__restaurant_table_repository.get_restaurant_tables(seller_id, table_id=table_id)[0]

    def create_and_publish_restaurant_table_events(self, tables, seller_id, created, deleted):
        messages = [RestaurantTableMessagingWrapper(restaurant_table, seller_id, created, deleted).get_json()
                    for restaurant_table in tables]
        for message in messages:
            self._messaging_service.publish_restaurant_table_message(message)

    @atomic_operation
    def soft_delete_area(self, area_id, seller_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        restaurant_area = self.__restaurant_area_repository.get_restaurant_area(
            seller_id=seller_id, area_id=area_id)
        restaurant_area = self.__restaurant_area_repository.delete_all_tables_and_seats_in_area(restaurant_area)
        restaurant_area.delete()
        self.create_and_publish_restaurant_table_events(restaurant_area.tables, seller_id, created=False, deleted=True)
        return self.__restaurant_area_repository._update(restaurant_area)
