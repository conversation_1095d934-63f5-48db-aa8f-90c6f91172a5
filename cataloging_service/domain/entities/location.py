from cataloging_service.models import Location


class CityEntity(object):
    def __init__(self, id, name, aliases, latitude, longitude):
        self.id = id
        self.name = name
        self.aliases = aliases
        self.latitude = latitude
        self.longitude = longitude


class LocationEntity(object):
    def __init__(self, city, legal_city, micro_market, locality, state, legal_state, loc: Location):
        self.city = city
        self.legal_city = legal_city
        self.micro_market = micro_market
        self.locality = locality
        self.state = state
        self.legal_state = legal_state
        self.id = loc.id
        self.latitude = loc.latitude
        self.legal_address = loc.legal_address
        self.legal_pincode = loc.legal_pincode
        self.longitude = loc.longitude
        self.maps_link = loc.maps_link
        self.pincode = loc.pincode
        self.postal_address = loc.postal_address