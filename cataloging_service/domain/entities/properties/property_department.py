from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic.types import PositiveInt
from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class PropertyDepartmentEntity(BaseDomainEntity):
    """Property-specific department domain entity"""

    id: Optional[PositiveInt] = None
    property_id: str = Field(..., description="Property ID this department belongs to")
    template_code: Optional[str] = Field(None, max_length=50, description="Source template code")
    code: str = Field(..., min_length=2, max_length=50, description="Unique department code within property")
    name: str = Field(..., min_length=1, max_length=255, description="Department name")
    parent_id: Optional[PositiveInt] = Field(None, description="Parent department ID")
    financial_code: Optional[str] = Field(None, max_length=20, description="Financial/GL code")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    is_custom: bool = Field(False, description="Whether this is a custom department")
    is_active: bool = Field(True, description="Whether department is active")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @classmethod
    @field_validator("code", mode="before")
    def validate_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Code must contain only alphanumeric characters, hyphens, and underscores")
        return v.upper()

    def is_root_department(self) -> bool:
        """Check if this is a root department (no parent)"""
        return self.parent_id is None

    def is_template_based(self) -> bool:
        """Check if this department was created from a template"""
        return self.template_code is not None
