from typing import List

from cataloging_service.domain.enums import ConfigValueType


class AllowedConfigValue(object):
    def __init__(self, value, label):
        self.value = value
        self.label = label


class AvailableConfig(object):
    def __init__(self, name, value_type: ConfigValueType, description=None,
                 allowed_values: List[AllowedConfigValue] = None):
        assert isinstance(value_type, ConfigValueType)
        self.name = name
        self.value_type = value_type
        self.description = description
        self.allowed_values = allowed_values
