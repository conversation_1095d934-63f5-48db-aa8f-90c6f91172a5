from cataloging_service.domain.entities.tenant_config.available_config import AvailableConfig


class TenantConfig(object):
    def __init__(self, config: AvailableConfig, config_value, property_id=None):
        self.config = config
        self.config_value = config_value
        self.property_id = property_id

    @property
    def config_name(self):
        return self.config.name

    @property
    def value_type(self):
        return self.config.value_type
