import json

from cataloging_service.models import Property, PropertyImage, RoomTypeConfiguration, TransportStation, Landmark, \
    PropertyLandmark, PropertyVideo

class PropertyEntity(object):
    def __init__(self, property: Property, guest_types, location, property_details, guest_facing_process, ownerships,
                 property_landmarks, description, neighbouring_place, transport_station_assocs, sku_categories,
                 room_count, property_images, property_videos, amenity_summary, room_type_configurations, property_skus,
                 brand, room_type_config_room_count, associated_modular_skus_for_all_room_types, logo, timezone,
                 base_currency_code, country_code, cost_center_id, region):

        self.id = property.id
        self.hx_id = property.hx_id
        self.external_hotel_id = property.external_hotel_id
        self.status = property.status
        self._name = property.name
        self.old_name = property.old_name
        self.new_name = property.name
        self.legal_name = property.legal_name
        self.guest_types = guest_types
        self.suited_to = guest_types
        self.location = location
        self.property_detail = property_details
        self.guest_facing_process = guest_facing_process
        self.signed_date = property.signed_date
        self.contractual_launch_date = property.contractual_launch_date
        self.launched_date = property.launched_date
        self.churned_date = property.churned_date
        self.timezone = timezone
        self.base_currency_code = base_currency_code
        self.country_code = country_code
        self.ownerships = ownerships
        self.property_landmarks = property_landmarks
        self.description = description
        self.neighbouring_place = neighbouring_place
        self.transport_station_assocs = transport_station_assocs
        self.sku_categories = sku_categories if sku_categories else None
        self.room_count = room_count
        self.property_images = property_images
        self.property_videos = property_videos
        self._amenity_summary = amenity_summary
        self.room_type_configurations = room_type_configurations
        self.property_sku = property_skus
        self.brand = brand
        self.policies = property_details.policies if property_details else None
        self.room_type_config_room_count = room_type_config_room_count
        self.associated_modular_skus_for_all_room_types = associated_modular_skus_for_all_room_types
        self.logo = logo
        self.current_business_date = property.current_business_date
        self.is_test = property.is_test
        self.cost_center_id = cost_center_id
        self.region = region

        if self.room_type_configurations:
            for room_type_config in self.room_type_configurations:
                room_type_config.room_count = self.room_type_config_room_count.get(room_type_config.id)
                associated_modular_sku = associated_modular_skus_for_all_room_types.get(
                    room_type_config.room_type.type.lower())
                room_type_config.room_type.associated_modular_sku_code = associated_modular_sku.code if \
                    associated_modular_sku else None

    @property
    def skus(self):
        skus = []
        if not self.property_sku:
            return None
        for property_sku in self.property_sku:
            skus.append(dict(id=property_sku.id, property_id=property_sku.property_id,
                             sku_name=property_sku.sku.name, sku_code=property_sku.sku.code,
                             status=property_sku.status, saleable=property_sku.saleable,
                             hsn_sac=property_sku.sku.hsn_sac, is_sku_saleable=property_sku.sku.saleable,
                             default_list_price=property_sku.sku.default_list_price,
                             default_sale_price=property_sku.sku.default_sale_price,
                             sku_category_code=property_sku.sku.category.code))
        return skus

    @property
    def owners(self):
        owners = []
        if not self.ownerships:
            return None
        for ownership in self.ownerships:
            owner = ownership.owner
            owner.is_primary_owner = ownership.primary
            owners.append(owner)
        return owners

    @property
    def amenity_summary(self):
        amenity_summary = self._amenity_summary
        if not amenity_summary:
            return {}
        return json.loads(amenity_summary.summary)

    @property
    def name(self):
        return dict(old_name=self.old_name, new_name=self.new_name, legal_name=self.legal_name)

    @property
    def transport_stations(self):
        stations = []
        if not self.transport_station_assocs:
            return None
        for assoc in self.transport_station_assocs:
            station = assoc.transport_station
            station.hotel_distance = assoc.distance_from_property
            station.hotel_direction = assoc.property_direction
            station.hatchback_cab_fare = assoc.hatchback_cab_fare
            station.sedan_cab_fare = assoc.sedan_cab_fare
            stations.append(station)
        return stations


class PropertyImageEntity(object):
    def __init__(self, property_image: PropertyImage, room_type_config: RoomTypeConfiguration):
        self.property_id = property_image.property_id
        self.path = property_image.path
        self.sort_order = property_image.sort_order
        self.tag_description = property_image.tag_description
        self.room_type_config = room_type_config


class PropertyVideoEntity(object):
    def __init__(self, property_video: PropertyVideo):
        self.property_id = property_video.property_id
        self.video_url = property_video.video_url
        self.sort_order = property_video.sort_order
        self.tag_description = property_video.tag_description
        self.youtube_video_url = property_video.youtube_video_url


class PropertyTransportStationEntity(object):
    def __init__(self, hatchback_cab_fare, hotel_direction, hotel_distance, sedan_cab_fare,
                 transport_station: TransportStation):
        self.hatchback_cab_fare = hatchback_cab_fare
        self.property_direction = hotel_direction
        self.distance_from_property = hotel_distance
        self.sedan_cab_fare = sedan_cab_fare
        self.transport_station = transport_station
        self.latitude = transport_station.latitude
        self.longitude = transport_station.longitude
        self.name = transport_station.name


class PropertyLandmarkEntity(object):
    def __init__(self, landmark: Landmark, property_landmark: PropertyLandmark):
        self.landmark = landmark
        self.type = property_landmark.type
        self.distance_from_property = property_landmark.distance_from_property
        self.property_direction = property_landmark.property_direction
        self.hatchback_cab_fare = property_landmark.hatchback_cab_fare
        self.sedan_cab_fare = property_landmark.sedan_cab_fare

    @property
    def hotel_direction(self):
        return self.property_direction

    @property
    def hotel_distance(self):
        return self.distance_from_property

    @property
    def id(self):
        return self.landmark.id

    @property
    def latitude(self):
        return self.landmark.latitude

    @property
    def longitude(self):
        return self.landmark.longitude

    @property
    def name(self):
        return self.landmark.name
