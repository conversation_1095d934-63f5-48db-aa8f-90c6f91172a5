from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import Field, field_validator
from pydantic.types import PositiveInt
from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class DepartmentTemplateEntity(BaseDomainEntity):
    """Brand-scoped department template domain entity"""

    id: Optional[PositiveInt] = None
    brand_ids: Optional[List[int]] = Field(None, description="List of brands this template is mapped to")
    code: str = Field(..., min_length=2, max_length=50, description="Unique department code")
    name: str = Field(..., min_length=1, max_length=255, description="Department name")
    parent_id: Optional[PositiveInt] = Field(None, description="Parent department template ID")
    description: Optional[str] = Field(None, max_length=1000, description="Department description")
    financial_code: Optional[str] = Field(None, max_length=20, description="Financial/GL code")
    is_active: bool = Field(True, description="Whether template is active")
    auto_create_on_property_launch: bool = Field(False, description="Auto-create on property launch")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("code")
    @classmethod
    def validate_code(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Code must contain only alphanumeric characters, hyphens, and underscores")
        return v.upper()

    def validate_hierarchy(self, existing_templates: List["DepartmentTemplateEntity"]) -> bool:
        """Business rule: Validate department hierarchy consistency"""
        if self.parent_code:
            parent_exists = any(
                t.code == self.parent_code and t.brand_id == self.brand_id
                for t in existing_templates
            )
            if not parent_exists:
                return False
            if self._has_circular_reference(existing_templates):
                return False
        return True

    def _has_circular_reference(self, existing_templates: List["DepartmentTemplateEntity"]) -> bool:
        """Check for circular references in hierarchy"""
        visited = set()
        current_code = self.parent_code

        while current_code:
            if current_code == self.code or current_code in visited:
                return True
            visited.add(current_code)

            parent_template = next(
                (t for t in existing_templates
                 if t.code == current_code and t.brand_id == self.brand_id), None
            )
            current_code = parent_template.parent_code if parent_template else None
        return False

class BrandDepartmentTemplateMappingEntity(BaseDomainEntity):
    brand_id: PositiveInt = Field(..., description="Brand ID")
    department_template_id: PositiveInt = Field(..., description="DepartmentTemplate ID")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

