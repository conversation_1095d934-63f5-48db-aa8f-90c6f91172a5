from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic.types import PositiveInt
from cataloging_service.domain.entities.base_entity import BaseDomainEntity


class TransactionDefaultMappingEntity(BaseDomainEntity):
    """Enhanced transaction default mapping with department support"""

    id: Optional[PositiveInt] = None
    brand_id: PositiveInt = Field(..., description="Brand ID this mapping belongs to")
    transaction_type: str = Field(..., min_length=1, max_length=50, description="Transaction type")
    transaction_type_code: str = Field(..., min_length=1, max_length=50, description="Specific transaction type code")
    entity_type: str = Field(..., min_length=1, max_length=50, description="Entity type or scope of the transaction")
    default_gl_code: Optional[str] = Field(None, max_length=50, description="Default GL code")
    default_erp_id: Optional[str] = Field(None, max_length=100, description="Default ERP ID")
    default_particulars: Optional[str] = Field(None, description="Default particulars")
    default_is_merge: bool = Field(False, description="Default merge flag")
    transaction_details: Dict[str, Any] = Field(default_factory=dict, description="Default transaction details JSON")
    is_active: bool = Field(True, description="Whether mapping is active or inactive")
    department_template_code: Optional[str] = Field(None, max_length=50, description="Department template code")
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("transaction_type", "transaction_type_code", "entity_type")
    @classmethod
    def validate_codes(cls, v: str) -> str:
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Codes must contain only alphanumeric characters, hyphens, and underscores")
        return v.upper()

    def get_department_specific_mapping(self, department_code: str) -> Dict[str, Any]:
        """Get department-specific transaction mapping"""
        return {
            "gl_code": self.default_gl_code,
            "erp_id": self.default_erp_id,
            "particulars": f"{self.default_particulars} - {department_code}" if self.default_particulars else f"Transaction - {department_code}",
            "is_merge": self.default_is_merge,
            "department_code": department_code,
            "transaction_details": {**self.transaction_details, "department_code": department_code}
        }

    def get_profit_center_specific_mapping(self, department_code: str, profit_center_code: str) -> Dict[str, Any]:
        """Get profit center-specific transaction mapping"""
        base_mapping = self.get_department_specific_mapping(department_code)
        base_mapping["profit_center_code"] = profit_center_code
        base_mapping["particulars"] = f"{self.default_particulars} - {department_code} - {profit_center_code}" if self.default_particulars else f"Transaction - {department_code} - {profit_center_code}"
        base_mapping["transaction_details"]["profit_center_code"] = profit_center_code
        return base_mapping

    def matches_transaction_type(self, transaction_type: str, transaction_type_code: str, entity_type: str) -> bool:
        """Check if this mapping matches the given transaction criteria"""
        return (
            self.transaction_type.upper() == transaction_type.upper() and
            self.transaction_type_code.upper() == transaction_type_code.upper() and
            self.entity_type.upper() == entity_type.upper() and
            self.is_active
        )
