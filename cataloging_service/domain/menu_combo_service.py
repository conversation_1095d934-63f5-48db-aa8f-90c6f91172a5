from cataloging_service.infrastructure.messaging.messaging_wrappers import ComboWrapper
import logging
import json


from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.models import Co<PERSON>, ComboItem, Sku
from cataloging_service.constants.model_choices import FoodTypeChoices
logger = logging.getLogger(__name__)


class ComboService:
    def __init__(self, combo_repository, item_repository, sku_service, seller_service, messaging_service):
        self._combo_repository = combo_repository
        self._item_repository = item_repository
        self._sku_service = sku_service
        self._seller_service = seller_service
        self._messaging_service = messaging_service

    def check_combo_code_exists(self, seller_id, code, combo_id=None):
        seller = self._seller_service.get_seller(seller_id)
        existing_combos = self._combo_repository.get_combos_in_property(property_id=seller.property_id, code=code,
                                                                        combo_id=combo_id)
        if existing_combos:
            raise CatalogingServiceException(error_codes.DUPLICATE_COMBO_CODE, context={
                "code": ["Combo code is duplicate"]})

    @atomic_operation
    def create_combo(self, create_combo_request):
        seller = self._seller_service.get_seller(seller_id=create_combo_request.seller_id)
        self.check_combo_code_exists(seller_id=create_combo_request.seller_id, code=create_combo_request.code)
        combo = Combo(name=create_combo_request.name, description=create_combo_request.description,
                      display_name=create_combo_request.display_name, code=create_combo_request.code,
                      image=create_combo_request.image, allergen_info=create_combo_request.allergen_info,
                      calorie_info=create_combo_request.calorie_info, prep_time=create_combo_request.prep_time,
                      contains_alcohol=create_combo_request.contains_alcohol, pre_tax_price=create_combo_request.pre_tax_price,
                      cost=create_combo_request.cost, sku_category_code=create_combo_request.sku_category_code,
                      seller_id=create_combo_request.seller_id)

        sku = self._sku_service.create_sku_for_combo(
            sku_category_code=create_combo_request.sku_category_code, combo=combo)
        combo.sku_id = sku.id

        if create_combo_request.combo_items:
            combo.combo_items = [ComboItem(item_id=item.item_id, item_variant_id=item.item_variant_id)
                                 for item in create_combo_request.combo_items]
            item_ids = [item.item_id for item in create_combo_request.combo_items]
            items = self._item_repository.get_all_items(seller_id=create_combo_request.seller_id, item_ids=item_ids)
            food_types = set([item.food_type for item in items])
            combo.food_type = self.get_food_type(food_types=food_types)
            active_list = [create_combo_request.active]
            sold_out_list = [create_combo_request.sold_out]

            for item in items:
                active_list.append(item.active)
                sold_out_list.append(item.sold_out)

            if False in active_list:
                combo.active = False
            else:
                combo.active = True

            if False in sold_out_list:
                combo.sold_out = False
            else:
                combo.sold_out = True

        created_combo = self._combo_repository.persist(combo)
        message = ComboWrapper(created_combo, created=True, deleted=False).get_json()
        self.publish_combo_message(message)
        return created_combo

    def get_all_combos(self, seller_id, name, food_type):
        return self._combo_repository.get_all_combos(seller_id=seller_id, name=name, food_type=food_type)

    def get_combo(self, combo_id):
        return self._combo_repository.get_combo(combo_id=combo_id)

    @atomic_operation
    def soft_delete_combo(self, combo_id, seller_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        existing_combo = self._combo_repository.load_for_update(combo_id=combo_id, seller_id=seller_id)
        existing_combo.delete()
        updated_combo = self._combo_repository._update(existing_combo)
        message = ComboWrapper(updated_combo, created=False, deleted=True).get_json()
        self.publish_combo_message(message)

    @atomic_operation
    def edit_combo(self, combo_id, seller_id, edit_combo_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        self.check_combo_code_exists(seller_id=edit_combo_request.seller_id, code=edit_combo_request.code,
                                        combo_id=combo_id)
        existing_combo = self._combo_repository.load_for_update(combo_id=combo_id, seller_id=seller_id)
        if not existing_combo:
            return

        existing_combo.name = getattr(edit_combo_request, "name", existing_combo.name)
        existing_combo.code = getattr(edit_combo_request, "code", existing_combo.code)
        existing_combo.description = getattr(edit_combo_request, "description", existing_combo.description)
        existing_combo.display_name = getattr(edit_combo_request, "display_name", existing_combo.display_name)
        existing_combo.image = getattr(edit_combo_request, "image", existing_combo.image)
        existing_combo.allergen_info = getattr(edit_combo_request, "allergen_info", existing_combo.allergen_info)
        existing_combo.calorie_info = getattr(edit_combo_request, "calorie_info", existing_combo.calorie_info)
        existing_combo.contains_alcohol = getattr(
            edit_combo_request, "contains_alcohol", existing_combo.contains_alcohol)
        existing_combo.prep_time = getattr(edit_combo_request, "prep_time", existing_combo.prep_time)
        existing_combo.cost = getattr(edit_combo_request, "cost", existing_combo.cost)
        existing_combo.seller_id = getattr(edit_combo_request, "seller_id", existing_combo.seller_id)
        existing_combo.pre_tax_price = getattr(edit_combo_request, "pre_tax_price", existing_combo.pre_tax_price)
        existing_combo.active = getattr(edit_combo_request, "active", existing_combo.active)
        existing_combo.sold_out = getattr(edit_combo_request, "sold_out", existing_combo.sold_out)
        existing_combo.sku_category_code = getattr(
            edit_combo_request, "sku_category_code", existing_combo.sku_category_code)
        existing_combo.sku_id = getattr(edit_combo_request, "sku_id", existing_combo.sku_id)

        entities_to_update = []

        self._sku_service.update_sku_for_combo(sku_category_code=existing_combo.sku_category_code, combo=existing_combo)

        if hasattr(edit_combo_request, "combo_items"):
            combo_items_to_create, combo_items_to_update, combo_items_to_delete = self._get_combo_items_to_create_update_and_delete(
                combo_id=combo_id, existing_combo_items=existing_combo.combo_items, edit_combo_request=edit_combo_request)
            entities_to_update.extend(combo_items_to_create + combo_items_to_update + combo_items_to_delete)

            item_ids = [item.item_id for item in edit_combo_request.combo_items]
            items = self._item_repository.get_all_items(seller_id=seller_id, item_ids=item_ids)
            food_types = set([item.food_type for item in items])
            existing_combo.food_type = self.get_food_type(food_types=food_types)

        self._combo_repository._update_all(entities_to_update)
        updated_combo = self._combo_repository._update(existing_combo)
        message = ComboWrapper(updated_combo, created=False, deleted=False).get_json()
        self.publish_combo_message(message)
        return updated_combo

    def _get_combo_items_to_create_update_and_delete(self, combo_id, existing_combo_items, edit_combo_request):
        combo_items_to_create = [ComboItem(combo_id=combo_id, item_id=combo_item.item_id, item_variant_id=combo_item.item_variant_id)
                                 for combo_item in edit_combo_request.combo_items if not combo_item.combo_item_id]
        combo_items_to_update = [ComboItem(id=combo_item.combo_item_id, combo_id=combo_id, item_id=combo_item.item_id,
                                           item_variant_id=combo_item.item_variant_id)
                                 for combo_item in edit_combo_request.combo_items if combo_item.combo_item_id]
        combo_item_ids_to_update = [combo_item.id for combo_item in combo_items_to_update]
        combo_items_to_delete = [combo_item.delete()
                                 for combo_item in existing_combo_items if combo_item.id not in combo_item_ids_to_update]

        return combo_items_to_create, combo_items_to_update, combo_items_to_delete

    @atomic_operation
    def mark_food_type_of_combos(self, food_type, item_id):
        combos = self._combo_repository.get_combos_via_item_id(item_id=item_id)

        for combo in combos:
            food_type_list = [
                combo_item.item.food_type for combo_item in combo.combo_items if combo_item.item_id != int(item_id)]
            food_type_list.append(food_type)
            food_types = set(food_type_list)
            food_type = self.get_food_type(food_types=food_types)
            combo.food_type = food_type

        return self._combo_repository._update_all(combos)

    def get_food_type(self, food_types):
        if FoodTypeChoices.NON_VEGETARIAN.value in food_types:
            return FoodTypeChoices.NON_VEGETARIAN.value
        elif FoodTypeChoices.VEGETARIAN.value in food_types:
            return FoodTypeChoices.VEGETARIAN.value
        else:
            return FoodTypeChoices.VEGAN.value

    def publish_combo_message(self, message):
        self._messaging_service.publish_combo_message(message)
