from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.models import City
from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.messaging.messaging_wrappers import CityWrapper

import logging

logger = logging.getLogger(__name__)


class LocationService:
    def __init__(self, property_location_repository, messaging_service):
        self.__property_location_repository = property_location_repository
        self.__messaging_service = messaging_service

    def get_all_countries(self):
        return self.__property_location_repository.get_all_countries()

    def get_country(self, id):
        country = self.__property_location_repository.get_country(id)

        if not country:
            raise CatalogingServiceException(error_codes.COUNTRY_NOT_FOUND)
        return country

    def get_country_by_name(self, country_name):
        country = self.__property_location_repository.get_country_by_name(country_name)

        if not country:
            raise CatalogingServiceException(error_codes.COUNTRY_NOT_FOUND)
        return country

    def get_states_of_country(self, country_id):
        country = self.get_country(country_id)

        return country.states

    def get_all_states(self):
        return self.__property_location_repository.get_all_states()

    def get_state(self, id):
        state = self.__property_location_repository.get_state(id)

        if not state:
            raise CatalogingServiceException(error_codes.STATE_NOT_FOUND)
        return state

    def get_state_by_name(self, name, country_id):
        state = self.__property_location_repository.get_state_by_name(name, country_id)

        if not state:
            raise CatalogingServiceException(error_codes.STATE_NAME_NOT_FOUND)
        return state

    def get_cities_of_state(self, state_id):
        state = self.get_state(state_id)

        return state.cities

    def get_all_cities(self):
        return self.__property_location_repository.get_all_cities()

    def get_all_related_cities(self, filter):
        return self.__property_location_repository.get_all_related_cities(filter)

    def get_clusters_of_state(self, state_id):
        cities = self.get_cities_of_state(state_id)

        clusters = set()
        for city in cities:
            clusters.add(city.cluster)

        return list(clusters)

    def get_all_clusters(self):
        cities = self.get_all_cities()

        clusters = set()
        for city in cities:
            clusters.add(city.cluster)

        return list(clusters)

    def get_clusters_by_region_and_state(self, state_id, region_id):
        cluster_list = self.get_clusters_of_state(state_id)
        region = self.get_region(region_id)
        return (
            [cluster for cluster in cluster_list if cluster.region_id == region.id]
            if cluster_list
            else []
        )

    def get_city(self, id):
        city = self.__property_location_repository.get_city(id)

        if not city:
            raise CatalogingServiceException(error_codes.CITY_NOT_FOUND)

        return city

    def get_localities_of_city(self, city_id):
        city = self.get_city(city_id)

        return city.localities

    def get_all_localities(self):
        return self.__property_location_repository.get_all_localities()

    def get_all_related_localities(self, filter):
        return self.__property_location_repository.get_all_related_localities(filter)

    def get_all_micro_markets(self):
        return self.__property_location_repository.get_all_micro_markets()

    def get_micro_markets_of_city(self, city_id):
        city = self.get_city(city_id)

        return city.micro_markets

    def get_all_regions(self):
        return self.__property_location_repository.get_all_regions()

    def get_region(self, region_id):
        region = self.__property_location_repository.get_region(region_id)

        if not region:
            raise CatalogingServiceException(error_codes.REGION_NOT_FOUND)

        return region

    def get_clusters_of_region(self, region_id):
        region = self.get_region(region_id)
        return region.clusters

    def get_cluster(self, cluster_id):
        cluster = self.__property_location_repository.get_cluster(cluster_id)
        if not cluster:
            raise CatalogingServiceException(error_codes.CLUSTER_NOT_FOUND)
        return cluster

    def get_cities_of_cluster(self, cluster_id):
        return self.get_cluster(cluster_id).cities

    def get_cities_by_cluster_and_state(self, state_id, cluster_id):
        state = self.get_state(state_id)
        cluster = self.get_cluster(cluster_id)
        return (
            self.__property_location_repository.get_city_by_cluster_and_state(cluster_id, state_id)
            if state and cluster
            else []
        )

    def publish_cities(self, ids):
        cities = self.__property_location_repository.get_all_cities(ids)
        messages = [CityWrapper(city, False, False).get_json() for city in cities]
        for message in messages:
            self.__messaging_service.publish_city_message(message)

    @atomic_operation
    def create_city(self, city_name, latitude, longitude, state_id, cluster_id=None):
        city = City(
            name=city_name,
            latitude=latitude,
            longitude=longitude,
            state_id=state_id,
            cluster_id=cluster_id,
        )

        try:
            self.__property_location_repository.save_city(city)
        except Exception:
            logger.exception("error saving city {0}".format(city_name))
            city = self.get_all_related_cities(city_name).first()
        return city
