from cataloging_service.domain.kitchen_service import Kitchen<PERSON>ervice
from cataloging_service.client import client_provider
from cataloging_service.domain import file_upload_service
from cataloging_service.domain.channel_service import ChannelService
from cataloging_service.domain.currency_conversion_rate import CurrencyConversionRateService
from cataloging_service.domain.file_upload_service import FileUploadService
from cataloging_service.domain.location_service import LocationService
from cataloging_service.domain.messaging_service import MessagingService
from cataloging_service.domain.meta_service import MetaService
from cataloging_service.domain.ota_service import OTAService
from cataloging_service.domain.property_service import PropertyService
from cataloging_service.domain.provider_service import ProviderService
from cataloging_service.domain.rate_plan_service import RatePlanService
from cataloging_service.domain.room_service import RoomService
from cataloging_service.domain.ruptub_legal_entity_service import LegalEntityDetailsService
from cataloging_service.domain.seller_service import SellerService
from cataloging_service.domain.seller_type_history_service import SellerTypeHistoryService
from cataloging_service.domain.sku_pricing_service import SkuPricingService
from cataloging_service.domain.sku_service import SkuService
from cataloging_service.domain.room_rack_rate_service import RoomRackRateService
from cataloging_service.domain.tenant_config_service import TenantConfigService
from cataloging_service.domain.menu_service import MenuService
from cataloging_service.infrastructure.messaging import messaging_provider
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.domain.item_service import ItemService
from cataloging_service.domain.menu_combo_service import ComboService
from cataloging_service.domain.restaurant_area_service import RestaurantAreaService


class ServiceProvider:
    def __init__(self):
        self.messaging_service = MessagingService(messaging_provider.rmq_message_publisher)
        self.location_service = LocationService(repo_provider.property_location_repository, self.messaging_service)
        self.meta_service = MetaService(repo_provider.meta_repository)
        self.property_service = PropertyService(repo_provider.property_repository,
                                                repo_provider.property_location_repository,
                                                repo_provider.google_drive_repository, repo_provider.meta_repository,
                                                self.messaging_service, client_provider.amazon_s3_client,
                                                repo_provider.property_policy_map_repository,
                                                repo_provider.sku_repository,
                                                repo_provider.owner_repository, repo_provider.ownership_repository,
                                                repo_provider.crs_client,
                                                repo_provider.seller_repository,
                                                repo_provider.room_rack_rate_repository)
        self.google_drive_file_upload_service = FileUploadService(client_provider.google_drive_client,
                                                                  repo_provider.google_drive_repository)
        self.room_service = RoomService(repo_provider.property_repository, self.messaging_service,
                                        repo_provider.sku_repository, repo_provider.room_rack_rate_repository)
        self.ota_service = OTAService(repo_provider.property_repository, repo_provider.meta_repository,
                                      client_provider.email_client)
        self.sku_service = SkuService(repo_provider.sku_repository, repo_provider.sku_redis_repository,
                                      self.messaging_service, repo_provider.property_repository)
        self.room_rack_rate_service = RoomRackRateService(repo_provider.room_rack_rate_repository)
        self.channel_service = ChannelService(repo_provider.channel_repository, self.messaging_service)
        self.provider_service = ProviderService(repo_provider.provider_repository)
        self.seller_type_history_service = SellerTypeHistoryService(repo_provider.seller_type_history_repository)
        self.legal_entity_details_service = LegalEntityDetailsService(repo_provider.legal_entity_details_repository)
        self.seller_service = SellerService(repo_provider.seller_sku_repository, repo_provider.seller_repository,
                                            repo_provider.menu_category_repository,
                                            repo_provider.restaurant_table_repository)
        self.rate_plan_service = RatePlanService(repo_provider.rate_plan_repository,
                                                 repo_provider.rate_plan_config_repository,
                                                 repo_provider.rate_plan_addon_repository)
        self.sku_pricing_service = SkuPricingService(
            repo_provider.sku_pricing_repository, repo_provider.seller_repository)
        self.combo_service = ComboService(repo_provider.combo_repository,
                                          repo_provider.item_repository, self.sku_service, self.seller_service,
                                          self.messaging_service)
        self.item_service = ItemService(repo_provider.item_repository, self.sku_service,
                                        self.seller_service, self.combo_service, self.messaging_service)
        self.menu_service = MenuService(repo_provider.menu_repository, self.seller_service, self.item_service,
                                        self.messaging_service)
        self.restaurant_area_service = RestaurantAreaService(repo_provider.restaurant_area_repository,
                                                             repo_provider.restaurant_table_repository, self.seller_service,
                                                             self.messaging_service)
        self.currency_conversion_rate_service = CurrencyConversionRateService(
            repo_provider.currency_conversion_rate_repository, repo_provider.property_repository)
        self.kitchen_service = KitchenService(repo_provider.kitchen_repository)

service_provider = ServiceProvider()


