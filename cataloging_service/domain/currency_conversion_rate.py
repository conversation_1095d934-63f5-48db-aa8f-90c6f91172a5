from cataloging_service.constants import error_codes
from cataloging_service.domain.entities.currency_conversion_rate import CurrencyConversionRate
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation


class CurrencyConversionRateService:
    def __init__(self, currency_conversion_rate_repository, property_repository):
        self.currency_conversion_rate_repository = currency_conversion_rate_repository
        self.property_repository = property_repository

    def get_currency_conversion_rates_details(self, property_id, from_currency=None, transaction_date=None):
        property = self.property_repository.get_property(property_id)
        if not property:
            raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND, context='property id: %s' % property_id,
                                             send_error_mail=False)
        return self.currency_conversion_rate_repository.load(property_id=property_id, from_currency=from_currency,
                                                             transaction_date=transaction_date)

    @atomic_operation
    def create_currency_conversion_rate(self, currency_conversion_create_request):
        property = self.property_repository.get_property(currency_conversion_create_request.property_id)
        if not property:
            raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                             context='property id: %s' % currency_conversion_create_request.property_id,
                                             send_error_mail=False)
        currency_conversion_rate = CurrencyConversionRate(currency_conversion_create_request.from_currency,
                                                          currency_conversion_create_request.to_currency,
                                                          currency_conversion_create_request.conversion_rate,
                                                          currency_conversion_create_request.start_date,
                                                          currency_conversion_create_request.end_date,
                                                          currency_conversion_create_request.property_id)
        self.currency_conversion_rate_repository.save(currency_conversion_rate)
        return currency_conversion_rate
