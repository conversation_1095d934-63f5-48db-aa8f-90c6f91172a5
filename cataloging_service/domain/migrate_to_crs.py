from cataloging_service.client.service_registry_client import ServiceRegistryClient
from datetime import timedelta

import requests
from flask import current_app, json
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id
from treebo_commons.request_tracing.outgoing_requests import enrich_outgoing_request

from cataloging_service.constants.messaging_constants import PropertyMessageActions
from cataloging_service.domain import service_provider
from cataloging_service.utils import Utils


class CRSMigrate:
    # TODO: Refactor to introduce CRS Client.
    NO_OF_DAYS_TO_SYNC_INVENTORY = 550

    HEADERS = {"X-User-Type": "crs-migration-user"}

    CRS_HOTEL_SYNC = "/v1/crs-migration/hotel-sync/{0}"
    CRS_SYNC_INVENTORY = "/v1/hotels/{0}/sync-inventories"
    CRS_INIT_MIGRATION = "/v1/crs-migration/init-migration/{0}"
    CRS_COMPLETE_MIGRATION = "/v1/crs-migration/complete-migration/{0}"

    ITS_SYNC_API = "/crs/property_inventory_sync"

    def __init__(self, property_id):
        self.property_id = property_id
        self.inventory_sync_start_date = Utils.get_current_date_ist()
        self.inventory_sync_end_date = self.inventory_sync_start_date + timedelta(
            days=self.NO_OF_DAYS_TO_SYNC_INVENTORY
        )

        self.crs_host = ServiceRegistryClient.get_crs_service_url()
        self.its_host = ServiceRegistryClient.get_its_service_url() + "/its"

    def headers(self):
        headers = self.HEADERS.copy()
        enrich_outgoing_request(headers)
        return headers

    def start(self):
        service_provider.property_service.publish_properties(
            [self.property_id], PropertyMessageActions.CRS_MIGRATE.value)
        self._sync_property_to_crs()
        self._sync_inventory_to_crs()
        self._initialize_migration_in_crs()
        self._complete_migration_in_crs()
        if get_current_tenant_id() == TenantClient.get_default_tenant():
            self._sync_inventory_to_its()

    def _sync_property_to_crs(self):
        url = self.crs_host + self.CRS_HOTEL_SYNC.format(self.property_id)
        response = requests.post(url, headers=self.headers())
        if response.status_code not in [200, 201]:
            raise RuntimeError(
                "sync_property_to_crs failed with response: {}".format(response.text)
            )

    def _sync_inventory_to_crs(self):
        url = self.crs_host + self.CRS_SYNC_INVENTORY.format(self.property_id)

        response = requests.post(
            url,
            headers=self.headers(),
            params={
                "from_date": self.inventory_sync_start_date.isoformat(),
                "to_date": self.inventory_sync_end_date.isoformat(),
            },
        )
        if response.status_code not in [201]:
            raise RuntimeError(
                "sync_inventory_to_crs failed with response: {}".format(response.text)
            )

    def _initialize_migration_in_crs(self):
        url = self.crs_host + self.CRS_INIT_MIGRATION.format(self.property_id)
        response = requests.post(url, headers=self.headers(), params={"dry_run": False})
        if response.status_code not in [200, 201]:
            raise RuntimeError(
                "initialize_migration_in_crs failed with response: {}".format(response.text)
            )

    def _complete_migration_in_crs(self):
        url = self.crs_host + self.CRS_COMPLETE_MIGRATION.format(self.property_id)
        response = requests.post(url, headers=self.headers())
        if response.status_code not in [200, 201]:
            raise RuntimeError(
                "complete_migration_in_crs failed with response: {}".format(response.text)
            )

    def _sync_inventory_to_its(self):
        url = "{}{}".format(self.its_host, self.ITS_SYNC_API)
        response = requests.get(
            url,
            params={
                "hotel_id": self.property_id,
                "start_date": self.inventory_sync_start_date,
                "end_date": self.inventory_sync_end_date,
            },
        )
        if response.status_code not in [200, 201]:
            raise RuntimeError(
                "sync_inventory_to_its failed with response: {}".format(response.text)
            )
