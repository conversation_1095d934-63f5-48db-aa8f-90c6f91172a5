import json
import logging

from flask import current_app

from cataloging_service.constants import error_codes
from cataloging_service.constants.constants import CostCenterConfig
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.repositories import PropertyRepository, SellerRepository
from cataloging_service.infrastructure.repositories.user_defined_enum_repository import UserDefinedEnumRepository

logger = logging.getLogger(__name__)


class EnumConfigService(object):
    def __init__(self):
        self.user_defined_enum_repository = UserDefinedEnumRepository()
        self.property_repository = PropertyRepository()
        self.seller_repository = SellerRepository()

    def get_enums(self, property_id=None, seller_id=None, enum_names=None, role=None):
        if seller_id:
            seller = self.seller_repository.get_seller_by_id(seller_id)
            if not seller:
                raise CatalogingServiceException(error_codes.SELLER_NOT_FOUND,
                                                 context='seller id: %s' % seller_id,
                                                 send_error_mail=False)
            property_id = seller[0].property_id

        if property_id and not seller_id:
            property = self.property_repository.get_property(property_id)
            if not property:
                raise CatalogingServiceException(error_codes.PROPERTY_NOT_FOUND,
                                                 context='property id: %s' % property_id,
                                                 send_error_mail=False)
        user_defined_enums = self.user_defined_enum_repository.load(property_id=property_id, enum_names=enum_names,
                                                                    role=role)
        return user_defined_enums

    def get_region_enum_values(self):
        user_defined_enums = self.get_enums(enum_names=[CostCenterConfig.REGIONS.value]).first()
        regions = [value.value for value in user_defined_enums.enum_values]
        return self._parse_region_values(regions)

    @staticmethod
    def _parse_region_values(regions):
        return [r.strip() for r in regions[0].split(',')] if regions else []
