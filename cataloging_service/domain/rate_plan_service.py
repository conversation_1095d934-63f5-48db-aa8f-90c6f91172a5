import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class RatePlanService:
    def __init__(self, rate_plan_repository, rate_plan_config_repository, rate_plan_addon_repository):
        self.rate_plan_repository = rate_plan_repository
        self.rate_plan_config_repository = rate_plan_config_repository
        self.rate_plan_addon_repository = rate_plan_addon_repository

    def get_rate_plans_for_property(self, property_id):
        rate_plan_configs = self.rate_plan_config_repository.get_rate_plan_config(property_id=property_id)

        room_based_rate_plans = defaultdict(list)
        for rate_plan_config in rate_plan_configs:
            room_based_rate_plans[rate_plan_config.room_type.type].append(rate_plan_config)
        rate_plans = []
        for room_type, rate_plan_configs in room_based_rate_plans.items():
            rate_plan_codes = []
            for rate_plan_config in rate_plan_configs:
                rate_plan_codes.append(rate_plan_config.rate_plan.code)
            plans = {
                "room_type": room_type,
                "rate_plan_codes": rate_plan_codes
            }
            rate_plans.append(plans)

        return rate_plans

    def get_rate_plans(self):
        rate_plans = self.rate_plan_repository.get_all_rate_plans()
        rate_plan_name_mapping = {rate_plan.code: rate_plan for rate_plan in rate_plans}
        rate_plan_addons = self.rate_plan_addon_repository.get_all_rate_plan_addons()
        rate_plan_addons_data = defaultdict(list)
        for rate_plan_addon in rate_plan_addons:
            rate_plan_addons_data[rate_plan_addon.rate_plan.code].append(rate_plan_addon)

        rate_plan_details = []
        for rate_plan_code, rate_plan in rate_plan_name_mapping.items():
            rate_plan_addons = rate_plan_addons_data.get(rate_plan_code)
            addons = []
            if not rate_plan_addons:
                data = dict(rate_plan_code=rate_plan_code, rate_plan_name=rate_plan.name, addons=addons)
                rate_plan_details.append(data)
                continue

            for rate_plan_addon in rate_plan_addons:
                addon_details = dict(code=rate_plan_addon.addon.code, name=rate_plan_addon.addon.name)
                addons.append(addon_details)
            data = dict(rate_plan_code=rate_plan_code, rate_plan_name=rate_plan.name, addons=addons)
            rate_plan_details.append(data)
        return rate_plan_details

    def get_all_rate_plans(self):
        rate_plans = self.rate_plan_repository.get_all_rate_plans()
        return rate_plans
