class SkuDto:
    def __init__(self, code, sku_category_code, name, display_name, saleable, rack_rate, offering, frequency,
                 default_sale_price, default_list_price, is_property_inclusion, tax_at_room_rate):
        self.code = code
        self.sku_category_code = sku_category_code
        self.name = name
        self.display_name = display_name
        self.saleable = saleable
        self.rack_rate = rack_rate
        self.offering = offering
        self.frequency = frequency
        self.default_sale_price = default_sale_price
        self.default_list_price = default_list_price
        self.is_property_inclusion = is_property_inclusion
        self.tax_at_room_rate = tax_at_room_rate
