from sqlalchemy.exc import IntegrityError
from typing import List, Optional

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import ValidationException
from object_registry import register_instance
from cataloging_service.domain.entities.templates.department_template import DepartmentTemplateEntity
from cataloging_service.infrastructure.repositories.department_template_repository import DepartmentTemplateRepository


@register_instance(dependencies=[DepartmentTemplateRepository])
class DepartmentTemplateService:
    """Service for department template business logic"""

    def __init__(self, repository: DepartmentTemplateRepository):
        self.repository: DepartmentTemplateRepository = repository

    def create_department_template(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Create a new department template with business validation"""
        self._validate_brand_code_uniqueness(entity)
        self._validate_hierarchy(entity)

        try:
            return self.repository.create(entity)
        except IntegrityError as e:
            self._handle_integrity_error(e, entity.brand_ids)
            raise

    def update_department_template(self, entity: DepartmentTemplateEntity) -> DepartmentTemplateEntity:
        """Update department template with business validation"""
        self._validate_brand_code_uniqueness(entity, exclude_id=entity.id)
        self._validate_hierarchy(entity)

        try:
            return self.repository.update(entity)
        except IntegrityError as e:
            self._handle_integrity_error(e, entity.brand_ids)
            raise

    def get_department_template_by_id(self, template_id: int) -> Optional[DepartmentTemplateEntity]:
        """Get department template by ID"""
        return self.repository.get_by_id(template_id)

    def get_department_templates_by_brand(self, brand_id: int, active_only: bool = True) -> List[DepartmentTemplateEntity]:
        """Get department templates for a brand"""
        return self.repository.get_by_brand(brand_id, active_only)

    def get_auto_create_templates(self, brand_id: int) -> List[DepartmentTemplateEntity]:
        """Get templates that should be auto-created on property launch"""
        return self.repository.get_auto_create_templates(brand_id)

    def delete_department_template(self, template_id: int) -> bool:
        """Delete department template with business validation"""
        # Business rule: Check if template is referenced by profit center templates
        # This would require checking profit center templates - simplified for now
        return self.repository.delete(template_id)

    def _validate_hierarchy(self, entity: DepartmentTemplateEntity):
        """
        Validates the hierarchy constraints for the given department template entity.

        - Ensures that if a parent_id is specified, the parent must exist within
          the same brand's templates.
        - Prevents circular references in the department hierarchy.

        Raises:
            ValueError: If the parent_id is invalid or creates a circular reference.
        """
        if entity.parent_id:
            # Get all templates for the brands this template is associated with
            all_templates = []
            for brand_id in entity.brand_ids:
                templates = self.repository.get_by_brand(brand_id, active_only=False)
                all_templates.extend(templates)

            if not entity.validate_hierarchy(all_templates):
                raise ValidationException(error_codes.INVALID_REQUEST_DATA,
                    f"Invalid hierarchy: Parent department with ID '{entity.parent_id}' not found or circular reference detected"
                )

    def _validate_brand_code_uniqueness(self, entity: DepartmentTemplateEntity, exclude_id: Optional[int] = None):
        for brand_id in entity.brand_ids:
            if self.repository.exists_by_brand_and_code(brand_id, entity.code, exclude_id=exclude_id):
                raise ValidationException(
                    error_codes.INVALID_REQUEST_DATA,
                    f"Department template with code '{entity.code}' already exists for brand {brand_id}"
                )

    def _handle_integrity_error(self, e: IntegrityError, brand_ids: List[int]):
        if "brand_id" in str(e.orig) or "brand_ids" in str(e.orig):
            raise ValidationException(
                error_codes.INVALID_REQUEST_DATA,
                f"One or more brands in {brand_ids} do not exist"
            )
