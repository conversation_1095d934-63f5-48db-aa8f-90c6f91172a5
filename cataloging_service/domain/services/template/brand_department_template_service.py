from typing import Optional

from cataloging_service.models import BrandDepartmentTemplateMap
from object_registry import register_instance
from cataloging_service.domain.entities.templates.department_template import BrandDepartmentTemplateMappingEntity
from cataloging_service.infrastructure.repositories.brand_department_template_repository import BrandDepartmentTemplateRepository


@register_instance(dependencies=[BrandDepartmentTemplateRepository])
class BrandDepartmentTemplateService:
    """Service for department template business logic"""

    def __init__(self, repository: BrandDepartmentTemplateRepository):
        self.repository: BrandDepartmentTemplateRepository = repository

    def get_department_template_by_brand_id(self, brand_id: int) -> Optional[BrandDepartmentTemplateMappingEntity]:
        """Get department template by ID"""
        return self.repository.get_by_brand_id(brand_id)

    # def get_department_templates_by_brand(self, brand_id: int, active_only: bool = True) -> List[DepartmentTemplateEntity]:
    #     """Get department templates for a brand"""
    #     return self.repository.get_by_brand(brand_id, active_only)
    #
    # def get_auto_create_templates(self, brand_id: int) -> List[DepartmentTemplateEntity]:
    #     """Get templates that should be auto-created on property launch"""
    #     return self.repository.get_auto_create_templates(brand_id)
    #
    # def delete_department_template(self, template_id: int) -> bool:
    #     """Delete department template with business validation"""
    #     # Business rule: Check if template is referenced by profit center templates
    #     # This would require checking profit center templates - simplified for now
    #     return self.repository.delete(template_id)
    #
    # def _validate_hierarchy(self, entity: DepartmentTemplateEntity):
    #     """
    #     Validates the hierarchy constraints for the given department template entity.
    #
    #     - Ensures that if a parent_code is specified, the parent must exist within
    #       the same brand's templates.
    #     - Prevents circular references in the department hierarchy.
    #
    #     Raises:
    #         ValueError: If the parent_code is invalid or creates a circular reference.
    #     """
    #     if entity.parent_code:
    #         templates = self.repository.get_by_brand(entity.brand_id, active_only=False)
    #         if not entity.validate_hierarchy(templates):
    #             raise ValidationException(error_codes.INVALID_REQUEST_DATA,
    #                 f"Invalid hierarchy: Parent department '{entity.parent_code}' not found or circular reference detected"
    #             )
    #
    # def _validate_brand_code_uniqueness(self, entity: DepartmentTemplateEntity, exclude_id: Optional[int] = None):
    #     for brand_id in entity.brand_ids:
    #         if self.repository.exists_by_brand_and_code(brand_id, entity.code, exclude_id=exclude_id):
    #             raise ValidationException(
    #                 error_codes.INVALID_REQUEST_DATA,
    #                 f"Department template with code '{entity.code}' already exists for brand {brand_id}"
    #             )
    #
    # def _handle_integrity_error(self, e: IntegrityError, brand_ids: List[int]):
    #     if "brand_id" in str(e.orig) or "brand_ids" in str(e.orig):
    #         raise ValidationException(
    #             error_codes.INVALID_REQUEST_DATA,
    #             f"One or more brands in {brand_ids} do not exist"
    #         )
