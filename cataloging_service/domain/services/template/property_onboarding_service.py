import logging

from datetime import datetime
from typing import Union

from cataloging_service.domain import service_provider
from cataloging_service.domain.services.template.brand_department_template_service import BrandDepartmentTemplateService
from cataloging_service.domain.services.transactions.transaction_default_mapping_service import \
    TransactionDefaultMappingService
from cataloging_service.domain.entities.properties.property_onboarding import (
    PropertyOnboardingEntity,
)

from cataloging_service.domain.entities.properties.property_department import (
    PropertyDepartmentEntity,
)
from cataloging_service.domain.entities.seller.seller import SellerEntity
from cataloging_service.domain.services.property.property_department_service import (
    PropertyDepartmentService,
)
from cataloging_service.domain.services.template.department_template_service import (
    DepartmentTemplateService,
)
from cataloging_service.domain.services.template.profit_center_template_service import (
    ProfitCenterTemplateService,
)
from cataloging_service.schemas.property_onboarding import PropertyOnboardingRequestSchema, \
    PropertyOnboardingResponseSchema
from object_registry import register_instance

logger = logging.getLogger(__name__)





@register_instance(
    dependencies=[
        DepartmentTemplateService,
        ProfitCenterTemplateService,
        PropertyDepartmentService,
        TransactionDefaultMappingService,
        BrandDepartmentTemplateService,
    ]
)
class PropertyOnboardingService:
    def __init__(
        self,
        department_template_service: DepartmentTemplateService,
        profit_center_template_service: ProfitCenterTemplateService,
        property_department_service: PropertyDepartmentService,
        transaction_default_mapping_service: TransactionDefaultMappingService,
        brand_department_template_service: BrandDepartmentTemplateService,
    ):
        self.department_template_service = department_template_service
        self.profit_center_template_service = profit_center_template_service
        self.property_department_service = property_department_service
        self.transaction_default_mapping_service = transaction_default_mapping_service
        self.brand_department_template_service = brand_department_template_service
        self.property_service = service_provider.property_service
        self.meta_service = service_provider.meta_service
        self.seller_service = service_provider.seller_service
        self.sku_service = service_provider.sku_service

    def onboard_property(
        self, request: PropertyOnboardingRequestSchema
    ) -> PropertyOnboardingResponseSchema:
        onboarding_entity = PropertyOnboardingEntity(
            property_id=request.property_id,
            brand_id=request.brand_id,
            template_filters=request.custom_config,
        )

        try:
            # Step 1: Validate property
            property_obj = self._validate_property(onboarding_entity.property_id)

            # Step 2: Create departments from templates
            # if request.auto_create_departments:
            #     departments_created = self._create_departments_from_templates(
            #         onboarding_entity,
            #     )
            #     onboarding_entity.departments_created = departments_created

            # Step 3: Create profit centers from templates
            # Profit centers are Sellers at property level
            # if request.auto_create_profit_centers:
            #     profit_centers_created = self._create_sellers_from_profit_center_templates(
            #         onboarding_entity=onboarding_entity,
            #         property_obj=property_obj
            #     )
            #     onboarding_entity.profit_centers_created = profit_centers_created
            #
            # # Step 4: Auto-create property SKUs
            if request.auto_create_property_skus:
                property_skus_created = self._auto_create_property_skus(
                    onboarding_entity=onboarding_entity,
                    property_obj=property_obj
                )
                onboarding_entity.property_skus_created = property_skus_created
            #
            # # Step 5: Auto-create seller SKUs
            # if request.auto_create_seller_skus:
            #     seller_skus_created = self._auto_create_seller_skus(
            #         onboarding_entity=onboarding_entity,
            #         property_obj=property_obj
            #     )
            #     onboarding_entity.seller_skus_created = seller_skus_created

            # Mark as completed
            onboarding_entity.mark_completed()

            return PropertyOnboardingResponseSchema(
                brand_id=onboarding_entity.brand_id,
                onboarding_status=onboarding_entity.onboarding_status,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
                property_id=onboarding_entity.property_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                property_skus_created=getattr(onboarding_entity, 'property_skus_created', 0),
                seller_skus_created=getattr(onboarding_entity, 'seller_skus_created', 0),
            )

        except Exception as e:
            onboarding_entity.add_error(f"Onboarding failed: {str(e)}")
            logger.exception(
                f"Property onboarding failed for property_id: {request.property_id}"
            )
            return PropertyOnboardingResponseSchema(
                brand_id=onboarding_entity.brand_id,
                onboarding_status=onboarding_entity.onboarding_status,
                errors=onboarding_entity.errors,
                warnings=onboarding_entity.warnings,
                onboarded_at=onboarding_entity.onboarded_at or datetime.now(),
                property_id=onboarding_entity.property_id,
                departments_created=onboarding_entity.departments_created,
                profit_centers_created=onboarding_entity.profit_centers_created,
                property_skus_created=getattr(onboarding_entity, 'property_skus_created', 0),
                seller_skus_created=getattr(onboarding_entity, 'seller_skus_created', 0),
            )

    def _validate_property(self, property_id: str):
        property_obj = self.property_service.get_property(property_id)
        if not property_obj:
            raise ValueError(f"Property with ID '{property_id}' not found")
        return property_obj

    def _create_departments_from_templates(
        self, onboarding_entity: PropertyOnboardingEntity
    ) -> int:
        created_department_count = 0
        try:
            department_templates = self.department_template_service.get_department_templates_by_brand(
                onboarding_entity.brand_id
            )
            if not department_templates:
                onboarding_entity.add_warning(
                    "No department templates found for auto-creation"
                )
                return created_department_count

            for department_template in department_templates:
                department_entity = self._convert_department_template_to_entity(
                    department_template, onboarding_entity.property_id
                )
                self.property_department_service.create_department(
                    department_entity
                )
                created_department_count += 1
            return created_department_count
        except Exception as e:
            error_msg = f"Failed to create departments from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
            raise e

    def _convert_department_template_to_entity(self, department_template, property_id: str):
        return PropertyDepartmentEntity(
            property_id=property_id,
            code=department_template.code,
            name=department_template.name,
            parent_id=department_template.parent_code,
            description=department_template.description,
            financial_code=department_template.financial_code,
            is_active=department_template.is_active,
            is_custom=False,
            template_code=department_template.code,
        )

    def _create_sellers_from_profit_center_templates(
        self, onboarding_entity: PropertyOnboardingEntity, property_obj
    ) -> int:
        created_sellers = 0
        try:
            profit_center_templates = (
                self.profit_center_template_service.get_auto_create_templates(
                    onboarding_entity.brand_id
                )
            )

            if not profit_center_templates:
                onboarding_entity.add_warning(
                    "No profit center templates found for auto-creation"
                )
                return created_sellers

            for profit_center_template in profit_center_templates:
                try:
                    # Convert template to seller entity with proper field population
                    seller = self._convert_profit_center_template_to_seller_entity(
                        profit_center_template, onboarding_entity.property_id, property_obj
                    )
                    self.seller_service.create_seller_for_department(seller)
                    created_sellers += 1
                    logger.info(f"Created profit center '{profit_center_template.name}' from template {profit_center_template.code}")

                except Exception as e:
                    error_msg = f"Failed to create profit center from template {profit_center_template.code}: {str(e)}"
                    onboarding_entity.add_error(error_msg)
                    logger.error(error_msg)

        except Exception as e:
            error_msg = f"Failed to create profit centers from templates: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
            raise e

        return created_sellers

    def _convert_profit_center_template_to_seller_entity(self, profit_center_template, property_id: str, property_obj):
        seller_id = self._generate_seller_id(property_id, profit_center_template.code)
        default_seller_category_id = 1
        default_city_id = 1

        return SellerEntity(
            seller_id=seller_id,
            name=profit_center_template.name,
            property_id=property_id,
            department_id=1,
            seller_category_id=default_seller_category_id,
            city_id=default_city_id,
            status="ACTIVE",
            timezone="UTC",
            base_currency_code=property_obj.base_currency_code,
            current_business_date=property_obj.current_business_date,
            created_from_template_code=profit_center_template.code,
            is_auto_created=True,
            legal_name=property_obj.legal_name,
        )

    def _generate_seller_id(self, property_id: str, template_code: str) -> str:
        seller_id = f"{property_id}_{template_code}"
        return seller_id

    def _auto_create_property_skus(
        self, onboarding_entity: PropertyOnboardingEntity, property_obj
    ) -> Union[int, None]:
        try:
            successful_property_ids = self.property_service.create_property_skus(ids=[onboarding_entity.property_id])
            print("successful_property_ids", successful_property_ids)
            if successful_property_ids:
                property_skus = self.property_service.get_created_property_skus(treebo_property=property_obj, skus=[], property_default_skus=[])
                print("property_skus", property_skus)
                property_skus_count = len(property_skus) if property_skus else 0
                logger.info(f"Successfully auto-created {property_skus_count} property SKUs for property {onboarding_entity.property_id}")
                return property_skus_count
            else:
                onboarding_entity.add_warning("Property SKU auto-create completed but no SKUs were created")
                return 0

        except Exception as e:
            error_msg = f"Failed to auto-create property SKUs: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
            raise

    def _auto_create_seller_skus(
        self, onboarding_entity: PropertyOnboardingEntity, property_obj
    ) -> int:
        created_seller_skus = []
        try:
            sellers = self.seller_service.get_sellers(property_id=onboarding_entity.property_id)
            if not sellers:
                onboarding_entity.add_warning("No sellers found for seller SKU auto-create")
                return 0

            seller_ids = [seller.seller_id for seller in sellers]

            department_ids = self.property_department_service.get_departments_by_property(property_id=onboarding_entity.property_id)
            for department in department_ids:
                created_seller_skus = self._create_seller_skus_with_department(
                    onboarding_entity.property_id, seller_ids, department.id
                )

            if created_seller_skus:
                seller_sku_ids = [seller_sku.id for seller_sku in created_seller_skus]
                self.sku_service.publish_seller_skus(seller_sku_ids)
                logger.info(f"Successfully auto-created {len(created_seller_skus)} seller SKUs for property {property_obj.id}")

            return len(created_seller_skus)

        except Exception as e:
            error_msg = f"Failed to auto-create seller SKUs: {str(e)}"
            onboarding_entity.add_error(error_msg)
            logger.error(error_msg)
            return 0

    def _create_seller_skus_with_department(self, property_id: str, seller_ids: list, department_id: int):
        created_seller_skus = []
        property_skus = self.property_service.get_property_skus_by_property_id(property_id)
        if not property_skus:
            return created_seller_skus
        for seller_id in seller_ids:
            for property_sku in property_skus:
                try:
                    existing_seller_sku = service_provider.seller_service.seller_sku_repository.get_seller_sku(
                        seller_id, property_sku.sku_id)
                    if existing_seller_sku:
                        continue
                except:
                    pass
                try:
                    sku_dict = {
                        "sku_category_code": property_sku.sku.category.code if property_sku.sku.category else "default",
                        "name": property_sku.sku.name,
                        "display_name": property_sku.display_name or property_sku.sku.name
                    }
                    seller_sku = self.sku_service._create_new_seller_sku(
                        sku_dict, property_sku.sku, seller_id, department_id
                    )
                    created_seller_skus.append(seller_sku)
                    logger.info(f"Created seller SKU for seller {seller_id}, SKU {property_sku.sku.name} with department {department_id}")
                except Exception as e:
                    logger.error(
                        f"Failed to create seller SKU for seller {seller_id}, SKU {property_sku.sku.name}: {str(e)}")

        return created_seller_skus
