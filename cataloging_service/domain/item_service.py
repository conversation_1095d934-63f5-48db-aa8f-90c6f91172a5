from cataloging_service.infrastructure.messaging.messaging_wrappers import ItemWrapper
import logging

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.models import (
    ItemCustomisation, Item, ItemVariant, ItemVariantVariantAssociation, SideItem,
    Variant, VariantGroup
)

logger = logging.getLogger(__name__)


class ItemService:
    def __init__(self, item_repository, sku_service, seller_service, combo_service, messaging_service):
        self.__sku_service = sku_service
        self.__item_repository = item_repository
        self._seller_service = seller_service
        self._combo_service = combo_service
        self._messaging_service = messaging_service

    def get_items(self, seller_id, filters):
        return self.__item_repository.get_all_items(seller_id=seller_id, name=filters.get("name"),
                                                    food_type=filters.get("food_type"),
                                                    is_menu_item=filters.get("is_menu_item"),
                                                    is_side=filters.get("is_side"))

    def get_side_items(self, seller_id, name):
        return self.__item_repository.get_all_sides(seller_id, name)

    def get_item(self, seller_id, item_id):
        return self.__item_repository.get_item(seller_id, item_id)

    def get_item_customisations(self, seller_id, item_id):
        return self.__item_repository.get_item_customisations(
            seller_id, item_id)

    def get_item_variants(self, seller_id, item_id):
        return self.__item_repository.get_item_variants(
            seller_id, item_id)

    def get_all_items_and_children(self, seller_id, item_ids):
        return self.__item_repository.get_all_items_and_children(seller_id, item_ids)

    def check_item_code_exists(self, seller_id, code, item_id=None):
        seller = self._seller_service.get_seller(seller_id)
        existing_items = self.__item_repository.get_items_in_property(property_id=seller.property_id, code=code,
                                                                      item_id=item_id)
        if existing_items:
            raise CatalogingServiceException(error_codes.DUPLICATE_ITEM_CODE, context={
                "code": ["Item code is duplicate"]})

    @atomic_operation
    def soft_delete_item(self, item_id, seller_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        existing_item, _item_associations, _item_variants = self.__item_repository.load_item_for_update(
            item_id=item_id, seller_id=seller_id)
        existing_item.delete()
        updated_item = self.__item_repository._update(existing_item)
        message = ItemWrapper(updated_item, created=False, deleted=True).get_json()
        self.publish_item_message(message)

    @atomic_operation
    def create_item(self, create_item_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=create_item_request.seller_id)

        self.check_item_code_exists(seller_id=create_item_request.seller_id, code=create_item_request.code)
        item = Item(name=create_item_request.name, description=create_item_request.description,
                    code=create_item_request.code, kitchen_id=create_item_request.kitchen_id,
                    sku_category_code=create_item_request.sku_category_code,
                    display_name=create_item_request.display_name,
                    print_name=create_item_request.print_name, sold_out=create_item_request.sold_out,
                    prep_time=create_item_request.prep_time,
                    use_as_side=create_item_request.use_as_side, contains_alcohol=create_item_request.contains_alcohol,
                    pre_tax_price=create_item_request.pre_tax_price, image=create_item_request.image,
                    allergen_info=create_item_request.allergen_info,
                    calorie_info=create_item_request.calorie_info, cost=create_item_request.cost,
                    active=create_item_request.active, food_type=create_item_request.food_type,
                    seller_id=create_item_request.seller_id)

        if create_item_request.item_variants:
            item_variant_skus = self.__sku_service.create_sku_for_item_variants(
                item_variants=create_item_request.item_variants, item_name=item.name,
                seller_id=create_item_request.seller_id)

            item.item_variants = [ItemVariant(sku_id=item_variant_skus[index].id,
                                              name=item_variant.name,
                                              display_order=index, pre_tax_price=item_variant.pre_tax_price,
                                              sku_category_code=item_variant.sku_category_code,
                                              cost=item_variant.cost, variants=[
                    self._create_variant_association(variant.variant_id) for variant in item_variant.variants],
                                              item_customisations=[ItemCustomisation(
                                                  display_order=index,
                                                  variant_id=customisation.variant_id,
                                                  cost=customisation.cost,
                                                  delta_price=customisation.delta_price)
                                                  for index, customisation in
                                                  enumerate(item_variant.item_customisations)])
                                  for index, item_variant in enumerate(create_item_request.item_variants)]
        else:
            sku = self.__sku_service.create_sku_for_item(
                sku_category_code=create_item_request.sku_category_code, item=item
            )
            item.sku_id = sku.id

        if create_item_request.side_items:
            side_item_ids = [side_item.item_id for side_item in create_item_request.side_items]
            side_item_variant_ids = [
                side_item.item_variant_id for side_item in create_item_request.side_items if side_item.item_variant_id]
            if not self.__item_repository.check_all_side_items_exists(create_item_request.seller_id, side_item_ids,
                                                                      side_item_variant_ids):
                raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context={
                    "side_items": [
                        "Side Items ids are either duplicate, doesn't exist or can't be used as side items"]})
            item.side_items = [SideItem(side_item_id=side_item.item_id, side_item_variant_id=side_item.item_variant_id)
                               for side_item in create_item_request.side_items]

        if create_item_request.item_customisations:
            item.item_customisations = [
                ItemCustomisation(delta_price=item_customisation.delta_price, cost=item_customisation.cost,
                                  variant_id=item_customisation.variant_id,
                                  display_order=index) for index, item_customisation
                in enumerate(create_item_request.item_customisations)]

        created_item = self.__item_repository.persist(item)
        message = ItemWrapper(created_item, created=True, deleted=False).get_json()
        self.publish_item_message(message)
        return created_item

    @staticmethod
    def _create_variant_association(variant_id):
        item_variant_variant_association = ItemVariantVariantAssociation()
        item_variant_variant_association.variant_id = variant_id
        return item_variant_variant_association

    def get_variant_groups(self, seller_id):
        return self.__item_repository.get_all_variant_groups(seller_id=seller_id)

    def search_variant_groups(self, seller_id, is_customisation, name, variant_group_ids):
        return self.__item_repository.search_variant_groups(seller_id=seller_id, is_customisation=is_customisation,
                                                            name=name,
                                                            variant_group_ids=variant_group_ids)

    @atomic_operation
    def edit_item(self, seller_id, item_id, edit_item_request):
        seller = self._seller_service.get_seller(seller_id=seller_id)
        existing_item, existing_associations, existing_item_variant_item_customisation = \
            self.__item_repository.load_item_for_update(
                seller_id=seller_id, item_id=item_id)
        if not existing_item:
            return

        self.check_item_code_exists(seller_id=edit_item_request.seller_id, code=edit_item_request.code,
                                    item_id=item_id)

        existing_item.name = getattr(edit_item_request, "name", existing_item.name)
        existing_item.code = getattr(edit_item_request, "code", existing_item.code)
        existing_item.description = getattr(edit_item_request, "description", existing_item.description)
        existing_item.sku_category_code = getattr(
            edit_item_request, "sku_category_code", existing_item.sku_category_code)
        existing_item.display_name = getattr(edit_item_request, "display_name", existing_item.display_name)
        existing_item.print_name = getattr(edit_item_request, "print_name", existing_item.print_name)
        existing_item.active = getattr(edit_item_request, "active", existing_item.active)
        existing_item.prep_time = getattr(edit_item_request, "prep_time", existing_item.prep_time)
        existing_item.use_as_side = getattr(edit_item_request, "use_as_side", existing_item.use_as_side)
        existing_item.contains_alcohol = getattr(edit_item_request, "contains_alcohol", existing_item.contains_alcohol)
        existing_item.pre_tax_price = getattr(edit_item_request, "pre_tax_price", existing_item.pre_tax_price)
        existing_item.image = getattr(edit_item_request, "image", existing_item.image)
        existing_item.allergen_info = getattr(edit_item_request, "allergen_info", existing_item.allergen_info)
        existing_item.calorie_info = getattr(edit_item_request, "calorie_info", existing_item.calorie_info)
        existing_item.cost = getattr(edit_item_request, "cost", existing_item.cost)
        existing_item.seller_id = getattr(edit_item_request, "seller_id", existing_item.seller_id)
        existing_item.sku_id = getattr(edit_item_request, "sku_id", existing_item.sku_id)
        existing_item.sold_out = getattr(edit_item_request, "sold_out", existing_item.sold_out)
        existing_item.food_type = getattr(edit_item_request, "food_type", existing_item.food_type)
        existing_item.kitchen_id = getattr(edit_item_request, "kitchen_id", existing_item.kitchen_id)

        if hasattr(edit_item_request, "food_type"):
            self._combo_service.mark_food_type_of_combos(
                food_type=edit_item_request.food_type, item_id=item_id)

        entities_to_update = []

        if hasattr(edit_item_request, "side_items"):
            side_items_to_create, side_items_to_update, side_items_to_delete = \
                self._get_side_items_to_create_update_and_delete(
                    item_id, existing_item.side_items, edit_item_request)
            entities_to_update.extend(side_items_to_create + side_items_to_update + side_items_to_delete)

        if hasattr(edit_item_request, "item_customisations"):
            # Note: These item_customisations below are created/updated inside item (Root Object)
            item_customisations_to_create, item_customisations_to_update, item_customisations_to_delete = \
                self._get_item_customisations_to_create_update_and_delete(
                    item_id, existing_item.item_customisations, edit_item_request)
            entities_to_update.extend(item_customisations_to_create +
                                      item_customisations_to_update + item_customisations_to_delete)

        if hasattr(edit_item_request, "item_variants"):
            item_variants_to_create, item_variants_to_update, item_variants_to_delete = \
                self._get_item_variants_to_create_update_and_delete(
                    item_id, existing_item.item_variants, edit_item_request)

            item_customisation_to_create, item_customisation_to_update, item_customisation_to_delete \
                = self._get_item_customisation_in_item_variants_to_create_update_and_delete(
                edit_item_request, existing_item_variant_item_customisation)
            item_variants_variant_association_to_create, item_variants_variant_association_to_update, \
            item_variants_variant_association_to_delete = \
                self._get_item_variants_variant_association_to_create_update_and_delete(edit_item_request,
                                                                                        existing_associations)

            entities_to_update.extend(item_variants_to_create + item_variants_to_update)
            entities_to_update.extend(item_customisation_to_delete)
            entities_to_update.extend(item_variants_variant_association_to_delete)
            entities_to_update.extend(item_variants_to_delete)
            entities_to_update.extend(item_customisation_to_create)
            entities_to_update.extend(item_customisation_to_update)
            entities_to_update.extend(item_variants_variant_association_to_create)
            entities_to_update.extend(item_variants_variant_association_to_update)

            if not (item_variants_to_create + item_variants_to_update):
                if existing_item.sku_id:
                    sku = self.__sku_service.update_sku_for_item(
                        sku_category_code=existing_item.sku_category_code, item=existing_item)
                else:
                    sku = self.__sku_service.create_sku_for_item(
                        sku_category_code=existing_item.sku_category_code, item=existing_item
                    )

                existing_item.sku_id = sku.id

            if item_variants_to_create:
                created_item_variant_skus = self.__sku_service.create_sku_for_item_variants(
                    item_variants=item_variants_to_create, item_name=existing_item.name, seller_id=seller.seller_id)

                for item_variant, created_sku in zip(item_variants_to_create, created_item_variant_skus):
                    item_variant.sku_id = created_sku.id

            if item_variants_to_update:
                self.__sku_service.update_sku_for_item_variants(item_variants=item_variants_to_update)

            if item_variants_to_delete:
                sku_ids_to_delete = [item_variant.sku_id for item_variant in item_variants_to_delete]
                self.__sku_service.disable_skus(sku_ids_list=sku_ids_to_delete)

        elif not existing_item.item_variants and existing_item.sku_id:
            self.__sku_service.update_sku_for_item(
                sku_category_code=existing_item.sku_category_code, item=existing_item)

        self.__item_repository._update_all(entities_to_update)
        updated_item = self.__item_repository._update(existing_item)
        message = ItemWrapper(updated_item, created=False, deleted=False).get_json()
        self.publish_item_message(message)
        return updated_item

    @staticmethod
    def _get_item_customisations_to_create_update_and_delete(item_id, existing_item_customisations,
                                                             edit_item_request):

        item_customisations = [ItemCustomisation(id=item_customisation.item_customisation_id, item_id=item_id,
                                                 display_order=index, variant_id=item_customisation.variant_id,
                                                 cost=item_customisation.cost,
                                                 delta_price=item_customisation.delta_price)
                               for index, item_customisation in enumerate(edit_item_request.item_customisations)]
        item_customisations_to_create = [
            item_customisation for item_customisation in item_customisations if not item_customisation.id]
        item_customisations_to_update = [
            item_customisation for item_customisation in item_customisations if item_customisation.id]
        item_customisation_ids_to_update = [item_customisation.id for item_customisation in item_customisations]
        item_customisations_to_delete = [item_customisation.delete(
        ) for item_customisation in existing_item_customisations if item_customisation.id not in
                                                                    item_customisation_ids_to_update]

        return item_customisations_to_create, item_customisations_to_update, item_customisations_to_delete

    def _get_side_items_to_create_update_and_delete(self, item_id, existing_side_items, edit_item_request):
        side_item_ids = [side_item.item_id for side_item in edit_item_request.side_items]
        side_item_variant_ids = [
            side_item.item_variant_id for side_item in edit_item_request.side_items if side_item.item_variant_id]
        if not self.__item_repository.check_all_side_items_exists(edit_item_request.seller_id, side_item_ids,
                                                                  side_item_variant_ids):
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context={
                "side_items": ["Side Items ids are either duplicate, doesn't exist or can't be used as side items"]})
        side_items = [SideItem(id=side_item.side_item_id, item_id=item_id,
                               side_item_variant_id=side_item.item_variant_id, side_item_id=side_item.item_id)
                      for side_item in edit_item_request.side_items]
        side_items_to_create = [side_item for side_item in side_items if not side_item.id]
        side_items_to_update = [side_item for side_item in side_items if side_item.id]
        side_item_ids_to_update = [side_item.id for side_item in side_items_to_update]
        side_items_to_delete = [side_item.delete()
                                for side_item in existing_side_items if side_item.id not in side_item_ids_to_update]

        return side_items_to_create, side_items_to_update, side_items_to_delete

    @staticmethod
    def _get_item_variants_variant_association_to_create_update_and_delete(edit_item_request,
                                                                           existing_associations):

        item_variants_variant_association_to_create = []
        item_variants_variant_association_to_update = []
        for item_variant in edit_item_request.item_variants:
            if item_variant.item_variant_id:
                for association in item_variant.variants:
                    new_association = ItemVariantVariantAssociation(
                        variant_id=association.variant_id, item_variant_id=item_variant.item_variant_id)
                    item_variants_variant_association_to_create.append(new_association)

        item_variants_variant_association_ids_to_update = [
            association.id for association in item_variants_variant_association_to_update]
        item_variants_variant_association_to_delete = [association.delete(
        ) for association in existing_associations if association.id not in
                                                      item_variants_variant_association_ids_to_update]
        return item_variants_variant_association_to_create, item_variants_variant_association_to_update, \
               item_variants_variant_association_to_delete

    @staticmethod
    def _get_item_customisation_in_item_variants_to_create_update_and_delete(edit_item_request,
                                                                             existing_item_customisations):
        item_customisation_to_create = []
        item_customisation_to_update = []

        for item_variant in edit_item_request.item_variants:
            if item_variant.item_variant_id:
                for index, item_customisation in enumerate(item_variant.item_customisations):
                    if not item_customisation.item_customisation_id:
                        new_item_customisation = ItemCustomisation(
                            item_variant_id=item_variant.item_variant_id, display_order=index,
                            cost=item_customisation.cost, delta_price=item_customisation.delta_price,
                            variant_id=item_customisation.variant_id)
                        item_customisation_to_create.append(new_item_customisation)
                    else:
                        updated_item_customisation = ItemCustomisation(
                            id=item_customisation.item_customisation_id, display_order=index,
                            cost=item_customisation.cost, delta_price=item_customisation.delta_price,
                            variant_id=item_customisation.variant_id)
                        item_customisation_to_update.append(updated_item_customisation)

        item_customisation_ids_to_update = [
            item_customisation.id for item_customisation in item_customisation_to_update]
        item_customisation_to_delete = [item_customisation.delete(
        ) for item_customisation in existing_item_customisations if item_customisation.id not in
                                                                    item_customisation_ids_to_update]
        return item_customisation_to_create, item_customisation_to_update, item_customisation_to_delete

    @staticmethod
    def _get_item_variants_to_create_update_and_delete(item_id, existing_item_variants,
                                                       edit_item_request):
        item_variants = [ItemVariant(id=item_variant.item_variant_id, item_id=item_id, display_order=index,
                                     pre_tax_price=item_variant.pre_tax_price,
                                     name=item_variant.name,
                                     cost=item_variant.cost, sku_category_code=item_variant.sku_category_code)
                         for index, item_variant in enumerate(edit_item_request.item_variants)]
        item_variants_to_create = [ItemVariant(id=item_variant.item_variant_id, item_id=item_id, display_order=index,
                                               pre_tax_price=item_variant.pre_tax_price,
                                               name=item_variant.name,
                                               cost=item_variant.cost,
                                               sku_category_code=item_variant.sku_category_code,
                                               variants=[ItemVariantVariantAssociation(
                                                   variant_id=variant.variant_id) for variant in item_variant.variants],
                                               item_customisations=[ItemCustomisation(
                                                   display_order=index,
                                                   cost=item_customisation.cost,
                                                   delta_price=item_customisation.delta_price,
                                                   variant_id=item_customisation.variant_id)
                                                   for item_customisation in
                                                   item_variant.item_customisations])
                                   for index, item_variant in enumerate(edit_item_request.item_variants) if not
                                   item_variant.item_variant_id]
        item_variants_to_update = [item_variant for item_variant in item_variants if item_variant.id]
        item_variant_ids_to_update = [item_variant.id for item_variant in item_variants_to_update]
        item_variants_to_delete = [item_variant.delete(
        ) for item_variant in existing_item_variants if item_variant.id not in item_variant_ids_to_update]

        return item_variants_to_create, item_variants_to_update, item_variants_to_delete

    @atomic_operation
    def create_variant_group(self, create_variant_group_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(
            seller_id=create_variant_group_request.seller_id)
        variant_group = VariantGroup(
            name=create_variant_group_request.name, display_name=create_variant_group_request.display_name,
            can_select_multiple=create_variant_group_request.can_select_multiple,
            can_select_quantity=create_variant_group_request.can_select_quantity,
            minimum_selectable_quantity=create_variant_group_request.minimum_selectable_quantity,
            maximum_selectable_quantity=create_variant_group_request.maximum_selectable_quantity,
            is_customisation=create_variant_group_request.is_customisation,
            seller_id=create_variant_group_request.seller_id, )

        if create_variant_group_request.variants:
            variant_group.variants = [Variant(name=variant.name, display_order=index, variant_group_id=variant)
                                      for index, variant in enumerate(create_variant_group_request.variants)]

        return self.__item_repository.persist(variant_group)

    @atomic_operation
    def edit_variant_group(self, variant_group_id, edit_variant_group_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(
            seller_id=edit_variant_group_request.seller_id)
        existing_variant_group = self.__item_repository.load_variant_group_for_update(
            variant_group_id=variant_group_id, seller_id=edit_variant_group_request.seller_id)

        existing_variant_group.name = getattr(edit_variant_group_request, "name", existing_variant_group.name)
        existing_variant_group.display_name = getattr(
            edit_variant_group_request, "display_name", existing_variant_group.display_name)
        existing_variant_group.can_select_multiple = getattr(
            edit_variant_group_request, "can_select_multiple", existing_variant_group.can_select_multiple)
        existing_variant_group.can_select_quantity = getattr(
            edit_variant_group_request, "can_select_quantity", existing_variant_group.can_select_quantity)
        existing_variant_group.minimum_selectable_quantity = getattr(
            edit_variant_group_request, "minimum_selectable_quantity",
            existing_variant_group.minimum_selectable_quantity)
        existing_variant_group.maximum_selectable_quantity = getattr(
            edit_variant_group_request, "maximum_selectable_quantity",
            existing_variant_group.maximum_selectable_quantity)
        existing_variant_group.is_customisation = getattr(
            edit_variant_group_request, "is_customisation", existing_variant_group.is_customisation)
        existing_variant_group.seller_id = getattr(
            edit_variant_group_request, "seller_id", existing_variant_group.seller_id)

        entities_to_update = []

        if hasattr(edit_variant_group_request, "variants"):
            variants_to_create, variants_to_update, variants_to_delete = self._get_variants_to_create_update_and_delete(
                variant_group_id, existing_variant_group.variants, edit_variant_group_request)

            entities_to_update.extend(variants_to_create + variants_to_update + variants_to_delete)

        self.__item_repository._update_all(entities_to_update)
        return self.__item_repository._update(existing_variant_group)

    def _get_variants_to_create_update_and_delete(self, variant_group_id, existing_variants,
                                                  edit_variant_group_request):
        variants = [Variant(id=variant.variant_id, variant_group_id=variant_group_id, display_order=index,
                            name=variant.name) for index, variant in enumerate(edit_variant_group_request.variants)]
        variants_to_create = [variant for variant in variants if not variant.id]
        variants_to_update = [variant for variant in variants if variant.id]
        variant_ids_to_update = [variant.id for variant in variants_to_update]
        variants_to_delete = [variant.delete()
                              for variant in existing_variants if variant.id not in variant_ids_to_update]

        return variants_to_create, variants_to_update, variants_to_delete

    def publish_item_message(self, message):
        self._messaging_service.publish_item_message(message)
