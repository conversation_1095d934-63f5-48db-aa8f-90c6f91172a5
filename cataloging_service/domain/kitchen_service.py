import json

from cataloging_service.constants import error_codes
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.models import Kitchen


class KitchenService:
    def __init__(self, kitchen_repository):
        self._kitchen_repository = kitchen_repository

    @atomic_operation
    def create(self, kitchen_create_request):
        return self._kitchen_repository.persist(Kitchen(name=kitchen_create_request.name,
                                                     property_id=kitchen_create_request.property_id,
                                                     config=kitchen_create_request.config))

    @atomic_operation
    def update(self, kitchen_id, kitchen_update_request):
        existing_kitchen = self._kitchen_repository.load_for_update(kitchen_id)
        if not existing_kitchen:
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context={
                "name": ["Kitchen ID is not valid"]})

        existing_kitchen.name = getattr(kitchen_update_request, "name", existing_kitchen.name)
        existing_kitchen.config = getattr(kitchen_update_request, "config", existing_kitchen.config)
        return self._kitchen_repository._update(existing_kitchen)

    @atomic_operation
    def soft_delete(self, kitchen_id, property_id):
        existing_kitchen = self._kitchen_repository.load_for_update(kitchen_id)
        if not existing_kitchen:
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context={
                "name": ["Kitchen ID is not valid"]})
        existing_kitchen.delete()
        return self._kitchen_repository._update(existing_kitchen)

    def get_kitchen(self, kitchen_id):
        return self._kitchen_repository.get_kitchen(kitchen_id=kitchen_id)

    def get_kitchens_in_property(self, property_id):
        return self._kitchen_repository.get_kitchens(property_id=property_id)
