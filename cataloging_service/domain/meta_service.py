from cataloging_service.constants import model_choices, error_codes
from cataloging_service.exceptions import CatalogingServiceException


class MetaService:
    def __init__(self, meta_repository):
        self.__meta_repository = meta_repository

    def test_db_connection(self):
        return self.__meta_repository.get_db_time()

    def get_cuisines(self):
        return self.__meta_repository.get_all_cuisines()

    def get_guest_types(self):
        return self.__meta_repository.get_all_guest_types()

    def get_meta_dictionary(self):
        return model_choices.get_choices()

    def sget_all_entities(self):
        return self.__meta_repository.rget_all_entities()

    def sget_entity_params(self, entity, field, validate):
        return self.__meta_repository.rget_entity_param(entity, field, validate)

    def sget_entity_param_by_key_val(self, entity, field, value, validate):
        param = self.__meta_repository.rget_entity_param_by_key_val(entity, field, value, validate)
        if not param:
            raise CatalogingServiceException(error_codes.PARAM_NOT_FOUND, context='param value: %s' % value,
                                             send_error_mail=False)

        return param

    def sget_bundle_rule(self):
        """
        TODO add caching here, see if dogpile cache can be used with timeout or go for custom implementation
        :return:
        """
        params = self.__meta_repository.rget_entity_param('Sku', 'bundle_rule', True)
        if not params or not params[0].value:
            raise CatalogingServiceException(error_codes.METADATA_NOT_FOUND,
                                             context='metadata name: %s' % 'bundle_rule')
        return params[0]

