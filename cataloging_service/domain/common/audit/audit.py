from cataloging_service.audit_extension import audit_manager
from cataloging_service.models import TenantConfigModel, User, Role, AvailableConfigModel, UserDefinedEnumModel, \
    SystemProperty, MigrationDetail, Notification, Param, PropertyPolicy, PropertyPolicyMap, GlobalPolicy, \
    GoogleDriveBaseFolder, GoogleDriveFile, PropertyVideo, PropertyImage, Country, Region, Cluster, State, City, \
    MicroMarket, Locality, Location, CityAlias, RoomType, RoomTypeConfiguration, Room, RoomRackRateModel, BankDetail, \
    PropertyDetail, GuestFacingProcess, GuestType, Owner, Landmark, Description, NeighbouringPlace, TransportStation, \
    TransportStationProperty, PropertyLandmark, PropertySku, PropertiesSkuCategories, SkuActivation, Ownership, \
    GuestTypeProperty, Property, OtaProperty, RatePlanConfiguration, OTA, RatePlan, SkuBundle, SkuAttribute, Channel, \
    SubChannel, Application, Provider, ProviderRoomTypeMapping, PricingMapping, PricingPolicy, Brand, \
    ProviderBrandMapping, RuptubLegalEntityDetails, CurrencyConversionRateModel, Addon, \
    RatePlanAddon, NewRatePlanConfig, SellerCategory, MenuCategory, SellerSku, RestaurantTable, SellerSkuCategory, \
    Kitchen, Seller, NewRatePlan, Sku, SkuCategory, AmenityPublicWashroom, AmenityElevator, AmenityParking, \
    AmenityDisableFriendly, AmenitySwimmingPool, AmenityGym, AmenityPrivateCab, AmenitySpa, AmenityLaundry, \
    AmenityBreakfast, AmenityPayment, Cuisine, PropertyAmenity, BanquetHall, AmenityIntercom, AmenityHotWater, \
    AmenityTV, AmenityAC, AmenityHeater, AmenityStove, AmenityTwinBed, RoomAmenity, AmenitySummary, FacilityCategory, \
    FacilityCategoryMapping, Restaurant, Bar


def attach_auditing():
    """
    Attach auditing listeners to all relevant models.
    """
    models = [
        TenantConfigModel,
        User,
        Role,
        AvailableConfigModel,
        UserDefinedEnumModel,
        SystemProperty,
        MigrationDetail,
        Notification,
        Param,
        PropertyPolicy,
        PropertyPolicyMap,
        GlobalPolicy,
        GoogleDriveBaseFolder,
        GoogleDriveFile,
        PropertyImage,
        PropertyVideo,
        Country,
        Region,
        Cluster,
        State,
        City,
        MicroMarket,
        Locality,
        Location,
        CityAlias,
        RoomType,
        Room,
        RoomTypeConfiguration,
        RoomRackRateModel,
        Property,
        BankDetail,
        PropertyDetail,
        GuestFacingProcess,
        GuestType,
        GuestTypeProperty,
        Owner,
        Landmark,
        Description,
        NeighbouringPlace,
        Ownership,
        TransportStation,
        TransportStationProperty,
        PropertyLandmark,
        PropertySku,
        PropertiesSkuCategories,
        SkuActivation,
        OtaProperty,
        OTA,
        RatePlanConfiguration,
        RatePlan,
        Sku,
        SkuCategory,
        SkuBundle,
        SkuAttribute,
        Channel,
        SubChannel,
        Application,
        Provider,
        ProviderRoomTypeMapping,
        PricingMapping,
        PricingPolicy,
        Brand,
        ProviderBrandMapping,

        RuptubLegalEntityDetails,
        CurrencyConversionRateModel,
        Addon,
        RatePlanAddon,
        NewRatePlanConfig,
        NewRatePlan,
        SellerCategory,
        Seller,
        MenuCategory,
        SellerSku,
        RestaurantTable,
        Kitchen,
        SellerSkuCategory,
        AmenityPublicWashroom,
        AmenityElevator,
        AmenityParking,
        AmenityDisableFriendly,
        AmenitySwimmingPool,
        AmenityGym,
        AmenityPrivateCab,
        AmenitySpa,
        AmenityLaundry,
        AmenityBreakfast,
        AmenityPayment,
        Cuisine,
        PropertyAmenity,
        Bar,
        Restaurant,
        BanquetHall,
        AmenityIntercom,
        AmenityHotWater,
        AmenityTV,
        AmenityAC,
        AmenityHeater,
        AmenityStove,
        AmenityTwinBed,
        RoomAmenity,
        AmenitySummary,
        FacilityCategory,
        FacilityCategoryMapping
    ]
    audit_manager.attach_auditing_listeners(models)
