from cataloging_service.exceptions import InvalidStateIdException


class LegalEntityDetailsService:
    def __init__(self, legal_entity_details_repository):
        self.__legal_entity_details_repository = legal_entity_details_repository

    def sget_all_legal_entities(self):
        return self.__legal_entity_details_repository.get_all_legal_entities()

    def sget_legal_entity_details_by_state_id(self, state_id):
        legal_entity = self.__legal_entity_details_repository.get_legal_entity_details_by_state_id(state_id)
        if not legal_entity:
            raise InvalidStateIdException("No legal entity details found for state id: %s" % state_id)
        return legal_entity
