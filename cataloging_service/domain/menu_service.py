import json
import datetime
import calendar
from pytz import timezone
from treebo_commons.utils import dateutils
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.constants import error_codes

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.models import Menu, MenuTiming, RestaurantMenuCategory, MenuItem, Item, MenuCombo,\
    MenuItemCategory, MenuComboCategory, MenuItemItemVariant, MenuItemSideItem, MenuItemItemCustomisation
from cataloging_service.infrastructure.messaging.messaging_wrappers import MenuWrapper


class MenuService:
    def __init__(self, menu_repository, seller_service, item_service, messaging_service):
        self.__menu_repository = menu_repository
        self._seller_service = seller_service
        self._item_service = item_service
        self._messaging_service = messaging_service

    def get_menus(self, seller_id, filters, menu_ids):
        is_active = filters.get('is_active', False)
        active_at = filters.get('active_at', None)

        menus = self.__menu_repository.get_all_menus(seller_id=seller_id, name=filters.get('name', ''),
                                                     menu_type=filters.get('menu_type', ''),
                                                     menu_ids=menu_ids)

        if is_active or active_at:
            return self.get_active_menus(menus=menus, seller_id=seller_id, active_at=active_at)
        return menus

    def get_menu_items(self, seller_id, parsed_request):
        menu_items = self.__menu_repository.get_menu_items(
            seller_id=seller_id, name=parsed_request.get("name"), menu_type=parsed_request.get("menu_type"))
        active_at = parsed_request.get("active_at")
        if active_at:
            return self._get_active_menu_items(seller_id, menu_items, active_at)
        return menu_items

    def get_menu_item(self, seller_id, menu_item_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        menu_item = self.__menu_repository.load_menu_item(menu_item_id=menu_item_id)
        if not menu_item:
            raise CatalogingServiceException(error_codes.MENU_ITEM_NOT_FOUND, context={
                "code": ["Menu item not found"]})

        return menu_item

    def _get_active_menu_items(self, seller_id, menu_items, active_at):
        active_menu_items = []
        seller = self._seller_service.get_seller(seller_id=seller_id)
        seller_timezone = timezone(seller.timezone)
        for menu_item in menu_items:
            if len(menu_item.menu.menu_timings) == 0:
                active_menu_items.append(menu_item)
                continue

            if self.is_menu_active(menu_timings=menu_item.menu.menu_timings, active_at=active_at,
                                   timezone=seller_timezone):
                active_menu_items.append(menu_item)
        return active_menu_items

    def get_menu(self, menu_id):
        return self.__menu_repository.get_menu(menu_id=menu_id)

    def get_menus_with_categories_and_item_counts(self, seller_id, menu_ids):
        return self.__menu_repository.get_menus_with_categories_and_item_counts(seller_id=seller_id, menu_ids=menu_ids)

    def get_active_menus(self, menus, seller_id, active_at=None):
        active_menus = []
        seller = self._seller_service.get_seller(seller_id=seller_id)

        seller_timezone = timezone(seller.timezone)

        if not active_at:
            active_at = dateutils.current_datetime(timezone=seller_timezone)

        for menu in menus:
            if len(menu.menu_timings) == 0:
                active_menus.append(menu)
                continue

            if self.is_menu_active(menu_timings=menu.menu_timings, active_at=active_at,
                                   timezone=seller_timezone):
                active_menus.append(menu)

        return active_menus

    def is_menu_active(self, menu_timings, active_at, timezone):
        current_day_of_week = active_at.weekday()
        applicable_days = [calendar.day_abbr[current_day_of_week].upper()]

        if current_day_of_week == 0:
            applicable_days.append(calendar.day_abbr[6].upper())
        else:
            applicable_days.append(calendar.day_abbr[current_day_of_week - 1].upper())

        for menu_timing in menu_timings:
            days = json.loads(menu_timing.days.decode())

            for day in days:
                if day not in applicable_days:
                    continue

                start_date = datetime.datetime.strptime(
                    str(active_at.year) + "-" + str(active_at.strftime("%W")) + "-" + day, "%Y-%W-%a")

                start_datetime = datetime.datetime.combine(start_date, menu_timing.start_time)
                end_datetime = datetime.datetime.combine(start_date, menu_timing.end_time)

                start_datetime = timezone.localize(start_datetime)
                end_datetime = timezone.localize(end_datetime)

                if menu_timing.start_time >= menu_timing.end_time:
                    end_datetime = dateutils.add(end_datetime, days=1)

                if start_datetime <= active_at <= end_datetime:
                    return True

        return False

    def check_menu_code_exists(self, seller_id, code, menu_id=None):
        seller = self._seller_service.get_seller(seller_id)
        existing_menus = self.__menu_repository.get_menus_in_property(property_id=seller.property_id, menu_id=menu_id, code=code)
        if existing_menus:
            raise CatalogingServiceException(error_codes.DUPLICATE_MENU_CODE, context={
                "code": ["Menu code is duplicate"]})

    @atomic_operation
    def soft_delete_menu(self, seller_id, menu_id):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        existing_menu = self.__menu_repository.load_for_update(menu_id, seller_id)
        existing_menu.delete()
        updated_menu = self.__menu_repository._update(existing_menu)
        message = MenuWrapper(updated_menu, created=False, deleted=True).get_json()
        self.publish_menu_message(message)
        return updated_menu

    @atomic_operation
    def create_menu(self, create_menu_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=create_menu_request.seller_id)
        self.check_menu_code_exists(seller_id=create_menu_request.seller_id, code=create_menu_request.code)
        menu_object = Menu(name=create_menu_request.name, code=create_menu_request.code, 
                           display_name=create_menu_request.display_name, description=create_menu_request.description, 
                           seller_id=create_menu_request.seller_id, menu_types=create_menu_request.menu_types,
                           menu_timings=[self._create_menu_timing(menu_timing=timing)
                                         for timing in create_menu_request.menu_timings])
        saved_menu = self.__menu_repository.persist(menu_object)
        message = MenuWrapper(saved_menu, created=True, deleted=False).get_json()
        self.publish_menu_message(message)
        return saved_menu

    def _create_menu_timing(self, menu_timing, menu_id=None):
        encoded_days = str.encode(json.dumps(menu_timing.days))
        return MenuTiming(
            id=menu_timing.menu_timing_id, days=encoded_days, start_time=menu_timing.start_time,
            end_time=menu_timing.end_time, menu_id=menu_id
        )

    @atomic_operation
    def edit_menu(self, menu_id, seller_id, edit_menu_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        self.check_menu_code_exists(seller_id=edit_menu_request.seller_id, menu_id=menu_id, code=edit_menu_request.code)
        existing_menu = self.__menu_repository.load_for_update(menu_id, seller_id)
        if not existing_menu:
            return

        menu = Menu(id=menu_id, name=edit_menu_request.name if hasattr(edit_menu_request, "name") else existing_menu.name,
                    code=edit_menu_request.code if hasattr(
                        edit_menu_request, "code") else existing_menu.code,
                    display_name=edit_menu_request.display_name if hasattr(
                        edit_menu_request, "display_name") else existing_menu.display_name,
                    description=edit_menu_request.description if hasattr(
                        edit_menu_request, "description") else existing_menu.description,
                    menu_types=edit_menu_request.menu_types if hasattr(edit_menu_request, "menu_types") else existing_menu.menu_types,)
        entities_to_update = []
        menu_category_ids_to_delete = []

        if hasattr(edit_menu_request, "menu_categories"):
            categories_to_create, categories_to_update, categories_to_delete = self._get_menu_categories_to_create_update_and_delete(
                menu_id=menu_id, existing_menu_categories=existing_menu.menu_categories, edit_menu_request=edit_menu_request)
            entities_to_update.extend(categories_to_update + categories_to_create + categories_to_delete)
            menu_category_ids_to_delete = [category.id for category in categories_to_delete]

        if hasattr(edit_menu_request, "menu_items"):
            menu_items_to_create, menu_items_to_update, menu_items_to_delete, menu_item_categories_to_create_and_delete = self._get_menu_items_to_process(
                menu_id=menu_id, existing_menu_items=existing_menu.menu_items,
                menu_category_ids_to_delete=menu_category_ids_to_delete, edit_menu_request=edit_menu_request)

            if menu_items_to_create:
                item_ids = [menu_item.item_id for menu_item in menu_items_to_create if not menu_item.item_variant_id]
                items = self._item_service.get_all_items_and_children(seller_id=seller_id, item_ids=item_ids)
                items.sort(key=lambda x: x.id)
                menu_items_to_create.sort(key=lambda x: x.item_id)

                for item, menu_item in zip(items, menu_items_to_create):
                    menu_item.menu_item_item_variants = [MenuItemItemVariant(
                        item_variant_id=item_variant.id) for item_variant in item.item_variants]
                    menu_item.menu_item_item_customisations = [MenuItemItemCustomisation(
                        item_customisation_id=item_customisation.id) for item_customisation in item.item_customisations]
                    menu_item.menu_item_side_items = [MenuItemSideItem(
                        side_item_id=side_item.id) for side_item in item.side_items]
            entities_to_update.extend(menu_items_to_create + menu_items_to_update +
                                      menu_items_to_delete + menu_item_categories_to_create_and_delete)

        if hasattr(edit_menu_request, "menu_timings"):
            menu_timings_to_create, menu_timings_to_update, menu_timings_to_delete = self._get_menu_timings_to_create_update_and_delete(
                menu_id=menu_id, existing_menu_timings=existing_menu.menu_timings, edit_menu_request=edit_menu_request)
            entities_to_update.extend(menu_timings_to_create + menu_timings_to_update + menu_timings_to_delete)

        if hasattr(edit_menu_request, "menu_combos"):
            menu_combos_to_create, menu_combos_to_update, menu_combos_to_delete, menu_combo_categories_to_create_and_delete = self._get_menu_combos_to_create_update_and_delete(
                menu_id=menu_id, existing_menu_combos=existing_menu.menu_combos,
                menu_category_ids_to_delete=menu_category_ids_to_delete, edit_menu_request=edit_menu_request)
            entities_to_update.extend(menu_combos_to_create + menu_combos_to_update +
                                      menu_combos_to_delete + menu_combo_categories_to_create_and_delete)

        self.__menu_repository._update_all(entities_to_update)
        updated_menu = self.__menu_repository._update(menu)
        message = MenuWrapper(updated_menu, created=False, deleted=False).get_json()
        self.publish_menu_message(message)
        return updated_menu

    def _get_menu_items_to_process(self, menu_id, existing_menu_items, menu_category_ids_to_delete, edit_menu_request):
        menu_items = [MenuItem(id=menu_item.menu_item_id, menu_id=menu_id, sold_out=menu_item.sold_out, display_order=index,
                               item_id=menu_item.item_id, item_variant_id=menu_item.item_variant_id)
                      for index, menu_item in enumerate(edit_menu_request.menu_items)]
        menu_items_to_create = [menu_item for menu_item in menu_items if not menu_item.id]
        menu_items_to_update = [menu_item for menu_item in menu_items if menu_item.id]
        menu_items_ids_to_update = [menu_item.id for menu_item in menu_items_to_update]
        menu_items_to_delete = [menu_item.delete()
                                for menu_item in existing_menu_items if menu_item.id not in menu_items_ids_to_update]
        menu_item_categories_to_create_and_delete = []

        for menu_item, existing_menu_item in zip(edit_menu_request.menu_items, existing_menu_items):
            if hasattr(menu_item, "menu_item_categories"):
                menu_item_categories_to_create_and_delete.extend(self._get_menu_item_categories_to_create_and_delete(
                    existing_menu_item_categories=existing_menu_item.menu_item_categories, menu_category_ids_to_delete=menu_category_ids_to_delete, menu_item_categories_request=menu_item.menu_item_categories))
            else:
                menu_item_categories_to_create_and_delete.extend(menu_item_category.delete(
                ) for menu_item_category in existing_menu_item.menu_item_categories
                    if menu_item_category.menu_category_id in menu_category_ids_to_delete)

        return menu_items_to_create, menu_items_to_update, menu_items_to_delete, menu_item_categories_to_create_and_delete

    def _get_menu_categories_to_create_update_and_delete(self, menu_id, existing_menu_categories,  edit_menu_request):
        categories = [RestaurantMenuCategory(id=category.menu_category_id, menu_id=menu_id, name=category.name,
                                             display_order=index) for index, category in enumerate(edit_menu_request.menu_categories)]
        categories_to_create = [category for category in categories if not category.id]
        categories_to_update = [category for category in categories if category.id]
        category_ids_to_update = [category.id for category in categories_to_update]
        categories_to_delete = [category.delete()
                                for category in existing_menu_categories if category.id not in category_ids_to_update]

        return categories_to_create, categories_to_update, categories_to_delete

    def _get_menu_timings_to_create_update_and_delete(self, menu_id, existing_menu_timings, edit_menu_request):
        menu_timings_to_create = [self._create_menu_timing(
            menu_timing, menu_id=menu_id) for menu_timing in edit_menu_request.menu_timings if not menu_timing.menu_timing_id]
        menu_timings_to_update = [self._create_menu_timing(
            menu_timing, menu_id=menu_id) for menu_timing in edit_menu_request.menu_timings if menu_timing.menu_timing_id]
        menu_timing_ids_to_update = [menu_timing.id for menu_timing in menu_timings_to_update]
        menu_timings_to_delete = [menu_timing.delete()
                                  for menu_timing in existing_menu_timings if menu_timing.id not in menu_timing_ids_to_update]

        return menu_timings_to_create, menu_timings_to_update, menu_timings_to_delete

    def _get_menu_combos_to_create_update_and_delete(self, menu_id, existing_menu_combos,
                                                     menu_category_ids_to_delete, edit_menu_request):
        menu_combos = [MenuCombo(id=menu_combo.menu_combo_id, combo_id=menu_combo.combo_id, menu_id=menu_id,
                                 sold_out=menu_combo.sold_out, display_order=index) for index, menu_combo in enumerate(edit_menu_request.menu_combos)]
        menu_combos_to_create = [menu_combo for menu_combo in menu_combos if not menu_combo.id]
        menu_combos_to_update = [menu_combo for menu_combo in menu_combos if menu_combo.id]
        menu_combo_ids_to_update = [menu_combo.id for menu_combo in menu_combos_to_update]
        menu_combos_to_delete = [menu_combo.delete()
                                 for menu_combo in existing_menu_combos if menu_combo.id not in menu_combo_ids_to_update]
        menu_combo_categories_to_create_and_delete = []

        for menu_combo, existing_menu_combo in zip(edit_menu_request.menu_combos, existing_menu_combos):
            if hasattr(menu_combo, "menu_combo_categories"):
                menu_combo_categories_to_create_and_delete.extend(self._get_menu_combo_categories_to_create_and_delete(
                    existing_menu_combo_categories=existing_menu_combo.menu_combo_categories, menu_category_ids_to_delete=menu_category_ids_to_delete,
                    menu_combo_categories_request=menu_combo.menu_combo_categories))
            else:
                menu_combo_categories_to_create_and_delete.extend(menu_combo_category.delete(
                ) for menu_combo_category in existing_menu_combo.menu_combo_categories
                    if menu_combo_category.menu_category_id in menu_category_ids_to_delete)
        return menu_combos_to_create, menu_combos_to_update, menu_combos_to_delete, menu_combo_categories_to_create_and_delete

    def _get_menu_item_categories_to_create_and_delete(self, existing_menu_item_categories,
                                                       menu_category_ids_to_delete, menu_item_categories_request):
        menu_item_categories = [MenuItemCategory(menu_item_id=menu_item_category.menu_item_id,
                                                 menu_category_id=menu_item_category.menu_category_id)
                                for menu_item_category in menu_item_categories_request]
        menu_item_categories_to_create = [
            menu_item_category for menu_item_category in menu_item_categories
            if not menu_item_category.id and menu_item_category.menu_category_id not in menu_category_ids_to_delete]

        menu_item_categories_to_delete = [menu_item_category.delete(
        ) for menu_item_category in existing_menu_item_categories]

        return menu_item_categories_to_create + menu_item_categories_to_delete

    def _get_menu_combo_categories_to_create_and_delete(self, existing_menu_combo_categories,
                                                        menu_category_ids_to_delete, menu_combo_categories_request):
        menu_combo_categories = [MenuComboCategory(menu_combo_id=menu_combo_category.menu_combo_id,
                                                   menu_category_id=menu_combo_category.menu_category_id)
                                 for menu_combo_category in menu_combo_categories_request]
        menu_combo_categories_to_create = [
            menu_combo_category for menu_combo_category in menu_combo_categories if not menu_combo_category.id
            and menu_combo_category.menu_category_id not in menu_category_ids_to_delete]

        menu_combo_categories_to_delete = [menu_combo_category.delete()
                                           for menu_combo_category in existing_menu_combo_categories]

        return menu_combo_categories_to_create + menu_combo_categories_to_delete

    def _get_menu_item_item_variants_to_create_and_delete(self, existing_menu_item_item_variants,
                                                          menu_item_item_variants_request):
        menu_item_item_variants = [MenuItemItemVariant(menu_item_id=menu_item_item_variant.menu_item_id,
                                                       item_variant_id=menu_item_item_variant.item_variant_id)
                                   for menu_item_item_variant in menu_item_item_variants_request]
        menu_item_item_variants_to_create = [
            menu_item_item_variant for menu_item_item_variant in menu_item_item_variants if not menu_item_item_variant.id]

        menu_item_item_variants_to_delete = [menu_item_item_variant.delete(
        ) for menu_item_item_variant in existing_menu_item_item_variants]

        return menu_item_item_variants_to_create, menu_item_item_variants_to_delete

    def __get_menu_item_side_items_to_create_and_delete(self, existing_menu_item_side_items,
                                                        menu_item_side_items_request):
        menu_item_side_items = [MenuItemSideItem(menu_item_id=menu_item_side_item.menu_item_id,
                                                 side_item_id=menu_item_side_item.side_item_id) for menu_item_side_item in menu_item_side_items_request]
        menu_item_side_items_to_create = [
            menu_item_side_item for menu_item_side_item in menu_item_side_items if not menu_item_side_item.id]

        menu_item_side_items_to_delete = [menu_item_side_item.delete(
        ) for menu_item_side_item in existing_menu_item_side_items]

        return menu_item_side_items_to_create, menu_item_side_items_to_delete

    def __get_menu_item_item_customisations_to_create_and_delete(self, existing_menu_item_item_customisations,
                                                                 menu_item_item_customisations_request):
        menu_item_item_customisations = [MenuItemItemCustomisation(menu_item_id=menu_item_item_customisation.menu_item_id,
                                                                   item_customisation_id=menu_item_item_customisation.item_customisation_id)
                                         for menu_item_item_customisation in menu_item_item_customisations_request]
        menu_item_item_customisations_to_create = [
            menu_item_item_customisation for menu_item_item_customisation in menu_item_item_customisations if not menu_item_item_customisation.id]

        menu_item_item_customisations_to_delete = [menu_item_item_customisation.delete(
        ) for menu_item_item_customisation in existing_menu_item_item_customisations]

        return menu_item_item_customisations_to_create, menu_item_item_customisations_to_delete

    @atomic_operation
    def edit_menu_combo(self, seller_id, menu_id, menu_combo_id, menu_combo_edit_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        menu_combo = self.__menu_repository.load_menu_combo_for_update(menu_combo_id=menu_combo_id, menu_id=menu_id)
        menu_combo = MenuCombo(id=menu_combo_id, combo_id=menu_combo_edit_request.combo_id, menu_id=menu_id,
                               sold_out=menu_combo_edit_request.sold_out if hasattr(menu_combo_edit_request, "sold_out") else menu_combo.sold_out)
        menu_combo_categories_to_create_and_delete = self._get_menu_combo_categories_to_create_and_delete(
            existing_menu_combo_categories=menu_combo.menu_combo_categories, menu_category_ids_to_delete=[],
            menu_combo_categories_request=menu_combo_edit_request.menu_combo_categories)

        menu_combo.menu_combo_categories.extend(menu_combo_categories_to_create_and_delete)

        return self.__menu_repository._update(menu_combo)

    @atomic_operation
    def edit_menu_item(self, seller_id, menu_item_id, menu_item_edit_request):
        self._seller_service.check_and_raise_error_if_seller_does_not_exist(seller_id=seller_id)
        existing_menu_item = self.__menu_repository.load_menu_item_for_update(menu_item_id=menu_item_id)
        if not existing_menu_item:
            return
        entities_to_update = []

        menu_item = MenuItem(id=menu_item_id, item_id=menu_item_edit_request.item_id if hasattr(menu_item_edit_request, "item_id")
                             else existing_menu_item.item_id, menu_id=existing_menu_item.menu_id,
                             sold_out=menu_item_edit_request.sold_out if hasattr(
                                 menu_item_edit_request, "sold_out") else existing_menu_item.sold_out,
                             item_variant_id=menu_item_edit_request.item_variant_id if hasattr(menu_item_edit_request, "item_variant_id")
                             else existing_menu_item.item_variant_id)

        if hasattr(menu_item_edit_request, "menu_item_item_variants"):
            menu_item_item_variants_to_create, menu_item_item_variants_to_delete = self._get_menu_item_item_variants_to_create_and_delete(
                existing_menu_item_item_variants=existing_menu_item.menu_item_item_variants,
                menu_item_item_variants_request=menu_item_edit_request.menu_item_item_variants)
            entities_to_update.extend(menu_item_item_variants_to_create + menu_item_item_variants_to_delete)

        if hasattr(menu_item_edit_request, "menu_item_side_items"):
            menu_item_side_items_to_create, menu_item_side_items_to_delete = self.__get_menu_item_side_items_to_create_and_delete(
                existing_menu_item_side_items=existing_menu_item.menu_item_side_items,
                menu_item_side_items_request=menu_item_edit_request.menu_item_side_items)
            entities_to_update.extend(menu_item_side_items_to_create + menu_item_side_items_to_delete)

        if hasattr(menu_item_edit_request, "menu_item_item_customisations"):
            menu_item_item_customisations_to_create,  menu_item_item_customisations_to_delete = self.__get_menu_item_item_customisations_to_create_and_delete(
                existing_menu_item_item_customisations=existing_menu_item.menu_item_item_customisations,
                menu_item_item_customisations_request=menu_item_edit_request.menu_item_item_customisations)
            entities_to_update.extend(
                menu_item_item_customisations_to_create + menu_item_item_customisations_to_delete)

        self.__menu_repository._update_all(entities_to_update)
        return self.__menu_repository._update(menu_item)

    def publish_menu_message(self, message):
        self._messaging_service.publish_menu_message(message)
