document.addEventListener("DOMContentLoaded", function(event) {
  var fileInput = document.getElementById("property_file_id");
  var fileSizeWarningBanner = document.getElementById("fileSizeWarningBanner");
  fileSizeWarningBanner.style.visibility = 'hidden';
  fileInput.addEventListener("change", function () {
    if (fileInput.files.length > 0) {
        const fileSize = fileInput.files.item(0).size;
        const fileMb = fileSize / 1024 ** 2;
        if (fileMb >= 60) {
            fileSizeWarningBanner.style.visibility = 'visible';
            fileInput.value = null;
        } else {
            fileSizeWarningBanner.style.visibility = 'hidden';
        }
    }
  })
})
