$(document).ready(function() {

    $('.ui.dropdown').dropdown();

    $('#add-user').on('click', function(e) {
		e.preventDefault();
		var elem = $(".user_role_detail").last();
		var new_elem = $(elem).clone();
		$(new_elem).insertAfter(elem);
		$(new_elem).find('.ui.dropdown').dropdown();
		$('.remove-user-button').on('click', function(e) {
			e.preventDefault();
			remove_user(this);
		});
	});

	$('.remove-user-button').on('click', function(e) {
		e.preventDefault();
		remove_user(this);
	});

	function remove_user(self) {
		if ($(".user_role_detail").length == 1) {
			$('body').toast({
				class: 'error',
				message: "Cannot remove"
			});
			return false;
		}
		$(self).parent().parent().remove();
	}

	$("#user-role-creation-form").submit(function(ev) {
		ev.preventDefault();
		var data = {};

		var user_role_details = [];
		Array.from($(".user_role_detail")).forEach(function(ele) {
			var user_role_detail = {};
			$(ele).find(":input").serializeArray().forEach(function(val) {
				user_role_detail[val['name']] = val['value'];
			});
			user_role_details.push(user_role_detail);
		});

		data['user_role_details'] = user_role_details;
		$.ajax({
			type: "POST",
			dataType: 'json',
			data: JSON.stringify(data),
			contentType: 'application/json',
			success: function(result) {
				$('body').toast({
					class: 'success',
					message: 'Role assigned'
				});
			},
			error: function(xhr, resp, text) {
				console.log(xhr, resp, text);
				xhr.responseJSON.errors.forEach(function(val) {
					$('body').toast({
						class: 'error',
						title: text,
						message: JSON.stringify(val['title'])
					});
				});

			}
		})
	});
});