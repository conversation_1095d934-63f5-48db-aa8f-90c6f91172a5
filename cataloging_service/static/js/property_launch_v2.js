$(document).ready(function() {

    function toTitleCase(str) {
        return str.replace(/\w\S*/g, function(txt){
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        });
    }

    $('#property_name').keyup(function() {
            this.value = toTitleCase(this.value);
     });

	$('.locality-search').search({
		apiSettings: {
			url: '/api/v3/localities/?q={query}'
		},
		fields: {
			results: 'data',
			title: 'display_name',
			description: 'micro_market',
		},
		minCharacters: 1,
		showNoResults: true,
		onSelect: function(selected_result) {
			$(this).find(':input').first().attr("data-id", selected_result['id']);
		}
	});

	$('.city-search').search({
		apiSettings: {
			url: '/api/v3/cities/?q={query}'
		},
		fields: {
			results: 'data',
			title: 'name',
			description: 'state',
		},
		minCharacters: 3,
		showNoResults: true,
		onSelect: function(selected_result) {
			$(this).find(':input').first().val(selected_result['id']);
		}
	});

    $("input[name='address_city']").focus(
    function(selected_result) {
			$(this).attr('autocomplete', 'new-password');
		});

    $("input[name='legal_address_city']").focus(
    function(selected_result) {
			$(this).attr('autocomplete', 'new-password');
		});

	$('.date_calendar').calendar({
		type: 'date',
    formatter: {
      date: function (date, settings) {
        if (!date) return '';
        return date.toISOString().split("T")[0];
      }
    }
	});

	$('.ui.dropdown').dropdown();

	$('.time_calendar').calendar({
		type: 'time'
	});

	$('.ui.checkbox').checkbox({
		onChecked: function() {
			$(this).attr("checked", true);
			$(this).attr("value", true);
		},
		onUnchecked: function() {
			$(this).attr("checked", false);
			$(this).attr("value", false);
		},
	});

	$('#add-room-button').on('click', function(e) {
		e.preventDefault();
		var elem = $(".room_detail").last();
		var new_elem = $(elem).clone();
		$(new_elem).insertAfter(elem);
		$(new_elem).find('.ui.dropdown').dropdown();
		$('.remove-room-button').on('click', function(e) {
			e.preventDefault();
			remove_room(this);
		});
	});

	$('.remove-room-button').on('click', function(e) {
		e.preventDefault();
		remove_room(this);
	});

	function remove_room(self) {
		if ($(".room_detail").length <= 1) {
			$('body').toast({
				class: 'error',
				message: "Cannot remove"
			});
			return false;
		}
		$(self).parent().parent().parent().remove();
	}

	$("#property-launch-form").submit(function(ev) {
		ev.preventDefault();
		var data = {};

		//		flatten form
		$(document.property_launch_form).serializeArray().forEach(function(val) {
			data[val['name']] = val['value'];
		});

		var room_details = [];
		Array.from($(".room_detail")).forEach(function(ele) {
			var room_detail = {};
			$(ele).find(":input").serializeArray().forEach(function(val) {
				room_detail[val['name']] = val['value'];
			});
			room_details.push(room_detail);
		});

		var room_type_details = [];
		Array.from($(".room_type_details")).forEach(function(ele) {
			var room_type_detail = {};
			$(ele).find(":input").serializeArray().forEach(function(val) {
				room_type_detail[val['name']] = val['value'];
			});
			var room_type = $(ele).find('.ui.header').first().attr('data-value');
			room_type_detail['room_type'] = room_type;
			room_type_details.push(room_type_detail);
		});

		data['room_details'] = room_details;
		data['room_type_details'] = room_type_details;
		console.log(JSON.stringify(data));
		$.ajax({
			url: '',
			type: "POST",
			dataType: 'json',
			data: JSON.stringify(data),
			contentType: 'application/json',
			success: function(result) {
				console.log(result);
				$('body').toast({
					class: 'success',
					message: 'Property: ' + result['data']['property_id'] + ' created'
				});
				setTimeout(function() {
					window.location.replace(result['data']['redirect_url']);
				}, 3000);
			},
			error: function(xhr, resp, text) {
				console.log(xhr, resp, text);
				xhr.responseJSON.errors.forEach(function(val) {
					$('body').toast({
						class: 'error',
						title: text,
						message: JSON.stringify(val['title'])
					});
				});

			}
		})
	});
});