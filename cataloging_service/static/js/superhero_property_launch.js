var rate_plans = []
all_currencies = ['AFN', 'EUR', 'ALL', 'DZD', 'USD', 'AOA', 'XCD', 'ARS', 'AMD', 'AWG', 'AUD', 'AZN', 'BHD', 'BSD',
'BDT', 'BBD', 'BYN', 'BZD', 'XOF', 'BMD', 'INR', 'BTN', 'BOB', 'BOV', 'BAM', 'BWP', 'NOK', 'BRL', 'BND', 'BGN', 'BIF',
'CVE', 'KHR', 'XAF', 'CAD', 'KYD', 'CLP', 'CLF', 'CNY', 'COP', 'COU', 'KMF', 'CDF', 'NZD', 'CRC', 'HRK', 'CUP', 'CUC',
'ANG', 'CZK', 'DKK', 'DJF', 'DOP', 'EGP', 'SVC', 'ERN', 'SZL', 'ETB', 'FKP', 'FJD', 'XPF', 'GMD', 'GEL', 'GHS', 'GIP',
'GTQ', 'GBP', 'GNF', 'GYD', 'HTG', 'HNL', 'HKD', 'HUF', 'ISK', 'IDR', 'XDR', 'IRR', 'IQD', 'ILS', 'JMD', 'JPY', 'JOD',
'KZT', 'KES', 'KPW', 'KRW', 'KWD', 'KGS', 'LAK', 'LBP', 'LSL', 'ZAR', 'LRD', 'LYD', 'CHF', 'MOP', 'MKD', 'MGA', 'MWK',
'MYR', 'MVR', 'MRU', 'MUR', 'XUA', 'MXN', 'MXV', 'MDL', 'MNT', 'MAD', 'MZN', 'MMK', 'NAD', 'NPR', 'NIO', 'NGN', 'OMR',
'PKR', '', 'PAB', 'PGK', 'PYG', 'PEN', 'PHP', 'PLN', 'QAR', 'RON', 'RUB', 'RWF', 'SHP', 'WST', 'STN', 'SAR', 'RSD',
'SCR', 'SLL', 'SGD', 'XSU', 'SBD', 'SOS', 'SSP', 'LKR', 'SDG', 'SRD', 'SEK', 'CHE', 'CHW', 'SYP', 'TWD', 'TJS', 'TZS',
'THB', 'TOP', 'TTD', 'TND', 'TRY', 'TMT', 'UGX', 'UAH', 'AED', 'USN', 'UYU', 'UYI', 'UYW', 'UZS', 'VUV', 'VES', 'VND',
'YER', 'ZMW', 'ZWL', 'XBA', 'XBB', 'XBC', 'XBD', 'XTS', 'XXX', 'XAU', 'XPD', 'XPT', 'XAG', 'AFA', 'FIM', 'ALK', 'ADP',
'ESP', 'FRF', 'AOK', 'AON', 'AOR', 'ARA', 'ARP', 'ARY', 'RUR', 'ATS', 'AYM', 'AZM', 'BYB', 'BYR', 'BEC', 'BEF', 'BEL',
'BOP', 'BAD', 'BRB', 'BRC', 'BRE', 'BRN', 'BRR', 'BGJ', 'BGK', 'BGL', 'BUK', 'HRD', 'CYP', 'CSJ', 'CSK', 'ECS', 'ECV',
'GQE', 'EEK', 'XEU', 'GEK', 'DDM', 'DEM', 'GHC', 'GHP', 'GRD', 'GNE', 'GNS', 'GWE', 'GWP', 'ITL', 'ISJ', 'IEP', 'ILP',
'ILR', 'LAJ', 'LVL', 'LVR', 'LSM', 'ZAL', 'LTL', 'LTT', 'LUC', 'LUF', 'LUL', 'MGF', 'MVQ', 'MLF', 'MTL', 'MTP', 'MRO',
'MXP', 'MZE', 'MZM', 'NLG', 'NIC', 'PEH', 'PEI', 'PES', 'PLZ', 'PTE', 'ROK', 'ROL', 'STD', 'CSD', 'SKK', 'SIT', 'RHD',
'ESA', 'ESB', 'SDD', 'SDP', 'SRG', 'CHC', 'TJR', 'TPE', 'TRL', 'TMM', 'UGS', 'UGW', 'UAK', 'SUR', 'USS', 'UYN', 'UYP',
'VEB', 'VEF', 'VNC', 'YDD', 'YUD', 'YUM', 'YUN', 'ZRN', 'ZRZ', 'ZMK', 'ZWC', 'ZWD', 'ZWN', 'ZWR', 'XFO', 'XRE', 'XFU']

function add_room_type() {
            $('.selectRoomType').empty()
            var options = [];
            $('.pickRoomType').each(function(){
              options.push($(this).val());
            });
            var list = $('.selectRoomType')
            $.each(options, function(index, item) {
                list.append(new Option(item, item));
            });
        }

function get_all_room_types() {
            $.ajax({
                type: "GET",
                url: '/api/v3/room-types/',
                data: {},
                success: function (data) {
                    const room_types = data.room_types.flat();
                    $('.selectFromGivenRoomType').empty()
                    var list = $(".selectFromGivenRoomType");
                    $.each(room_types, function(index, item) {
                      list.append(new Option(item, item));
                    });
                }
            })
        }

function get_all_rate_plans() {
            $.ajax({
                type: "GET",
                url: '/api/v3/rate-plans/',
                data: {},
                success: function (data) {
                    rate_plans = data.rate_plans.flat();
                    $('.selectRatePlans')
                        .append(`<input type="checkbox" onclick="map_all_rate_plans(event)" id='checkall' /> Select All<br/>`)
                    for (var value of rate_plans) {
                      $('.selectRatePlans')
                        .append(`<div class="field">`)
                            .append(`<div class="ui checkbox">`)
                                .append(`<input type="checkbox" class="checkbox_rateplan" name="${value}" value='${value}'>`)
                                .append(`  ${value}`)
                            .append(`</div>`)
                        .append(`</div>`)
                    }
                }
            })
        }


function map_all_rate_plans({target }){
    const {
        parentElement
    } = target
    $(target).is(':checked') ? $(parentElement).find('input[type="checkbox"]').prop('checked', true) : $(parentElement).find('input[type="checkbox"]').prop('checked', false);;
}

function get_all_currencies() {
        all_currencies.forEach(function(currency) {
            var select = document.getElementById('supported_payment_currencies')
            var opt = document.createElement('option');
            opt.value = currency;
            opt.innerHTML = currency;
            select.appendChild(opt)
        });
}

$(document).ready(function() {

    function toTitleCase(str) {
        return str.replace(/\w\S*/g, function(txt){
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        });
    }

    $('#property_name').keyup(function() {
            this.value = toTitleCase(this.value);
     });

	$('.locality-search').search({
		apiSettings: {
			url: '/api/v3/localities/?q={query}'
		},
		fields: {
			results: 'data',
			title: 'display_name',
			description: 'micro_market',
		},
		minCharacters: 1,
		showNoResults: true,
		onSelect: function(selected_result) {
			$(this).find(':input').first().attr("data-id", selected_result['id']);
		}
	});

	$('.city-search').search({
		apiSettings: {
			url: '/api/v3/cities/?q={query}'
		},
		fields: {
			results: 'data',
			title: 'name',
			description: 'state',
		},
		minCharacters: 3,
		showNoResults: true,
		onSelect: function(selected_result) {
			$(this).find(':input').first().val(selected_result['id']);
		}
	});

    $("input[name='address_city']").focus(
    function(selected_result) {
			$(this).attr('autocomplete', 'new-password');
		});

    $("input[name='legal_address_city']").focus(
    function(selected_result) {
			$(this).attr('autocomplete', 'new-password');
		});

	$('.date_calendar').calendar({
		type: 'date',
    formatter: {
      date: function (date, settings) {
        if (!date) return '';
        return date.toISOString().split("T")[0];
      }
    }
	});

	$('.ui.dropdown').dropdown();

	$('.time_calendar').calendar({
		type: 'time'
	});

	$('.ui.checkbox').checkbox({
		onChecked: function() {
			$(this).attr("checked", true);
			$(this).attr("value", true);
		},
		onUnchecked: function() {
			$(this).attr("checked", false);
			$(this).attr("value", false);
		},
	});

    $(".checkbox").click(function(){
        if($(".checkbox").length == $(".checkbox:checked").length) {
          $("#checkall").prop("checked", true);
        } else {
          $("#checkall").removeAttr("checked");
        }
    });

    $('#add-room-type-button').on('click', function(e) {
		e.preventDefault();
		var elem = $(".room_type_details").last();
		var new_elem = $(elem).clone();
		$(new_elem).insertAfter(elem);
		$(new_elem).find('.ui.dropdown').dropdown();
		$(new_elem).find('.ui.checkbox').checkbox();
		$('.remove-room-type-button').on('click', function(e) {
			e.preventDefault();
			remove_room_type(this);
		});
	});

	$('#add-room-button').on('click', function(e) {
		e.preventDefault();
		var elem = $(".room_detail").last();
		var new_elem = $(elem).clone();
		$(new_elem).insertAfter(elem);
		$(new_elem).find('.ui.dropdown').dropdown();
		$('.remove-room-button').on('click', function(e) {
			e.preventDefault();
			remove_room(this);
		});
	});

	$('.remove-room-button').on('click', function(e) {
		e.preventDefault();
		remove_room(this);
	});

	$('.remove-room-type-button').on('click', function(e) {
		e.preventDefault();
		remove_room_type(this);
	});

	function remove_room(self) {
		if ($(".room_detail").length <= 1) {
			$('body').toast({
				class: 'error',
				message: "Cannot remove"
			});
			return false;
		}
		$(self).parent().parent().parent().remove();
	}

	function remove_room_type(self) {
		if ($(".room_type_details").length <= 1) {
			$('body').toast({
				class: 'error',
				message: "Cannot remove"
			});
			return false;
		}
		$(self).parent().parent().remove();
		add_room_type()
	}

	$("#property-launch-form").submit(function(ev) {
		ev.preventDefault();
		var data = {};
        config_vars = ['hotel_level_accounts_receivable', 'is_tds_override_enabled', 'is_tds_settlement_enabled',
        'cashiering_enabled', 'external_user_id', 'ups_enabled', 'tds_settlement_percent', 'e_reg_card']

		var room_types_selected = [];
        $('.pickRoomType').each(function(){
          room_types_selected.push($(this).val());
        });
        if(room_types_selected.some(x => room_types_selected.indexOf(x) !== room_types_selected.lastIndexOf(x))){
            $('body').toast({
				class: 'error',
				message: "More than 1 room types found with similar name in Room level details"
			});
        }

        tenant_configs_arr = ['ups_enabled', 'external_user_id', 'cashiering_enabled', 'e_reg_card']
        ar_related_tenant_configs_arr = ['hotel_level_accounts_receivable', 'is_tds_override_enabled',
        'is_tds_settlement_enabled', 'tds_settlement_percent']

        tenant_configs = {}
        ar_related_tenant_configs = {}

        var supported_payment_currencies = [];
        $('#supported_payment_currencies').each(function(){
          supported_payment_currencies.push($(this).val());
        });
        if(supported_payment_currencies[0].length){
        tenant_configs['supported_payment_currencies'] = supported_payment_currencies[0];}

		//		flatten form
		$(document.property_launch_form).serializeArray().forEach(function(val) {
		    if(config_vars.includes(val['name']) && (val['value'] == "null" ||val['value']=="")){
		        return true;
		    }
		    else{
		        if(tenant_configs_arr.includes(val['name'])){
		            tenant_configs[val['name']] = val['value'];
		            }
		        else if(ar_related_tenant_configs_arr.includes(val['name'])){
		            ar_related_tenant_configs[val['name']] = val['value'];
		            }
		        else{
		        data[val['name']] = val['value'];
		            }
		        }
		});

		data['tenant_configs'] = tenant_configs
		data['ar_related_tenant_configs'] = ar_related_tenant_configs

		var room_details = [];
		Array.from($(".room_detail")).forEach(function(ele) {
			var room_detail = {};
			$(ele).find(":input").serializeArray().forEach(function(val) {
				room_detail[val['name']] = val['value'];
			});
			room_details.push(room_detail);
		});

		var room_type_details = [];
		Array.from($(".room_type_details")).forEach(function(ele) {
			var room_type_detail = {};
			var rate_plan_list = []
			$(ele).find(":input").serializeArray().forEach(function(val) {
			    if(rate_plans.includes(val['name'])){
			        rate_plan_list.push(val['name'])
			    }
			    else{room_type_detail[val['name']] = val['value'];}
			});
			room_type_detail['rate_plans'] = rate_plan_list
			room_type_details.push(room_type_detail);
		});

		data['room_details'] = room_details;
		data['room_type_details'] = room_type_details;
		$.ajax({
			type: "POST",
			dataType: 'json',
			data: JSON.stringify(data),
			contentType: 'application/json',
			success: function(result) {
				$('body').toast({
					class: 'success',
					message: 'Property: ' + result['data']['property_id'] + ' created'
				});
			},
			error: function(xhr, resp, text) {
				console.log(xhr, resp, text);
				xhr.responseJSON.errors.forEach(function(val) {
					$('body').toast({
						class: 'error',
						title: text,
						message: JSON.stringify(val['title'])
					});
				});

			}
		})
	});
});