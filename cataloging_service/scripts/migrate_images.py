import logging

from flask_script import Option
from flask_script.commands import Command

from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import PropertyImage

logger = logging.getLogger(__name__)


class MigrateImages(Command):
    property_repository = repo_provider.property_repository

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read image data from'),)

    def run(self, file_path):
        self.migrate_images(file_path)

    def migrate_images(self, file_path):
        unsuccessful_rows = []
        with open(file_path) as image_file:
            for row in image_file:
                # logger.info('Migrating row: %s' % row)
                row_split = row.split(',')
                property_id = row_split[0].strip().lower()
                room_type = row_split[1].strip().upper()
                uri = row_split[2].strip()
                sort_order = row_split[3].strip()
                try:
                    self.migrate_image(property_id, room_type, uri, sort_order)
                except Exception as e:
                    # logger.exception('Error while migrating row: %s' % row)
                    unsuccessful_rows.append((row, e))

        print('Unsuccessful rows:')
        for unsuccessful_row in unsuccessful_rows:
            print('%s | %s' % (
                unsuccessful_row[0], unsuccessful_row[1]))  # Using print because it is easier to parse later

    @atomic_operation
    def migrate_image(self, property_id, room_type, uri, sort_order):
        property = repo_provider.property_repository.get_property(property_id)

        if not property:
            raise CatalogingServiceException(context='Property not found', send_error_mail=False)

        room_config = None
        if room_type:
            room_type = repo_provider.property_repository.get_room_type_by_name(room_type)
            room_config = repo_provider.property_repository.get_property_room_type_configuration_by_room_type(
                property_id,
                room_type.id)
            if room_type and not room_config:
                raise CatalogingServiceException(context='Room config not found', send_error_mail=False)

        image = PropertyImage()
        image.property_id = property_id
        image.path = uri
        image.sort_order = sort_order
        if room_config:
            image.room_type_config_id = room_config.id
        try:
            repo_provider.property_repository.persist(image)
        except Exception as e:
            raise CatalogingServiceException(context=str(e), send_error_mail=False)
