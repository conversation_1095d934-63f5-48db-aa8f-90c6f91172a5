import logging
import time

import pandas as pd

from flask_script import Option
from flask_script.commands import Command

from cataloging_service.models import RoomRackRateModel, PropertySku
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.client.rate_manager_client import RateManagerClient
from cataloging_service.domain import service_provider
from cataloging_service.domain.tenant_config_service import TenantConfigService
from cataloging_service.extensions import cache

import requests
import redis

logger = logging.getLogger(__name__)


class MigrateTreeboPropertySkusRatesCommand(Command):
    tenant_config_service = TenantConfigService()
    sku_service = service_provider.sku_service
    room_service = service_provider.room_service
    property_service = service_provider.property_service

    sku_repository = repo_provider.sku_repository
    room_rack_rate_repository = repo_provider.room_rack_rate_repository
    hotel_repository = repo_provider.property_repository
    base_room_sku_names = ['ACACIA', 'OAK', 'MAPLE', 'MAHOGANY']
    base_room_with_adult_sku_names = ['ACACIA-ADULT', 'OAK-ADULT', 'MAPLE-ADULT', 'MAHOGANY-ADULT']
    adult_skus_price_mapping = {}
    failure_reasons_list_for_property = []
    failed_hotel_id_with_reason = {}

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read sku rates data from'),)

    def run(self, file_path):
        self.migrate_treebo_property_skus_rates(file_path)

    def migrate_treebo_property_skus_rates(self, file_path):
            property_skus_rates = pd.read_csv(file_path)

            distinct_property_ids = property_skus_rates.property_code.unique()

            for property_id in distinct_property_ids:

                print('')
                print('')

                self.failure_reasons_list_for_property = []

                csv_property_id = property_id
                property_id = str(property_id)
                property_id = property_id.zfill(7) if len(property_id) < 7 else property_id

                try:
                    property_details = self.property_service.get_property(property_id)
                except Exception as e:
                    msg = "Error while fetching property details for {0} due to exception {1}".format(property_id, str(e))
                    logger.exception(msg)
                    print(msg)
                    self.failure_reasons_list_for_property.append(msg)
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                if not self.create_tenant_configs(property_id):
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                if not self.clear_scavenger_redis_cache(property_id):
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                if self.hotel_exists_in_tenant_gateway(property_id):
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                self.create_hotel_in_tenant_gateway(property_id)

                if not self.hotel_exists_in_tenant_gateway(property_id):
                    msg = "Hotel {0} not created on tenant gateway".format(property_id)
                    print(msg)
                    self.failure_reasons_list_for_property.append(msg)
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property

                    if not self.update_tenant_configs(property_id):
                        self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                        continue
                    if not self.clear_scavenger_redis_cache(property_id):
                        self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                        continue
                    continue

                filtered_property_skus_rates = property_skus_rates[(property_skus_rates.property_code == csv_property_id)]
                if filtered_property_skus_rates.empty:
                    msg = "No sku rates present for property {0}".format(property_id)
                    print(msg)
                    self.failure_reasons_list_for_property.append(msg)
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                self.adult_skus_price_mapping = {}
                self.adult_skus_price_mapper(filtered_property_skus_rates)
                print("created adult sku price mapper for property {0}".format(property_id))

                self.create_properties_sku_categories_for_property(property_id, property_details)
                print("created properties sku categories for property {0}".format(property_id))

                self.populate_rackrate(property_id, filtered_property_skus_rates)
                print("populated rackrate and property sku rates for property {0}".format(property_id))

                self.create_linkage_for_base_rooms_in_rate_manager(property_id, filtered_property_skus_rates)
                print("created linkage with base rooms in ratemanger for property {0}".format(property_id))

                self.create_packages_and_rate_plans_for_property(property_id, property_details)
                print("created all 5 rateplans for property {0}".format(property_id))

                try:
                    property_details.property_detail.is_hotel_superhero_setup_completed = True
                    self.hotel_repository.persist(property_details)
                    self.hotel_repository.session().commit()

                except Exception as e:
                    msg = "Not able to mark super hero setup complete for property {0} due to exception {1}".format(
                        property_id, str(e))
                    self.failure_reasons_list_for_property.append(msg)
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                try:
                    cache.expire_cache(property_details)
                except Exception as e:
                    msg = "Not able to clear property cache for property {0} due to exception {1}".format(property_id, str(e))
                    print(msg)
                    self.failure_reasons_list_for_property.append(msg)
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                if not self.clear_scavenger_redis_cache(property_id):
                    self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property
                    continue

                self.failed_hotel_id_with_reason[property_id] = self.failure_reasons_list_for_property

            print(self.failed_hotel_id_with_reason)

    def adult_skus_price_mapper(self, property_skus_rates):
        for row in property_skus_rates.iterrows():
            sku_id = str(row[1]['sku_code'])
            rack_rate = float(row[1]['default_sale_price'])
            sku_name = self.sku_repository.rget_sku_by_code(sku_id).name.upper()
            if sku_name in self.base_room_with_adult_sku_names:
                self.adult_skus_price_mapping[sku_name] = rack_rate

    def populate_rackrate(self, property_id,  property_skus_rates):
        try:
            for row in property_skus_rates.iterrows():
                property_sku = PropertySku(sell_separate=True)
                property_id = property_id
                sku_id = str(row[1]['sku_code'])
                rack_rate = float(row[1]['default_sale_price'])
                sku = self.sku_repository.rget_sku_by_code(sku_id)

                # populate data in room_rack_rate_table
                sku_id = sku.id
                sku_name = sku.name.upper()
                sku_category_id = sku.category_id
                sku_category_name = self.sku_repository.rget_sku_category(sku_category_id).name.upper()
                if sku_name in self.base_room_sku_names:
                    room_type = self.hotel_repository.get_room_type(sku_id)
                    max_adult = self.hotel_repository.get_property_room_type_configuration(property_id,
                                                                                           room_type=sku_name).adults
                    for adult in range(1, max_adult + 1):
                        room_rack_rate = RoomRackRateModel()
                        if adult > 1:
                            sku_name_adult = sku_name + '-ADULT'
                            rack_rate = rack_rate + ((adult - 1) * self.adult_skus_price_mapping[
                                sku_name_adult] if self.adult_skus_price_mapping.get(sku_name_adult) else 0)
                        room_rack_rate_id = str(property_id) + '-' + str(sku_name) + '-' + str(adult)
                        existed_room_rack_rate = self.room_rack_rate_repository.get_room_rack_rate_by_id(room_rack_rate_id)
                        if existed_room_rack_rate:
                            existed_room_rack_rate.rack_rate = rack_rate
                            room_rack_rate = existed_room_rack_rate
                        else:
                            room_rack_rate.property_id = property_id
                            room_rack_rate.room_rack_rate_id = room_rack_rate_id
                            room_rack_rate.room_type_id = sku_id
                            room_rack_rate.adult_count = adult
                            room_rack_rate.rack_rate = rack_rate
                            room_rack_rate.room_type = room_type
                        self.room_rack_rate_repository.save_room_rack_rate(room_rack_rate)

                # populate data in property_sku_table
                elif sku_category_name != 'STAY':
                    existed_property_sku = self.sku_repository.get_property_sku(
                        property_id, sku_id
                    )
                    if existed_property_sku:
                        existed_property_sku.rack_rate = rack_rate
                        property_sku = existed_property_sku
                    else:
                        property_sku.property_id = property_id
                        property_sku.sku_id = sku_id
                        property_sku.rack_rate = rack_rate
                    self.sku_repository.save_property_sku(property_sku)
            self.room_rack_rate_repository.session().commit()
            self.sku_repository.session().commit()
        except Exception as e:
            msg = "Error while populating rackrate for property {0} due to exception {1}".format(property_id, str(e))
            logger.exception(msg)
            self.room_rack_rate_repository.session().rollback()
            self.sku_repository.session().rollback()
            raise

    def create_linkage_for_base_rooms_in_rate_manager(self, property_id, property_skus_rates):
        try:
            for row in property_skus_rates.iterrows():
                property_id = property_id
                sku_id = str(row[1]['sku_code'])
                sku = self.sku_repository.rget_sku_by_code(sku_id)
                sku_name = sku.name.upper()
                if sku_name in self.base_room_sku_names:
                    max_adult = self.hotel_repository.get_property_room_type_configuration(property_id,
                                                                                           room_type=sku_name).adults
                    room_code = self.hotel_repository.get_room_type(sku_id).code
                    for adult in range(1, max_adult + 1):
                        if adult > 1:
                            sku_name_adult = sku_name + '-ADULT'
                            price = (adult-1) * self.adult_skus_price_mapping[
                                    sku_name_adult] if self.adult_skus_price_mapping.get(sku_name_adult) else 0
                            RateManagerClient.update_linkage_for_base_rooms_for_old_property_migration(property_id,
                                                                                                       room_code.upper(),
                                                                                                       adult, price)
        except Exception as e:
            msg = "Not able to create linkage for property {0} due to exception {1}".format(property_id, str(e))
            logger.exception(msg)
            self.failure_reasons_list_for_property.append(msg)

    def create_properties_sku_categories_for_property(self, property_id, property_details):
        sku_categories = property_details.sku_categories
        available_property_sku_category_ids = [sku_category.id for sku_category in sku_categories]
        property_skus = property_details.sku_s
        property_sku_category_ids = list({sku.category_id for sku in property_skus})

        properties_sku_categories_id_to_be_created = [catg_id for catg_id in property_sku_category_ids if
                                                      catg_id not in available_property_sku_category_ids]
        if properties_sku_categories_id_to_be_created:
            try:
                self.hotel_repository.create_properties_sku_categories(property_id,
                                                                       properties_sku_categories_id_to_be_created)
                self.hotel_repository.session().commit()
            except Exception as e:
                msg = "Error while populating sku categories for property {0} due to exception {0}".format(property_id, str(e))
                logger.exception(msg)
                self.hotel_repository.session().rollback()
                self.failure_reasons_list_for_property.append(msg)

    def create_packages_and_rate_plans_for_property(self, property_id, property_details):
        room_type_configs = self.room_service.get_room_type_configurations(property_id)
        skus = property_details.sku_s
        try:
            self._create_rate_plans(property_id, skus, room_type_configs)
        except Exception as e:
            msg = "Not able to create packages and rateplan for property {0} due to exception {1}".format(property_id,
                                                                                                        str(e))
            logger.exception(msg)
            self.failure_reasons_list_for_property.append(msg)

    def _create_rate_plans(self, property_id, skus, room_type_configs):
        child_policy = {
            "charge_per_child": 0,
            "child_allowed": True,
            "unit_of_charge": "fixed_value",
        }
        payment_policy = {"pay_at_checkin_required": False, "advance_payment_percentage": 100,
                          "unit_of_payment_percentage": "percent_of_booking_value",
                          "payment_before_checkin_required": True,
                          "days_before_checkin_to_make_payment": 1}
        cancellation_policy = [
            {"cancellation_charge_unit": "percent_of_room_night_charge", "cancellation_charge_value": 100,
             "cancellation_duration_before_checkin_end": 24, "cancellation_duration_before_checkin_start": 0}]
        restrictions = {"maximum_los": 14, "minimum_los": 1}
        room_type_occupancy_mappings = []
        for room_type_config in room_type_configs:
            room_type_occupancy_mappings.extend(
                [
                    {
                        "adult_count": idx + 1,
                        "room_type_id": room_type_config.room_type.code,
                    }
                    for idx in range(room_type_config.adults)
                ]
            )

        rate_plans = [
            # {"name": "EP", "code": "EP", "description": "EP Rate Plan", "inclusions": []},
            {
                "name": "CP",
                "code": "CP",
                "description": "CP Rate Plan",
                "inclusions": ["breakfast"],
            },
            # {
            #     "name": "MAP",
            #     "code": "MAP",
            #     "description": "MAP Rate Plan",
            #     "inclusions": ["breakfast", "lunch"],
            # },
            # {
            #     "name": "AP",
            #     "code": "AP",
            #     "description": "AP Rate Plan",
            #     "inclusions": ["breakfast", "lunch", "dinner"],
            # },
            {
                "name": "TRB-DEFAULT",
                "code": "TRB-DEFAULT",
                "description": "Treebo Default Rate Plan",
                "inclusions": []
            }
        ]
        for rate_plan_config in rate_plans:
            package_name = rate_plan_config["code"]
            treebo_breakfast = "treebo-breakfast" if package_name == "TRB-DEFAULT" else None
            trb_default_restrictions = {"minimum_los": 1} if package_name == "TRB-DEFAULT" else None
            inclusions = self.get_inclusion_config(skus, treebo_breakfast, *rate_plan_config["inclusions"])
            try:
                package = RateManagerClient.create_package(property_id, package_name, inclusions)
                rate_plan = RateManagerClient.create_rateplan(
                    rate_plan_config["name"],
                    rate_plan_config["code"],
                    rate_plan_config["description"],
                    package["package_id"],
                    child_policy,
                    payment_policy,
                    cancellation_policy,
                    property_id,
                    trb_default_restrictions or restrictions,
                    room_type_occupancy_mappings,
                )
                logger.info(
                    "{0} with id {1} successfully created for hotel {2}".format(
                        rate_plan_config["description"], rate_plan["rate_plan_id"], str(property_id)
                    )
                )
            except Exception:
                logger.exception(
                    "Error creating rate plan {0} for hotel {1}".format(
                        rate_plan_config["name"], str(property_id)
                    )
                )
        # call Rate Manager and get all rate plans for the hotel
        try:
            rate_plans = RateManagerClient.get_all_rate_plans(str(property_id))
            if len(rate_plans) == 2:
                logger.info("All rateplans created for given property {0}".format(property_id))
            elif len(rate_plans) > 0:
                logger.info("Some rateplans not created for given property {0}".format(property_id))
            else:
                logger.info("No rate plans plans created for given property {0}".format(property_id))
        except Exception as e:
            msg = "Not able to fetch rateplans for property {0} due to exception {1}".format(property_id, str(e))
            self.failure_reasons_list_for_property.append(msg)

    def get_inclusion_config(self, skus, treebo_breakfast, breakfast=None, lunch=None, dinner=None):
        breakfast_inclusion = None
        lunch_inclusion = None
        dinner_inclusion = None
        treebo_breakfast_inclusion = None
        for sku in skus:
            sku_data = {
                    "sku_id": str(sku.code),
                    "frequency": sku.frequency,
                    "offering": sku.offering,
                }
            if breakfast and not breakfast_inclusion and sku.code == 'breakfast':
                breakfast_inclusion = sku_data
            elif lunch and not lunch_inclusion and sku.code == 'lunch':
                lunch_inclusion = sku_data
            elif dinner and not dinner_inclusion and sku.code == 'dinner':
                dinner_inclusion = sku_data
            elif treebo_breakfast and not treebo_breakfast_inclusion and sku.code == '3665':
                treebo_breakfast_inclusion = sku_data

            if breakfast_inclusion and lunch_inclusion and dinner_inclusion and treebo_breakfast_inclusion:
                break

        inclusions = [breakfast_inclusion, lunch_inclusion, dinner_inclusion, treebo_breakfast_inclusion]
        inclusions = [x for x in inclusions if x]
        return inclusions

    def create_hotel_in_tenant_gateway(self, property_id):
        try:
            payload = {"data": {"hotel_id": property_id}}
            response = requests.post(
                url="https://tenant-gateway.treebo.com/tenant-gateway/v1/su/hotels", json=payload,
                headers={"X-Tenant-Id": "treebo"})
            if response.status_code in [200, 201]:
                msg = "Succesfully created the hotel {0}".format(property_id)
                print(msg)
            else:
                msg = "Didn't get valid response while creating hotel {0} with error {1}".format(property_id,
                                                                                                 response.content)
                print(msg)
                self.failure_reasons_list_for_property.append(msg)
        except Exception as e:
            msg = "Some exception {0} occurred while creating hotel -> {1}".format(str(e), property_id)
            print(msg)
            self.failure_reasons_list_for_property.append(msg)

    def hotel_exists_in_tenant_gateway(self, property_id):
        try:
            response = requests.get(
                url="https://tenant-gateway.treebo.com/tenant-gateway/v1/su/hotels/{0}".format(property_id),
                headers={"X-Tenant-Id": "treebo"})
            if response.status_code in [200, 201]:
                msg = "Succesfully got the response for hotel {0}".format(property_id)
                print(msg)
                return True
            else:
                msg = "Didn't get valid response while fetching hotel {0} with error {1}".format(property_id,
                                                                                                 response.content)
                print(msg)
                self.failure_reasons_list_for_property.append(msg)
                return False
        except Exception as e:
            msg = "Some exception {0} occurred while fetching hotel -> {1}".format(str(e), property_id)
            print(msg)
            self.failure_reasons_list_for_property.append(msg)

    def create_tenant_configs(self, property_id):
        channel_manager_tenant_config = {'config_value': 'true',
                                         'config_name': 'is_channel_manager_enabled',
                                         'value_type': 'boolean', 'active': True}

        rate_manager_tenant_config = {'config_value': 'true', 'config_name': 'rate_manager_enabled',
                                      'value_type': 'boolean', 'active': True}

        disabled_modules = {'config_value': '["its", "pricing", "HRMS"]',
                            'config_name': 'disabled_modules',
                            'value_type': 'array', 'active': True}

        try:
            self.tenant_config_service.create_or_update_tenant_config(channel_manager_tenant_config, property_id)
            self.tenant_config_service.create_or_update_tenant_config(rate_manager_tenant_config, property_id)
            self.tenant_config_service.create_or_update_tenant_config(disabled_modules, property_id)
            return True
        except Exception as e:
            msg = "Not able to create tenant_config for property {0} due to exception {1}".format(property_id, str(e))
            print(msg)
            self.failure_reasons_list_for_property.append(msg)
            return False

    def update_tenant_configs(self, property_id):
        channel_manager_tenant_config = {'config_value': 'false',
                                         'config_name': 'is_channel_manager_enabled',
                                         'value_type': 'boolean', 'active': False}

        rate_manager_tenant_config = {'config_value': 'false', 'config_name': 'rate_manager_enabled',
                                      'value_type': 'boolean', 'active': False}

        disabled_modules = {'config_value': '["its", "pricing", "HRMS"]',
                            'config_name': 'disabled_modules',
                            'value_type': 'array', 'active': False}

        try:
            self.tenant_config_service.create_or_update_tenant_config(channel_manager_tenant_config, property_id)
            self.tenant_config_service.create_or_update_tenant_config(rate_manager_tenant_config, property_id)
            self.tenant_config_service.create_or_update_tenant_config(disabled_modules, property_id)
            return True
        except Exception as e:
            msg = "Not able to update tenant_config for property {0} due to exception {1}".format(property_id, str(e))
            print(msg)
            self.failure_reasons_list_for_property.append(msg)
            return False

    def clear_scavenger_redis_cache(self, property_id):
        try:
            redis_client = redis.Redis(
                host='p-2621-aps1-01-elastic-cache-001.bgtmzs.0001.aps1.cache.amazonaws.com', port='6379', db='8'
            )
            for key in redis_client.keys('*' + property_id + '*'):
                redis_client.delete(key)
            return True
        except Exception as e:
            msg = "Not able to delete cache for property {0} due to exception {1}".format(property_id, e)
            print(msg)
            self.failure_reasons_list_for_property.append(msg)
            return False
