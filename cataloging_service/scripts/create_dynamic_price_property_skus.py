import logging
from concurrent.futures.thread import Thread<PERSON>oolExecutor

from flask_script.commands import Command, Option
from treebo_commons.utils import dateutils

from cataloging_service.constants.constants import DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES
from cataloging_service.constants.messaging_constants import PropertyMessageActions
from cataloging_service.constants.model_choices import TaxTypeChoices, SkuTypeChoices
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.messaging.messaging_wrappers import RoomTypeConfigMessagingWrapper
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.infrastructure.alerts.alerts import slack_alert
from cataloging_service.models import Sku
from cataloging_service.utils import chunks

logger = logging.getLogger(__name__)


class CreateDynamicPricePropertySkus(Command):
    property_repo = repo_provider.property_repository
    property_service = service_provider.property_service
    sku_repo = repo_provider.sku_repository
    sku_service = service_provider.sku_service
    room_service = service_provider.room_service
    messaging_service = service_provider.messaging_service

    option_list = (
        Option(
            "--include_hotel_ids",
            dest="include_hotel_ids",
            help="Comma separated list of hotel IDs to include",
        ),
        Option(
            "--exclude_hotel_ids", 
            dest="exclude_hotel_ids",
            help="Comma separated list of hotel IDs to exclude",
        ),
    )

    def run(self, include_hotel_ids=None, exclude_hotel_ids=None):
        include_ids = include_hotel_ids.split(',') if include_hotel_ids else None
        exclude_ids = set(exclude_hotel_ids.split(',')) if exclude_hotel_ids else set()

        live_properties = self.property_repo.rget_all_live_properties(ids=include_ids)

        if exclude_ids:
            live_properties = [prop for prop in live_properties if prop.id not in exclude_ids]

        property_default_skus = self.sku_repo.get_property_default_skus()
        sku_codes = {sku.code for sku in property_default_skus}
        sku_categories = self.sku_repo.rget_all_sku_categories_by_codes(
            list(DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES.keys())
        )
        dynamic_price_sku_categories = {
            sku_category.code: sku_category for sku_category in sku_categories
        }
        total_failed_properties = set()
        self.create_skus_for_properties(dynamic_price_sku_categories)
        for properties in chunks(live_properties, 25):
            start_time = dateutils.current_datetime()
            property_ids = {treebo_property.id for treebo_property in properties}
            try:
                failed_property_ids = self.create_property_skus_bulk(
                    properties, property_default_skus, sku_codes
                )
                if failed_property_ids:
                    logger.info("Failed Property Ids: {0}".format(failed_property_ids))
                    total_failed_properties.update(failed_property_ids)
                self.property_service.publish_properties(
                    property_ids - failed_property_ids, PropertyMessageActions.ADMIN_CREATE.value
                )
                # Syncing max-adults to downstream services
                self._sync_room_type_config(property_ids)
                logger.info(
                    "Batch Processing Completed in {0:.2f} sec".format(
                        (dateutils.current_datetime() - start_time).total_seconds()
                    )
                )
            except Exception as e:
                logger.info(
                    "Exception occurred while creating property skus {0} exception {1}".format(property_ids, str(e))
                )
        if total_failed_properties:
            slack_alert(
                data=total_failed_properties,
                message="Dynamic Pricing Sku creation failed for given properties"
            )
        logger.info('Dynamic Pricing Sku Creation Script Complete')

    def _sync_room_type_config(self, property_ids):
        room_configs = self.property_repo.get_room_type_configs(property_ids)
        messages = [RoomTypeConfigMessagingWrapper(room, False, False).get_json() for room in room_configs]
        for message in messages:
            self.messaging_service.publish_room_type_config_message(message)

    @atomic_operation
    def create_property_skus_bulk(
        self, properties, property_default_skus, sku_codes
    ):
        def bulk_property_sku_creation(treebo_property, property_default_skus, sku_codes):
            try:
                applicable_skus = self.sku_service.applicable_skus_to_property(
                    treebo_property,
                    sku_codes=property_default_skus,
                )
                created_property_skus = self.property_service.get_created_property_skus(
                    treebo_property, applicable_skus, sku_codes
                )
                return created_property_skus, treebo_property.id
            except Exception as ex:
                logger.exception(
                    "Exception while adding property_sku for {0} msg={1}".format(
                        treebo_property.id, str(ex)
                    )
                )
                return [], treebo_property.id

        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(
                    bulk_property_sku_creation, treebo_property, property_default_skus, sku_codes
                )
                for treebo_property in properties
            ]
        all_created_property_skus = []
        failed_property_ids = set()
        for future in futures:
            property_skus, property_id = future.result()
            if not property_skus:
                failed_property_ids.add(property_id)
            else:
                all_created_property_skus.extend(property_skus)
        if all_created_property_skus:
            self.property_repo.persist_all(all_created_property_skus)
        return failed_property_ids

    @atomic_operation
    def create_skus_for_properties(self, dynamic_price_sku_category):
        created_skus = []
        try:
            for room_type in ['ACACIA', 'OAK', 'MAPLE', 'MAHOGANY']:
                for adults in range(10):
                    tag = str.format("%s-%s" % (room_type, adults + 1))
                    created_skus.extend(self.create_dynamic_pricing_skus(dynamic_price_sku_category, tag))
        except Exception as ex:
            logger.info("Exception occurred when creating skus {0}".format(str(ex)))
            raise ex
        if not created_skus:
            return
        created_skus = self.sku_repo.persist_all(created_skus)
        self.sku_service.publish_sku([sku.id for sku in created_skus])

    def create_dynamic_pricing_skus(self, dynamic_price_sku_category, tag):
        created_skus = []
        for category_code, dynamic_price_sku_prefixes in DYNAMIC_PRICE_SKU_CATEGORY_TO_PREFIXES.items():
            for sku_prefix in dynamic_price_sku_prefixes:
                dynamic_pricing_sku_tag = "{0} {1}-0".format(sku_prefix, tag)
                identifier = "{0}:{1}".format(sku_prefix.lower(), tag.lower())
                if not self.sku_service.scheck_if_sku_exists(identifier):
                    created_dynamic_pricing_sku = Sku(
                        name=dynamic_pricing_sku_tag,
                        display_name=dynamic_pricing_sku_tag.upper(),
                        saleable=True,
                        is_modular=True,
                        tax_type=TaxTypeChoices.unit,
                        sku_type=SkuTypeChoices.sku,
                        category=dynamic_price_sku_category[category_code],
                        identifier=identifier,
                        sku_count=0,
                        tag=dynamic_pricing_sku_tag,
                        is_property_inclusion=True,
                    )
                    created_skus.append(created_dynamic_pricing_sku)
        return created_skus
