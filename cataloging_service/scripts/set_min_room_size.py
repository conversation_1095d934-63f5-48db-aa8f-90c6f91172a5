import logging

from flask_script.commands import Command

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider

logger = logging.getLogger(__name__)


class SetMinRoomSize(Command):
    property_repository = repo_provider.property_repository

    def run(self):
        self.set_min_room_size()

    @atomic_operation
    def set_min_room_size(self):
        properties = self.property_repository.get_all_properties()
        for prop in properties:
            room_configs = self.property_repository.get_property_room_type_configurations(prop.id)
            min_size_map = self.property_repository.get_property_min_room_size_map(prop.id)
            for room_config in room_configs:
                room_config.min_room_size = min_size_map.get(room_config.room_type_id, None)

            self.property_repository.persist_all(room_configs)
