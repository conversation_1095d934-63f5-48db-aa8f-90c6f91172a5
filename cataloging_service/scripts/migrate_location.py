import logging

from flask_script.commands import Command

from cataloging_service.client import client_provider
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import Country, State, City, MicroMarket, Locality, Cluster

logger = logging.getLogger(__name__)


class MigrateLocationCommand(Command):
    def __init__(self):
        super(MigrateLocationCommand, self).__init__()
        self.sheet_id = '1L05RH9DEtuFBWe8OZb4fown7KceaBt25Tcwo4t6kYC0'
        self.sheet_name = 'Location Info'
        self.sheet_client = client_provider.google_sheets_client
        self.location_repository = repo_provider.property_location_repository

    def run(self):
        self.start_migration()

    @atomic_operation
    def start_migration(self):
        row_num = 2

        country = None
        state = None
        cluster = None
        city = None
        micro_market = None
        locality = None

        while True:
            row = self.sheet_client.read_row(self.sheet_id, self.sheet_name, row_num, 6)
            logger.info('Row %s: %s' % (row_num, row))
            row_num += 1

            if not row:
                break

            country_name = row[0]
            state_name = row[1]
            cluster_name = row[5]
            city_name = row[2]
            micro_market_name = row[3]
            locality_name = row[4]

            if country_name:
                country = self.location_repository.get_country_by_name(country_name)

                if not country:
                    country = Country()
                    country.name = country_name
                    country = self.location_repository.persist(country)

            if state_name:
                state = self.location_repository.get_state_by_name(state_name, country.id)
                if not state:
                    state = State()
                    state.name = state_name
                    state.country_id = country.id
                    state = self.location_repository.persist(state)

            if city_name:
                city = self.location_repository.get_city_by_name(city_name, state.id)
                if not city:
                    city = City()
                    city.name = city_name
                    city.state_id = state.id
                    city = self.location_repository.persist(city)

            if micro_market_name:
                micro_market = self.location_repository.get_micro_market_by_name(micro_market_name, city.id)
                if not micro_market:
                    micro_market = MicroMarket()
                    micro_market.city_id = city.id
                    micro_market.name = micro_market_name
                    micro_market = self.location_repository.persist(micro_market)

            if locality_name:
                locality = self.location_repository.get_locality_by_name(locality_name, city.id)
                if not locality:
                    locality = Locality()
                    locality.micro_market_id = micro_market.id
                    locality.name = locality_name
                    locality.city_id = city.id
                    locality = self.location_repository.persist(locality)

            if cluster_name and city_name:
                cluster = self.location_repository.get_cluster_by_name(cluster_name)
                if not cluster:
                    cluster = Cluster()
                    cluster.name = cluster_name
                    cluster = self.location_repository.persist(cluster)

                cluster.cities.append(city)
                self.location_repository.persist(cluster)
