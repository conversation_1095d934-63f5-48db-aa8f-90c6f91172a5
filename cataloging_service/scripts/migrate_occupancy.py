import csv
import logging

from flask_script import Option
from flask_script.commands import Command

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import RoomTypeConfiguration

logger = logging.getLogger(__name__)


class MigrateOccupancyCommand(Command):
    property_repository = repo_provider.property_repository

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read occupancy data from'),
                   Option('--error_csv_path', dest='error_path', required=True,
                          help='The CSV file to read error occupancy data from')
                   )

    def run(self, file_path, error_path):
        self.migrate_occupancies(file_path, error_path)

    def migrate_occupancies(self, file_path, error_path):
        unsuccessful_rows = []
        with open(file_path) as occupancy_file:
            reader = csv.reader(occupancy_file)
            for row in reader:
                # logger.info('Migrating row: %s' % row)
                property_id = row[0].strip().lower()
                room_type = row[1].strip().upper()
                adults = row[3].strip()
                max_total = row[4].strip()
                children = row[5].strip()

                try:
                    if max_total < adults:
                        raise Exception('Max occupancy less than adults')

                    self.migrate_single(property_id, room_type, adults, max_total, children)
                except Exception as e:
                    # logger.exception('Error while migrating row: %s' % row)
                    unsuccessful_rows.append((row, e))

        print('Unsuccessful rows:')
        for unsuccessful_row in unsuccessful_rows:
            print('%s | %s' % (
                unsuccessful_row[0], unsuccessful_row[1]))  # Using print because it is easier to parse later

        self.write_csv(error_path, unsuccessful_rows)

    @atomic_operation
    def migrate_single(self, property_id, room_type, adults, max_total, children):
        property = repo_provider.property_repository.get_property(property_id)
        if not property:
            raise Exception('Property not found')

        if room_type:
            room_type = repo_provider.property_repository.get_room_type_by_name(str(room_type).upper())
            room_config = repo_provider.property_repository.get_property_room_type_configuration_by_room_type(
                property_id,
                room_type.id)

            if not room_config:
                room_config = RoomTypeConfiguration()
                room_config.min_occupancy = 1

            room_config.adults = adults
            room_config.max_total = max_total
            diff = int(max_total) - int(adults)
            room_config.children = children
            room_config.max_occupancy = str.format('%s + %s' % (adults, diff))

            try:
                repo_provider.property_repository.persist(room_config)
            except Exception:
                raise

    def write_csv(self, error_path, unsuccessful_rows):
        with open(error_path, 'w') as csvfile:
            filewriter = csv.writer(csvfile)
            try:
                for error_row in unsuccessful_rows:
                    data, error_str = error_row
                    row = data + [error_str]
                    filewriter.writerow(row)
            except Exception as err:
                print("Caught Exception. {err}".format(
                    err=err.__repr__()
                ))
                raise
