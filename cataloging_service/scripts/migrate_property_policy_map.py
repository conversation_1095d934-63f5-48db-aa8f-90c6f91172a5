import csv
import logging

from flask_script import Command, Option

from cataloging_service.models import PropertyPolicyMap
from cataloging_service.infrastructure.repositories import repo_provider

logger = logging.getLogger('migrate_property_policy_command')

class MigratePropertyPolicyMapCommand(Command):

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read occupancy data from'),)

    def run(self, file_path):
        self.migrate_property_policy_map(file_path)

    def migrate_property_policy_map(self, file_path):
        with open(file_path) as property_policy_map_csv:
            reader = csv.DictReader(property_policy_map_csv, delimiter='|')
            property_policy_map_repository = repo_provider.property_policy_map_repository
            try:
                for row in reader:
                    property_policy_map = PropertyPolicyMap()
                    property_policy_map.id = row['id']
                    property_policy_map.created_at = row['created_at']
                    property_policy_map.modified_at = row['modified_at']
                    property_detail_id = property_policy_map_repository.get_property_detail_id_by_property_id(row['cs_id'])
                    property_policy_map.property_detail_id = property_detail_id
                    property_policy_map.property_policy_id = row['policy_id_id']
                    property_policy_map_repository.persist(property_policy_map)
                property_policy_map_repository.session().commit()
            except AttributeError:
                logger.exception(row)

