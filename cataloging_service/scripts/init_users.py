from flask_script.commands import Command

from cataloging_service.constants import constants
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import User, Role


class InitUserCommand(Command):
    def __init__(self):
        super(InitUserCommand, self).__init__()
        self.meta_repository = repo_provider.meta_repository

    def run(self):
        self.insert_initial_user_and_roles()

    @atomic_operation
    def insert_initial_user_and_roles(self):
        user = User()
        user.email = '<EMAIL>'
        user.password = 'prowl'
        user = self.meta_repository.persist(user)

        admin_role = Role()
        admin_role.name = constants.ADMIN_ACCESS_ROLE
        self.meta_repository.persist(admin_role)

        superuser_role = Role()
        superuser_role.name = constants.SUPER_USER_ROLE
        superuser_role.users.append(user)
        self.meta_repository.persist(superuser_role)
