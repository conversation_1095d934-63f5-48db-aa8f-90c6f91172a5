import logging

from flask_script import Option
from flask_script.commands import Command

from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import OtaProperty, OtaPropertyMapping, OtaRoomMapping, OtaRatePlanMappings
from cataloging_service.utils import Utils

logger = logging.getLogger(__name__)


class MigrateOtaMappings(Command):
    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='Provides the file path containing ota mappings'),)

    property_repository = repo_provider.property_repository

    def run(self, file_path):
        self.migrate_mappings(file_path)

    def migrate_mappings(self, file_path):
        failures = []
        with open(file_path) as file:
            for line in file:
                line = line.replace('\n', '')
                csv = line.split(',')
                ota_code = csv[0]
                property_id = csv[1]
                room_type = csv[2]
                rate_plan = csv[3]
                ota_hotel_code = csv[4]
                ota_username = csv[5]
                ota_access_token = csv[6]
                ota_room_code = csv[7]
                ota_rate_plan_code = csv[8]
                ota_rate_plan_name = csv[9]
                rate_push_enabled = csv[10]

                try:
                    logger.info('Migrating: %s', line)
                    self.migrate_mapping(ota_code, property_id, room_type, rate_plan, ota_hotel_code, ota_username,
                                         ota_access_token, ota_room_code, ota_rate_plan_code, ota_rate_plan_name,
                                         rate_push_enabled)
                except CatalogingServiceException as e:
                    failures.append((line, e))

        for failure in failures:
            logger.error('%s | %s', failure[0], failure[1])

    @atomic_operation
    def migrate_mapping(self, ota_code, property_id, room_type, rate_plan, ota_hotel_code, ota_username,
                        ota_access_token, ota_room_code, ota_rate_plan_code, ota_rate_plan_name, rate_push_enabled):
        ota = self.property_repository.get_ota(ota_code)
        if not ota:
            raise CatalogingServiceException(context='OTA %s not found' % ota_code, send_error_mail=False)

        property = self.property_repository.get_property(property_id)
        if not property:
            raise CatalogingServiceException(context='Property %s not found' % property_id, send_error_mail=False)

        if not self._check_if_property_is_provided_by_treebo(property):
            raise CatalogingServiceException('Not a treebo property', send_error_mail=False)

        room_type_config = self.property_repository.get_property_room_type_configuration(property_id,
                                                                                         room_type=room_type)
        if room_type and not room_type_config:
            raise CatalogingServiceException(context='Room Config %s, %s not found' % (property_id, room_type),
                                             send_error_mail=False)

        rate_plan_config = self.property_repository.get_rate_plan_configuration(property_id, rate_plan)
        if rate_plan and not rate_plan_config:
            raise CatalogingServiceException(context='Rate config %s, %s not found' % (property_id, rate_plan),
                                             send_error_mail=False)

        ota_property = self.property_repository.get_ota_property(property_id, ota_code)
        if not ota_property:
            ota_property = OtaProperty()
            ota_property.property_id = property_id
            ota_property.ota_id = ota.id
            ota_property.rate_push_complete = True
            ota_property.promo_push_complete = True
            ota_property.inventory_push_complete = True
            ota_property.rcs_push_complete = True
            ota_property.rcs_callback_complete = True
            ota_property.unirate_push_complete = True
            ota_property.rcs_push_time = Utils.get_current_time_utc()
            ota_property.rcs_callback_time = Utils.get_current_time_utc()
            ota_property.unirate_push_time = Utils.get_current_time_utc()
            self.property_repository.persist(ota_property)

        property_mapping = self.property_repository.get_ota_property_mapping(ota_property.id)
        if not property_mapping:
            property_mapping = OtaPropertyMapping()
        property_mapping.ota_hotel_code = ota_hotel_code
        property_mapping.ota_property_id = ota_property.id
        property_mapping.username = ota_username
        property_mapping.access_token = ota_access_token
        self.property_repository.persist(property_mapping)

        if room_type_config:
            room_mapping = self.property_repository.get_ota_room_mapping(ota_property.id, room_type_config.room_type_id)
            if not room_mapping:
                room_mapping = OtaRoomMapping()
            room_mapping.ota_property_id = ota_property.id
            room_mapping.room_type_id = room_type_config.room_type_id
            room_mapping.ota_room_code = ota_room_code
            self.property_repository.persist(room_mapping)

        if rate_plan_config:
            rate_plan_mapping = self.property_repository.get_ota_rate_plan_mapping(ota_property.id,
                                                                                   room_type_config.room_type_id,
                                                                                   rate_plan_config.rate_plan_id,
                                                                                   ota_rate_plan_code)
            if not rate_plan_mapping:
                rate_plan_mapping = OtaRatePlanMappings()
            rate_plan_mapping.ota_property_id = ota_property.id
            rate_plan_mapping.room_type_id = room_type_config.room_type_id
            rate_plan_mapping.rate_plan_id = rate_plan_config.rate_plan_id
            rate_plan_mapping.ota_rate_plan_code = ota_rate_plan_code
            rate_plan_mapping.ota_rate_plan_name = ota_rate_plan_name
            rate_plan_mapping.rate_push_enabled = rate_push_enabled == 'true'
            self.property_repository.persist(rate_plan_mapping)

    def _check_if_property_is_provided_by_treebo(self, property):
        """
        # ...HARDCODED now!!
        if property provider is null or treebo, then its treated as provided by treebo
        :param ota_property:
        :return:
        """
        return not property.property_detail or not property.property_detail.provider or \
            (str(property.property_detail.provider.name).lower() == 'treebo')
