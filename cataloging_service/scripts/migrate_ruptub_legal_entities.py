import csv

from flask_script import Command, Option


from cataloging_service.models import RuptubLegalEntityDetails
from cataloging_service.models import State
from cataloging_service.infrastructure.repositories import repo_provider


class MigrateRuptubLegalEntitiesCommand(Command):

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read occupancy data from'),)

    def run(self, file_path):
        self.migrate_ruptub_legal_entities(file_path)

    def migrate_ruptub_legal_entities(self, file_path):
        with open(file_path) as legal_entities_csv:
            reader = csv.DictReader(legal_entities_csv)
            legal_entity_details_repository = repo_provider.legal_entity_details_repository
            for row in reader:
                ruptub_legal_entity_details = RuptubLegalEntityDetails()
                state_details = State.query.filter(State.name.ilike(row['state'])).first()
                ruptub_legal_entity_details.state = state_details
                ruptub_legal_entity_details.gstin = row['gstin']
                ruptub_legal_entity_details.date_of_registration = row['date_of_registration']
                ruptub_legal_entity_details.address_line_1 = row['address_line_1']
                ruptub_legal_entity_details.address_line_2 = row['address_line_2']
                ruptub_legal_entity_details.address_city = row['address_city']
                ruptub_legal_entity_details.address_pincode = row['address_pincode']
                legal_entity_details_repository.persist(ruptub_legal_entity_details)
            legal_entity_details_repository.session().commit()

