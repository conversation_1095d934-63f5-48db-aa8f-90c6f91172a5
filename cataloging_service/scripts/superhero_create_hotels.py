import random
import string
from cataloging_service.domain.migrate_to_crs import CRSMigrate
from cataloging_service.client.authz_client import AuthZClient
from cataloging_service.constants.model_choices import PropertyChoices
from collections import defaultdict
import uuid
from treebo_commons.utils import dateutils
from treebo_commons.request_tracing.context import request_context
from cataloging_service.api.v3.schemas.superhero_property_launch import PropertyLaunchSchema
from cataloging_service.client.rate_manager_client import RateManagerClient
from cataloging_service.client.authn_client import AuthNClient

import logging
import os
import csv
import json
import requests
import time

from flask_script.commands import Command, Option
from flask import current_app

from core.property.superhero_property_launch import launch_property
from cataloging_service.domain import service_provider


logger = logging.getLogger(__name__)


class SuperheroCreateHotelsCommand(Command):
    sku_service = service_provider.sku_service
    location_service = service_provider.location_service
    property_service = service_provider.property_service
    room_service = service_provider.room_service

    option_list = (
        Option(
            "--tenant",
            dest="tenant",
            help="tenant id",
        ),
        Option(
            "--filename",
            dest="filename",
            required=True,
            help="The CSV file to read occupancy data from",
        ),
    )

    ROOM_TYPE_CONFIG = {
        "STANDARD": {
            "max_adults": 2,
            "max_children": 1,
            "max_total": 3,
            "room_size": "20",
            "bed_type": "queen_bed",
        },
        "DELUXE": {
            "max_adults": 2,
            "max_children": 2,
            "max_total": 3,
            "room_size": "30",
            "bed_type": "queen_bed",
        },
        "PREMIUM": {
            "max_adults": 3,
            "max_children": 2,
            "max_total": 4,
            "room_size": "40",
            "bed_type": "king_bed",
        },
        "DEFAULT": {
            "max_adults": 2,
            "max_children": 2,
            "max_total": 3,
            "room_size": "20",
            "bed_type": "queen_bed",
        },
    }

    def run(self, filename=None, tenant="tnt987760"):
        if not filename:
            logger.error("missing parameter filename")
            return

        request_context.tenant_id = tenant
        request_context.disable_rmq_events = True
        filepath = os.path.join(os.path.abspath("resources"), filename)
        logger.info("filepath " + filepath)
        data_packets = []
        if not os.path.exists(filepath):
            logger.info("the file path doesnt exists " + filename)
            return

        with open(filepath) as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)
            headers = [x.lower() for x in headers]
            for row in reader:
                packet = {}
                for idx in range(len(row)):
                    packet[headers[idx]] = row[idx]
                data_packets.append(packet)
                # break

        errors = [["Row No.", "Hotel Name", "Error"]]
        searlized_data = []
        for idx, packet in enumerate(data_packets):
            data_row_index = idx + 1
            packet = self.lower_(packet)
            try:
                data = self._create_schema_packet(packet)
                # handle tarrif and rate plan relates fields now
            except Exception as e:
                logger.exception(e)
                errors.append([data_row_index, packet["property_name"], str(e)])
                continue

            try:
                serializer_data = PropertyLaunchSchema().load(data)
                if packet.get("property_id"):
                    serializer_data["id"] = packet["property_id"]
                searlized_data.append(serializer_data)
            except Exception as e:
                errors.append([data_row_index, data["name"], str(e)])
                continue

        if len(errors) > 1:
            # abort process ?
            # TODO maybe if more than 10% data is eroneous, we should abort
            resp_file_name = "resp_" + filename
            logger.error(
                "DATA ISSUE - few rows do not have data in proper format, fix them and upload the file again.\
                     please check the file {0}".format(
                    resp_file_name
                )
            )
            filepath = self._write_data_to_file(
                errors, os.path.abspath("resources"), resp_file_name
            )
            return

        # If there are no searialization errors, it means the file is good for processing.
        # Begin hotel creation now
        response_data = []
        response_data.append(["Hotel ID/Name", "Status", "Details"])
        for data in searlized_data:
            if data.get("id"):
                # ignore property creation, simply create the rate plans
                new_property = self.property_service.get_property(data["id"])
                room_type_configs = self.room_service.get_room_type_configurations(data["id"])
                skus = self.sku_service.sget_all_skus_for_hotels([new_property.id])
            else:
                try:
                    new_property, room_type_configs, skus = launch_property(
                        PropertyLaunchSchema().dump(data)
                    )
                    logger.info("new property created with id " + str(new_property.id))
                except Exception as e:
                    logger.exception("Error creating hotel " + data["name"])
                    response_data.append([data["name"], "HOTEL_NOT_CREATED", str(e)])
                    continue

            status = []
            try:
                # migrate the property to CRS
                crs_migrate = CRSMigrate(str(new_property.id))
                crs_migrate.start()
            except Exception:
                logger.exception("Error running CRS Migration " + data["name"])
                status.append("CRS_MIGRATION_FAILED")

            # create AuthN user for primary email
            try:
                auth_id = AuthNClient.create_user(
                    data["owner_name"], "", data["owner_email"], "abc123"
                )
                application_id = "treebo-pms"
                AuthZClient.assign_role(
                    str(auth_id), str(new_property.id), "super-admin", application_id
                )
            except Exception:
                # ignore if already created.
                status.append("AUTHN_AUTHZ_USER_CREATION_FAILED")

            # create rate plan packages & rate plans
            rate_plan_success = self._create_rate_plans(new_property, skus, room_type_configs)

            if rate_plan_success:
                if len(status) > 0:
                    status.append("RATE_PLAN_CREATION_SUCCESS")
                else:
                    status.append("ALL_SUCCESS")
            else:
                status.append("SOME_RATE_PLANS_NOT_CREATED")

            response_data.append([data["name"], json.dumps(status), str(new_property.id)])

        # write data to a file
        resp_file_name = "resp_" + filename
        logger.info(
            "File successfully processed. Check final status in file {0}".format(resp_file_name)
        )
        filepath = self._write_data_to_file(
            response_data, os.path.abspath("resources"), resp_file_name
        )
        request_context.disable_rmq_events = False

    def _write_data_to_file(self, data, directory_path, filename=None):
        if not filename:
            filename = uuid.uuid4().hex + ".csv"

        filepath = os.path.join(directory_path, filename)
        with open(filepath, "w") as f:
            writer_ = csv.writer(f)
            for row in data:
                writer_.writerow(row)
        return filepath

    def _create_rate_plans(self, property, skus, room_type_configs):
        child_policy = {
            "charge_per_child": 0,
            "child_allowed": True,
            "unit_of_charge": "fixed_value",
        }
        payment_policy = {
            "advance_payment_percentage": 0,
            "payment_before_checkin_required": False,
            "pay_at_checkin_required": False,
        }
        cancellation_policy = []
        restrictions = None
        room_type_occupancy_mappings = []
        for room_type_config in room_type_configs:
            room_type_occupancy_mappings.extend(
                [
                    {
                        "adult_count": idx + 1,
                        "room_type_id": room_type_config.room_type.code,
                    }
                    for idx in range(room_type_config.adults)
                ]
            )

        rate_plans = [
            {"name": "EP Plan", "code": "EP", "description": "EP Rate Plan", "inclusions": []},
            {
                "name": "CP Plan",
                "code": "CP",
                "description": "CP Rate Plan",
                "inclusions": ["breakfast"],
            },
            {
                "name": "MAP Plan",
                "code": "MAP",
                "description": "MAP Rate Plan",
                "inclusions": ["breakfast", "lunch"],
            },
            {
                "name": "AP Plan",
                "code": "AP",
                "description": "AP Rate Plan",
                "inclusions": ["breakfast", "lunch", "dinner"],
            },
        ]
        for rate_plan_config in rate_plans:
            package_name = rate_plan_config["code"]
            inclusions = self._get_inclusion_config(skus, *rate_plan_config["inclusions"])
            try:
                package = RateManagerClient.create_package(property.id, package_name, inclusions)
                rate_plan = RateManagerClient.create_rateplan(
                    rate_plan_config["name"],
                    rate_plan_config["code"] + "-" + str(property.id),
                    rate_plan_config["description"],
                    package["package_id"],
                    child_policy,
                    payment_policy,
                    cancellation_policy,
                    property.id,
                    restrictions,
                    room_type_occupancy_mappings,
                )
                logger.info(
                    "{0} with id {1} successfully created for hotel {2}".format(
                        rate_plan_config["description"], rate_plan["rate_plan_id"], str(property.id)
                    )
                )
            except Exception:
                logger.exception(
                    "Error creating rate plan {0} for hotel {1}, hotel id {2}".format(
                        rate_plan_config["name"], property.name, str(property.id)
                    )
                )
        # call Rate Manager and get all rate plans for the hotel
        try:
            rate_plans = RateManagerClient.get_all_rate_plans(str(property.id))
            return len(rate_plans) == 4
        except Exception:
            return False

    def _get_inclusion_config(self, skus, breakfast=None, lunch=None, dinner=None):
        breakfast_inclusion = None
        lunch_inclusion = None
        dinner_inclusion = None
        for sku in skus:
            sku_name = sku.name.lower()
            if breakfast and not breakfast_inclusion and sku_name.find(breakfast) > 0:
                breakfast_inclusion = {
                    "sku_id": str(sku.id),
                    "frequency": sku.frequency,
                    "offering": sku.offering,
                }
            elif lunch and not lunch_inclusion and sku_name.find(lunch) > 0:
                lunch_inclusion = {
                    "sku_id": str(sku.id),
                    "frequency": sku.frequency,
                    "offering": sku.offering,
                }
            elif dinner and not dinner_inclusion and sku_name.find(dinner) > 0:
                dinner_inclusion = {
                    "sku_id": str(sku.id),
                    "frequency": sku.frequency,
                    "offering": sku.offering,
                }

            if breakfast_inclusion and lunch_inclusion and dinner_inclusion:
                break

        inclusions = [breakfast_inclusion, lunch_inclusion, dinner_inclusion]
        inclusions = [x for x in inclusions if x]
        return inclusions

    def _create_schema_packet(self, packet):
        address = json.loads(packet["complete_address"])

        city_object = self.location_service.get_all_related_cities(address["city"]).first()
        if not city_object:
            # try searching it for localilty
            locality = self.location_service.get_all_related_localities(address["city"]).first()
            if locality:
                city_object = self.location_service.get_city(locality.city_id)
            else:
                city_object = self._create_city(
                    address["city"],
                    address.get("state", ""),
                    address["country"],
                    address["pincode"],
                )

        state_object = self.location_service.get_state(city_object.state_id)
        state_name = state_object.name

        property_address = {
            "line1": address["address"],
            "line2": "",
            "city_id": str(city_object.id),
            "city_name": city_object.name,
            "state_name": state_name,
            "country_name": address["country"],
            "pincode": address["pincode"],
        }  # Nested Schema

        room_type_tariffs = defaultdict(dict)
        single_occupancy_tariff = json.loads(packet["single_occupancy_tariff"])
        for room_tariff in single_occupancy_tariff:
            room_type = room_tariff["room_type"].upper()
            room_type_tariffs[room_type]["single_occupancy_tariff"] = int(
                room_tariff["single_occupancy_tariff"]
            )

        extra_occupancy_tariff = json.loads(packet["extra_occupancy_tariff"])
        for extra_tariff in extra_occupancy_tariff:
            room_type = extra_tariff["room_type"].upper()
            room_type_tariffs[room_type]["extra_occupancy_tariff"] = int(
                extra_tariff["extra_occupancy_tariff"]
            )

        room_types = json.loads(packet["room_type"])
        room_type_details = []
        for room_type in room_types:
            room_type_name = room_type["room_type"].upper()
            room_type_config = (
                self.ROOM_TYPE_CONFIG[room_type_name]
                if self.ROOM_TYPE_CONFIG.get(room_type_name)
                else self.ROOM_TYPE_CONFIG["DEFAULT"]
            )
            room_type_details.append(
                {
                    "max_adults": room_type_config["max_adults"],
                    "max_children": room_type_config["max_children"],
                    "max_total": room_type_config["max_total"],
                    "room_size": room_type_config["room_size"],
                    "room_type": room_type_name,
                    "bed_type": room_type_config["bed_type"],
                    "rate_plans": [],
                }
            )

        room_type_tariffs = [
            {
                "room_type": room_type["room_type"].upper(),
                "single_occupancy_tariff": room_type_tariffs[room_type["room_type"].upper()][
                    "single_occupancy_tariff"
                ],
                "extra_occupancy_tariff": room_type_tariffs[room_type["room_type"].upper()][
                    "extra_occupancy_tariff"
                ],
            }
            for room_type in room_types
        ]

        room_details = json.loads(packet["room_type_code"])
        room_details_dict = [
            {
                "room_number": room["room_type_code"],
                "room_type": room["room_type"].upper(),
                "building_number": "1",
                "floor_number": 1,
            }
            for room in room_details
        ]  # Nested Schema

        supported_currencies = [x.strip() for x in packet["supported currencies"].split(",")]
        if "INR" not in supported_currencies:
            supported_currencies.append("INR")
        tenant_configs = {
            "supported_payment_currencies": supported_currencies,
            "e_reg_card": '{"enabled":true,"required":false,"level":"room"}',
            "cashiering_enabled": "true",
            "ups_enabled": "true",
            "rate_manager_enabled": "true",
        }  # Nested Schema

        ar_related_tenant_configs = {
            "hotel_level_accounts_receivable": "true",
            "is_tds_override_enabled": "false",
            "is_tds_settlement_enabled": "false",
            "tds_settlement_percent": "0",
        }  # Nested Schema

        sku_categories = self.sku_service.sget_all_sku_categories()
        sku_categories = {x.name: x for x in sku_categories}

        # Sku details
        sku_details = []
        add_on_tariffs = json.loads(packet["add_on_tariff"])

        sku_name_concat = " ".join(
            [sku["add_on_name"].replace("_", " ").lower() for sku in add_on_tariffs]
        )

        if (
            sku_name_concat.find("breakfast") == -1
            or sku_name_concat.find("lunch") == -1
            or sku_name_concat.find("dinner") == -1
        ):
            raise Exception("Don't have all the required SKUs to create all the rate plans")

        for sku in add_on_tariffs:
            category = sku["add_on_category"]
            if category not in sku_categories.keys():
                raise Exception(
                    "Couldn't find sku category "
                    + sku["sku_category"]
                    + ", please create it manually"
                )

            sku_name = sku["add_on_name"].replace("_", " ")
            sky_category = sku_categories[category]
            frequency = None
            offering = None
            if (
                sku_name.lower().find("breakfast") > 0
                or sku_name.lower().find("lunch") > 0
                or sku_name.lower().find("dinner") > 0
            ):
                frequency = {"count": 1, "frequency_type": "daily"}
                offering = {"offered_quantity": 1, "offering_type": "per_guest"}

            sku_details.append(
                {
                    "category": category,
                    "category_id": sky_category.id,
                    "name": sku_name,
                    "rack_rate": int(sku["add_on_tariff"]),
                    "offering": offering,
                    "frequency": frequency,
                    "hsn_sac": sky_category.hsn_sac,
                }
            )

        google_map_link = "https://www.google.com/maps/search/?api=1&query={0},{1}".format(
            packet["latitude"], packet["longitude"]
        )
        pan, gstin = self._generate_random_legal_ids(
            property_address["state_name"], property_address["country_name"]
        )
        data = dict(
            name=packet["property_name"],
            previous_name=packet["property_name"],
            signed_date=dateutils.date_to_ymd_str(dateutils.current_date()),
            launch_date=dateutils.date_to_ymd_str(dateutils.current_date()),
            legal_name=packet["property_name"],
            status=PropertyChoices.STATUS_LIVE,
            gstin=gstin,
            pan=pan,
            address=property_address,
            legal_address=property_address,
            legal_signature="",
            reception_mobile="",
            reception_landline="",
            email="",
            owner_name=packet["partner_name"],
            owner_email=packet["partner_email"],
            owner_phone=packet["partner_phone"],
            room_type_tariffs=room_type_tariffs,
            room_type_details=room_type_details,
            room_details=room_details_dict,
            star_rating=3,
            total_floors=1,
            base_currency_code="INR",
            timezone=packet["time_zone"],
            country_code="IN",
            hotel_logo="",
            latitude=packet["latitude"],
            longitude=packet["longitude"],
            google_maps_link=google_map_link,
            standard_checkin_time=packet["checkin_time"],
            free_early_checkin_time="6:00 AM",
            free_late_checkout_time="3:00 PM",
            standard_checkout_time=packet["checkout_time"],
            early_checkin_fee=0,
            late_checkout_fee=0,
            sku_details=sku_details,
            ar_related_tenant_configs=ar_related_tenant_configs,
            tenant_configs=tenant_configs,
        )
        # data["bank_details"] = None  # Nested Schema
        return data

    def lower_(self, data):
        if type(data) == list:
            data = [self.lower_(x) for x in data]
        elif type(data) == dict:
            data = {x.lower(): self.lower_(y) for x, y in data.items()}
        elif type(data) == str:
            # check if its a valid json
            try:
                json_object = json.loads(data)
                data = json.dumps(self.lower_(json_object))
            except ValueError:
                pass

        return data

    def _generate_random_legal_ids(self, state_name, country_name):
        # frist 2 digits are state ids 01-35
        # next 10 digits are PAN number
        # 13th digit or 1 or 2
        # 14th digit it Z
        # 15th digit is random
        country = self.location_service.get_country_by_name(country_name)
        state = self.location_service.get_state_by_name(state_name, country.id)
        if not state:
            raise Exception("couldn't find state {0}".format(state_name))
        pan = (
            "".join(random.choice(string.ascii_uppercase) for _ in range(5))
            + "".join(random.choice(string.digits) for _ in range(4))
            + random.choice(string.ascii_uppercase)
        )
        state_code = str(state.code) if len(str(state.code)) == 2 else "0" + str(state.code)
        gstin = str(state_code) + pan + "1Z" + random.choice(string.ascii_uppercase)
        return pan, gstin

    def _create_city(self, city_name, state_name, country_name, pincode):

        api_key = current_app.config["GOOGLE_API_KEY"]
        address = city_name
        if state_name:
            address += "," + state_name
        if country_name:
            address += "," + country_name
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = dict(key=api_key, address=address)

        time.sleep(0.02)
        response = requests.get(url, params)

        if response.status_code != 200:
            logger.error(response.text)
            raise Exception("Invalid city name " + city_name)

        results = response.json()["results"]
        if len(results) > 1:
            raise Exception("More than one city found with name - " + city_name)
        result = results[0]
        lat = result["geometry"]["location"]["lat"]
        lng = result["geometry"]["location"]["lng"]
        city = ""
        state = ""
        for component in result["address_components"]:
            if "locality" in component["types"]:
                city = component["long_name"]

            if "administrative_area_level_1" in component["types"]:
                state = component["long_name"]

        if not city:
            raise Exception(
                "couldn't identify city '{0}', please create manually".format(city_name)
            )

        city_object = self.location_service.get_all_related_cities(city).first()
        if city_object:
            return city_object

        try:
            country_object = self.location_service.get_country_by_name(country_name)
        except Exception:
            logger.exception("error loading country '{0}'".format(country_name))
            raise

        try:
            state_object = self.location_service.get_state_by_name(state_name, country_object.id)
        except Exception:
            logger.exception("error loading state '{0}', please create manually".format(state_name))
            raise

        city_object = self.location_service.create_city(city, lat, lng, state_object.id)
        return city_object
