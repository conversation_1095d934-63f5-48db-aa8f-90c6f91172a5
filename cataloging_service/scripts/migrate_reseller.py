import csv
import logging
import re

from flask_script import Option
from flask_script.commands import Command

from cataloging_service.constants import constants
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import BankDetail

logger = logging.getLogger(__name__)


class MigrateResellerCommand(Command):
    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read occupancy data from'),
                   Option('--error_csv_path', dest='error_path', required=True,
                          help='The CSV file to read error occupancy data from')
                   )

    def run(self, file_path, error_path):
        self.migrate_resellers(file_path, error_path)

    def migrate_resellers(self, file_path, error_path):
        unsuccessful_rows = []
        with open(file_path) as reseller_file:
            reader = csv.reader(reseller_file)
            for row in reader:
                # logger.info('Migrating row: %s' % row)
                property_id = row[0].strip().lower()
                gstin = row[1].strip().upper()
                gst_state_code = row[2].strip()
                treebo_state_code = row[3].strip()
                pan = row[4].strip()
                bank_name = row[5].strip()
                bank_acc_holder_name = row[6].strip()
                bank_acc_number = row[7].strip()
                bank_ifsc_code = row[8].strip()
                bank_acc_type = row[9].strip()
                navision_code = row[10].strip()
                sold_as = row[11].strip()
                hotel_seal = row[12].strip()

                try:
                    if property_id and gstin and gst_state_code and treebo_state_code and pan \
                             and sold_as and hotel_seal:
                        if navision_code:
                            if not re.match(constants.NAVISION_CODE_PATTERN, navision_code):
                                raise Exception('Invalid navision code')
                        info_dict = dict(property_id=property_id, gstin=gstin, gst_state_code=gst_state_code,
                                         treebo_state_code=treebo_state_code, pan=pan, bank_name=bank_name,
                                         bank_acc_holder_name=bank_acc_holder_name, bank_acc_number=bank_acc_number,
                                         bank_ifsc_code=bank_ifsc_code, bank_acc_type=bank_acc_type,
                                         navision_code=navision_code, sold_as=sold_as, hotel_seal=hotel_seal)
                        self.migrate_single(info_dict)
                    else:
                        raise Exception('Invalid row of data')
                except Exception as e:
                    # logger.exception('Error while migrating row: %s' % row)
                    unsuccessful_rows.append((row, e))

        print('Unsuccessful rows:')
        for unsuccessful_row in unsuccessful_rows:
            print('%s | %s' % (
                unsuccessful_row[0], unsuccessful_row[1]))  # Using print because it is easier to parse later

        self.write_csv(error_path, unsuccessful_rows)

    @atomic_operation
    def migrate_single(self, info_dict):
        # check for property/hotel
        property = repo_provider.property_repository.get_property(info_dict.get("property_id"))
        if not property:
            raise Exception('Property not found')

        property_detail = property.property_detail

        # check for gst_state_code and state info
        state = repo_provider.property_location_repository.get_state(info_dict.get('treebo_state_code'))
        if not state:
            raise Exception('State not found')

        # Bank info
        existing_bank_detail = property_detail.bank_detail
        if not existing_bank_detail:
            bank_detail = BankDetail()
            # update bank details
            bank_detail.account_name = info_dict.get('bank_acc_holder_name', None)
            bank_detail.account_number = info_dict.get('bank_acc_number', None)
            bank_detail.account_type = info_dict.get('bank_acc_type', None)
            bank_detail.ifsc_code = info_dict.get('bank_ifsc_code', None)
            bank_detail.bank = info_dict.get('bank_name', None)
            property_detail.bank_detail = bank_detail
        else:
            if not existing_bank_detail.account_name:
                existing_bank_detail.account_name = info_dict.get('bank_acc_holder_name', None)
            if not existing_bank_detail.account_number:
                existing_bank_detail.account_number = info_dict.get('bank_acc_number', None)
            if not existing_bank_detail.account_type:
                existing_bank_detail.account_type = info_dict.get('bank_acc_type', None)
            if not existing_bank_detail.ifsc_code:
                existing_bank_detail.ifsc_code = info_dict.get('bank_ifsc_code', None)
            if not existing_bank_detail.bank:
                existing_bank_detail.bank = info_dict.get('bank_name', None)
            property_detail.bank_detail = existing_bank_detail

        # taxation details
        sold_as = repo_provider.meta_repository.rget_entity_param_by_key_val(entity='PropertyDetail',
                                                                             field='sold_as_id',
                                                                             value=info_dict.get('sold_as'),
                                                                             validate=True)
        if not sold_as:
            raise Exception('Incorrect entry for sold_as param of property')
        property_detail.gstin = info_dict.get('gstin')
        property_detail.pan = info_dict.get('pan')
        property_detail.navision_code = info_dict.get('navision_code')
        property_detail.sold_as = sold_as

        # create path
        s3_path = '/' + 'reseller/' + info_dict.get('hotel_seal')
        property_detail.legal_signature = s3_path

        try:
            repo_provider.property_repository.persist(property_detail)
        except Exception:
            raise

    def write_csv(self, error_path, unsuccessful_rows):
        with open(error_path, 'w') as csvfile:
            filewriter = csv.writer(csvfile)
            try:
                for error_row in unsuccessful_rows:
                    data, error_str = error_row
                    row = data + [error_str]
                    filewriter.writerow(row)
            except Exception as err:
                print("Caught Exception. {err}".format(
                    err=err.__repr__()
                ))
                raise
