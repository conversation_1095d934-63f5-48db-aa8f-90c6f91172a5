import logging
import re

from flask_script.commands import Command

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider

logger = logging.getLogger(__name__)


class SetRoomSize(Command):
    property_repository = repo_provider.property_repository

    def run(self):
        self.set_room_size()

    @atomic_operation
    def set_room_size(self):
        rooms = self.property_repository.get_all_rooms()
        for room in rooms:
            if room.size:
                size_search = re.search('(\d+\.?\d*)', room.size)
                if size_search:
                    room.room_size = float(size_search.group(1))

        self.property_repository.persist_all(rooms)
