import logging

from flask_script import Command

from cataloging_service.client import client_provider
from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import Landmark, PropertyLandmark

logger = logging.getLogger(__name__)


class MigrateLandmarkCommand(Command):

    def __init__(self):
        super(MigrateLandmarkCommand, self).__init__()
        # https://docs.google.com/spreadsheets/d/1sEZTINfe79dFkstEw97NiC6WjN4EacJsQzSsteXw6ls/edit#gid=**********
        self.sheet_id = '11Guy3UYJPxApdXWSoqsFh6_BI1p_6gz8P75_0P4TNRI'
        self.sheet_name = 'Cat_DB_Directions_insertF1'
        self.sheet_client = client_provider.google_sheets_client
        self.property_repository = repo_provider.property_repository

    def run(self):
        self.process_landmarks()

    @atomic_operation
    def create_property_landmark(self, cs_id, landmark, direction, distance, is_update):

        property = self.property_repository.get_property(cs_id)
        if property:
            if is_update:
                # check if property_landmark has both given cs_id and landmark_id
                # get the object and update
                property_landmark = self.property_repository.get_property_landmark_by_property_and_landmark(property.id,
                                                                                                            landmark.id)
                if property_landmark:
                    if distance:
                        property_landmark.distance_from_property = distance
                    if direction:
                        property_landmark.property_direction = direction

                    self.property_repository.persist(property_landmark)
                    logger.info('Existing property Landmark updated property %s and landmark %s' %
                                (property_landmark.property, property_landmark.landmark))
                else:
                    self.insert_property_landmark(property, landmark, distance, direction)
            else:
                # Create a new record for given property
                self.insert_property_landmark(property, landmark, distance, direction)
        else:
            raise Exception('Property not found for cs_id %s' % cs_id)

    @atomic_operation
    def insert_property_landmark(self, property, landmark, distance, direction):
        property_landmark = PropertyLandmark()
        property_landmark.landmark_id = landmark.id
        property_landmark.property_id = property.id
        property_landmark.type = 'Landmark {}'.format(self.get_type_count(property))
        property_landmark.landmark = landmark
        property_landmark.property = property
        if distance:
            property_landmark.distance_from_property = distance
        if direction:
            property_landmark.property_direction = direction
        self.property_repository.persist(property_landmark)

        logger.info('Property Landmark created property %s and landmark %s' % (property_landmark.property,
                                                                               property_landmark.landmark))

    def get_type_count(self, property):
        # check if property_landmark exists in property_landmark, create a new type
        properties_landmarks = self.property_repository.get_latest_property_landmark_by_property(property.id)
        return (len(properties_landmarks) + 1) if properties_landmarks else 1

    @atomic_operation
    def process_landmarks(self):
        row_num = 2

        landmark = None

        while True:
            try:
                row = self.sheet_client.read_row(self.sheet_id, self.sheet_name, row_num, 6)
                logger.info('Row %s: %s' % (row_num, row))
                row_num += 1

                if not row:
                    break

                cs_id = row[0]
                landmark_name = row[1]
                direction = row[2]
                distance = row[6]
                latitude = row[8]
                longitude = row[9]
                is_update = True

                if landmark_name and latitude and longitude:
                    landmark = self.property_repository.get_landmark_by_name(landmark_name)
                    logger.info('Existing Landmark : %s' % landmark)
                    if not landmark:
                        landmark = Landmark()
                        landmark.name = landmark_name
                        landmark.latitude = latitude
                        landmark.longitude = longitude
                        landmark = self.property_repository.persist(landmark)
                        logger.info('Created Landmark %s: %s' % (landmark.id, landmark.name))
                        is_update = False

                    if cs_id:
                        # cs_id = str(cs_id).replace("'", "").strip()
                        self.create_property_landmark(cs_id, landmark, direction, distance,
                                                                          is_update)
                else:
                    logger.info('Landmark data not proper for row %s: %s' % (row_num, row))
            except Exception as e:
                logger.error('Error with msg %s' % e)

