import logging

from flask_script.commands import Command

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider

logger = logging.getLogger(__name__)


class SetRoomConfig(Command):
    property_repository = repo_provider.property_repository

    def run(self):
        self.update_room_config()

    @atomic_operation
    def update_room_config(self):
        room_configs = self.property_repository.get_property_room_type_configurations()

        for room_config in room_configs:
            rooms = self.property_repository.get_property_rooms(room_config.property_id, room_config.room_type_id)
            room_config.property_rooms = rooms
            self.property_repository.persist(room_config)
