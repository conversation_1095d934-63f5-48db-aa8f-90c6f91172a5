import logging

from flask_script.commands import Command
from sqlalchemy.exc import IntegrityError

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import RatePlanConfiguration

logger = logging.getLogger(__name__)


class SetHotelRatePlan(Command):
    property_repository = repo_provider.property_repository

    def run(self):
        self.set_hotel_rate_plan()

    def set_hotel_rate_plan(self):
        properties = self.property_repository.get_all_properties()
        rate_plans = self.property_repository.get_all_rate_plans()
        for hotel in properties:
            for rate_plan in rate_plans:
                try:
                    self._set_hotel_rate_plan(hotel, rate_plan)
                except IntegrityError:
                    logger.exception('Duplicate rate plan: %s, %s', hotel, rate_plan)
                else:
                    logger.info('rate plan: %s, %s set', hotel, rate_plan)

    @atomic_operation
    def _set_hotel_rate_plan(self, hotel, rate_plan):
        hotel_rate_plan = RatePlanConfiguration()
        hotel_rate_plan.property = hotel
        hotel_rate_plan.rate_plan = rate_plan

        self.property_repository.persist(hotel_rate_plan)
