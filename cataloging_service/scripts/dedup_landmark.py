import logging

from flask_script.commands import Command

from cataloging_service.infrastructure.decorators import atomic_operation
from cataloging_service.infrastructure.repositories import repo_provider
from cataloging_service.models import PropertyLandmark
from cataloging_service.utils import Utils

logger = logging.getLogger(__name__)


class DedupLandmark(Command):
    property_repository = repo_provider.property_repository

    def run(self):
        self.dedup_landmark()

    def dedup_landmark(self):
        landmarks = self.property_repository.get_landmarks()
        landmark_map = dict()
        for landmark in landmarks:
            try:
                self.create_property_landmark(landmark, landmark_map)
            except Exception as e:
                logger.error('Error while creating property landmark: %s' % e)

    @atomic_operation
    def create_property_landmark(self, landmark, landmark_map):
        name = Utils.strip_extra_whitespace(landmark.name)
        name = name.lower()
        key = (name, landmark.latitude, landmark.longitude)
        if key not in landmark_map:
            landmark_map[key] = landmark
        else:
            logger.info('Found duplicate landmark: %s, %s' % (landmark, landmark_map[key]))
        property_landmark = PropertyLandmark()
        property_landmark.landmark_id = landmark_map[key].id
        property_landmark.property_id = landmark.property_id
        property_landmark.type = landmark.type
        property_landmark.distance_from_property = landmark.distance_from_property
        property_landmark.hatchback_cab_fare = landmark.hatchback_cab_fare
        property_landmark.sedan_cab_fare = landmark.sedan_cab_fare
        property_landmark.property_direction = landmark.property_direction
        self.property_repository.persist(property_landmark)
