import pandas as pd
import logging
from flask_script import Command, Option

from cataloging_service.constants.constants import DEFAULT_COST_CENTER_ID_BRAND_CODE
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.repositories import repo_provider, TenantConfigRepository
from cataloging_service.models import Brand
from cataloging_service.utils import Utils

logger = logging.getLogger(__name__)

tenant_brand_mapping = {
    "treebo": ["Treebo", "Medalio"],
    "tntpis": ["PIS"]
}


class MigrateCostCenterIDs(Command):
    property_repo = repo_provider.property_repository
    property_service = service_provider.property_service

    option_list = (Option('--file_path', dest='file_path', required=True,
                          help='The CSV file to read sku rates data from'),
                   Option('--tenant_id', dest='tenant_id', required=True,
                          help='Tenant id for the tenant this ')
                   )

    def run(self, file_path, tenant_id):
        self.migrate_cost_center_ids(file_path, tenant_id)

    def migrate_cost_center_ids(self, file_path, tenant_id):
        try:
            df = pd.read_csv(file_path)
        except Exception as e:
            logger.error("Error reading CSV file: {0}".format(e))
            return
        properties, failed_properties = [], []
        for index, row in df.iterrows():
            if index == 0:
                continue
            property_id = row[0]
            try:
                region = row[4]
                brand = row[10]
                city = row[2]

                brand = self.property_service.get_brand_for_cost_center_id(brand)
                brand_code = brand.brand_code if (brand and brand.brand_code) else None
                if not brand_code:
                    brand_code = TenantConfigRepository().load_v2(config_name=DEFAULT_COST_CENTER_ID_BRAND_CODE)[0].config_value
                print("Generating cost center id for {0}, {1}, {2}, {3}".format(property_id, region, brand_code, city))
                cost_center_id = Utils.generate_cost_center_id(property_id, brand_code, region, city)
                existing_property = self.property_repo.get_property(property_id=property_id)
                if not existing_property:
                    logger.warning("Property with id {0} not found. Skipping.".format(property_id))
                    continue

                existing_property.region = region
                existing_property.cost_center_id = cost_center_id
                properties.append(existing_property)
                logger.info("Updated property {0} with cost_center_id {1}".format(property_id, cost_center_id))
            except Exception as e:
                logger.error("Error processing row {0} for property_id {1}: {2}".format(index, property_id, e))
                failed_properties.append(property_id)
                continue
        self.property_repo.persist_all(properties)
        self.property_repo.session().commit()
        print("Success {0}, Failed {1}".format(properties, failed_properties))
