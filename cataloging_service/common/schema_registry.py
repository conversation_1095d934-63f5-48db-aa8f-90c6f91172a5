"""
Schema registry utility for OpenAPI documentation
Provides the swag_schema decorator without circular import issues
"""

# Global list to store registered schemas
registered_schemas = []


def swag_schema(cls):
    """
    Decorator to register Pydantic schemas for OpenAPI documentation

    Usage:
        @swag_schema
        class MySchema(BaseModel):
            field: str

    Args:
        cls: Pydantic schema class to register

    Returns:
        The same class (transparent decorator)
    """
    if cls not in registered_schemas:
        registered_schemas.append(cls)
    return cls


def get_registered_schemas():
    """
    Get all registered schemas

    Returns:
        List of registered schema classes
    """
    return registered_schemas


def clear_registered_schemas():
    """
    Clear all registered schemas (useful for testing)
    """
    global registered_schemas
    registered_schemas = []
