from flask import Blueprint, request, jsonify
from marshmallow import fields, Schema
from treebo_commons.money.money_field import MoneyField

from cataloging_service.domain import service_provider


class PriceItemResponseSchema(Schema):
    pre_tax_price = MoneyField(description='Amount exclusive of taxes')
    sku_code = fields.String()


class PriceResponseSchema(Schema):
    seller_id = fields.String()
    sku_prices = fields.Nested(PriceItemResponseSchema, many=True)


bp = Blueprint('sku_pricing_apis', __name__)

sku_pricing_service = service_provider.sku_pricing_service


@bp.route('/v1/sellers/<string:seller_id>/prices', methods=['GET'])
def get_pretax_prices(seller_id):
    if not seller_id:
        return "seller id missing in request", 400

    sku_prices = sku_pricing_service.get_pretax_prices(seller_id=seller_id,
                                                      sku_codes=list(request.args['sku_codes'].split(',')))
    response = PriceResponseSchema().dump(sku_prices)
    return jsonify({'data': response}), 200
