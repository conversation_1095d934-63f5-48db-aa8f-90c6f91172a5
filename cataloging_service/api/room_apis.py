import logging

from flask import jsonify, request
from flask.blueprints import Blueprint

from cataloging_service.api.request_objects import RoomTypeConfigurationRequest, RoomRequest
from cataloging_service.api.schemas import RoomTypeConfigurationSchema, RoomSchema, RoomTypeSchema
from cataloging_service.api.validators import RoomTypeConfigurationValida<PERSON>, RoomValidator, ModifyRoomValidator
from cataloging_service.domain import service_provider
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.decorators import raw_json
from cataloging_service.models import RoomTypeConfiguration, Room
from cataloging_service.thread_locals import app_context

bp = Blueprint('room_apis', __name__)

logger = logging.getLogger(__name__)

property_service = service_provider.property_service
room_service = service_provider.room_service


@bp.route('/properties/<string:property_id>/room-type-configs/', methods=['POST'])
@bp.route('/v1/properties/<string:property_id>/room-type-configs/', methods=['POST'])
@raw_json(RoomTypeConfigurationValidator)
def add_room_type_configuration(property_id, parsed_request):
    # TODO: 0 calls
    property = property_service.get_property(property_id)
    room_type_config_addition_request = RoomTypeConfigurationRequest(parsed_request)
    room_type_config_object = room_service.add_property_room_type_config(property, room_type_config_addition_request)

    return jsonify(RoomTypeConfigurationSchema().dump(room_type_config_object)), 201


@bp.route('/properties/<string:property_id>/room-type-configs/<property_room_type_config_id>', methods=['PUT'])
@bp.route('/v1/properties/<string:property_id>/room-type-configs/<property_room_type_config_id>', methods=['PUT'])
@raw_json(RoomTypeConfigurationValidator)
def update_room_type_configuration(property_id, property_room_type_config_id, parsed_request):
    # TODO: 0 calls
    room_type_config_object = room_service.get_property_room_type_config(property_id, property_room_type_config_id)
    room_type_config_updation_request = RoomTypeConfigurationRequest(parsed_request, room_type_config_object)
    room_type_config_object = room_service.update_property_room_type_config(room_type_config_object,
                                                                            room_type_config_updation_request)

    return jsonify(RoomTypeConfigurationSchema().dump(room_type_config_object)), 200


@bp.route('/properties/<string:property_id>/room-type-configs/', methods=['GET'])
@bp.route('/v1/properties/<string:property_id>/room-type-configs/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={RoomTypeConfiguration: lambda x: x.property_id,
                                                                        Room: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600)
def get_room_type_configurations(property_id):
    # TODO: Optimise this also
    # TODO: 206,550.90 calls
    return jsonify(
        RoomTypeConfigurationSchema().dump(room_service.get_room_type_configurations(property_id), many=True)), 200


@bp.route('/properties/<string:property_id>/rooms/', methods=['POST'])
@bp.route('/v1/properties/<string:property_id>/rooms/', methods=['POST'])
@raw_json(RoomValidator)
def add_property_room(property_id, parsed_request):
    # TODO: 0 calls
    property = property_service.get_property(property_id)
    add_room_request = RoomRequest(parsed_request)
    return jsonify(RoomSchema().dump(room_service.add_property_room(property, add_room_request))), 201


@bp.route('/properties/<string:property_id>/rooms/<int(min=1):room_id>', methods=['PUT'])
@bp.route('/v1/properties/<string:property_id>/rooms/<int(min=1):room_id>', methods=['PUT'])
@raw_json(ModifyRoomValidator)
def update_property_room(property_id, room_id, parsed_request):
    # TODO: 0 calls
    room_object = room_service.get_property_room(property_id, room_id)
    update_room_request = RoomRequest(parsed_request, room_object)
    return jsonify(RoomSchema().dump(room_service.update_property_room(room_object, update_room_request))), 200


@bp.route('/properties/<string:property_id>/rooms/', methods=['GET'])
@bp.route('/v1/properties/<string:property_id>/rooms/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={Room: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_rooms(property_id):
    # TODO: 117,950.44 calls
    property = property_service.get_property(property_id)
    return jsonify(RoomSchema().dump(room_service.get_property_rooms(property), many=True)), 200


@bp.route('/roomtypes/', methods=['GET'])
def get_room_types():
    # TODO: 271,157.76 calls
    room_codes = request.args.get('codes')
    if room_codes:
        room_codes = room_codes.split(',')

    room_types = room_service.get_all_room_types_by_codes(room_codes)
    payload = RoomTypeSchema().dump(room_types, many=True) if room_types else []
    return jsonify(payload), 200
