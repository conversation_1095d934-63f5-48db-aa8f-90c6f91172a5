from marshmallow import validates, ValidationError
from marshmallow.decorators import validates_schema, pre_load

from cataloging_service.constants import model_choices
from cataloging_service.extensions import ma
from cataloging_service.utils import Utils
from cataloging_service.common.schema_registry import swag_schema


class RoomTypeConfigurationValidator(ma.Schema):
    room_type_id = ma.Integer(required=True, validate=lambda x: x >= 1)
    mm_id = ma.String(allow_none=True)
    minimum_occupancy = ma.Integer(allow_none=True)
    maximum_occupancy = ma.String(allow_none=True)
    adults = ma.Integer(allow_none=True)
    extra_bed_form = ma.String(validate=lambda x: x in model_choices.RoomTypeConfigurationChoices.EXTRA_BED_CHOICES)
    children = ma.Integer(allow_none=True)
    max_total = ma.Integer(allow_none=True)


class RoomValidator(ma.Schema):
    room_number = ma.String(required=True)
    room_type_id = ma.Integer(required=True, validate=lambda x: x >= 1)
    building_number = ma.String(allow_none=True)
    floor_number = ma.Integer(allow_none=True)
    size = ma.String(allow_none=True)
    is_active = ma.Boolean(allow_none=True)


class ModifyRoomValidator(RoomValidator):
    room_number = ma.String(allow_none=True)
    room_type_id = ma.Integer(allow_none=True)


class PropertyNameValidator(ma.Schema):
    legal_name = ma.String(allow_none=True)
    old_name = ma.String(required=True)
    new_name = ma.String(allow_none=True)


class ModifyPropertyNameValidator(PropertyNameValidator):
    old_name = ma.String()


class PropertyLocationValidator(ma.Schema):
    postal_address = ma.String(allow_none=True)
    locality_id = ma.Integer(validate=lambda x: x >= 1, allow_none=True)
    city_id = ma.Integer(validate=lambda x: x >= 1, required=True)
    pincode = ma.String(allow_none=True)
    micro_market_id = ma.Integer(validate=lambda x: x >= 1, allow_none=True)
    latitude = ma.Decimal(allow_none=True)
    longitude = ma.Decimal(allow_none=True)
    maps_link = ma.String(allow_none=True)


class ModifyPropertyLocationValidator(PropertyLocationValidator):
    city_id = ma.Integer(validate=lambda x: x >= 1)


class BankDetailValidator(ma.Schema):
    account_name = ma.String(required=True)
    account_number = ma.String(required=True)
    type = ma.String(validate=lambda x: x in model_choices.BankDetailChoices.ACCOUNT_TYPE_CHOICES)
    ifsc_code = ma.String(required=True, validate=lambda ifsc: Utils.is_valid_ifsc_code(ifsc))
    bank = ma.String(required=True)
    branch = ma.String(required=True)


class PropertyDetailValidator(ma.Schema):
    neighbourhood_type = ma.String(validate=lambda x: x in model_choices.PropertyDetailChoices.NEIGHBOURHOOD_CHOICES)
    neighbourhood_detail = ma.String(allow_none=True)
    style = ma.String(validate=lambda x: x in model_choices.PropertyDetailChoices.PROPERTY_STYLE_CHOICES)
    style_detail = ma.String(allow_none=True)
    property_type = ma.String(validate=lambda x: x in model_choices.PropertyDetailChoices.PROPERTY_TYPE_CHOICES)
    construction_year = ma.Integer(allow_none=True)
    building_type = ma.String(validate=lambda x: x in model_choices.PropertyDetailChoices.BUILDING_CHOICES)
    floor_count = ma.Integer(validate=lambda x: x >= 1, allow_none=True)
    star_rating = ma.Integer(validate=lambda x: 7 >= x >= 1, allow_none=True)
    previously_different_franchise = ma.Boolean(allow_none=True)
    bank_details = ma.Nested(BankDetailValidator)
    reception_landline = ma.String(allow_none=True)
    reception_mobile = ma.String(allow_none=True)
    is_hotel_superhero_setup_completed = ma.Boolean(allow_none=True)


class GuestFacingValidator(ma.Schema):
    checkin_time = ma.Time(allow_none=True)
    free_early_checkin_time = ma.Time(allow_none=True)
    checkout_time = ma.Time(allow_none=True)
    free_late_checkout_time = ma.Time(allow_none=True)
    early_checkin_fee = ma.String(allow_none=True)
    late_checkout_fee = ma.String(allow_none=True)


class OwnerValidator(ma.Schema):
    first_name = ma.String(required=True)
    middle_name = ma.String(allow_none=True)
    last_name = ma.String(required=True)
    gender = ma.String(validate=lambda x: x in model_choices.OwnerChoices.CHOICES_GENDER)
    email = ma.Email(allow_none=True)
    phone_number = ma.String(allow_none=True)
    dob = ma.Date(required=True)
    occupation = ma.String(allow_none=True)
    education = ma.String(allow_none=True)
    is_primary_owner = ma.Boolean(allow_none=True)


class ModifyOwnerValidator(OwnerValidator):
    first_name = ma.String()
    last_name = ma.String()
    dob = ma.Date()
    is_primary_owner = ma.Boolean()


class LandmarkValidator(ma.Schema):
    type = ma.String(required=True)
    name = ma.String(required=True)
    latitude = ma.Decimal(required=True)
    longitude = ma.Decimal(required=True)
    hotel_distance = ma.Decimal(allow_none=True)
    hotel_direction = ma.String(allow_none=True)
    hatchback_cab_fare = ma.Decimal(allow_none=True)
    sedan_cab_fare = ma.Decimal(allow_none=True)


class ModifyLandmarkValidator(LandmarkValidator):
    name = ma.String()
    latitude = ma.Decimal()
    longitude = ma.Decimal()


class DescriptionValidator(ma.Schema):
    property_description = ma.String(allow_none=True)
    acacia_description = ma.String(allow_none=True)
    maple_description = ma.String(allow_none=True)
    oak_description = ma.String(allow_none=True)
    mahogany_description = ma.String(allow_none=True)
    trilight_one = ma.String(allow_none=True)
    trilight_two = ma.String(allow_none=True)
    trilight_three = ma.String(allow_none=True)


class NeighbouringPlacesValidator(ma.Schema):
    nearest_hospital = ma.String(allow_none=True)
    utility_shops = ma.String(allow_none=True)
    restaurants = ma.String(allow_none=True)
    tourist_spots = ma.String(allow_none=True)
    corporate_offices = ma.String(allow_none=True)
    popular_malls = ma.String(allow_none=True)
    shopping_streets = ma.String(allow_none=True)
    city_centre = ma.String(allow_none=True)


class PropertyRequestValidator(ma.Schema):
    name = ma.Nested(PropertyNameValidator, required=True)
    location = ma.Nested(PropertyLocationValidator, required=True)
    property_details = ma.Nested(PropertyDetailValidator)
    guest_facing_details = ma.Nested(GuestFacingValidator)
    owners = ma.Nested(OwnerValidator, many=True)
    landmarks = ma.Nested(LandmarkValidator, many=True)
    description = ma.Nested(DescriptionValidator)
    neighbouring_places = ma.Nested(NeighbouringPlacesValidator)
    suited_to = ma.List(ma.Integer)
    hx_id = ma.String(allow_none=True)


class ModifyPropertyRequestValidator(PropertyRequestValidator):
    name = ma.Nested(ModifyPropertyNameValidator)
    location = ma.Nested(PropertyLocationValidator)
    owners = ma.Nested(ModifyOwnerValidator, many=True)
    landmarks = ma.Nested(ModifyLandmarkValidator, many=True)


class LocationQueryParamValidator(ma.Schema):
    country_id = ma.Integer(validate=lambda x: x >= 1)
    state_id = ma.Integer(validate=lambda x: x >= 1)
    city_id = ma.Integer(validate=lambda x: x >= 1)
    region_id = ma.Integer(validate=lambda x: x >= 1)
    cluster_id = ma.Integer(validate=lambda x: x >= 1)


class OtaMappingConfirmationValidator(ma.Schema):
    hotel_code = ma.String(required=True)
    ota_code = ma.String(required=True)


class OtaConfirmationValidator(ma.Schema):
    ota_code = ma.String(required=True)


class PropertySkuAndBundleActivationValidator(ma.Schema):
    cs_id = ma.String(required=True)
    service = ma.String(required=True)
    sku_codes = ma.List(ma.String, required=True)

    @validates('sku_codes')
    def validate_length(self, value):
        if len(value) < 1:
            raise ValidationError('Must not be empty sku_codes.')


class BundleRuleValidator(ma.Schema):
    class RoomConfigRuleValidator(ma.Schema):
        maple = ma.String(required=True)
        mahogany = ma.String(required=True)
        oak = ma.String(required=True)
        acacia = ma.String(required=True)

    class OccupancyRuleValidator(ma.Schema):
        acacia_adult = ma.String(data_key="acacia-adult", required=True)
        acacia_child = ma.String(data_key="acacia-child", required=True)
        oak_adult = ma.String(data_key="oak-adult", required=True)
        oak_child = ma.String(data_key="oak-child", required=True)
        maple_adult = ma.String(data_key="maple-adult", required=True)
        maple_child = ma.String(data_key="maple-child", required=True)
        mahogany_adult = ma.String(data_key="mahogany-adult", required=True)
        mahogany_child = ma.String(data_key="mahogany-child", required=True)
        # acacia_subsequent_extra_adult = ma.String(data_key="acacia-subsequent-extra-adult", required=True)
        # oak_subsequent_extra_adult = ma.String(data_key="oak-subsequent-extra-adult", required=True)
        # maple_subsequent_extra_adult = ma.String(data_key="maple-subsequent-extra-adult", required=True)
        # mahogany_subsequent_extra_adult = ma.String(data_key="mahogany-subsequent-extra-adult", required=True)
        # acacia_extra_adult = ma.String(data_key="acacia-extra-adult", required=True)
        # oak_extra_adult = ma.String(data_key="oak-extra-adult", required=True)
        # maple_extra_adult = ma.String(data_key="maple-extra-adult", required=True)
        # mahogany_extra_adult = ma.String(data_key="mahogany-extra-adult", required=True)

    room_config_skus = ma.Nested(RoomConfigRuleValidator, required=True)
    occupancy_skus = ma.Nested(OccupancyRuleValidator, required=True)
    chargeable_skus_per_occupant = ma.Dict(required=True)


class PropertySkuRoomConfigValidator(ma.Schema):
    hotels = ma.List(ma.String, required=True)
    room_types = ma.List(ma.String, required=False)
    configs = ma.List(ma.String, required=False)


class HotelOwnerDetialsValidator(ma.Schema):
    class HotelOwnerSchema(ma.Schema):
        first_name = ma.String(allow_none=True)
        second_name = ma.String(allow_none=True)
        email = ma.String(allow_none=True)
        contact_number = ma.String(allow_none=True)

    owner_details = ma.Nested(HotelOwnerSchema, many=True)


@swag_schema
class TenantConfigRequestValidator(ma.Schema):
    config_name = ma.String()
    config_value = ma.String()
    value_type = ma.String()
    active = ma.Boolean()


class PrepTimeValidatorMixin:

    @validates('prep_time')
    def validate_prep_time(self, value):
        if len(value.split(":")) != 3:
            raise ValidationError("prep_time field has wrong format")


class DaysListValidatorMixin:

    @validates('days')
    def validate_days(self, value):
        days = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"]

        for day in value:
            if day not in days:
                raise ValidationError("days field has wrong format")


class ItemVariantAttachVariantRequestValidator(ma.Schema):
    variant_group_id = ma.Integer(required=True)
    variant_id = ma.Integer(required=True)


class ItemCustomisationEditRequestValidator(ma.Schema):
    item_customisation_id = ma.Integer(required=False)
    variant_id = ma.Integer(required=True)
    variant_group_id = ma.Integer(required=True)
    delta_price = ma.Decimal(required=True)
    cost = ma.Decimal(required=True)


class ItemCustomisationCreateRequestValidator(ma.Schema):
    variant_id = ma.Integer(required=True)
    variant_group_id = ma.Integer(required=True)
    delta_price = ma.Decimal(required=True)
    cost = ma.Decimal(required=True)


class ItemVariantCreateValidator(ma.Schema):
    name = ma.String(required=True)
    pre_tax_price = ma.Decimal(required=True, as_string=True)
    cost = ma.Decimal(required=True, as_string=True)
    sku_category_code = ma.String(required=True)
    variants = ma.Nested(ItemVariantAttachVariantRequestValidator, many=True)
    item_customisations = ma.Nested(ItemCustomisationCreateRequestValidator, many=True)


class ItemVariantEditValidator(ma.Schema):
    item_variant_id = ma.Integer(required=False)
    name = ma.String(required=True)
    pre_tax_price = ma.Decimal(required=True, as_string=True)
    cost = ma.Decimal(required=True, as_string=True)
    sku_category_code = ma.String(required=True)
    variants = ma.Nested(ItemVariantAttachVariantRequestValidator, many=True)
    item_customisations = ma.Nested(ItemCustomisationEditRequestValidator, many=True)


class MenuTimingCreateRequestValidator(ma.Schema, DaysListValidatorMixin):
    days = ma.List(ma.String, required=True)
    start_time = ma.Time(required=True)
    end_time = ma.Time(required=True)


class MenuTimingEditRequestValidator(ma.Schema, DaysListValidatorMixin):
    menu_timing_id = ma.Integer(required=False)
    days = ma.List(ma.String, required=True)
    start_time = ma.Time(required=True)
    end_time = ma.Time(required=True)


@swag_schema
class MenuCreateRequestValidator(ma.Schema):
    name = ma.String(required=True)
    code = ma.String(required=True)
    display_name = ma.String(required=False, allow_none=True)
    description = ma.String(required=False, allow_none=True)
    menu_types = ma.List(
        ma.String(required=True, validate=lambda x: x in model_choices.MENU_TYPE_CHOICES), required=True)
    menu_timings = ma.Nested(MenuTimingCreateRequestValidator, many=True)


class SideItemAttachValidator(ma.Schema):
    item_id = ma.Integer(required=True)
    item_variant_id = ma.Integer(required=False)
    side_item_id = ma.Integer(required=False)


@swag_schema
class ItemCreateRequestValidator(ma.Schema, PrepTimeValidatorMixin):
    name = ma.String(required=True)
    code = ma.String(required=True)
    description = ma.String(required=False, allow_none=True)
    sku_category_code = ma.String(required=False)
    display_name = ma.String(required=False, allow_none=True)
    print_name = ma.String(required=False, allow_none=True)
    sold_out = ma.Boolean(required=True)
    prep_time = ma.String(required=True)
    kitchen_id = ma.Integer(required=True)
    use_as_side = ma.Boolean(required=True)
    contains_alcohol = ma.Boolean(required=True)
    active = ma.Boolean(required=False, allow_none=True)
    pre_tax_price = ma.Decimal(required=False, as_string=True, allow_none=True)
    cost = ma.Decimal(required=False, as_string=True, allow_none=True)
    image = ma.String(required=False, allow_none=True)
    allergen_info = ma.String(required=False, allow_none=True)
    calorie_info = ma.String(required=False, allow_none=True)
    item_variants = ma.Nested(ItemVariantCreateValidator, many=True)
    side_items = ma.Nested(SideItemAttachValidator, many=True)
    item_customisations = ma.Nested(ItemCustomisationCreateRequestValidator, many=True)
    food_type = ma.String(required=True, validate=lambda x: x in model_choices.FOOD_TYPE_CHOICES)


@swag_schema
class ItemEditRequestValidator(ma.Schema, PrepTimeValidatorMixin):
    name = ma.String(required=False)
    code = ma.String(required=True, allow_none=False)
    description = ma.String(required=False, allow_none=True)
    sku_category_code = ma.String(required=False)
    display_name = ma.String(required=False, allow_none=True)
    print_name = ma.String(required=False, allow_none=True)
    kitchen_id = ma.Integer(required=False, allow_none=True)
    active = ma.Boolean(required=False)
    sold_out = ma.Boolean(required=False)
    prep_time = ma.String(required=False)
    use_as_side = ma.Boolean(required=False)
    contains_alcohol = ma.Boolean(required=False)
    pre_tax_price = ma.Decimal(required=False, as_string=True, allow_none=True)
    cost = ma.Decimal(required=False, as_string=True, allow_none=True)
    image = ma.String(required=False, allow_none=True)
    allergen_info = ma.String(required=False, allow_none=True)
    calorie_info = ma.String(required=False, allow_none=True)
    item_variants = ma.Nested(ItemVariantEditValidator, many=True)
    side_items = ma.Nested(SideItemAttachValidator, many=True)
    item_customisations = ma.Nested(ItemCustomisationEditRequestValidator, many=True)
    food_type = ma.String(required=False, validate=lambda x: x in model_choices.FOOD_TYPE_CHOICES)


class VariantCreateValidator(ma.Schema):
    name = ma.String(required=True)


class VariantEditValidator(ma.Schema):
    variant_id = ma.Integer(required=False)
    variant_group_id = ma.Integer(required=False)
    name = ma.String(required=True)


@swag_schema
class VariantGroupCreateRequestValidator(ma.Schema):
    name = ma.String(required=True)
    display_name = ma.String(required=False, allow_none=True)
    can_select_multiple = ma.Boolean(required=True)
    can_select_quantity = ma.Boolean(required=True)
    minimum_selectable_quantity = ma.Integer(required=False, allow_none=True)
    maximum_selectable_quantity = ma.Integer(required=False, allow_none=True)
    is_customisation = ma.Boolean(required=True)
    variants = ma.Nested(VariantCreateValidator, many=True)

    @pre_load
    def validate(self, data, **kwargs):
        if data.get("can_select_quantity"):
            if not (data.get("minimum_selectable_quantity") and data.get("maximum_selectable_quantity")):
                raise ValidationError("Either one of minimum_selectable_quantity and maximum_selectable_quantity\
                                      cannot be set when the other attribute has no value.")

            if data.get("minimum_selectable_quantity") > data.get("maximum_selectable_quantity"):
                raise ValidationError("minimum_selectable_quantity cannot be greater than\
                        maximum_selectable_quantity")


@swag_schema
class VariantGroupEditRequestValidator(ma.Schema):
    name = ma.String(required=False)
    display_name = ma.String(required=False, allow_none=True)
    can_select_multiple = ma.Boolean(required=False)
    can_select_quantity = ma.Boolean(required=False)
    minimum_selectable_quantity = ma.Integer(required=False, allow_none=True)
    maximum_selectable_quantity = ma.Integer(required=False, allow_none=True)
    is_customisation = ma.Boolean(required=False)
    variants = ma.Nested(VariantEditValidator, many=True)

    @pre_load
    def validate(self, data, **kwargs):
        if data.get("can_select_quantity"):
            if not (data.get("minimum_selectable_quantity") and data.get("maximum_selectable_quantity")):
                raise ValidationError("Either one of minimum_selectable_quantity and maximum_selectable_quantity\
                                      cannot be set when the other attribute has no value.")

            if data.get("minimum_selectable_quantity") > data.get("maximum_selectable_quantity"):
                raise ValidationError("minimum_selectable_quantity cannot be greater than\
                        maximum_selectable_quantity")


class MenuItemCategoryEditValidator(ma.Schema):
    menu_category_id = ma.Integer(required=True)


class MenuComboCategoryRequestValidator(ma.Schema):
    menu_category_id = ma.Integer(required=True)


class MenuComboEditRequestValidator(ma.Schema):
    menu_combo_id = ma.Integer(required=False)
    combo_id = ma.Integer(required=True)
    sold_out = ma.Boolean(required=False)
    menu_combo_categories = ma.Nested(MenuComboCategoryRequestValidator, many=True)


class MenuCategoryEditRequestValidator(ma.Schema):
    menu_category_id = ma.Integer(required=False)
    name = ma.String(required=True)


class MenuItemItemVariantValidator(ma.Schema):
    item_variant_id = ma.Integer(required=True)


class MenuItemSideItemValidator(ma.Schema):
    side_item_id = ma.Integer(required=True)


class MenuItemItemCustomisationValidator(ma.Schema):
    item_customisation_id = ma.Integer(required=True)


class MenuItemEditRequestValidator(ma.Schema):
    menu_item_id = ma.Integer(required=False)
    item_id = ma.Integer(required=True)
    item_variant_id = ma.Integer(required=False)
    sold_out = ma.Boolean(required=False)
    menu_item_categories = ma.Nested(MenuItemCategoryEditValidator, many=True)
    menu_item_side_items = ma.Nested(MenuItemSideItemValidator, many=True)
    menu_item_item_variants = ma.Nested(MenuItemItemVariantValidator, many=True)
    menu_item_item_customisations = ma.Nested(MenuItemItemCustomisationValidator, many=True)


@swag_schema
class MenuEditRequestValidator(ma.Schema):
    name = ma.String(required=False)
    code = ma.String(required=True, allow_none=False)
    display_name = ma.String(required=False, allow_none=True)
    description = ma.String(required=False, allow_none=True)
    menu_types = ma.List(
        ma.String(required=False, validate=lambda x: x in model_choices.MENU_TYPE_CHOICES), required=False)
    menu_timings = ma.Nested(MenuTimingEditRequestValidator, many=True)
    menu_categories = ma.Nested(MenuCategoryEditRequestValidator, many=True)
    menu_items = ma.Nested(MenuItemEditRequestValidator, many=True)
    menu_combos = ma.Nested(MenuComboEditRequestValidator, many=True)


class MenuSearchRequestValidator(ma.Schema):
    name = ma.String(required=False)
    menu_type = ma.String(required=False, validate=lambda x: x in model_choices.MENU_TYPE_CHOICES)
    compressed = ma.Boolean(required=False)
    menu_ids = ma.String(required=False)
    active_at = ma.DateTime(required=False)
    is_active = ma.Boolean(required=False)


class ComboItemCreateRequestValidator(ma.Schema):
    item_id = ma.Integer(required=True)
    item_variant_id = ma.Integer(required=False)


class ComboItemEditRequestValidator(ma.Schema):
    combo_item_id = ma.Integer(required=False)
    item_id = ma.Integer(required=True)
    item_variant_id = ma.Integer(required=False)


@swag_schema
class ComboCreateRequestValidator(ma.Schema, PrepTimeValidatorMixin):
    name = ma.String(required=True)
    code = ma.String(required=True)
    display_name = ma.String(required=False, allow_none=True)
    description = ma.String(required=False, allow_none=True)
    image = ma.String(required=False, allow_none=True)
    allergen_info = ma.String(required=False, allow_none=True)
    calorie_info = ma.String(required=False, allow_none=True)
    contains_alcohol = ma.Boolean(required=True)
    prep_time = ma.String(required=True)
    pre_tax_price = ma.Decimal(required=True, as_string=True)
    cost = ma.Decimal(required=True, as_string=True)
    sku_category_code = ma.String(required=True)
    combo_items = ma.Nested(ComboItemCreateRequestValidator, many=True)
    active = ma.Boolean(required=False, allow_none=True)
    sold_out = ma.Boolean(required=False, allow_none=True)


@swag_schema
class ComboEditRequestValidator(ma.Schema, PrepTimeValidatorMixin):
    name = ma.String(required=False)
    code = ma.String(required=True, allow_none=False)
    display_name = ma.String(required=False, allow_none=True)
    description = ma.String(required=False, allow_none=True)
    image = ma.String(required=False, allow_none=True)
    allergen_info = ma.String(required=False, allow_none=True)
    calorie_info = ma.String(required=False, allow_none=True)
    use_as_side = ma.Boolean(required=False)
    contains_alcohol = ma.Boolean(required=False)
    prep_time = ma.String(required=False)
    pre_tax_price = ma.Decimal(required=False, as_string=True)
    cost = ma.Decimal(required=False, as_string=True)
    sku_category_code = ma.String(required=False)
    combo_items = ma.Nested(ComboItemEditRequestValidator, many=True)
    active = ma.Boolean(required=False, allow_none=True)
    sold_out = ma.Boolean(required=False, allow_none=True)


class ItemSearchRequestValidator(ma.Schema):
    name = ma.String(required=False)
    is_side = ma.Boolean(required=False)
    food_type = ma.String(required=False)
    is_menu_item = ma.Boolean(required=False)
    active = ma.Boolean(required=False, allow_none=True)
    sold_out = ma.Boolean(required=False, allow_none=True)


class RestaurantTableCreateValidator(ma.Schema):
    seats_count = ma.Integer(required=True)
    number_of_tables = ma.Integer(required=True)


@swag_schema
class RestaurantAreaCreateRequestValidator(ma.Schema):
    name = ma.String(required=True)
    tables = ma.Nested(RestaurantTableCreateValidator, many=True, allow_none=True)


@swag_schema
class RestaurantTableBulkCreateRequestValidator(ma.Schema):
    tables = ma.Nested(RestaurantTableCreateValidator, many=True)


class RestaurantTableRequestValidator(ma.Schema):
    table_id = ma.Integer(required=False)
    name = ma.String(required=False)
    x_coordinate = ma.Integer(required=False)
    y_coordinate = ma.Integer(required=False)
    width = ma.Integer(required=False)
    height = ma.Integer(required=False)


@swag_schema
class RestaurantAreaUpdateRequestValidator(ma.Schema):
    name = ma.String(required=False)
    tables = ma.Nested(RestaurantTableRequestValidator, many=True)


class PrinterConfigRequestValidator(ma.Schema):
    ip = ma.String(required=True)
    printer_id = ma.String(required=True)
    port = ma.String(required=False)


@swag_schema
class KitchenCreateRequestValidator(ma.Schema):
    name = ma.String(required=True)
    config = ma.Nested(PrinterConfigRequestValidator, many=True)


@swag_schema
class KitchenEditRequestValidator(ma.Schema):
    name = ma.String(required=None)
    config = ma.Nested(PrinterConfigRequestValidator, many=True)


@swag_schema
class CurrencyConversionRateRequestValidator(ma.Schema):
    from_currency = ma.String(required=True)
    to_currency = ma.String(required=True)
    conversion_rate = ma.String(required=True)
    start_date = ma.Date(required=True)
    end_date = ma.Date(required=True)
    property_id = ma.String(required=True)

    @validates_schema
    def validate(self, data):
        if data['start_date'] > data['end_date']:
            raise ValidationError("End date should always be greater than start date")


class RolloverBusinessDate(ma.Schema):
    business_date = ma.Date(required=False)
