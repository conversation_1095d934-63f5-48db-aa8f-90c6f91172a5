from flask import Blueprint, jsonify

from cataloging_service.api.schemas import CountrySchema, StateSchema, CitySchema, ClusterSchema
from cataloging_service.api.validators import LocationQueryParamValidator
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import query_params

import logging

bp = Blueprint('location_apis', __name__)

logger = logging.getLogger(__name__)

location_service = service_provider.location_service


@bp.route('/countries/', methods=['GET'])
@bp.route('/v1/countries/', methods=['GET'])
def get_countries():
    # TODO: 0 calls
    all_countries = location_service.get_all_countries()
    return jsonify(CountrySchema().dump(all_countries, many=True)), 200


@bp.route('/states/', methods=['GET'])
@bp.route('/v1/states/', methods=['GET'])
@query_params(LocationQueryParamValidator)
def get_states(parsed_request):
    # TODO: 0 calls
    if 'country_id' in parsed_request:
        country_id = parsed_request['country_id']
        states = location_service.get_states_of_country(country_id)
    else:
        states = location_service.get_all_states()
    return jsonify(StateSchema().dump(states, many=True)), 200


@bp.route('/cities/', methods=['GET'])
@bp.route('/v1/cities/', methods=['GET'])
@query_params(LocationQueryParamValidator)
def get_cities(parsed_request):
    # TODO: 9.83 calls -> Duplicate DB calls
    if 'cluster_id' in parsed_request and 'state_id' in parsed_request:
        cluster_id = parsed_request['cluster_id']
        state_id = parsed_request['state_id']
        cities = location_service.get_cities_by_cluster_and_state(state_id, cluster_id)
    elif 'state_id' in parsed_request:
        state_id = parsed_request['state_id']
        cities = location_service.get_cities_of_state(state_id)
    elif 'cluster_id' in parsed_request:
        cluster_id = parsed_request['cluster_id']
        cities = location_service.get_cities_of_cluster(cluster_id)
    else:
        cities = location_service.get_all_cities()
    return jsonify(CitySchema().dump(cities, many=True)), 200


@bp.route('/clusters/', methods=['GET'])
@bp.route('/v1/clusters/', methods=['GET'])
@query_params(LocationQueryParamValidator)
def get_clusters(parsed_request):
    # TODO: 0 calls
    if 'state_id' in parsed_request and 'region_id' in parsed_request:
        state_id = parsed_request['state_id']
        region_id = parsed_request['region_id']
        clusters = location_service.get_clusters_by_region_and_state(state_id, region_id)
    elif 'state_id' in parsed_request:
        state_id = parsed_request['state_id']
        clusters = location_service.get_clusters_of_state(state_id)
    elif 'region_id' in parsed_request:
        region_id = parsed_request['region_id']
        clusters = location_service.get_clusters_of_region(region_id)
    else:
        clusters = location_service.get_all_clusters()
    return jsonify(ClusterSchema().dump(clusters, many=True)), 200


@bp.route('/localities/', methods=['GET'])
@bp.route('/v1/localities/', methods=['GET'])
@query_params(LocationQueryParamValidator)
def get_localities(parsed_request):
    # TODO: 9.83 calls
    if 'city_id' in parsed_request:
        city_id = parsed_request['city_id']
        localities = location_service.get_localities_of_city(city_id)
    else:
        localities = location_service.get_all_localities()
    return jsonify(ClusterSchema().dump(localities, many=True)), 200


@bp.route('/micro-markets/', methods=['GET'])
@bp.route('/v1/micro-markets/', methods=['GET'])
@query_params(LocationQueryParamValidator)
def get_micro_markets(parsed_request):
    # TODO: 0 calls
    if 'city_id' in parsed_request:
        city_id = parsed_request['city_id']
        micro_markets = location_service.get_micro_markets_of_city(city_id)
    else:
        micro_markets = location_service.get_all_micro_markets()
    return jsonify(ClusterSchema().dump(micro_markets, many=True)), 200


@bp.route('/regions/', methods=['GET'])
@bp.route('/v1/regions/', methods=['GET'])
def get_regions():
    # TODO: 0 calls
    regions = location_service.get_all_regions()
    return jsonify(ClusterSchema().dump(regions, many=True)), 200
