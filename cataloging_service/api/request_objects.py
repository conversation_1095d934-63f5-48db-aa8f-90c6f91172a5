from logging import disable


class RoomTypeConfigurationRequest:
    def __init__(self, dictionary, room_type_config_object=None):
        if room_type_config_object:
            self.apply_diff(dictionary, room_type_config_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.room_type_id = dictionary.get('room_type_id', None)
        self.minimum_occupancy = dictionary.get('minimum_occupancy', None)
        self.maximum_occupancy = dictionary.get('maximum_occupancy', None)
        self.adults = dictionary.get('adults', None)
        self.extra_bed_form = dictionary.get('extra_bed_form', None)
        self.mm_id = dictionary.get('mm_id', None)
        self.children = dictionary.get('children', None)
        self.max_total = dictionary.get('max_total', None)

    def apply_diff(self, dictionary, room_type_config_object):
        self.room_type_id = dictionary.get('room_type_id', room_type_config_object.room_type_id)
        self.minimum_occupancy = dictionary.get('minimum_occupancy', room_type_config_object.min_occupancy)
        self.maximum_occupancy = dictionary.get('maximum_occupancy', room_type_config_object.max_occupancy)
        self.adults = dictionary.get('adults', room_type_config_object.adults)
        self.extra_bed_form = dictionary.get('extra_bed_form', room_type_config_object.extra_bed)
        self.mm_id = dictionary.get('mm_id', room_type_config_object.mm_id)
        self.children = dictionary.get('children', room_type_config_object.children)
        self.max_total = dictionary.get('max_total', room_type_config_object.max_total)


class RoomRequest:
    def __init__(self, dictionary, room_object=None):
        if room_object:
            self.apply_diff(dictionary, room_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.room_number = dictionary.get('room_number', None)
        self.room_type_id = dictionary.get('room_type_id', None)
        self.building_number = dictionary.get('building_number', None)
        self.floor_number = dictionary.get('floor_number', None)
        self.size = dictionary.get('size', None)
        self.is_active = dictionary.get('is_active', None)
        self.room_size = dictionary.get('room_size', None)

    def apply_diff(self, dictionary, room_object):
        self.room_number = dictionary.get('room_number', room_object.room_number)
        self.room_type_id = dictionary.get('room_type_id', room_object.room_type_id)
        self.building_number = dictionary.get('building_number', room_object.building_number)
        self.floor_number = dictionary.get('floor_number', room_object.floor_number)
        self.size = dictionary.get('size', room_object.size)
        self.is_active = dictionary.get('is_active', room_object.is_active)
        self.room_size = dictionary.get('room_size', None)


class PropertyRequest:
    def __init__(self, dictionary, property_object=None):
        if property_object:
            self.apply_diff(dictionary, property_object)
        else:
            self.initialize(dictionary)

    def _get_sub_dictionary(self, dictionary):
        name_dictionary = dictionary.get('name', None)
        location_dictionary = dictionary.get('location', None)
        details_dictionary = dictionary.get('property_details', None)
        guest_facing_details_dictionary = dictionary.get('guest_facing_details', None)
        owners_list = dictionary.get('owners', None)
        landmark_list = dictionary.get('landmarks', None)
        description_dictionary = dictionary.get('description', None)
        neighbouring_place_dictionary = dictionary.get('neighbouring_places', None)

        return name_dictionary, location_dictionary, details_dictionary, guest_facing_details_dictionary, owners_list, \
            landmark_list, description_dictionary, neighbouring_place_dictionary

    def initialize(self, dictionary):
        name_dictionary, location_dictionary, details_dictionary, guest_facing_details_dictionary, owners_list, \
            landmark_list, description_dictionary, neighbouring_place_dictionary = self._get_sub_dictionary(dictionary)

        self.name = None if not name_dictionary else PropertyNameRequest(name_dictionary)
        self.location = None if not location_dictionary else LocationRequest(location_dictionary)
        self.property_details = None if not details_dictionary else PropertyDetailsRequest(details_dictionary)
        self.guest_facing_details = None if not guest_facing_details_dictionary else GuestFacingDetailsRequest(
            guest_facing_details_dictionary)
        self.suited_to = dictionary.get('suited_to', None)
        self.owners = None if not owners_list else [OwnerRequest(owner) for owner in owners_list]
        self.landmarks = None if not landmark_list else [LandmarkRequest(landmark) for landmark in landmark_list]
        self.description = None if not description_dictionary else DescriptionRequest(description_dictionary)
        self.neighbouring_places = None if not neighbouring_place_dictionary else NeighbouringPlaceRequest(
            neighbouring_place_dictionary)
        self.hx_id = dictionary.get('hx_id', None)

    def apply_diff(self, dictionary, property_object):
        name_dictionary, location_dictionary, details_dictionary, guest_facing_details_dictionary, owners_list, \
            landmark_list, description_dictionary, neighbouring_place_dictionary = self._get_sub_dictionary(dictionary)

        self.name = None if not name_dictionary else PropertyNameRequest(name_dictionary, property_object)

        self.location = None if not location_dictionary else LocationRequest(location_dictionary,
                                                                             None if not property_object.location
                                                                             else property_object.location)
        self.property_details = None if not details_dictionary else \
            PropertyDetailsRequest(details_dictionary, None if not property_object.property_detail
                                   else property_object.property_detail)

        self.guest_facing_details = None if not guest_facing_details_dictionary else GuestFacingDetailsRequest(
            guest_facing_details_dictionary,
            None if not property_object.guest_facing_process else property_object.guest_facing_process)

        self.suited_to = dictionary.get('suited_to', None)

        self.owners = None if not owners_list else [OwnerRequest(owner) for owner in owners_list]

        self.landmarks = None if not landmark_list else [LandmarkRequest(landmark) for landmark in landmark_list]

        self.description = None if not description_dictionary else \
            DescriptionRequest(description_dictionary,
                               None if not property_object.description else property_object.description)

        self.neighbouring_places = None if not neighbouring_place_dictionary else NeighbouringPlaceRequest(
            neighbouring_place_dictionary,
            None if not property_object.neighbouring_place else property_object.neighbouring_place)
        self.hx_id = dictionary.get('hx_id', property_object.hx_id)


class PropertyNameRequest:
    def __init__(self, dictionary, property_object=None):
        if property_object:
            self.apply_diff(dictionary, property_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.legal_name = dictionary.get('legal_name', None)
        self.old_name = dictionary.get('old_name', None)
        self.new_name = dictionary.get('new_name', None)

    def apply_diff(self, dictionary, property_object):
        self.legal_name = dictionary.get('legal_name', property_object.legal_name)
        self.old_name = dictionary.get('old_name', property_object.old_name)
        self.new_name = dictionary.get('new_name', property_object.name)


class LocationRequest:
    def __init__(self, dictionary, location_object=None):
        if location_object:
            self.apply_diff(dictionary, location_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.postal_address = dictionary.get('postal_address', None)
        self.locality_id = dictionary.get('locality_id', None)
        self.city_id = dictionary.get('city_id', None)
        self.pincode = dictionary.get('pincode', None)
        self.micro_market_id = dictionary.get('micro_market_id', None)
        self.maps_link = dictionary.get('maps_link', None)
        self.latitude = dictionary.get('latitude', None)
        self.longitude = dictionary.get('longitude', None)

    def apply_diff(self, dictionary, location_object):
        self.postal_address = dictionary.get('postal_address', location_object.postal_address)
        self.locality_id = dictionary.get('locality_id', location_object.locality_id)
        self.city_id = dictionary.get('city_id', location_object.city_id)
        self.pincode = dictionary.get('pincode', location_object.pincode)
        self.micro_market_id = dictionary.get('micro_market_id', location_object.micro_market_id)
        self.maps_link = dictionary.get('maps_link', location_object.maps_link)
        self.latitude = dictionary.get('latitude', location_object.latitude)
        self.longitude = dictionary.get('longitude', location_object.longitude)


class BankDetailsRequest:
    def __init__(self, dictionary, bank_detail_object=None):
        if bank_detail_object:
            self.apply_diff(dictionary, bank_detail_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.account_name = dictionary.get('account_name', None)
        self.account_number = dictionary.get('account_number', None)
        self.type = dictionary.get('type', None)
        self.ifsc_code = dictionary.get('ifsc_code', None)
        self.bank = dictionary.get('bank', None)
        self.branch = dictionary.get('branch', None)

    def apply_diff(self, dictionary, bank_detail_object):
        self.account_name = dictionary.get('account_name', bank_detail_object.account_name)
        self.account_number = dictionary.get('account_number', bank_detail_object.account_number)
        self.type = dictionary.get('type', bank_detail_object.account_type)
        self.ifsc_code = dictionary.get('ifsc_code', bank_detail_object.ifsc_code)
        self.bank = dictionary.get('bank', bank_detail_object.bank)
        self.branch = dictionary.get('branch', bank_detail_object.branch)


class GuestFacingDetailsRequest:
    def __init__(self, dictionary, guest_facing_process_object=None):
        if guest_facing_process_object:
            self.apply_diff(dictionary, guest_facing_process_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.checkin_time = dictionary.get('checkin_time', None)
        self.free_early_checkin_time = dictionary.get('free_early_checkin_time', None)
        self.checkout_time = dictionary.get('checkout_time', None)
        self.free_late_checkout_time = dictionary.get('free_late_checkout_time', None)
        self.early_checkin_fee = dictionary.get('early_checkin_fee', None)
        self.late_checkout_fee = dictionary.get('late_checkout_fee', None)
        self.system_freeze_time = dictionary.get('system_freeze_time', None)

    def apply_diff(self, dictionary, guest_facing_process_object):
        self.checkin_time = dictionary.get('checkin_time', guest_facing_process_object.checkin_time)
        self.free_early_checkin_time = dictionary.get('free_early_checkin_time',
                                                      guest_facing_process_object.free_early_checkin)
        self.checkout_time = dictionary.get('checkout_time', guest_facing_process_object.checkout_time)
        self.free_late_checkout_time = dictionary.get('free_late_checkout_time',
                                                      guest_facing_process_object.free_late_checkout)
        self.early_checkin_fee = dictionary.get('early_checkin_fee', guest_facing_process_object.early_checkin_fee)
        self.late_checkout_fee = dictionary.get('late_checkout_fee', guest_facing_process_object.late_checkout_fee)
        self.system_freeze_time = dictionary.get('system_freeze_time', guest_facing_process_object.system_freeze_time)


class OwnerRequest:
    def __init__(self, dictionary, owner_object=None, property_object=None):
        if owner_object and property_object:
            self.apply_diff(dictionary, owner_object, property_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.first_name = dictionary.get('first_name', None)
        self.middle_name = dictionary.get('middle_name', None)
        self.last_name = dictionary.get('last_name', None)
        self.gender = dictionary.get('gender', None)
        self.email = dictionary.get('email', None)
        self.phone_number = dictionary.get('phone_number', None)
        self.dob = dictionary.get('dob', None)
        self.occupation = dictionary.get('occupation', None)
        self.education = dictionary.get('education', None)
        self.is_primary_owner = dictionary.get('is_primary_owner', None)

    def apply_diff(self, dictionary, owner_object, property_object):
        default_primary = None
        for ownership in owner_object.ownerships:
            if ownership.property_id == property_object.id:
                default_primary = ownership.primary
                break

        self.first_name = dictionary.get('first_name', owner_object.first_name)
        self.middle_name = dictionary.get('middle_name', owner_object.middle_name)
        self.last_name = dictionary.get('last_name', owner_object.last_name)
        self.gender = dictionary.get('gender', owner_object.gender)
        self.email = dictionary.get('email', owner_object.email)
        self.phone_number = dictionary.get('phone_number', owner_object.phone_number)
        self.dob = dictionary.get('dob', owner_object.date_of_birth)
        self.occupation = dictionary.get('occupation', owner_object.occupation)
        self.education = dictionary.get('education', owner_object.education)
        self.is_primary_owner = dictionary.get('is_primary_owner', default_primary)


class LandmarkRequest:
    def __init__(self, dictionary, landmark_object=None):
        if landmark_object:
            self.apply_diff(dictionary, landmark_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.type = dictionary.get('type', None)
        self.name = dictionary.get('name', None)
        self.latitude = dictionary.get('latitude', None)
        self.longitude = dictionary.get('longitude', None)
        self.hotel_distance = dictionary.get('hotel_distance', None)
        self.hotel_direction = dictionary.get('hotel_direction', None)
        self.hatchback_cab_fare = dictionary.get('hatchback_cab_fare', None)
        self.sedan_cab_fare = dictionary.get('sedan_cab_fare', None)

    def apply_diff(self, dictionary, landmark_object):
        self.type = dictionary.get('type', landmark_object.type)
        self.name = dictionary.get('name', landmark_object.name)
        self.latitude = dictionary.get('latitude', landmark_object.latitude)
        self.longitude = dictionary.get('longitude', landmark_object.longitude)
        self.hotel_distance = dictionary.get('hotel_distance', landmark_object.distance_from_property)
        self.hotel_direction = dictionary.get('hotel_direction', landmark_object.property_direction)
        self.hatchback_cab_fare = dictionary.get('hatchback_cab_fare', landmark_object.hatchback_cab_fare)
        self.sedan_cab_fare = dictionary.get('sedan_cab_fare', landmark_object.sedan_cab_fare)


class NeighbouringPlaceRequest:
    def __init__(self, dictionary, neighbouring_place_object=None):
        if neighbouring_place_object:
            self.apply_diff(dictionary, neighbouring_place_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.nearest_hospital = dictionary.get('nearest_hospital', None)
        self.utility_shops = dictionary.get('utility_shops', None)
        self.restaurants = dictionary.get('restaurants', None)
        self.tourist_spots = dictionary.get('tourist_spots', None)
        self.corporate_offices = dictionary.get('corporate_offices', None)
        self.popular_malls = dictionary.get('popular_malls', None)
        self.shopping_streets = dictionary.get('shopping_streets', None)
        self.city_centre = dictionary.get('city_centre', None)

    def apply_diff(self, dictionary, neighbouring_place_object):
        self.nearest_hospital = dictionary.get('nearest_hospital', neighbouring_place_object.nearest_hospital)
        self.utility_shops = dictionary.get('utility_shops', neighbouring_place_object.utility_shops)
        self.restaurants = dictionary.get('restaurants', neighbouring_place_object.restaurants)
        self.tourist_spots = dictionary.get('tourist_spots', neighbouring_place_object.tourist_spots)
        self.corporate_offices = dictionary.get('corporate_offices', neighbouring_place_object.corporate_offices)
        self.popular_malls = dictionary.get('popular_malls', neighbouring_place_object.popular_malls)
        self.shopping_streets = dictionary.get('shopping_streets', neighbouring_place_object.shopping_streets)
        self.city_centre = dictionary.get('city_centre', neighbouring_place_object.city_centre)


class DescriptionRequest:
    def __init__(self, dictionary, description_object=None):
        if description_object:
            self.apply_diff(dictionary, description_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.property_description = dictionary.get('property_description', None)
        self.acacia_description = dictionary.get('acacia_description', None)
        self.maple_description = dictionary.get('maple_description', None)
        self.oak_description = dictionary.get('oak_description', None)
        self.mahogany_description = dictionary.get('mahogany_description', None)
        self.trilight_one = dictionary.get('trilight_one', None)
        self.trilight_two = dictionary.get('trilight_two', None)
        self.trilight_three = dictionary.get('trilight_three', None)

    def apply_diff(self, dictionary, description_object):
        self.property_description = dictionary.get('property_description', description_object.property_description)
        self.acacia_description = dictionary.get('acacia_description', description_object.acacia_description)
        self.maple_description = dictionary.get('maple_description', description_object.maple_description)
        self.oak_description = dictionary.get('oak_description', description_object.oak_description)
        self.mahogany_description = dictionary.get('mahogany_description', description_object.mahogany_description)
        self.trilight_one = dictionary.get('trilight_one', description_object.trilight_one)
        self.trilight_two = dictionary.get('trilight_two', description_object.trilight_two)
        self.trilight_three = dictionary.get('trilight_three', description_object.trilight_three)


class PropertyDetailsRequest:
    def __init__(self, dictionary, property_detail_object=None):
        if property_detail_object:
            self.apply_diff(dictionary, property_detail_object)
        else:
            self.initialize(dictionary)

    def initialize(self, dictionary):
        self.neighbourhood_type = dictionary.get('neighbourhood_type', None)
        self.neighbourhood_detail = dictionary.get('neighbourhood_detail', None)
        self.style = dictionary.get('style', None)
        self.style_detail = dictionary.get('style_detail', None)
        self.property_type = dictionary.get('property_type', None)
        self.construction_year = dictionary.get('construction_year', None)
        self.building_type = dictionary.get('building_type', None)
        self.is_hotel_superhero_setup_completed = dictionary.get('is_hotel_superhero_setup_completed', None)
        self.floor_count = dictionary.get('floor_count', None)
        self.star_rating = dictionary.get('star_rating', None)
        self.previously_different_franchise = dictionary.get('previously_different_franchise', None)
        self.reception_landline = dictionary.get('reception_landline', None)
        self.reception_mobile = dictionary.get('reception_mobile', None)
        bank_detail_dictionary = dictionary.get('bank_details', None)
        self.bank_details = None if not bank_detail_dictionary else BankDetailsRequest(bank_detail_dictionary)

    def apply_diff(self, dictionary, property_detail_object):
        self.neighbourhood_type = dictionary.get('neighbourhood_type', property_detail_object.neighbourhood_type)
        self.neighbourhood_detail = dictionary.get('neighbourhood_detail', property_detail_object.neighbourhood_detail)
        self.style = dictionary.get('style', property_detail_object.property_style)
        self.style_detail = dictionary.get('style_detail', property_detail_object.style_detail)
        self.property_type = dictionary.get('property_type', property_detail_object.property_type)
        self.construction_year = dictionary.get('construction_year', property_detail_object.construction_year)
        self.building_type = dictionary.get('building_type', property_detail_object.building_style)
        self.is_hotel_superhero_setup_completed = dictionary.get('is_hotel_superhero_setup_completed',
                                                                 property_detail_object.is_hotel_superhero_setup_completed)
        self.floor_count = dictionary.get('floor_count', property_detail_object.floor_count)
        self.star_rating = dictionary.get('star_rating', property_detail_object.star_rating)
        self.previously_different_franchise = dictionary.get('previously_different_franchise',
                                                             property_detail_object.previous_franchise)
        self.reception_landline = dictionary.get('reception_landline', property_detail_object.reception_landline)
        self.reception_mobile = dictionary.get('reception_mobile', property_detail_object.reception_mobile)
        bank_detail_dictionary = dictionary.get('bank_details', None)

        if not bank_detail_dictionary:
            self.bank_details = None
        elif not property_detail_object.bank_detail:
            self.bank_details = BankDetailsRequest(bank_detail_dictionary)
        else:
            self.bank_details = BankDetailsRequest(bank_detail_dictionary, property_detail_object.bank_detail)


class MenuCreateRequest:
    def __init__(self, seller_id, dictionary):
        self.name = dictionary.get('name')
        self.code = dictionary.get('code')
        self.display_name = dictionary.get('display_name')
        self.description = dictionary.get('description')
        self.seller_id = seller_id
        self.menu_types = dictionary.get('menu_types')
        self.menu_timings = [MenuTimingRequest(timing) for timing in dictionary.get('menu_timings', [])]


class MenuTimingRequest:
    def __init__(self, dictionary):
        self.menu_timing_id = dictionary.get('menu_timing_id')
        self.days = dictionary.get('days')
        self.start_time = dictionary.get('start_time')
        self.end_time = dictionary.get('end_time')
        self.menu_id = dictionary.get('menu_id')


class MenuItemCategoryRequest:
    def __init__(self, menu_item_id, dictionary):
        self.menu_item_id = menu_item_id
        self.menu_category_id = dictionary.get('menu_category_id')


class MenuItemItemVariantRequest:
    def __init__(self, menu_item_id, dictionary):
        self.menu_item_id = menu_item_id
        self.item_variant_id = dictionary.get('item_variant_id')


class MenuItemSideItemRequest:
    def __init__(self, menu_item_id, dictionary):
        self.menu_item_id = menu_item_id
        self.side_item_id = dictionary.get('side_item_id')


class MenuItemItemCustomisationRequest:
    def __init__(self, menu_item_id, dictionary):
        self.menu_item_id = menu_item_id
        self.item_customisation_id = dictionary.get('item_customisation_id')


class MenuItemRequest:
    def __init__(self, menu_id, dictionary):
        self.menu_item_id = dictionary.get('menu_item_id')
        self.menu_id = menu_id
        self.item_id = dictionary.get('item_id')
        self.item_variant_id = dictionary.get('item_variant_id')
        self.sold_out = dictionary.get('sold_out', False)
        if 'menu_item_categories' in dictionary:
            self.menu_item_categories = [] if not self.menu_item_id else [MenuItemCategoryRequest(
                menu_item_id=self.menu_item_id, dictionary=menu_item_category) for menu_item_category in
                dictionary.get('menu_item_categories')]


class MenuItemEditRequest:
    def __init__(self, menu_item_id, dictionary):
        self.menu_item_id = menu_item_id
        self.item_id = dictionary.get('item_id')
        self.item_variant_id = dictionary.get('item_variant_id')
        self.sold_out = dictionary.get('sold_out', False)
        if 'menu_item_categories' in dictionary:
            self.menu_item_categories = [] if not self.menu_item_id else [MenuItemCategoryRequest(
                menu_item_id=self.menu_item_id, dictionary=menu_item_category) for menu_item_category in
                dictionary.get('menu_item_categories')]
        if 'menu_item_item_variants' in dictionary:
            self.menu_item_item_variants = [] if not self.menu_item_id else [MenuItemItemVariantRequest(
                menu_item_id=self.menu_item_id, dictionary=menu_item_item_variant) for menu_item_item_variant in
                dictionary.get('menu_item_item_variants')]
        if 'menu_item_side_items' in dictionary:
            self.menu_item_side_items = [] if not self.menu_item_id else [MenuItemSideItemRequest(
                menu_item_id=self.menu_item_id, dictionary=menu_item_side_item) for menu_item_side_item in
                dictionary.get('menu_item_side_items')]
        if 'menu_item_item_customisations' in dictionary:
            self.menu_item_item_customisations = [] if not self.menu_item_id else [MenuItemItemCustomisationRequest(
                menu_item_id=menu_item_id, dictionary=menu_item_item_customisation) for menu_item_item_customisation in
                dictionary.get('menu_item_item_customisations')]


class ItemCreateRequest:
    def __init__(self, seller_id, dictionary) -> None:
        self.name = dictionary.get("name")
        self.code = dictionary.get("code")
        self.description = dictionary.get("description")
        self.sku_category_code = dictionary.get("sku_category_code")
        self.display_name = dictionary.get("display_name")
        self.print_name = dictionary.get("print_name")
        self.active = dictionary.get("active")
        self.sold_out = dictionary.get("sold_out")
        self.prep_time = dictionary.get("prep_time")
        self.use_as_side = dictionary.get("use_as_side")
        self.contains_alcohol = dictionary.get("contains_alcohol")
        self.pre_tax_price = dictionary.get("pre_tax_price")
        self.image = dictionary.get("image")
        self.allergen_info = dictionary.get("allergen_info")
        self.calorie_info = dictionary.get("calorie_info")
        self.cost = dictionary.get("cost")
        self.food_type = dictionary.get("food_type")
        self.kitchen_id = dictionary.get("kitchen_id")
        self.seller_id = seller_id
        self.item_variants = [ItemVariantRequest(
            item_variant) for item_variant in dictionary.get("item_variants", [])]
        self.side_items = [SideItemRequest(
            side_item) for side_item in dictionary.get("side_items", [])]
        self.item_customisations = [ItemCustomisationRequest(
            item_customisation) for item_customisation in dictionary.get("item_customisations", [])]


class SideItemRequest:
    def __init__(self, dictionary) -> None:
        self.side_item_id = dictionary.get("side_item_id")
        self.item_id = dictionary.get("item_id")
        self.item_variant_id = dictionary.get("item_variant_id")


class ItemCustomisationRequest:
    def __init__(self, dictionary) -> None:
        self.item_customisation_id = dictionary.get("item_customisation_id")
        self.name = dictionary.get("name")
        self.cost = dictionary.get("cost")
        self.delta_price = dictionary.get("delta_price")
        self.variant_id = dictionary.get("variant_id")


class ItemVariantRequest:
    def __init__(self, dictionary) -> None:
        self.item_variant_id = dictionary.get("item_variant_id")
        self.name = dictionary.get("name")
        self.display_order = dictionary.get("display_order")
        self.pre_tax_price = dictionary.get("pre_tax_price")
        self.cost = dictionary.get("cost")
        self.sku_category_code = dictionary.get("sku_category_code")
        self.item_id = dictionary.get("item_id")
        self.variants = [] if not dictionary.get("variants") else [ItemVariantAttachVariantRequest(
            variant) for variant in dictionary.get("variants")]
        self.item_customisations = [] if not dictionary.get("item_customisations") else [ItemCustomisationRequest(
            item_customisation) for item_customisation in dictionary.get("item_customisations")]


class ItemVariantAttachVariantRequest:
    def __init__(self, dictionary) -> None:
        self.variant_id = dictionary.get("variant_id")


class VariantGroupCreateRequest:
    def __init__(self, seller_id, dictionary):
        self.name = dictionary.get("name")
        self.display_name = dictionary.get("display_name")
        self.can_select_multiple = dictionary.get("can_select_multiple", None)
        self.can_select_quantity = dictionary.get("can_select_quantity", None)
        self.minimum_selectable_quantity = dictionary.get("minimum_selectable_quantity")
        self.maximum_selectable_quantity = dictionary.get("maximum_selectable_quantity")
        self.is_customisation = dictionary.get("is_customisation")
        self.seller_id = seller_id
        self.variants = [] if not dictionary.get("variants") else [VariantRequest(variant)
                                                                   for variant in dictionary.get("variants")]


class VariantRequest:
    def __init__(self, dictionary):
        self.variant_id = dictionary.get("variant_id")
        self.name = dictionary.get("name")
        self.variant_group_id = dictionary.get("variant_group_id")


class ItemEditRequest:
    def __init__(self, seller_id, dictionary) -> None:
        self.item_id = dictionary.get("item_id")
        self.seller_id = seller_id

        if 'name' in dictionary:
            self.name = dictionary.get('name')
        if 'code' in dictionary:
            self.code = dictionary.get('code')
        if 'description' in dictionary:
            self.description = dictionary.get('description')
        if 'sku_category_code' in dictionary:
            self.sku_category_code = dictionary.get('sku_category_code')
        if 'display_name' in dictionary:
            self.display_name = dictionary.get('display_name')
        if 'print_name' in dictionary:
            self.print_name = dictionary.get('print_name')
        if 'active' in dictionary:
            self.active = dictionary.get('active')
        if 'sold_out' in dictionary:
            self.sold_out = dictionary.get('sold_out')
        if 'prep_time' in dictionary:
            self.prep_time = dictionary.get('prep_time')
        if 'use_as_side' in dictionary:
            self.use_as_side = dictionary.get('use_as_side')
        if 'contains_alcohol' in dictionary:
            self.contains_alcohol = dictionary.get('contains_alcohol')
        if 'pre_tax_price' in dictionary:
            self.pre_tax_price = dictionary.get('pre_tax_price')
        if 'tax_value' in dictionary:
            self.tax_value = dictionary.get('tax_value')
        if 'image' in dictionary:
            self.image = dictionary.get("image")
        if 'allergen_info' in dictionary:
            self.allergen_info = dictionary.get("allergen_info")
        if 'calorie_info' in dictionary:
            self.calorie_info = dictionary.get("calorie_info")
        if 'cost' in dictionary:
            self.cost = dictionary.get('cost')
        if 'food_type' in dictionary:
            self.food_type = dictionary.get("food_type")
        if 'kitchen_id' in dictionary:
            self.kitchen_id = dictionary.get("kitchen_id")
        if 'item_variants' in dictionary:
            self.item_variants = [] if not dictionary.get("item_variants") else [ItemVariantRequest(
                item_variant) for item_variant in dictionary.get("item_variants")]
        if 'side_items' in dictionary:
            self.side_items = [] if not dictionary.get("side_items") else [SideItemRequest(
                side_item) for side_item in dictionary.get("side_items")]
        if 'item_customisations' in dictionary:
            self.item_customisations = [] if not dictionary.get("item_customisations") else [ItemCustomisationRequest(
                item_customisation) for item_customisation in dictionary.get("item_customisations")]


class VariantGroupEditRequest:
    def __init__(self, seller_id, variant_group_id, dictionary) -> None:
        self.id = variant_group_id
        self.seller_id = seller_id
        if 'display_name' in dictionary:
            self.display_name = dictionary.get('display_name')
        if 'name' in dictionary:
            self.name = dictionary.get('name')
        if 'can_select_multiple' in dictionary:
            self.can_select_multiple = dictionary.get('can_select_multiple')
        if 'can_select_quantity' in dictionary:
            self.can_select_quantity = dictionary.get('can_select_quantity')
        if 'minimum_selectable_quantity' in dictionary:
            self.minimum_selectable_quantity = dictionary.get('minimum_selectable_quantity')
        if 'maximum_selectable_quantity' in dictionary:
            self.maximum_selectable_quantity = dictionary.get('maximum_selectable_quantity')
        if 'is_customisation' in dictionary:
            self.is_customisation = dictionary.get('is_customisation')
        if 'variants' in dictionary:
            self.variants = [] if not dictionary.get("variants") else [VariantRequest(variant)
                                                                       for variant in dictionary.get("variants")]


class MenuEditRequest:
    def __init__(self, seller_id, menu_id, dictionary):
        self.id = menu_id
        self.seller_id = seller_id
        if 'name' in dictionary:
            self.name = dictionary.get('name')
        if 'code' in dictionary:
            self.code = dictionary.get('code')
        if 'display_name' in dictionary:
            self.display_name = dictionary.get('display_name')
        if 'description' in dictionary:
            self.description = dictionary.get('description')
        if 'menu_items' in dictionary:
            self.menu_items = [MenuItemRequest(menu_id=menu_id, dictionary=item)
                               for item in dictionary.get('menu_items')]
        if 'menu_types' in dictionary:
            self.menu_types = dictionary.get('menu_types')
        if 'menu_timings' in dictionary:
            self.menu_timings = [MenuTimingRequest(timing) for timing in dictionary.get('menu_timings')]
        if 'menu_categories' in dictionary:
            self.menu_categories = [MenuCategoryRequest(category) for category in dictionary.get('menu_categories')]
        if 'menu_combos' in dictionary:
            self.menu_combos = [MenuComboRequest(menu_id=menu_id, dictionary=menu_combo)
                                for menu_combo in dictionary.get('menu_combos')]


class MenuCategoryRequest:
    def __init__(self, dictionary):
        self.menu_category_id = dictionary.get('menu_category_id')
        self.name = dictionary.get('name')


class MenuComboCategoryRequest:
    def __init__(self, menu_combo_id, dictionary):
        self.menu_combo_id = menu_combo_id
        self.menu_category_id = dictionary.get('menu_category_id')


class MenuComboRequest:
    def __init__(self, menu_id, dictionary):
        self.menu_combo_id = dictionary.get('menu_combo_id')
        self.menu_id = menu_id
        self.combo_id = dictionary.get('combo_id')
        self.sold_out = dictionary.get('sold_out', False)
        self.menu_combo_categories = [] if not (dictionary.get('menu_combo_categories') and self.menu_combo_id) else [
            MenuComboCategoryRequest(
                menu_combo_id=self.menu_combo_id, dictionary=menu_combo_category) for menu_combo_category in
            dictionary.get('menu_combo_categories')]


class MenuComboEditRequest:
    def __init__(self, menu_combo_id, dictionary):
        self.menu_combo_id = menu_combo_id
        self.combo_id = dictionary.get('combo_id')
        self.sold_out = dictionary.get('sold_out', False)
        self.menu_combo_categories = [] if not (dictionary.get('menu_combo_categories') and self.menu_combo_id) else [
            MenuComboCategoryRequest(
                menu_combo_id=self.menu_combo_id, dictionary=menu_combo_category) for menu_combo_category in
            dictionary.get('menu_combo_categories')]


class ComboCreateRequest:
    def __init__(self, seller_id, dictionary):
        self.name = dictionary.get("name")
        self.code = dictionary.get("code")
        self.description = dictionary.get("description")
        self.display_name = dictionary.get("display_name")
        self.image = dictionary.get("image")
        self.allergen_info = dictionary.get("allergen_info")
        self.calorie_info = dictionary.get("calorie_info")
        self.prep_time = dictionary.get("prep_time")
        self.contains_alcohol = dictionary.get("contains_alcohol")
        self.pre_tax_price = dictionary.get("pre_tax_price")
        self.cost = dictionary.get("cost")
        self.seller_id = seller_id
        self.sku_category_code = dictionary.get("sku_category_code")
        self.active = dictionary.get('active')
        self.sold_out = dictionary.get('sold_out')
        self.combo_items = [] if not dictionary.get("combo_items") else [ComboItemRequest(
            combo_item) for combo_item in dictionary.get("combo_items")]


class ComboItemRequest:
    def __init__(self, dictionary):
        self.item_id = dictionary.get("item_id")
        self.combo_item_id = dictionary.get("combo_item_id")
        self.item_variant_id = dictionary.get("item_variant_id")


class ComboEditRequest:
    def __init__(self, combo_id, seller_id, dictionary):
        self.combo_id = combo_id
        self.seller_id = seller_id
        if 'name' in dictionary:
            self.name = dictionary.get('name')
        if 'code' in dictionary:
            self.code = dictionary.get('code')
        if 'display_name' in dictionary:
            self.display_name = dictionary.get('display_name')
        if 'description' in dictionary:
            self.description = dictionary.get('description')
        if 'image' in dictionary:
            self.image = dictionary.get('image')
        if 'allergen_info' in dictionary:
            self.allergen_info = dictionary.get('allergen_info')
        if 'calorie_info' in dictionary:
            self.calorie_info = dictionary.get('calorie_info')
        if 'prep_time' in dictionary:
            self.prep_time = dictionary.get('prep_time')
        if 'contains_alcohol' in dictionary:
            self.contains_alcohol = dictionary.get('contains_alcohol')
        if 'pre_tax_price' in dictionary:
            self.pre_tax_price = dictionary.get('pre_tax_price')
        if 'cost' in dictionary:
            self.cost = dictionary.get('cost')
        if 'sku_category_code' in dictionary:
            self.sku_category_code = dictionary.get('sku_category_code')
        if 'active' in dictionary:
            self.active = dictionary.get('active')
        if 'sold_out' in dictionary:
            self.sold_out = dictionary.get('sold_out')
        if 'combo_items' in dictionary:
            self.combo_items = [] if not dictionary.get("combo_items") else [ComboItemRequest(
                combo_item) for combo_item in dictionary.get("combo_items")]


class RestaurantTableCreateRequest:
    def __init__(self, seller_id, dictionary):
        self.seller_id = seller_id
        self.seats_count = dictionary.get("seats_count")
        self.number_of_tables = dictionary.get("number_of_tables")


class RestaurantAreaCreateRequest:
    def __init__(self, seller_id, dictionary):
        self.seller_id = seller_id
        self.name = dictionary.get('name')
        self.tables = [] if not dictionary.get("tables") else [RestaurantTableCreateRequest(self.seller_id, table)
                                                               for table in dictionary.get("tables")]


class RestaurantTableBulkCreateRequest:
    def __init__(self, seller_id, dictionary):
        self.seller_id = seller_id
        self.tables = [] if not dictionary.get("tables") else [RestaurantTableCreateRequest(self.seller_id, table)
                                                               for table in dictionary.get("tables")]


class RestaurantTableUpdateRequest:
    def __init__(self, dictionary):
        self.table_id = dictionary.get("table_id")
        self.name = dictionary.get("name")
        self.x_coordinate = dictionary.get("x_coordinate")
        self.y_coordinate = dictionary.get("y_coordinate")
        self.width = dictionary.get("width")
        self.height = dictionary.get("height")


class RestaurantAreaUpdateRequest:
    def __init__(self, area_id, dictionary):
        self.area_id = area_id
        if "name" in dictionary:
            self.name = dictionary.get("name")
        if "tables" in dictionary:
            self.tables = [] if not dictionary.get("tables") else [RestaurantTableUpdateRequest(
                table) for table in dictionary.get("tables")]


class KitchenCreateRequest:
    def __init__(self, property_id, dictionary):
        self.property_id = property_id
        self.name = dictionary.get("name")
        self.config = dictionary.get("config")


class KitchenUpdateRequest:
    def __init__(self, kitchen_id, property_id, dictionary):
        self.kitchen_id = kitchen_id
        self.property_id = property_id
        if "name" in dictionary:
            self.name = dictionary.get("name")
        if "config" in dictionary:
            self.config = dictionary.get("config")


class CurrencyConversionRateRequest:
    def __init__(self, dictionary):
        self.from_currency = dictionary.get('from_currency')
        self.to_currency = dictionary.get('to_currency')
        self.conversion_rate = dictionary.get('conversion_rate')
        self.start_date = dictionary.get('start_date')
        self.end_date = dictionary.get('end_date')
        self.property_id = dictionary.get('property_id')
