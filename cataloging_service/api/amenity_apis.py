import json
import logging

from flask import jsonify
from flask.blueprints import Blueprint

from cataloging_service.api.schemas import PropertyAmenitySchema, RestaurantSchema, BarSchema, BanquetHallSchema, \
    RoomAmenitySchema
from cataloging_service.domain import service_provider
from cataloging_service.extensions import cache
from cataloging_service.models import PropertyAmenity, AmenityPublicWashroom, AmenityElevator, AmenityParking, \
    AmenityDisableFriendly, AmenitySwimmingPool, AmenityGym, AmenityPrivateCab, AmenitySpa, AmenityLaundry, \
    AmenityBreakfast, AmenityPayment, Restaurant, Bar, BanquetHall, AmenitySummary, RoomAmenity, AmenityIntercom, \
    AmenityHotWater, AmenityTV, AmenityAC, AmenityHeater, AmenityStove, AmenityTwinBed
from cataloging_service.thread_locals import app_context

bp = Blueprint('amenity_apis', __name__)

logger = logging.getLogger(__name__)

property_service = service_provider.property_service
room_service = service_provider.room_service


@bp.route('/properties/<string:property_id>/amenities/', methods=['GET'])
@bp.route('/v1/properties/<string:property_id>/amenities/', methods=['GET'])
@cache.clear_cache_on_model_change(
    model_classes_with_cache_key_getter={
        PropertyAmenity: lambda x: x.property_id,
        AmenityPublicWashroom: lambda x: x.property_amenity.property_id,
        AmenityElevator: lambda x: x.property_amenity.property_id,
        AmenityParking: lambda x: x.property_amenity.property_id,
        AmenityDisableFriendly: lambda x: x.property_amenity.property_id,
        AmenitySwimmingPool: lambda x: x.property_amenity.property_id,
        AmenityGym: lambda x: x.property_amenity.property_id,
        AmenityPrivateCab: lambda x: x.property_amenity.property_id,
        AmenitySpa: lambda x: x.property_amenity.property_id,
        AmenityLaundry: lambda x: x.property_amenity.property_id,
        AmenityBreakfast: lambda x: x.property_amenity.property_id,
        AmenityPayment: lambda x: x.property_amenity.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_amenities(property_id):
    # TODO: 0 calls
    property_object = property_service.get_property(property_id)

    property_amenity = property_object.property_amenity

    if not property_amenity:
        return jsonify({}), 200

    return jsonify(PropertyAmenitySchema().dump(property_amenity)), 200


@bp.route('/v1/properties/<string:property_id>/amenity-summary/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={AmenitySummary: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_amenity_summary(property_id):
    # TODO: 13,202.14 calls
    property_object = property_service.get_property(property_id)

    amenity_summary = property_object.amenity_summary

    if not amenity_summary:
        return jsonify({}), 200

    return jsonify(json.loads(amenity_summary.summary)), 200


@bp.route('/properties/<string:property_id>/restaurants/', methods=['GET'])
@bp.route('/v1/properties/<string:property_id>/restaurants/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={Restaurant: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_restaurants(property_id):
    # TODO: 0 calls
    property_object = property_service.get_property(property_id)

    restaurants = property_object.restaurants

    if not restaurants:
        return jsonify([]), 200

    return jsonify(RestaurantSchema().dump(restaurants, many=True)), 200


@bp.route('/properties/<string:property_id>/bars/', methods=['GET'])
@bp.route('/v1/properties/<string:property_id>/bars/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={Bar: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_bars(property_id):
    # TODO: 0 calls
    property_object = property_service.get_property(property_id)

    bars = property_object.bars

    if not bars:
        return jsonify([]), 200

    return jsonify(BarSchema().dump(bars, many=True)), 200


@bp.route('/properties/<string:property_id>/banquet-halls/', methods=['GET'])
@bp.route('/v1/properties/<string:property_id>/banquet-halls/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={BanquetHall: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_banquet_halls(property_id):
    # TODO: 0 calls
    property_object = property_service.get_property(property_id)

    halls = property_object.banquet_halls

    if not halls:
        return jsonify([]), 200

    return jsonify(BanquetHallSchema().dump(halls, many=True)), 200


@bp.route('/properties/<string:property_id>/rooms/<int(min=1):room_id>/amenities/', methods=['GET'])
@bp.route('/v1/properties/<string:property_id>/rooms/<int(min=1):room_id>/amenities/', methods=['GET'])
@cache.clear_cache_on_model_change(
    model_classes_with_cache_key_getter={
        RoomAmenity: lambda x: (x.room.property_id, x.room_id),
        AmenityIntercom: lambda x: (x.room_amenity.room.property_id, x.room_amenity.room_id),
        AmenityHotWater: lambda x: (x.room_amenity.room.property_id, x.room_amenity.room_id),
        AmenityTV: lambda x: (x.room_amenity.room.property_id, x.room_amenity.room_id),
        AmenityAC: lambda x: (x.room_amenity.room.property_id, x.room_amenity.room_id),
        AmenityHeater: lambda x: (x.room_amenity.room.property_id, x.room_amenity.room_id),
        AmenityStove: lambda x: (x.room_amenity.room.property_id, x.room_amenity.room_id),
        AmenityTwinBed: lambda x: (x.room_amenity.room.property_id, x.room_amenity.room_id),
    }, default_memoize=True)
@cache.memoize(timeout=3600, unless=lambda: app_context.include_test)
def get_room_amenities(property_id, room_id):
    # TODO: 0 calls
    room = room_service.get_property_room(property_id, room_id)

    room_amenity = room.amenity

    if not room_amenity:
        return jsonify({}), 200

    return jsonify(RoomAmenitySchema().dump(room_amenity)), 200
