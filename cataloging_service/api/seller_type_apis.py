import datetime

from flask import Blueprint, request, jsonify

from cataloging_service.domain import service_provider
from cataloging_service.utils import Utils

bp = Blueprint('seller_type_apis', __name__)

seller_type_service = service_provider.seller_type_history_service


@bp.route('/v1/seller-type/', methods=['GET'])
def get_seller_type():
    # TODO: 10,900.69 calls
    """
    ---
    get:
        tags:
            - Property
        description: "Get Seller Type of given property_id on a given date"

        parameters:
            - name: property_id
              in: query
              description: Property Id to fetch seller_type for
              required: True
              schema:
                  type: string
            - name: date
              in: query
              description: Date on which seller_type of property needs to be fetched
              required: True
              schema:
                  type: string
        responses:
            200:
                description: Seller Type of the property on the date
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                seller_type:
                                    type: string
    """
    property_id = request.args.get('property_id')
    date = request.args.get('date')

    if not property_id or not date:
        return "property id or date missing in request", 400
    date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
    if Utils.is_future_date(date):
        return "invalid date: future date", 400

    seller_type = seller_type_service.get_seller_type_by_date(property_id, date)
    return jsonify({'seller_type': seller_type}), 200
