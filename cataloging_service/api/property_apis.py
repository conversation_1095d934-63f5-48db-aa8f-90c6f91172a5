import logging

from flask import Blueprint, jsonify
from flask.globals import request
from newrelic.agent import add_custom_parameter

from cataloging_service.api.new_schemas.property_entity_schema import PropertyEntitySchema
from cataloging_service.api.request_objects import PropertyRequest
from cataloging_service.api.schemas import PropertySchema, BulkPropertySchema, PropertyImageSchema, \
    SkuActivationSchema, GetPropertiesV2RequestSchema, TestPropertyIdsResponseSchema
from cataloging_service.api.validators import PropertyRequestValidator, ModifyPropertyRequestValidator, \
    PropertySkuAndBundleActivationValidator, RolloverBusinessDate
from cataloging_service.constants import error_codes
from cataloging_service.constants.constants import PMS
from cataloging_service.domain import service_provider
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.decorators import raw_json, log_request
from cataloging_service.models import Property, Description, GuestFacingProcess, Landmark, PropertyLandmark, Location, \
    NeighbouringPlace, Ownership, BankDetail, PropertyDetail, Owner, TransportStation, TransportStationProperty, \
    PropertyPolicy, MicroMarket, City, Locality, Provider, AmenitySummary, SkuCategory, \
    CityAlias, PropertyImage, RoomTypeConfiguration
from cataloging_service.thread_locals import app_context

bp = Blueprint('property_apis', __name__)

logger = logging.getLogger('exceptions')

property_service = service_provider.property_service


@bp.route('/v1/properties/', methods=['GET'])
# @cache.cached(timeout=3600, key_prefix=lambda: request.url)
def get_all_properties():
    # TODO: 645.31 calls -> heavy api
    """
    ---
    get:
        tags:
            - Property
        description: Get all properties
    """
    prop_ids = request.args.get('ids')
    statuses = request.args.get('status')
    page = request.args.get('page', 0, type=int)
    sold_as = request.args.get('sold_as', None)

    items_per_page = request.args.get('item_count', 20, type=int)
    if prop_ids:
        prop_ids = prop_ids.split(',')
    if statuses:
        statuses = statuses.split(',')

    properties = property_service.get_all_properties_by_status_and_given_ids_and_pagination_and_sold_as(statuses, page,
                                                                                                        items_per_page,
                                                                                                        prop_ids,
                                                                                                        sold_as)
    return jsonify(BulkPropertySchema().dump(properties, many=True)), 200


@bp.route('/v1/get-test-properties', methods=['GET'])
def get_all_test_properties():
    """
    ---
    get:
        tags:
            - Property
        description: Get all test properties
    """
    property_ids = property_service.get_test_properties()
    return TestPropertyIdsResponseSchema().dump({"property_ids": property_ids}), 200


@bp.route('/properties/', methods=['POST'])
@raw_json(PropertyRequestValidator)
def create_property(parsed_request):
    # TODO: 0 calls
    """
    ---
    post:
        tags:
            - Property
        description: Create Property
    """
    create_property_request = PropertyRequest(parsed_request)
    created_property = property_service.create_property(create_property_request)
    return jsonify(PropertySchema().dump(created_property)), 201


@bp.route('/v1/properties/', methods=['POST'])
@raw_json(PropertyRequestValidator)
def create_property_v1(parsed_request):
    # TODO: 0 calls
    """
    ---
    post:
        tags:
            - Property
        description: Create Property
    """
    create_property_request = PropertyRequest(parsed_request)
    created_property = property_service.create_property(create_property_request)
    return jsonify(PropertySchema().dump(created_property)), 201


@cache.clear_cache_on_model_change(
    model_classes_with_cache_key_getter={
        Property: lambda x: x.id,
        Description: lambda x: x.property_id,
        GuestFacingProcess: lambda x: x.property_id,
        Landmark: lambda x: [pl.property_id for pl in x.property_landmarks],
        PropertyLandmark: lambda x: x.property_id,
        Location: lambda x: x.property_id,
        NeighbouringPlace: lambda x: x.property_id,
        Ownership: lambda x: x.property_id,
        BankDetail: lambda x: x.property_detail.property_id,
        PropertyDetail: lambda x: x.property_id,
        Owner: lambda x: [ownership.property_id for ownership in x.ownerships],
        TransportStation: lambda x: [tsp.property_id for tsp in x.transport_station_assocs],
        TransportStationProperty: lambda x: x.property_id,
        MicroMarket: lambda x: [loc.property_id for loc in x.locations],
        Locality: lambda x: [loc.property_id for loc in x.locations],
        City: lambda x: [loc.property_id for loc in x.locations],
        CityAlias: lambda x: [loc.property_id for loc in x.city.locations],
        PropertyPolicy: lambda x: [pd.property_id for pd in x.property_details],
        Provider: lambda x: [pd.property_id for pd in x.property_detail],
        AmenitySummary: lambda x: x.property_id,
        SkuCategory: lambda x: [p.id for p in x.properties],
    })
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_cached_property(property_id):
    property = property_service.get_property(property_id)
    return PropertySchema().dump(property)

@cache.memoize_unhashed_key(attribute_for_cache_key='hx_property_id', timeout=7200,
                            unless=lambda: app_context.include_test)
def get_cached_property_by_hx_property_id(hx_property_id):
    # TODO: Should we drop support?
    property = property_service.get_property_by_hx_property_id(hx_property_id)
    return PropertySchema().dump(property)


@bp.route('/properties/<string:property_id>', methods=['GET'])
def get_property(property_id):
    # TODO: 565,983.66 calls
    """
    ---
    get:
        tags:
            - Property
        description: Get Property
    """
    return jsonify(get_cached_property(property_id)), 200


@bp.route('/v1/properties/<string:property_id>', methods=['GET'])
def get_property_v1(property_id):
    # TODO: 92,348 calls
    """
    ---
    get:
        tags:
            - Property
        description: Get Property
    """
    return jsonify(get_cached_property(property_id)), 200


def get_properties(property_ids, fields=None):
    properties = property_service.get_properties_by_ids(property_ids, fields=fields)
    schema = PropertyEntitySchema(many=True, only=tuple(fields)) if fields else PropertyEntitySchema(many=True)
    return schema.dump(properties)


@cache.clear_cache_on_model_change(
    model_classes_with_cache_key_getter={
        Property: lambda x: x.id,
        Description: lambda x: x.property_id,
        GuestFacingProcess: lambda x: x.property_id,
        Landmark: lambda x: [pl.property_id for pl in x.property_landmarks],
        PropertyLandmark: lambda x: x.property_id,
        Location: lambda x: x.property_id,
        NeighbouringPlace: lambda x: x.property_id,
        Ownership: lambda x: x.property_id,
        BankDetail: lambda x: x.property_detail.property_id,
        PropertyDetail: lambda x: x.property_id,
        Owner: lambda x: [ownership.property_id for ownership in x.ownerships],
        TransportStation: lambda x: [tsp.property_id for tsp in x.transport_station_assocs],
        TransportStationProperty: lambda x: x.property_id,
        MicroMarket: lambda x: [loc.property_id for loc in x.locations],
        Locality: lambda x: [loc.property_id for loc in x.locations],
        City: lambda x: [loc.property_id for loc in x.locations],
        CityAlias: lambda x: [loc.property_id for loc in x.city.locations],
        PropertyPolicy: lambda x: [pd.property_id for pd in x.property_details],
        Provider: lambda x: [pd.property_id for pd in x.property_detail],
        AmenitySummary: lambda x: x.property_id,
        SkuCategory: lambda x: [p.id for p in x.properties],
    })
@cache.multi_key_memoize(attribute_for_cache_key='property_ids',
                         cache_key_id_getter=lambda item: item['id'], timeout=3600,
                         unless=lambda: app_context.include_test)
def get_cached_properties(property_ids):
    return get_properties(property_ids)


@bp.route('/v2/properties/', methods=['GET'])
def get_properties_v2():
    # TODO: 4,912,465.28 calls
    """
    ---
    get:
        tags:
            - Property
        description: Get Property
    """
    parsed_request = GetPropertiesV2RequestSchema().load(request.args)
    property_ids = parsed_request.get('property_id')
    if property_ids:
        add_custom_parameter('property_count', len(property_ids))

    fields = request.args.get("fields")
    if fields:
        add_custom_parameter('fields', fields)
        fields = fields.split(",")
        logger.info("Request fields: %s", fields)

    if not property_ids:
        property_ids = property_service.get_property_ids(parsed_request.get('status'),
                                                         parsed_request.get('modified_date'))

    if parsed_request.get('from_cache', 'true').lower() == 'true' and not fields:
        properties = get_cached_properties(property_ids=property_ids)
    else:
        properties = get_properties(property_ids, fields=fields)

    return jsonify(properties), 200


@bp.route('/properties/<string:property_id>', methods=['PUT'])
@raw_json(ModifyPropertyRequestValidator)
def modify_property(property_id, parsed_request):
    # TODO: 0 calls
    """
    ---
    put:
        tags:
            - Property
        description: Update property
    """
    property = property_service.get_property(property_id)
    modify_property_request = PropertyRequest(parsed_request, property)
    property = property_service.update_property(property, modify_property_request)
    return jsonify(PropertySchema().dump(property)), 200


@bp.route('/v1/properties/<string:property_id>', methods=['PUT'])
@raw_json(ModifyPropertyRequestValidator)
def modify_property_v1(property_id, parsed_request):
    # TODO: 0 calls
    """
    ---
    put:
        tags:
            - Property
        description: Update property
    """
    property = property_service.get_property(property_id)
    modify_property_request = PropertyRequest(parsed_request, property)
    property = property_service.update_property(property, modify_property_request)
    return jsonify(PropertySchema().dump(property)), 200


@bp.route('/properties/<string:property_id>/images/', methods=['GET'])
@cache.clear_cache_on_model_change(
    model_classes_with_cache_key_getter={PropertyImage: lambda x: x.property_id,
                                         RoomTypeConfiguration: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_images(property_id):
    # TODO: 42,675.94 calls
    """
    ---
    get:
        tags:
            - Property
        description: Get Property images
    """
    property_object = property_service.get_property(property_id)
    property_images = property_service.get_property_images(property_object)
    return jsonify(PropertyImageSchema().dump(property_images, many=True)), 200


@bp.route('/v1/properties/<string:property_id>/images/', methods=['GET'])
@cache.clear_cache_on_model_change(
    model_classes_with_cache_key_getter={PropertyImage: lambda x: x.property_id,
                                         RoomTypeConfiguration: lambda x: x.property_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='property_id', timeout=3600,
                            unless=lambda: app_context.include_test)
def get_property_images_v1(property_id):
    # TODO: 0 calls
    """
    ---
    get:
        tags:
            - Property
        description: Get Property images
    """
    property_object = property_service.get_property(property_id)
    property_images = property_service.get_property_images(property_object)
    return jsonify(PropertyImageSchema().dump(property_images, many=True)), 200


@bp.route('/v1/activation/sync/', methods=['POST'])
@log_request
@raw_json(PropertySkuAndBundleActivationValidator)
def activate_sku_and_bundle(parsed_request):
    # TODO: 520.26 calls -> Duplicate DB calls
    """
    Updated records are returned in response, empty list if nothing is updated
    ---
    post:
        tags:
            - SKU
        description: Activates SKU Bundles
    """
    updated_property_skus = property_service. \
        update_bundles_and_sku_activation_status_for_property(parsed_request['cs_id'],
                                                              parsed_request['service'],
                                                              parsed_request['sku_codes'],
                                                              )
    return jsonify(SkuActivationSchema().dump(updated_property_skus, many=True)
                   if updated_property_skus else []), 200


@bp.route('/property/create_sku', methods=['POST'])
def create_property_sku():
    # TODO: 0 calls
    """
    Create property SKU
    ---
    post:
        tags:
            - SKU
        description: Create property SKU
    """
    property_service.auto_create_sku_for_given_properties(ids=None)
    return jsonify(dict(code="CREATED", msg="Property sku created")), 201


@bp.route('/properties/<string:property_id>/pms/<string:pms_value>', methods=['GET'])
def get_property_by_pms_and_property_id(property_id, pms_value):
    # TODO: 11.00 calls -> Duplicate sku DB calls
    """
    Get Property by PMS name and property id.
    ---
    get:
        tags:
            - Property
        description: Get Property by PMS name and property id.
    """
    if pms_value.strip().lower() == PMS.hx.name:
        return jsonify(get_cached_property_by_hx_property_id(property_id)), 200
    elif pms_value.strip().lower() == PMS.crs.name:
        return jsonify(get_cached_property(property_id)), 200
    else:
        raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context='Invalid PMS')


@bp.route('/v1/properties/<string:property_id>/pms/<string:pms_value>', methods=['GET'])
def get_property_by_pms_and_property_id_v1(property_id, pms_value):
    # TODO: 11.00 calls
    """
    Get Property by PMS name and property id.
    ---
    get:
        tags:
            - Property
        description: Get Property by PMS name and property id.
    """
    if pms_value.strip().lower() == PMS.hx.name:
        return jsonify(get_cached_property_by_hx_property_id(property_id)), 200
    elif pms_value.strip().lower() == PMS.crs.name:
        return jsonify(get_cached_property(property_id)), 200
    else:
        raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context='Invalid PMS')


@bp.route('/v1/properties/<string:property_id>/rollover-business-date', methods=['POST'])
@raw_json(RolloverBusinessDate)
def rollover_business_date(property_id, parsed_request):
    """
    Rollovers current business date of given property by 1 day.
    Also, rolls over current business date of all related Sellers (POSes), by 1 day too.

    ---
    post:
        tags:
            - Property
        description: Incremement current business date by 1 day
    """
    property = property_service.rollover_current_business_date(
        property_id, business_date=parsed_request.get('business_date')
    )
    return jsonify(PropertySchema().dump(property)), 200
