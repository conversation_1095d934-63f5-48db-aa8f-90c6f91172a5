import logging

from flask import Blueprint, jsonify, request

from cataloging_service.api.schemas import ChannelSchema, SubChannelSchema, ApplicationSchema, \
    PricingPolicySchema
from cataloging_service.domain import service_provider
from cataloging_service.extensions import cache
from cataloging_service.models import Application, Channel, SubChannel, PricingPolicy

bp = Blueprint('channel_apis', __name__)

logger = logging.getLogger(__name__)

channel_service = service_provider.channel_service


@bp.route('/channels/', methods=['GET'])
def get_all_channels():
    # TODO: 27.91 calls -> Duplicate DB calls
    """
    array in query string
    https://stackoverflow.com/a/4541433/1790760
    :return:
    """
    channel_ids = request.args.get('ids')
    if channel_ids:
        channel_ids = channel_ids.split(',')

    channels = channel_service.get_all_channels(channel_ids)
    payload = ChannelSchema().dump(channels, many=True) if channels else []
    return jsonify(payload), 200


@bp.route('/channels/<string:channel_id>', methods=['GET'])
def get_channel(channel_id):
    # TODO: 24.00 calls -> Duplicate DB calls
    channel = channel_service.get_channel(channel_id)
    channel = ChannelSchema().dump(channel)
    return jsonify(channel or {}), 200


@bp.route('/channels/<string:channel_id>/subchannels/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={Channel: lambda x: x.id,
                                                                        SubChannel: lambda x: x.channel_id})
@cache.memoize_unhashed_key(attribute_for_cache_key='channel_id', timeout=3600)
def get_sub_channels_for_channel(channel_id):
    # TODO: 121,919.88 calls
    sub_channels = channel_service.get_sub_channels(channel_id)
    payload = SubChannelSchema().dump(sub_channels, many=True) if sub_channels else []
    return jsonify(payload), 200


@bp.route('/applications/', methods=['GET'])
def get_all_applications():
    # TODO: 9.83 calls
    """
    array in query string
    https://stackoverflow.com/a/4541433/1790760
    :return:
    """
    app_ids = request.args.get('ids')
    if app_ids:
        app_ids = app_ids.split(',')

    apps = channel_service.get_all_applications(app_ids)
    payload = ApplicationSchema().dump(apps, many=True) if apps else []
    return jsonify(payload), 200


@bp.route('/channels/<string:channel_id>/applications/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={Channel: lambda x: x.id})
@cache.memoize_unhashed_key(attribute_for_cache_key='channel_id', timeout=3600)
def get_applications_for_channel(channel_id):
    # TODO: 121,920.86 calls
    apps = channel_service.get_applications(channel_id)
    payload = ApplicationSchema().dump(apps, many=True) if apps else []
    return jsonify(payload), 200


@bp.route('/pricing_policies/', methods=['GET'])
@cache.clear_cache_on_model_change(model_classes_with_cache_key_getter={PricingPolicy: lambda x: None})
@cache.memoize_unhashed_key(timeout=3600)
def get_all_pricing_policies():
    # TODO: 7.00 calls
    policies = channel_service.sget_all_pricing_policies(ids=None)
    payload = PricingPolicySchema().dump(policies, many=True) if policies else []
    return jsonify(payload), 200
