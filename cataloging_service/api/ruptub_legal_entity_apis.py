import logging

from flask import Blueprint, jsonify
from cataloging_service.domain import service_provider
from cataloging_service.api.schemas import RuptubLegalEntityDetailsSchema
from cataloging_service.exceptions import InvalidStateIdException

bp = Blueprint('treebo_legal_entity', __name__)

logger = logging.getLogger(__name__)

ruptub_legal_entity_service = service_provider.legal_entity_details_service


@bp.route('/v1/ruptub_legal_entities', methods=['GET'])
def get_all_legal_entities():
    # TODO: 0 calls
    try:
        ruptub_legal_entities = ruptub_legal_entity_service.sget_all_legal_entities()
        payload = RuptubLegalEntityDetailsSchema().dump(ruptub_legal_entities, many=True)
        return jsonify(payload)
    except Exception as e:
        logger.exception(e.args)
        raise


@bp.route('/v1/ruptub_legal_entities/<state_id>', methods=['GET'])
def get_legal_entity_by_state_id(state_id):
    # TODO: 0 calls
    try:
        # The range and exception message can be changed here if the range extends or shrinks.
        ruptub_legal_entity = ruptub_legal_entity_service.sget_legal_entity_details_by_state_id(state_id)
        payload = RuptubLegalEntityDetailsSchema().dump(ruptub_legal_entity)
        return jsonify(payload)
    except InvalidStateIdException as e:
        logger.exception(e.message)
        return jsonify({'errors': e.message}), 400
    except Exception as e:
        logger.exception(e.args)
        raise
