import re

from marshmallow import fields, pre_dump, Schema

from cataloging_service.api.schemas import MicroMarketSchema
from cataloging_service.common.schema_registry import swag_schema


class _Address:
    REGEX_REMOVE_NON_ALPHANUM_AT_START_AND_END = re.compile('^[^a-zA-Z0-9]*|[^a-zA-Z0-9]*$')

    def __init__(self, line_1, line_2, city, state, country, pincode):
        self.line_1 = line_1
        self.line_2 = line_2
        self.city = city
        self.state = state
        self.country = country
        self.pincode = pincode
        self.smart_cleanup()

    def smart_cleanup(self):
        replaceable_attribs = [self.city.name, self.state.name, self.country.name, self.pincode]

        for attrib in replaceable_attribs:
            self.line_1 = self.line_1.replace(str(attrib), "").strip().strip(",") if self.line_1 else ""
            self.line_2 = self.line_2.replace(str(attrib), "").strip().strip(",") if self.line_2 else ""

        self.line_1 = _Address.REGEX_REMOVE_NON_ALPHANUM_AT_START_AND_END.sub('', self.line_1)
        self.line_2 = _Address.REGEX_REMOVE_NON_ALPHANUM_AT_START_AND_END.sub('', self.line_2)
        self.line_1 = self.line_1.replace(self.line_2, "") if self.line_1 else ""

    def human_readable_string(self):
        return "{a}, {ct} - {p}, {s}, {cn}".format(
            a="{l1}, {l2}" if self.line_2 else self.line_1,
            ct=self.city.name,
            p=self.pincode,
            s=self.state.name,
            cn=self.country.name,
        )

    def __str__(self):
        return self.human_readable_string()


class IdNameGeneric(Schema):
    id = fields.String()
    name = fields.String()


class LocalitySchema(IdNameGeneric):
    pass


class CitySchema(IdNameGeneric):
    pass


class StateSchema(IdNameGeneric):
    pass


class CountrySchema(IdNameGeneric):
    pass


class Address(Schema):
    line_1 = fields.String()
    line_2 = fields.String()
    pincode = fields.String()
    city = fields.Nested(CitySchema)
    state = fields.Nested(StateSchema)
    country = fields.Nested(CountrySchema)


class BrandShallowSchema(Schema):
    code = fields.String()


class Location(Schema):
    latitude = fields.String()
    longitude = fields.String()
    address = fields.Nested(Address)
    human_readable_address = fields.String()
    locality = fields.Nested(LocalitySchema)
    google_maps_link = fields.URL()

    @pre_dump
    def set_dynamic_properties(self, obj, **kwargs):
        address = _Address(
            obj.postal_address,
            '',
            obj.city,
            obj.city.state,
            obj.city.state.country,
            obj.pincode,
        )
        obj.address = address
        obj.human_readable_address = address.human_readable_string()
        obj.google_maps_link = obj.maps_link
        return obj


class LegalDetails(Schema):
    gstin = fields.String()
    pan = fields.String()
    legal_signature = fields.URL()
    address = fields.Nested(Address)

    @pre_dump
    def set_dynamic_properties(self, obj, **kwargs):
        location = obj.location
        obj.address = _Address(
            location.legal_address,
            '',
            location.legal_pincode,
            location.legal_city,
            location.legal_city.state if location.legal_city else None,
            location.legal_city.state.country if location.legal_city else None,
        )
        obj.gstin = obj.property_detail.gstin
        obj.pan = obj.property_detail.pan
        obj.legal_signature = obj.property_detail.legal_signature
        return obj


class PropertyImportantDates(Schema):
    launched_date = fields.Date()
    signed_date = fields.Date()
    churned_date = fields.Date()


@swag_schema
class PropertySchema(Schema):
    id = fields.String()
    name = fields.String()
    status = fields.String()
    important_dates = fields.Nested(PropertyImportantDates)
    location = fields.Nested(Location)
    brands = fields.Nested(BrandShallowSchema, many=True)
    micro_market = fields.Method('get_micro_market')
    current_business_date = fields.Date()

    def get_micro_market(self, property):
        if not (property.location and property.location.micro_market):
            return None
        return MicroMarketSchema(only=('name',)).dump(property.location.micro_market)

    @pre_dump
    def set_dynamic_properties(self, obj, **kwargs):
        obj.important_dates = obj
        return obj
