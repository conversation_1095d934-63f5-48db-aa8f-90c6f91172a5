from flask_marshmallow import Schema
from marshmallow import fields, validates_schema, utils, ValidationError, validate, pre_load
from marshmallow.validate import Range, Length
from treebo_commons.money.constants import CurrencyType

room_types = ['rt01', 'rt02', 'rt03', 'rt04']
room_type_names = ['acacia', 'oak', 'maple', 'mahogany']


class AddressSchema(Schema):
    line1 = fields.String(validate=[Length(min=1, error="line1 is required")])
    line2 = fields.String()
    city_id = fields.String(allow_none=False, required=True)
    pincode = fields.String(required=True, validate=[Length(min=6, max=6, error="pincode should be 6 digits")])

    @validates_schema
    def validate_locality_id(self, data, **kwargs):
        if not data.get('city_id'):
            raise ValidationError('Please choose city from search.')


class Facilitychema(Schema):
    is_property_leased = fields.Boolean(default=False)
    is_security_guard_available = fields.Boolean(default=False)
    is_public_washroom_available = fields.Boolean(default=False)
    is_elevator_available = fields.Boolean(default=False)
    is_parking_available = fields.Boolean(default=False)
    is_disabled_friendly = fields.Boolean(default=False)
    is_bar_available = fields.Boolean(default=False)
    is_restaurant_available = fields.Boolean(default=False)
    is_banquet_hall_available = fields.Boolean(default=False)
    is_pool_available = fields.Boolean(default=False)
    is_gym_available = fields.Boolean(default=False)
    is_spa_available = fields.Boolean(default=False)
    is_laundry_available = fields.Boolean(default=False)
    is_amex_payment_available = fields.Boolean(default=False)
    is_roof_top_cafe_available = fields.Boolean(default=False)
    is_pool_table_available = fields.Boolean(default=False)
    is_private_cab_service_available = fields.Boolean(default=False)
    is_private_intercom_available = fields.Boolean(default=False)
    is_hot_water_available = fields.Boolean(default=False)
    is_tv_available = fields.Boolean(default=False)
    is_moquito_repellant_available = fields.Boolean(default=False)
    is_room_heater_available = fields.Boolean(default=False)
    is_ac_available = fields.Boolean(default=False)
    is_unmarried_couple_allowed = fields.Boolean(default=False)
    is_local_ids_accepted = fields.Boolean(default=False)


class RoomDetailSchema(Schema):
    room_number = fields.String(required=True, validate=[Length(min=1, error="room number is required")])
    building_number = fields.String(required=True)
    floor_number = fields.Integer(required=True)
    room_type = fields.String(validate=validate.OneOf(choices=room_types))


class RoomTypeDetailSchema(Schema):
    max_adults = fields.Integer(required=True, validate=[Range(min=1, error="max_adults must be greater than 0")])
    max_children = fields.Integer(required=True, validate=[Range(min=0, error="max_children must be 0 or more than 0")])
    max_total = fields.Integer(required=True, validate=[Range(min=1, error="max_total must be greater than 0")])
    room_size = fields.Integer(required=True, validate=[Range(min=1, error="room_size must be greater than 0")])
    room_type = fields.String(validate=validate.OneOf(choices=room_type_names))

    is_wardrobe_available = fields.Boolean(default=False)
    is_luggage_shelf_available = fields.Boolean(default=False)
    is_coffee_table_available = fields.Boolean(default=False)
    is_smoking_permitted = fields.Boolean(default=False)
    is_study_table_available = fields.Boolean(default=False)
    is_mini_fridge_available = fields.Boolean(default=False)
    is_kitchenette_available = fields.Boolean(default=False)
    is_microwave_oven_available = fields.Boolean(default=False)
    is_sofa_chair_available = fields.Boolean(default=False)
    is_dining_table_available = fields.Boolean(default=False)
    bed_type = fields.String(validate=[Length(min=1, error='bed_type is required')])


class PropertyLaunchSchema(Schema):
    name = fields.String()
    previous_name = fields.String()

    signed_date = fields.Date(format='yyyy-mm-dd')
    launch_date = fields.Date(format='yyyy-mm-dd')

    legal_name = fields.String()
    gstin = fields.String(required=True, validate=[Length(min=15, max=15, error="Value must be 15digits")])
    address = fields.Nested(AddressSchema)
    legal_address = fields.Nested(AddressSchema)

    reception_mobile = fields.String()
    reception_landline = fields.String()
    email = fields.String()

    owner_name = fields.String(validate=[Length(min=3, error="owner_name is required")])
    owner_email = fields.Email()
    owner_phone = fields.String(validate=[Length(min=5, error="owner_phone is required")])

    secondary_owner_name = fields.String(allow_none=True)
    secondary_owner_email = fields.String(allow_none=True)
    secondary_owner_phone = fields.String(allow_none=True)

    is_msme = fields.Boolean(default=False)
    msme_number = fields.String(allow_none=True)
    tan = fields.String(allow_none=True)

    facilities = fields.Nested(Facilitychema)
    room_type_details = fields.Nested(RoomTypeDetailSchema, many=True)
    room_details = fields.Nested(RoomDetailSchema, many=True)
    suited_for = fields.String()
    star_rating = fields.Integer(allow_none=False, required=True)
    total_floors = fields.Integer(allow_none=False, required=True)
    google_maps_link = fields.String(validate=[Length(min=1, error='google_maps_link is required')])
    base_currency_code = fields.String(default=CurrencyType.INR.value)
    timezone = fields.String()
    country_code = fields.String()
    hotel_logo = fields.String()

    latitude = fields.Decimal(required=True)
    longitude = fields.Decimal(required=True)

    laundry_timings = fields.String()
    room_service_timings = fields.String()
    restaurant_timings = fields.String()
    bar_timings = fields.String()

    standard_checkin_time = fields.Time(required=True)
    free_early_checkin_time = fields.Time(required=True)
    free_late_checkout_time = fields.Time(required=True)
    standard_checkout_time = fields.Time(required=True)

    early_checkin_fee = fields.Decimal(required=True)
    late_checkout_fee = fields.Decimal(required=True)
    super_hero_price_slab = fields.String(required=False)
    hygiene_shield_name = fields.String(required=False)

    @pre_load
    def split_room_numbers(self, in_data, **kwargs):
        room_details = in_data['room_details']
        split_room_details = []
        for room_detail in room_details:
            room_number = room_detail.get('room_number')
            room_numbers = room_number.split(',')
            new_room_details = [{**room_detail, **{'room_number': r}} for r in room_numbers]
            split_room_details += new_room_details

        in_data['room_details'] = split_room_details
        return in_data

    @validates_schema
    def validate_timings(self, data, **kwargs):

        if not data.get('room_type_details'):
            raise ValidationError('Atleast one room type should be enabled')

        if not data.get('room_details'):
            raise ValidationError('Atleast one room should be present')

        room_numbers = [r.get('room_number') for r in data['room_details']]

        if len(room_numbers) != len(set(room_numbers)):
            raise ValidationError('Duplicate room numbers are given')

        def validate(timing, field_name):
            if not timing:
                return timing

            timings = timing.split('-')
            try:
                start = utils.from_iso_time(timings[0].strip())
                end = utils.from_iso_time(timings[1].strip())
                return '{s}-{e}'.format(s=start, e=end)
            except (AttributeError, TypeError, ValueError, IndexError) as e:
                raise ValidationError("Invalid timing: {t} due to {e}".format(t=timing, e=e), field_names=[field_name])

        data['laundry_timings'] = validate(data.get('laundry_timings'), 'laundry_timings')
        data['room_service_timings'] = validate(data.get('room_service_timings'), 'room_service_timings')
        data['restaurant_timings'] = validate(data.get('restaurant_timings'), 'restaurant_timings')
        data['bar_timings'] = validate(data.get('bar_timings'), 'bar_timings')
