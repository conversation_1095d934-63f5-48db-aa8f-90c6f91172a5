from flask import Blueprint

from cataloging_service.api.v3.superhero_property_launch import SuperheroPropertyLaunchAP<PERSON>
from cataloging_service.api.v3.user_role_creation import UserRole<PERSON>reationAP<PERSON>
from cataloging_service.api.v3.city_search import CitySearch
from cataloging_service.api.v3.room_types import RoomTypeSearch
from cataloging_service.api.v3.rate_plans import RatePlanSearch
from cataloging_service.api.v3.locality_search import LocalitySearch
from cataloging_service.api.v3.property import property_search_api_method_view
from cataloging_service.api.v3.property_koopan import property_koopan_api_method_view
from cataloging_service.api.v3.property_launch import PropertyLaunch<PERSON><PERSON>
from cataloging_service.api.v3.upload_csv import UploadPosMenuAPI
from cataloging_service.api.v3.upload_csv import UploadHygieneShieldAPI
v3_bp = Blueprint('property_v3', __name__, url_prefix='/api/v3')

v3_bp.add_url_rule('/properties/', view_func=property_search_api_method_view)
v3_bp.add_url_rule('/koopan/properties/', view_func=property_koopan_api_method_view)
v3_bp.add_url_rule('/properties/launch/', view_func=PropertyLaunchAPI.as_view('property_launch'))
v3_bp.add_url_rule('/properties/launch/superhero', view_func=SuperheroPropertyLaunchAPI.as_view('superhero_property_launch'))
v3_bp.add_url_rule('/localities/', view_func=LocalitySearch.as_view('locality_search'))
v3_bp.add_url_rule('/cities/', view_func=CitySearch.as_view('city_search'))
v3_bp.add_url_rule('/room-types', view_func=RoomTypeSearch.as_view('room_types'))
v3_bp.add_url_rule('/rate-plans', view_func=RatePlanSearch.as_view('rate_plans'))
v3_bp.add_url_rule('/upload/', view_func=UploadPosMenuAPI.as_view('pos_menu'))
v3_bp.add_url_rule('/upload-shield-detail/', view_func=UploadHygieneShieldAPI.as_view('hygiene_shield'))
v3_bp.add_url_rule('/user-role-creation/', view_func=UserRoleCreationAPI.as_view('user_role_creation'))
