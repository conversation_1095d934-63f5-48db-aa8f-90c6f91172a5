from flask import request
from flask_login import login_required
from flask_marshmallow import Schema
from marshmallow import fields

from cataloging_service.infrastructure.repositories.pagination import Pagination
from core.common.api import BaseAPI
from core.common.api.api_response import response
from cataloging_service.domain import service_provider

location_service = service_provider.location_service


class LocalitySchema(Schema):
    id = fields.String()
    name = fields.String()
    city = fields.String()
    micro_market = fields.String()
    display_name = fields.Function(lambda obj: str(obj))


class LocalitySearch(BaseAPI):

    @login_required
    def get(self):
        # TODO: 0 calls
        query = request.args.get('q')
        if query:
            filter = '%{query}%'.format(query=query)
            localities = location_service.get_all_related_localities(filter)
        else:
            localities = location_service.get_all_localities()

        localities = Pagination.paginate(localities, error_out=False)
        response.data = LocalitySchema(many=True).dump(localities.items)
        return response

