import logging

from flask import Blueprint, jsonify

from cataloging_service.api.schemas import <PERSON><PERSON><PERSON><PERSON><PERSON>che<PERSON>, GuestTypeSchema
from cataloging_service.domain import service_provider

meta_service = service_provider.meta_service

bp = Blueprint('meta_apis', __name__)

logger = logging.getLogger(__name__)


@bp.route('/cuisines/', methods=['GET'])
@bp.route('/v1/cuisines/', methods=['GET'])
def get_cuisines():
    # TODO: 0 calls
    return jsonify(CuisineSchema().dump(meta_service.get_cuisines(), many=True)), 200


@bp.route('/guest-types/', methods=['GET'])
@bp.route('/v1/guest-types/', methods=['GET'])
def get_guest_types():
    # TODO: 0 calls
    return jsonify(GuestTypeSchema().dump(meta_service.get_guest_types(), many=True)), 200


@bp.route('/meta/', methods=['GET'])
def get_meta_dictionary():
    # TODO: 0 calls
    return jsonify(meta_service.get_meta_dictionary()), 200


@bp.route('/clear-cache', methods=['POST'])
def clear_cache():
    # TODO: 0 calls
    return 'Unsupported API', 400
