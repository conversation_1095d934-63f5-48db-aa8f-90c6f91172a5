import logging

from flask import abort, redirect, url_for
from flask.blueprints import Blueprint
from flask.templating import render_template
from flask_login import current_user
from flask_security.utils import url_for_security

from cataloging_service.client import client_provider
from cataloging_service.constants import model_choices
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.repositories import PropertyRepository
from cataloging_service.utils import Utils

bp = Blueprint('ui_routes', __name__)

logger = logging.getLogger(__name__)


@bp.route('/myhotel/<string:property_id>', methods=['GET'])
def show_property(property_id):
    # TODO: 3,209.55 calls
    if not (current_user and current_user.is_authenticated):
        return redirect(url_for_security('login', next=url_for('ui_routes.show_property', property_id=property_id)))

    property_object = service_provider.property_service.get_property(property_id)
    property_ids_and_names = PropertyRepository().get_all_properties(only_id_name=True)
    try:
        return render_template('ui/hotel-detail.html',
                               title='Home',
                               property=property_object,
                               all_properties=property_ids_and_names,
                               station_map=get_transport_station_map(property_object),
                               room_details=get_room_details(property_object),
                               users=get_qams(property_object))
    except Exception as exception:
        logger.error('Error while rendering template %s' % exception)
        Utils.send_email()
        abort(500)


@bp.route('/myhotel', methods=['GET'])
def index():
    # TODO: 4.00 calls
    properties = service_provider.property_service.get_all_properties()
    if properties:
        property = properties[0]
        return redirect(url_for('ui_routes.show_property', property_id=property.id))
    abort(404)


def get_transport_station_map(property_object):
    station_map = dict()
    for property_station_mapping in property_object.transport_station_assocs:
        if property_station_mapping.transport_station.type not in station_map:
            station_map[property_station_mapping.transport_station.type] = []
        station_map[property_station_mapping.transport_station.type].append(
            {'station': property_station_mapping.transport_station, 'mapping': property_station_mapping})

    return station_map


def get_qams(property_object):
    all_qams = client_provider.prowl_client.get_hotel_users(cs_id=property_object.id)

    qams = []
    user_group = []
    count = 0
    completed = True
    for user in all_qams:
        user_group.append(user)
        completed = False

        if count == 3:
            completed = True
            qams.append(user_group)
            user_group = []

        count = (count + 1) % 4

    if not completed:
        qams.append(user_group)

    return qams


def get_room_details(property_object):
    room_details_map = dict()
    for room in property_object.rooms:

        if not room.is_active:
            continue

        if room.room_type.type not in room_details_map:
            room_details_map[room.room_type.type] = dict()

        if 'rooms' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['rooms'] = []

        if 'mini_fridge' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['mini_fridge'] = {'rooms': []}
        if 'balcony' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['balcony'] = {'rooms': []}
        if 'kitchenette' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['kitchenette'] = {'rooms': []}
        if 'kitchenette_utensils' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['kitchenette_utensils'] = {'rooms': []}
        if 'king_sized_beds' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['king_sized_beds'] = {'rooms': []}
        if 'queen_sized_beds' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['queen_sized_beds'] = {'rooms': []}
        if 'single_beds' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['single_beds'] = {'rooms': []}
        if 'wardrobe' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['wardrobe'] = {'rooms': []}
        if 'locker_available' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['locker_available'] = {'rooms': []}
        if 'microwave' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['microwave'] = {'rooms': []}
        if 'luggage_shelf' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['luggage_shelf'] = {'rooms': []}
        if 'study_table_chair' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['study_table_chair'] = {'rooms': []}
        if 'sofa_chair' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['sofa_chair'] = {'rooms': []}
        if 'coffee_table' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['coffee_table'] = {'rooms': []}
        if 'other_furniture' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['other_furniture'] = {'rooms': []}
        if 'smoking_room' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['smoking_room'] = {'rooms': []}
        if 'bath_tub' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['bath_tub'] = {'rooms': []}
        if 'shower_curtain' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['shower_curtain'] = {'rooms': []}
        if 'smoke_alarm' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['smoke_alarm'] = {'rooms': []}
        if 'shower_cabinets' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['shower_cabinets'] = {'rooms': []}
        if 'living_room' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['living_room'] = {'rooms': []}
        if 'dining_table' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['dining_table'] = {'rooms': []}
        if 'windows' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['windows'] = {'rooms': []}
        if 'treebo_toiletries' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['treebo_toiletries'] = {'rooms': []}
        if 'heater' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['heater'] = {'rooms': []}
        if 'intercom' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['intercom'] = {'rooms': []}
        if 'hot_water' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['hot_water'] = {'rooms': []}
        if 'tv' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['tv'] = {'rooms': []}
        if 'ac' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['ac'] = {'rooms': []}
        if 'stove' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['stove'] = {'rooms': []}
        if 'joinable_twin_beds' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['joinable_twin_beds'] = {'rooms': []}
        if 'non_joinable_twin_beds' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['non_joinable_twin_beds'] = {'rooms': []}
        if 'bucket_mug' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['bucket_mug'] = {'rooms': []}
        if 'fan' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['fan'] = {'rooms': []}
        if 'room_lock' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['room_lock'] = {'rooms': []}
        if 'mosquito_repellent' not in room_details_map[room.room_type.type]:
            room_details_map[room.room_type.type]['mosquito_repellent'] = {'rooms': []}

        room_details_map[room.room_type.type]['rooms'].append(room.room_number)
        if room.amenity:
            if room.amenity.mini_fridge:
                room_details_map[room.room_type.type]['mini_fridge']['rooms'].append(room.room_number)
            if room.amenity.balcony:
                room_details_map[room.room_type.type]['balcony']['rooms'].append(room.room_number)
            if room.amenity.kitchenette:
                room_details_map[room.room_type.type]['kitchenette']['rooms'].append(room.room_number)
            if room.amenity.kitchenette_utensils:
                room_details_map[room.room_type.type]['kitchenette_utensils']['rooms'].append(room.room_number)
            if room.amenity.king_sized_beds:
                room_details_map[room.room_type.type]['king_sized_beds']['rooms'].append(room.room_number)
            if room.amenity.queen_sized_beds:
                room_details_map[room.room_type.type]['queen_sized_beds']['rooms'].append(room.room_number)
            if room.amenity.single_beds:
                room_details_map[room.room_type.type]['single_beds']['rooms'].append(room.room_number)
            if room.amenity.wardrobe:
                room_details_map[room.room_type.type]['wardrobe']['rooms'].append(room.room_number)
            if room.amenity.locker_available:
                room_details_map[room.room_type.type]['locker_available']['rooms'].append(room.room_number)
            if room.amenity.microwave:
                room_details_map[room.room_type.type]['microwave']['rooms'].append(room.room_number)
            if room.amenity.luggage_shelf:
                room_details_map[room.room_type.type]['luggage_shelf']['rooms'].append(room.room_number)
            if room.amenity.study_table_chair:
                room_details_map[room.room_type.type]['study_table_chair']['rooms'].append(room.room_number)
            if room.amenity.sofa_chair:
                room_details_map[room.room_type.type]['sofa_chair']['rooms'].append(room.room_number)
            if room.amenity.coffee_table:
                room_details_map[room.room_type.type]['coffee_table']['rooms'].append(room.room_number)
            if room.amenity.other_furniture:
                room_details_map[room.room_type.type]['other_furniture']['rooms'].append(room.room_number)
            if room.amenity.smoking_room:
                room_details_map[room.room_type.type]['smoking_room']['rooms'].append(room.room_number)
            if room.amenity.bath_tub:
                room_details_map[room.room_type.type]['bath_tub']['rooms'].append(room.room_number)
            if room.amenity.shower_curtain:
                room_details_map[room.room_type.type]['shower_curtain']['rooms'].append(room.room_number)
            if room.amenity.smoke_alarm:
                room_details_map[room.room_type.type]['smoke_alarm']['rooms'].append(room.room_number)
            if room.amenity.shower_cabinets:
                room_details_map[room.room_type.type]['shower_cabinets']['rooms'].append(room.room_number)
            if room.amenity.living_room:
                room_details_map[room.room_type.type]['living_room']['rooms'].append(room.room_number)
            if room.amenity.dining_table:
                room_details_map[room.room_type.type]['dining_table']['rooms'].append(room.room_number)
            if room.amenity.windows:
                room_details_map[room.room_type.type]['windows']['rooms'].append(room.room_number)
            if room.amenity.treebo_toiletries:
                room_details_map[room.room_type.type]['treebo_toiletries']['rooms'].append(room.room_number)
            if room.amenity.heater:
                room_details_map[room.room_type.type]['heater']['rooms'].append(room.room_number)
                room_details_map[room.room_type.type]['heater'][
                    'on_request'] = room.amenity.heater.availability == model_choices.RoomAmenityChoices.ON_REQUEST
            if room.amenity.intercom:
                room_details_map[room.room_type.type]['intercom']['rooms'].append(room.room_number)
            if room.amenity.hot_water:
                room_details_map[room.room_type.type]['hot_water']['rooms'].append(room.room_number)
            if room.amenity.tv:
                room_details_map[room.room_type.type]['tv']['rooms'].append(room.room_number)
            if room.amenity.ac:
                room_details_map[room.room_type.type]['ac']['rooms'].append(room.room_number)
            if room.amenity.stove:
                room_details_map[room.room_type.type]['stove']['rooms'].append(room.room_number)
                room_details_map[room.room_type.type]['stove'][
                    'on_request'] = room.amenity.stove.availability == model_choices.StoveChoices.STOVE_ON_REQUEST
            if room.amenity.twin_bed and room.amenity.twin_bed.joinable:
                room_details_map[room.room_type.type]['joinable_twin_beds']['rooms'].append(room.room_number)
            if room.amenity.twin_bed and not room.amenity.twin_bed.joinable:
                room_details_map[room.room_type.type]['non_joinable_twin_beds']['rooms'].append(room.room_number)
            if room.amenity.bucket_mug:
                room_details_map[room.room_type.type]['bucket_mug']['rooms'].append(room.room_number)
                room_details_map[room.room_type.type]['bucket_mug'][
                    'on_request'] = room.amenity.bucket_mug == model_choices.RoomAmenityChoices.ON_REQUEST
            if room.amenity.fan_type:
                room_details_map[room.room_type.type]['fan']['rooms'].append(room.room_number)
            if room.amenity.lock_type:
                room_details_map[room.room_type.type]['room_lock']['rooms'].append(room.room_number)
            if room.amenity.mosquito_repellent:
                room_details_map[room.room_type.type]['mosquito_repellent']['rooms'].append(room.room_number)
                room_details_map[room.room_type.type]['mosquito_repellent'][
                    'on_request'] = room.amenity.mosquito_repellent == model_choices.RoomAmenityChoices.ON_REQUEST

    for config in property_object.room_type_configurations:
        if config.room_type.type in room_details_map:
            room_details_map[config.room_type.type]['config'] = config

    return room_details_map
