from flask import Blueprint, request, jsonify

from cataloging_service.domain import service_provider

bp = Blueprint('rate_plan_apis', __name__)

rate_plan_service = service_provider.rate_plan_service


@bp.route('/v1/properties/<string:property_id>/rate-plans', methods=['GET'])
def get_rate_plans_for_property(property_id):
    """
    ---
    get:
        tags:
            - RatePlan
        description: "Get rate plans of given property_id"

        parameters:
            - name: property_id
              in: query
              description: Property Id to fetch rate plan for
              required: True
              schema:
                  type: string
        responses:
            200:
                description: Rate Plan of the property
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                rate_plan:
                                    type: string
    """
    if not property_id:
        return "property id missing in request", 400

    rate_plans = rate_plan_service.get_rate_plans_for_property(property_id=property_id)
    return jsonify({'rate_plans': rate_plans}), 200




@bp.route('/v1/rate-plans', methods=['GET'])
def get_rate_plans():
    """
    ---
    get:
        tags:
            - RatePlan
        description: "Get all rate plans addon mapping"
        responses:
            200:
                description: Rate Plan addon mapping
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                rate_plan:
                                    type: string
    """
    rate_plans = rate_plan_service.get_rate_plans()
    return jsonify({'rate_plans': rate_plans}), 200