from flask import Blueprint, jsonify

from cataloging_service.api.validators import OtaConfirmationValidator, OtaMappingConfirmationValidator
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import raw_json, log_request

import logging

bp = Blueprint('ota_apis', __name__)

logger = logging.getLogger(__name__)

ota_service = service_provider.ota_service


# rcs call-back
@bp.route('/api/v1/ota-mapping-created', methods=['PUT', 'POST'])
@log_request
@raw_json(OtaMappingConfirmationValidator)
def ota_mapping_created(parsed_request):
    # TODO: 93.00 calls
    logger.info('Received ITS OTA Mapping Callback for %s - %s', parsed_request['hotel_code'],
                parsed_request['ota_code'])
    ota_property = ota_service.get_ota_property(parsed_request['hotel_code'], parsed_request['ota_code'])
    ota_service.process_its_ota_mappings_push(ota_property)

    return jsonify(dict(status='SUCCESS')), 200


# ota created callback from RCS
@bp.route('/api/v1/ota-created', methods=['PUT', 'POST'])
@log_request
@raw_json(OtaConfirmationValidator)
def ota_created(parsed_request):
    # TODO: 0 calls
    logger.info('Received RCS OTA Callback for %s', parsed_request['ota_code'])
    ota_object = ota_service.get_ota(parsed_request['ota_code'])
    # Pushes to CM, not to other services
    ota_service.process_rcs_ota_push(ota_object)

    return jsonify(dict(status='SUCCESS')), 200
