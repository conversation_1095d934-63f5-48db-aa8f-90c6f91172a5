import os
from flask import Blueprint, logging

from cataloging_service.domain import service_provider
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.messaging import messaging_provider

import logging

bp = Blueprint('health_check_apis', __name__)

logger = logging.getLogger(__name__)

message_publisher = messaging_provider.get_rmq_message_publisher()
meta_service = service_provider.meta_service


@bp.route('/ping', methods=['GET'])
def ping():
    logger.info("Inside server")
    message_publisher.test_connection()
    meta_service.test_db_connection()
    return 'pong'


@bp.route('/app-version', methods=['GET'])
def app_version():
    return str(os.environ.get('APP_VERSION', ''))


@bp.route('/ping/<username>', methods=['GET'])
def ping_user(username):
    logger.info("Inside server" + username)
    return 'pong:' + username


@bp.route('/ping/<username>', methods=['DELETE'])
def purge_user_cache(username):
    logger.info("Now deleting user cache for " + username)
    cache.delete_memoized(ping_user, username)
    return 'OK'
