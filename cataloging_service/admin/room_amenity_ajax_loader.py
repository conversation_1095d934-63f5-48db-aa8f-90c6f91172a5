from flask_admin.contrib.sqla.ajax import QueryAjaxModelLoader
from flask_admin.model.ajax import DEFAULT_PAGE_SIZE
from sqlalchemy import or_
from sqlalchemy.orm import joinedload

from cataloging_service.models import Room, Property, RoomAmenity


class RoomAmenityQueryAjaxModelLoader(QueryAjaxModelLoader):

    def get_list(self, term, offset=0, limit=DEFAULT_PAGE_SIZE):
        assert self.model is RoomAmenity

        query = RoomAmenity.query.options(joinedload(RoomAmenity.room))
        property_ids = self.session.query(Property.id).filter(or_(Property.name.ilike('%{t}%'.format(t=term)),
                                                                  Property.id.ilike('%{t}%'.format(t=term))))
        room_ids = self.session.query(Room.id).filter(Room.property_id.in_(property_ids))
        query = query.filter(RoomAmenity.room_id.in_(room_ids))
        if self.order_by:
            query = query.order_by(self.order_by)

        return query.offset(offset).limit(limit).all()

    def format(self, model):
        if not model:
            return None

        return getattr(model, self.pk), str(model.room)
