from marshmallow import Schema, fields


class Policy(Schema):
    id = fields.Integer(required=True, allow_none=False)
    policy_type = fields.Str(required=True, allow_none=False)
    title = fields.Str(required=True, allow_none=False)
    description = fields.Str(required=True)
    display_in_need_to_know = fields.Boolean(required=True)
    display_in_policy = fields.Boolean(required=True)
    global_policy = fields.Boolean(required=True)

