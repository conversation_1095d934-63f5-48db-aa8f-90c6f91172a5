# ERROR CODES
ERROR_CODES = [400, 403, 409, 404]

# SUCCESS CODES
SUCCESS_CODES = [200, 201]

# Nullable fields test cases
NULL_FIELD_TEST_CASES = ['ParentEntity_03.1', 'ParentEntity_06.1', 'ParentEntity_09.1', 'ParentEntity_11.1',
                         'ParentEntity_13.1', 'ParentEntity_22.1', 'ParentEntity_24.1', 'ParentEntity_27.1',
                         'ParentEntity_29.1', 'ParentEntity_30.1', 'ParentEntity_30.2', 'ParentEntity_42.1',
                         'ParentEntity_70', 'ParentEntity_71', 'ParentEntity_72', 'ParentEntity_73', 'ParentEntity_74',
                         'ParentEntity_77', 'ParentEntity_33.1', 'UpdateParentEntity_102', 'UpdateParentEntity_103',
                         'UpdateParentEntity_104', 'UpdateParentEntity_105', 'UpdateParentEntity_106',
                         'UpdateParentEntity_107', 'UpdateParentEntity_108', 'UpdateParentEntity_109',
                         'UpdateParentEntity_110', 'UpdateParentEntity_111', 'UpdateParentEntity_112',
                         'UpdateParentEntity_114', 'UpdateParentEntity_117', 'UpdateParentEntity_121']

# Hotel id use for creation of parent entity
HOTEL_ID = ['001', '0016932', '000123', '0001234', '0001235', '0001231', '0001232']

# Seller Id use for the creation of any item
SELLER_ID = ['1','3']
