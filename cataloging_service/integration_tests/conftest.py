import os
import pytest
from cataloging_service.integration_tests.utilities import excel_utils

dir_path = os.path.dirname(os.path.abspath(__file__))

print("Module loaded: %s" % __name__)


@pytest.fixture(scope="session", autouse=True)
def excel_data():
    excel_utils.extract_excel_data(dir_path + '/resources/PosTestData.xlsx')


@pytest.fixture(scope="function", autouse=True)
def reporting():
    yield
    print("-------------TC ended ------------")
