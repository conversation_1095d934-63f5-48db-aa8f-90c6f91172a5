import json
import datetime as dt
from cataloging_service.integration_tests.utilities import excel_utils
from cataloging_service.integration_tests.utilities.common_utils import sanitize_test_data, del_none


class CreateComboBuilder(object):
    def __init__(self, sheet_name, test_case_id):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name

    def create_combo_request(self):
        combo_data_from_sheet = excel_utils.get_test_data(self.sheet_name, self.test_case_id)
        combo_items_from_combo_sheet = self.get_combo_items(combo_data_from_sheet[0]['combo_items'])
        current_datetime = dt.datetime.now()
        code = combo_data_from_sheet[0]['name'] + " " + str(current_datetime.microsecond)
        response_json = {
            'code': code if self.test_case_id not in ['combo_50'] else None,
            'combo_items': combo_items_from_combo_sheet,
            'contains_alcohol': combo_data_from_sheet[0]['contains_alcohol'],
            'cost': sanitize_test_data(combo_data_from_sheet[0]['cost']),
            'description': sanitize_test_data(combo_data_from_sheet[0]['description']),
            'display_name': sanitize_test_data(combo_data_from_sheet[0]['display_name']),
            'name': sanitize_test_data(combo_data_from_sheet[0]['name']),
            'pre_tax_price': sanitize_test_data(combo_data_from_sheet[0]['pre_tax_price']),
            'prep_time': sanitize_test_data(combo_data_from_sheet[0]['prep_time']),
            'sku_category_code': sanitize_test_data(combo_data_from_sheet[0]['sku_category_code']),
            'allergen_info': sanitize_test_data(combo_data_from_sheet[0]['allergen_info']),
            'calorie_info': sanitize_test_data(combo_data_from_sheet[0]['calorie_info']),
            'image': sanitize_test_data(combo_data_from_sheet[0]['image']),
            'sold_out': sanitize_test_data(combo_data_from_sheet[0]['sold_out']),
        }
        return json.dumps(del_none(response_json))

    @staticmethod
    def get_combo_items(combo_items):
        if sanitize_test_data(combo_items) and combo_items != 'NULL':
            return json.loads(combo_items)
        elif combo_items == 'Empty Array' or combo_items == 'NULL':
            return sanitize_test_data(combo_items)
        else:
            return None
