import json
from cataloging_service.integration_tests.utilities import excel_utils
from cataloging_service.integration_tests.utilities.common_utils import sanitize_test_data, del_none


class CreateAreaBuilder(object):
    def __init__(self, sheet_name, test_case_id):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name

    def create_area_request(self):
        area_data_from_sheet = excel_utils.get_test_data(self.sheet_name, self.test_case_id)
        response_json = {
            'name': sanitize_test_data(area_data_from_sheet[0]['name']),
            'tables': sanitize_test_data(json.loads(area_data_from_sheet[0]['tables'])),
        }
        return json.dumps(del_none(response_json))
