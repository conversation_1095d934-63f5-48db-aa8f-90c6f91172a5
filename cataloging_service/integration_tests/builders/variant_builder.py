import json
from cataloging_service.integration_tests.utilities import excel_utils
from cataloging_service.integration_tests.utilities.common_utils import sanitize_test_data, del_none


class CreateVariantBuilder(object):
    def __init__(self, sheet_name, test_case_id):
        self.test_case_id = test_case_id
        self.sheet_name = sheet_name

    def create_variant_request(self):
        variant_data_from_sheet = self.get_test_data(self.sheet_name, self.test_case_id)
        variants_from_variant_sheet = self.get_variants(variant_data_from_sheet[0]['variants'])
        response_json = {
            'can_select_multiple': sanitize_test_data(variant_data_from_sheet[0]['can_select_multiple']),
            'can_select_quantity': sanitize_test_data(variant_data_from_sheet[0]['can_select_quantity']),
            'description': sanitize_test_data(variant_data_from_sheet[0]['description']),
            'display_name': sanitize_test_data(variant_data_from_sheet[0]['display_name']),
            'is_customisation': sanitize_test_data(variant_data_from_sheet[0]['is_customization']),
            'maximum_selectable_quantity': (sanitize_test_data(
                variant_data_from_sheet[0]['maximum_selectable_quantity'])),
            'minimum_selectable_quantity': (sanitize_test_data(
                variant_data_from_sheet[0]['minimum_selectable_quantity'])),
            'name': sanitize_test_data(variant_data_from_sheet[0]['name']),
            'variants': variants_from_variant_sheet,
        }
        return json.dumps(del_none(response_json))

    def update_variant_request(self):
        update_variant_test_data = self.get_test_data(self.sheet_name, self.test_case_id)[0]
        variants = self.get_variants(update_variant_test_data['variants'])
        update_variant_request = {
            "variants": variants,
        }
        return json.dumps(del_none(update_variant_request))

    @staticmethod
    def get_test_data(sheet_name, test_case_id):
        return excel_utils.get_test_case_data(sheet_name, test_case_id)

    @staticmethod
    def get_variants(variants):
        if sanitize_test_data(variants) and variants != 'NULL':
            return json.loads(variants)
        elif variants == 'Empty Array' or variants == 'NULL':
            return sanitize_test_data(variants)
        else:
            return None
