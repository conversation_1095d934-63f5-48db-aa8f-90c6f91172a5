import json, copy, ast
from cataloging_service.integration_tests.config import sheet_names
from cataloging_service.integration_tests.requests.area_request import AreaRequest
from cataloging_service.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from cataloging_service.integration_tests.utilities.excel_utils import get_test_case_data
from cataloging_service.integration_tests.tests.base_validation import BaseValidation


class AreaValidation(BaseValidation):
    def __init__(self, client, test_case_id, response, area_id, seller_id, area_request, patch_api_called,
                 previous_actions=None):
        self.client = client
        self.response = response
        self.area_id = area_id
        self.seller_id = seller_id
        self.area_request = area_request
        self.patch_api_called = patch_api_called
        self.previous_actions = previous_actions
        self.test_case_id = test_case_id
        self.AreaRequest = AreaRequest()

    def validate_response(self, test_case_id):
        area_test_data = get_test_case_data(sheet_names.AREA_SHEET_NAME, test_case_id)[0]
        self.validate_area(self.response, area_test_data)

    def validate_update_response(self, test_case_id):
        area_test_data = get_test_case_data(sheet_names.AREA_SHEET_NAME, test_case_id)[0]
        self.validate_update_area(self.response, area_test_data)

    def validate_area(self, response, area_test_data):
        assert_(response['name'], sanitize_test_data(area_test_data['name']))
        assert response['area_id'] is not None
        assert response['seller_id'] is not None
        assert_(response['tables'][0]['area_id'], sanitize_test_data(response['area_id']))
        assert_(str(response['tables'][0]['number_of_seats']),
                str(sanitize_test_data(json.loads(area_test_data['tables'])[0]['seats_count'])))
        assert response['tables'][0]['number_of_seats'] is not None

    def validate_update_area(self, response, area_test_data):
        assert_(response['name'], sanitize_test_data(area_test_data['name']))
        assert response['area_id'] is not None
        assert response['seller_id'] is not None

    def validate_area_exist_in_get_areas_response(self, client_, seller_id, status_code):
        get_areas_response = self.AreaRequest.get_areas_request(client_, seller_id, status_code)
        assert any(area_id['area_id'] == self.area_request.area_id for area_id in get_areas_response)

    def validate_get_area_response(self, client_, seller_id, expected_area_id, status_code, test_case_id):
        expected_data = get_test_case_data(sheet_names.AREA_SHEET_NAME, test_case_id)[0]
        get_areas_response = self.AreaRequest.get_area_request(client_, seller_id, expected_area_id, status_code)
        self.validate_area(get_areas_response, expected_data)

    def validate_get_update_area_response(self, client_, seller_id, expected_area_id, status_code, test_case_id):
        expected_data = get_test_case_data(sheet_names.AREA_SHEET_NAME, test_case_id)[0]
        get_areas_response = self.AreaRequest.get_area_request(client_, seller_id, expected_area_id, status_code)
        self.validate_update_area(get_areas_response, expected_data)
