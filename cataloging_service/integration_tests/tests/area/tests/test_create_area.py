import pytest
from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.area.validation.area_validation import AreaValidation
from cataloging_service.integration_tests.tests.base_test import BaseTest


class TestCreateArea(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id, previous_test_cases",
        [

            # --------------------- Test cases Related to area name-------------------#

            ("area_01", "create an area", 201, False, None, None, None, SELLER_ID[0], None),
            ("area_02", "create an area with multiple tables", 201, False, None, None, None, SELLER_ID[0], None),
            ("area_03", "Create an area with area as NULL", 400, False, None, "Invalid request data", None,
             SELLER_ID[0], None),
            ("area_04", "Create an area with area as EMPTY", 201, False, None, None, None, SELLER_ID[0], None),
            ("area_05", "Create an area with area as having special character", 201, False, None, None, None,
             SELLER_ID[0], None),

            # --------------------------------Test cases Related to tables---------------------------------#

            ("area_06", "Create an area with tables", 201, False, None, None, None, SELLER_ID[0], None),
            ("area_07", "create an area with all fields", 201, False, None, None, None, SELLER_ID[0], None),
            ("area_08", "Create an Area having two table", 201, False, None, None, None, SELLER_ID[0], None),

            # --------------------- Test cases Related to number of tables-------------------------------#

            ("area_09", "Create an Area with number of tables having special char", 400, False, None,
             "Invalid request data", None, SELLER_ID[0], None),
            ("area_10", "Create an Area with number of tables as EMPTY", 400, False, None, "Invalid request data",
             None, SELLER_ID[0], None),

            # --------------------- Test cases Related to seat count-----------------------------------------#

            ("area_11", "Create an Area with seat_count as EMPTY", 400, False, None, "Invalid request data", None,
             SELLER_ID[0], None),
            ("area_12", "Create an Area with seat_count having special character", 400, False, None,
             "Invalid request data", None, SELLER_ID[0], None),

        ])
    @pytest.mark.regression
    def test_create_area(self, client, test_case_id, tc_description, status_code,
                         skip_case, skip_message, error_message, existing_error_message_test_id, seller_id,
                         previous_test_cases):
        if skip_case:
            pytest.skip(skip_message)

        if previous_test_cases:
            self.common_request_caller(client, previous_test_cases, seller_id)
        response = self.area_request.create_area_request(client, test_case_id, status_code, seller_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            area_id = self.area_request.area_id
            self.validation(client, test_case_id, response, area_id, seller_id, self.area_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, area_id, seller_id, area_request):
        validation = AreaValidation(client, test_case_id, response, area_id, seller_id, area_request,
                                    False)
        validation.validate_response(test_case_id)
        validation.validate_get_area_response(client, seller_id, area_id, 200, test_case_id)
        validation.validate_area_exist_in_get_areas_response(client, seller_id, 200)
