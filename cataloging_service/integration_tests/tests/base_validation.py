import json
import ast
from cataloging_service.integration_tests.utilities.common_utils import assert_, sanitize_test_data


class BaseValidation(object):

    def validate_item(self, response_data, expected_data, seller_id=None):
        if expected_data['contains_alcohol'] in (0, 1):
            assert_(response_data['contains_alcohol'], expected_data['contains_alcohol'])
        else:
            assert response_data['contains_alcohol'] is False

        if expected_data['active'] in (0, 1):
            assert_(response_data['active'], expected_data['active'])
        else:
            assert response_data['active'] is True

        if expected_data['allergen_info'] and expected_data['allergen_info'] != 'NULL':
            assert_(response_data['allergen_info'], sanitize_test_data(expected_data['allergen_info']))
        else:
            assert response_data['allergen_info'] is None

        if expected_data['calorie_info'] and expected_data['calorie_info'] != 'NULL':
            assert_(response_data['calorie_info'], sanitize_test_data(expected_data['calorie_info']))
        else:
            assert response_data['calorie_info'] is None

        if expected_data['image'] and expected_data['image'] != 'NULL':
            assert_(response_data['image'], sanitize_test_data(expected_data['image']))
        else:
            assert response_data['image'] is None

        if expected_data['prep_time'] and expected_data['prep_time'] != 'NULL':
            assert_(response_data['prep_time'], sanitize_test_data(expected_data['prep_time']))
        else:
            assert response_data['prep_time'] is None

        if sanitize_test_data(expected_data['cost']) and expected_data['cost'] != 'NULL':
            assert_(str(response_data['cost']), str(sanitize_test_data(expected_data['cost'])))
        else:
            assert response_data['cost'] is None

        if (expected_data['description'] or expected_data['description'] == '') and \
                expected_data['description'] != 'NULL':
            assert_(response_data['description'], sanitize_test_data(expected_data['description']))

        if (expected_data['display_name'] or expected_data['display_name'] == '') and \
                expected_data['display_name'] != 'NULL':
            assert_(response_data['display_name'], sanitize_test_data(expected_data['display_name']))

        if expected_data['food_type'] in ('vegan', 'vegetarian', 'non_vegetarian'):
            assert_(response_data['food_type'], sanitize_test_data(expected_data['food_type']))
        else:
            assert response_data['food_type'] == "vegetarian"

        if expected_data['name']:
            assert_(response_data['name'], sanitize_test_data(expected_data['name']))

        if expected_data['pre_tax_price'] and expected_data['pre_tax_price'] != 'NULL':
            assert_(response_data['pre_tax_price'], str(expected_data['pre_tax_price']))
        else:
            assert response_data['pre_tax_price'] is None

        if expected_data['prep_time'] and expected_data['prep_time'] != 'NULL':
            assert_(response_data['prep_time'], sanitize_test_data(expected_data['prep_time']))
        else:
            assert response_data['prep_time'] is None

        if sanitize_test_data(expected_data['sku_category_code']):
            assert_(response_data['sku_category_code'], expected_data['sku_category_code'])

        if sanitize_test_data(expected_data['use_as_side']):
            assert_(response_data['use_as_side'], expected_data['use_as_side'])

        if seller_id:
            assert_(response_data['seller_id'], seller_id)

    @staticmethod
    def validate_item_variants(response_data, expected_data):

        for expected_item_variant_data_id, expected_item_variant_data in enumerate(json.loads(expected_data)):
            assert_(expected_item_variant_data['cost'], response_data[expected_item_variant_data_id]['cost'])
            assert_(expected_item_variant_data['name'], response_data[expected_item_variant_data_id]['name'])
            assert_(expected_item_variant_data['pre_tax_price'],
                    response_data[expected_item_variant_data_id]['pre_tax_price'])
            assert_(expected_item_variant_data['sku_category_code'],
                    response_data[expected_item_variant_data_id]['sku_category_code'])
            for variant_id, variant_value in enumerate(expected_item_variant_data['variants']):
                assert_(variant_value['variant_id'],
                        response_data[expected_item_variant_data_id]['variants'][variant_id]['variant']['variant_id'])
                assert_(variant_value['variant_name'],
                        response_data[expected_item_variant_data_id]['variants'][variant_id]['variant']['name'])

    def validate_variant(self, response_data, expected_data, variant_id=None):
        if sanitize_test_data(expected_data['can_select_multiple']):
            assert_(response_data['can_select_multiple'], expected_data['can_select_multiple'])
        else:
            assert response_data['can_select_multiple'] is False

        if sanitize_test_data(expected_data['can_select_quantity']):
            assert_(response_data['can_select_quantity'], expected_data['can_select_quantity'])
        else:
            assert response_data['can_select_quantity'] is False

        if (expected_data['display_name'] or expected_data['display_name'] == '') and \
                expected_data['display_name'] != 'NULL':
            assert_(response_data['display_name'], sanitize_test_data(expected_data['display_name']))

        if sanitize_test_data(expected_data['is_customization']):
            assert_(response_data['is_customisation'], expected_data['is_customization'])
        else:
            assert response_data['is_customisation'] is False

        if sanitize_test_data(expected_data['maximum_selectable_quantity']) and \
                expected_data['maximum_selectable_quantity'] != 'NULL':
            assert_(float(response_data['maximum_selectable_quantity']), (expected_data['maximum_selectable_quantity']))
        else:
            assert response_data['maximum_selectable_quantity'] is None

        if sanitize_test_data(expected_data['minimum_selectable_quantity']) and \
                expected_data['minimum_selectable_quantity'] != 'NULL':
            assert_(float(response_data['minimum_selectable_quantity']), (expected_data['minimum_selectable_quantity']))

        if expected_data['name']:
            assert_(response_data['name'], sanitize_test_data(expected_data['name']))

        if sanitize_test_data(expected_data['variant_group_id']):
            assert_(response_data['variant_group_id'], (expected_data['variant_group_id']))

        if sanitize_test_data(expected_data['expected_variants']):
            for id, value in enumerate(json.loads(expected_data['expected_variants'])):
                assert_(value['name'], response_data['variants'][id]['name'])
                assert_(value['display_order'], response_data['variants'][id]['display_order'])
                assert_(value['variant_group_id'], response_data['variants'][id]['variant_group_id'])
                assert_(value['variant_id'], response_data['variants'][id]['variant_id'])

    def validate_combo(self, response_data, expected_data):

        if expected_data['description'] and expected_data['description'] != 'NULL':
            assert_(response_data['description'], sanitize_test_data(expected_data['description']))
        else:
            assert response_data['description'] is None

        if expected_data['display_name'] and expected_data['display_name'] != 'NULL':
            assert_(response_data['display_name'], sanitize_test_data(expected_data['display_name']))
        else:
            assert response_data['display_name'] is None

        if sanitize_test_data(expected_data['contains_alcohol']):
            assert_(response_data['contains_alcohol'], expected_data['contains_alcohol'])
        else:
            assert response_data['contains_alcohol'] is False

        if sanitize_test_data(expected_data['cost']) and expected_data['cost'] != 'NULL':
            assert_(str(response_data['cost']), str(sanitize_test_data(expected_data['cost'])))
        else:
            assert response_data['cost'] is None

        if expected_data['active'] in (0, 1):
            assert_(response_data['active'], expected_data['active'])
        else:
            assert response_data['active'] is True

        if expected_data['allergen_info'] and expected_data['allergen_info'] != 'NULL':
            assert_(response_data['allergen_info'], sanitize_test_data(expected_data['allergen_info']))

        if expected_data['calorie_info'] and expected_data['calorie_info'] != 'NULL':
            assert_(response_data['calorie_info'], sanitize_test_data(expected_data['calorie_info']))

        if expected_data['image'] and expected_data['image'] != 'NULL':
            assert_(response_data['image'], sanitize_test_data(expected_data['image']))

        if expected_data['name']:
            assert_(response_data['name'], sanitize_test_data(expected_data['name']))

        if expected_data['pre_tax_price'] and expected_data['pre_tax_price'] != 'NULL':
            assert_(response_data['pre_tax_price'], str(expected_data['pre_tax_price']))
        else:
            assert response_data['pre_tax_price'] is None

        if expected_data['prep_time'] and expected_data['prep_time'] != '0:10':
            assert_(response_data['prep_time'], sanitize_test_data(expected_data['prep_time']))
        else:
            assert response_data['prep_time'] == "0:10:00"

        if sanitize_test_data(expected_data['sku_category_code']):
            assert_(response_data['sku_category_code'], expected_data['sku_category_code'])

        if sanitize_test_data(expected_data['expected_items']):
            for id, value in enumerate(json.loads(expected_data['expected_items'])):
                assert_(value['cost'], response_data['combo_items'][id]['item']['cost'])
                assert_(value['description'], response_data['combo_items'][id]['item']['description'])
                assert_(value['display_name'], response_data['combo_items'][id]['item']['display_name'])
                assert_(value['item_id'], response_data['combo_items'][id]['item']['item_id'])
                assert_(value['name'], response_data['combo_items'][id]['item']['name'])
                assert_(value['pre_tax_price'], response_data['combo_items'][id]['item']['pre_tax_price'])
                assert_(value['prep_time'], response_data['combo_items'][id]['item']['prep_time'])
                assert_(value['print_name'], response_data['combo_items'][id]['item']['print_name'])

    def validate_menu(self, response_data, expected_data):
        if expected_data['description'] and expected_data['description'] != 'NULL':
            assert_(response_data['description'], sanitize_test_data(expected_data['description']))
        else:
            assert response_data['description'] is None

        if expected_data['display_name'] and expected_data['display_name'] != 'NULL':
            assert_(response_data['display_name'], sanitize_test_data(expected_data['display_name']))
        else:
            assert response_data['display_name'] is None

        if sanitize_test_data(expected_data['menu_timings']):
            sorted_menu_timings = sorted(response_data['menu_timings'], key=lambda i: i['menu_timing_id'])
            for index, menu_timing_value in enumerate(ast.literal_eval(expected_data['menu_timings'])):
                assert_(menu_timing_value['start_time'], sorted_menu_timings[index]['start_time'])
                assert_(menu_timing_value['end_time'], sorted_menu_timings[index]['end_time'])
                assert_(menu_timing_value['days'], sorted_menu_timings[index]['days'])
                assert sorted_menu_timings[index]['menu_timing_id'] is not None
        else:
            assert len(response_data['menu_timings']) is 0

        if expected_data['name'] and expected_data['name'] != 'NULL':
            assert_(response_data['name'], sanitize_test_data(expected_data['name']))
        else:
            assert response_data['name'] is None

        if sanitize_test_data(expected_data['menu_types']):
            assert_(str(response_data['menu_types']), expected_data['menu_types'])
