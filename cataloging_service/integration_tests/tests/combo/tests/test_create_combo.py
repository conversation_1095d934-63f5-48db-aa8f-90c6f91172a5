import pytest
from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.before_test_actions import *
from cataloging_service.integration_tests.tests.combo.validation.combo_validation import ComboValidation
from cataloging_service.integration_tests.resources.db_queries import *
from cataloging_service.integration_tests.utilities.common_utils import *


class TestCreateCombo(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id, previous_test_cases",
        [

            # --------------------- Test cases Related to combo_items-------------------#
            ("combo_01", "create variant with all the fields ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_02", "create a combo with combo items as NULL", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_03", "create a combo with not sending combo_items in the request", 201, False,
             None, None, None, SELLER_ID[0], None),
            ("combo_04", "create a combo with combo_items as EMPTY", 201, False,
             None, None, None, SELLER_ID[0], None),
            ("combo_05", "create a combo with combo_items having some special characters", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),

            # -------------------Test cases Related to contains_alcohol------------------#

            ("combo_06", "create a combo with contains_alcohol as NULL", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_07", "create a combo with contains_alcohol as EMPTY", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_08", "create a combo with not sending contains_alcohol in the request", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_09", "create a combo with contains_alcohol as FALSE", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),

            # -------------------Test cases related to cost -------------------#

            ("combo_11", "create a combo with cost as NULL", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_12", "create a combo with cost as BLANK", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_13", "create a combo with not sending in the request", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_14", "create a combo with cost having some special characters", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),

            # -------------------Test cases related to Description --------------#

            ("combo_15", "create a combo with description as NULL", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_16", "create a combo with description as BLANK", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_17", "create a combo with not sending description in the request", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_18", "create a combo with description having special characters", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),

            # ------------------Test cases related to display name ---------------#

            ("combo_19", "create a combo with display_name as NULL", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_20", "create a combo with display_name as BLANK", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_04),
            ("combo_21", "create a combo with not sending display_name in the request", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_22", "create a combo with name having special characters ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_23", "create a combo with display_name ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),

            # ---------------Test cases related to name ------------------------#
            ("combo_24", "create a combo with name", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_25", "create a combo with name as NULL", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_26", "create a combo with name as BLANK", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_27", "create a combo with name as EMPTY", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_28", "create a combo with not sending name in the request ", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_29", "create a combo with name having special characters", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),

            # -------------Test Cases Related to Pre_tax_price----------#

            ("combo_30", "create a combo with pre_tax_price", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_31", "create a combo with pre_tax_price as NULL", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_32", "create a combo with pre_tax_price as BLANK", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_33", "create a combo with pre_tax_price having special characters", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_34", "create a combo without sending pre_tax_price in the request", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),

            # -----------Test Cases Related to prep - time ----------------------#

            ("combo_35", "create a combo with prep_time ", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_36", "create a combo with prep_time as NULL", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_37", "create a combo with prep_time as BLANK", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_38", "create a combo with prep_time having Special characters ", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),
            ("combo_39", "create a combo with not sending prep_time in the request ", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], None),

            # ------------Test cases related to is_available ------------------#

            ("combo_40", "create a combo with is_available as TRUE ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_41", "create a combo with is_available as False ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_42", "create a combo with is_available as NULL ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_43", "create a combo with is_available as EMPTY ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),
            ("combo_44", "create a combo with not sending is_available in the request ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_01),

            ("combo_45", "create a combo with item having variant ", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_02),
            ("combo_46", "create a combo with item having more than one side item ", 201, False,
             None, None, None, SELLER_ID[0], Create_Item_43),
            ("combo_48", "create a combo with Item having customization in variant", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_03),
            ("combo_49", "create a combo with Item having customization in variant", 201, False,
             None, None, None, SELLER_ID[0], Create_combo_05),
            ("combo_50", "create a combo without combo code", 400, False,
             None, "Invalid request data", None, SELLER_ID[0], Create_combo_01),

        ])
    @pytest.mark.regression
    def test_create_combo(self, client, test_case_id, tc_description, status_code,
                          skip_case, skip_message, error_message, existing_error_message_test_id, seller_id,
                          previous_test_cases):
        if skip_case:
            pytest.skip(skip_message)

        query_execute(DELETE_VARIANT)
        query_execute(DELETE_ITEM)
        query_execute(DELETE_VARIANT_GROUP)
        query_execute(DELETE_SIDE_ITEM)
        query_execute(DELETE_COMBO)

        if previous_test_cases:
            self.common_request_caller(client, previous_test_cases, seller_id)
        response = self.combo_request.create_combo_request(client, test_case_id, status_code, seller_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            combo_id = self.combo_request.combo_id
            self.validation(client, test_case_id, response, combo_id, seller_id, self.combo_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, combo_id, seller_id, combo_request):
        validation = ComboValidation(client, test_case_id, response, combo_id, seller_id, combo_request,
                                     False)
        validation.validate_response()
        validation.validate_combo_response()
        validation.validate_combo_exist_in_get_combos_response()
        validation.validate_combo_using_name_display_name()
