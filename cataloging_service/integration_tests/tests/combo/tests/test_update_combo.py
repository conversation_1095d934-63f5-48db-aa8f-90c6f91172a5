import pytest
from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.combo.validation.combo_validation import ComboValidation
from cataloging_service.integration_tests.tests.before_test_actions import *
from cataloging_service.integration_tests.resources.db_queries import *
from cataloging_service.integration_tests.utilities.common_utils import *


class TestUpdateCombo(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id",
        [

            # -----------------Test cases related to contains_alcohol--------------------------#

            ("Patch_combo_01", Patch_Combo_01,
             "Update contains_alcohol of combo_01 to FALSE", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_02", Patch_Combo_01,
             "Update cost of combo_01 ", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_03", Patch_Combo_01,
             "Updated description of combo_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_04", Patch_Combo_01,
             "Update display name of combo_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_05", Patch_Combo_01,
             "Updated name of combo_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_06", Patch_Combo_01,
             "Update pre tax price of combo_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_07", Patch_Combo_01,
             "updated prep_time of combo_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_08", Patch_Combo_01,
             "update sku_category_code of combo_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_combo_09", Patch_Combo_01,
             "update combo_items of combo_01", 200, False, None, None, None, SELLER_ID[0]),

        ])
    @pytest.mark.regression
    def test_update_combo(self, client, test_case_id, previous_actions, tc_description, status_code,
                          skip_case, skip_message, error_message, existing_error_message_test_id, seller_id):
        if skip_case:
            pytest.skip(skip_message)

        query_execute(DELETE_ITEM)
        query_execute(DELETE_COMBO)

        if previous_actions:
            self.common_request_caller(client, previous_actions, seller_id)

        combo_id = self.combo_request.combo_id
        response = self.combo_request.update_combo_request(client, test_case_id, seller_id, status_code, combo_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(client, test_case_id, response, combo_id, seller_id, self.combo_request,
                            previous_actions)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, item_id, seller_id, item_request, previous_actions):
        validation = ComboValidation(client, test_case_id, response, item_id, seller_id, item_request, True,
                                     previous_actions)
        validation.validate_response()
        validation.validate_combo_response()
        validation.validate_combo_exist_in_get_combos_response()
        validation.validate_combo_using_name_display_name()
