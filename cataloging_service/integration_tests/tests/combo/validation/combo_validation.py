import copy, ast
import json
from cataloging_service.integration_tests.config import sheet_names
from cataloging_service.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from cataloging_service.integration_tests.utilities.excel_utils import get_test_case_data
from cataloging_service.integration_tests.tests.base_validation import BaseValidation


class ComboValidation(BaseValidation):

    def __init__(self, client, test_case_id, response, combo_id, seller_id, combo_request,
                 patch_api_called,
                 previous_actions=None):
        self.client = client
        self.response = response
        self.combo_id = combo_id
        self.seller_id = seller_id
        self.combo_request = combo_request
        self.patch_api_called = patch_api_called
        self.previous_actions = previous_actions
        self.test_case_id = test_case_id

    def validate_response(self):
        expected_data = self.get_test_data(patch_api_called=self.patch_api_called,
                                           previous_actions=self.previous_actions,
                                           test_case_id=self.test_case_id)
        self.validate_combo(self.response, expected_data)
        self.validate_variant_and_customization(expected_data)

    def validate_combo_response(self):
        expected_data = self.get_test_data(patch_api_called=self.patch_api_called,
                                           previous_actions=self.previous_actions,
                                           test_case_id=self.test_case_id)
        combo_response = self.combo_request.get_combo_request(self.client, self.seller_id, self.combo_id, 200)
        self.validate_combo(combo_response, expected_data)

    def validate_get_combo_response(self):
        expected_data = self.get_test_data(self.patch_api_called, self.previous_actions,
                                           test_case_id=self.test_case_id)
        get_combo_response = self.combo_request.get_combo_request(self.client, self.seller_id, self.combo_id, 200)
        self.validate_menu(get_combo_response, expected_data)
        if self.patch_api_called:
            assert_(expected_data['combo_id'], self.response['combo_id'])
            for index, value in enumerate(expected_data['combo_items']):
                assert_(value['cost'], get_combo_response['combo_items'][id]['item']['cost'])
                assert_(value['description'], get_combo_response['combo_items'][id]['item']['description'])
                assert_(value['display_name'], get_combo_response['combo_items'][id]['item']['display_name'])
                assert_(value['item_id'], get_combo_response['combo_items'][id]['item']['item_id'])
                assert_(value['name'], get_combo_response['combo_items'][id]['item']['name'])
                assert_(value['pre_tax_price'], get_combo_response['combo_items'][id]['item']['pre_tax_price'])
                assert_(value['prep_time'], get_combo_response['combo_items'][id]['item']['prep_time'])
                assert_(value['print_name'], get_combo_response['combo_items'][id]['item']['print_name'])

    def validate_combo_exist_in_get_combos_response(self):
        get_combos_response = self.combo_request.get_combos_request(self.client, self.seller_id, 200)
        assert any(combo_id['combo_id'] == self.combo_request.combo_id for combo_id in get_combos_response)

    def validate_combo_using_name_display_name(self):
        combo_name = self.response['name']
        get_combos_response_using_combo_name = self.combo_request.get_combos_request(self.client, self.seller_id, 200,
                                                                                     combo_name)
        assert any(
            combo_id['combo_id'] == self.combo_request.combo_id for combo_id in get_combos_response_using_combo_name)
        combo_display_name = self.response['display_name']
        get_combos_response_using_combo_display_name = self.combo_request.get_combos_request(self.client,
                                                                                             self.seller_id,
                                                                                             200, combo_display_name)
        assert any(combo_id['combo_id'] == self.combo_request.combo_id for combo_id in
                   get_combos_response_using_combo_display_name)

    def get_test_data(self, patch_api_called, previous_actions=None, test_case_id=None):
        combo_test_data = get_test_case_data(sheet_names.COMBO_SHEET_NAME, test_case_id)[0]
        if patch_api_called:
            if test_case_id in ('Patch_Item_08', 'Patch_Item_15', 'Patch_Item_16'):
                create_combo_test_data = get_test_case_data(sheet_names.COMBO_SHEET_NAME,
                                                            previous_actions[1]['id'])[0]
            else:
                for previous_action in previous_actions:
                    if previous_action['type'] == 'create_combo':
                        create_combo_test_data = \
                            get_test_case_data(sheet_names.COMBO_SHEET_NAME, previous_action['id'])[0]
            create_combo_json_copy = copy.deepcopy(create_combo_test_data)
            for key in combo_test_data:
                combo_test_data[key] = sanitize_test_data(combo_test_data[key])
                if combo_test_data[key] or combo_test_data[key] in (0, 1):
                    create_combo_json_copy[key] = combo_test_data[key]
            return create_combo_json_copy
        return combo_test_data

    def validate_variant_and_customization(self, expected_data):
        if sanitize_test_data(expected_data['item_variants']):
            get_item_variants_response = self.combo_request.get_item_variants(self.client, self.seller_id, 200,
                                                                              self.combo_id)
            if self.test_case_id == 'Patch_Item_17':
                assert len(get_item_variants_response) is 0
                return
            for item_variant_index, expected_item_variant_value in enumerate(
                    ast.literal_eval(expected_data['item_variants'])):
                assert_(expected_item_variant_value['cost'], get_item_variants_response[item_variant_index]['cost'])
                assert_(expected_item_variant_value['name'], get_item_variants_response[item_variant_index]['name'])
                assert_(expected_item_variant_value['pre_tax_price'],
                        get_item_variants_response[item_variant_index]['pre_tax_price'])
                assert_(expected_item_variant_value['sku_category_code'],
                        get_item_variants_response[item_variant_index]['sku_category_code'])
                for variant_index, variant_value in enumerate(expected_item_variant_value['variants']):
                    assert_(variant_value['variant_id'],
                            get_item_variants_response[item_variant_index]['variants'][variant_index]['variant_id'])
                if expected_item_variant_value.get('item_customisations'):
                    for variant_custom_index, value in enumerate(expected_item_variant_value['item_customisations']):
                        assert_(int(value['cost']),
                                get_item_variants_response[item_variant_index]['item_customisations'][
                                    variant_custom_index]['cost'])
                        assert_(int(value['delta_price']),
                                get_item_variants_response[item_variant_index]['item_customisations'][
                                    variant_custom_index]['delta_price'])
                        assert_(value['variant_name'],
                                get_item_variants_response[item_variant_index]['item_customisations'][
                                    variant_custom_index]['name'])
        if sanitize_test_data(expected_data['customised_item']):
            get_item_customisation_response = self.combo_request.get_item_customisation(self.client, self.seller_id,
                                                                                        200,
                                                                                        self.combo_id)
            for customisation_index, customisation_value in enumerate(
                    ast.literal_eval(expected_data['customised_item'])):
                assert_(int(customisation_value['cost']), get_item_customisation_response[customisation_index]['cost'])
                assert_(customisation_value['variant_name'],
                        get_item_customisation_response[customisation_index]['name'])
                assert_(int(customisation_value['delta_price']),
                        get_item_customisation_response[customisation_index]['delta_price'])
