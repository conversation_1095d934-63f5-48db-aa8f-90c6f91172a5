import json, copy, ast
from cataloging_service.integration_tests.config import sheet_names
from cataloging_service.integration_tests.utilities.common_utils import assert_, sanitize_test_data, del_none
from cataloging_service.integration_tests.utilities.excel_utils import get_test_case_data
from cataloging_service.integration_tests.tests.base_validation import BaseValidation


class ItemValidation(BaseValidation):

    def __init__(self, client, test_case_id, response, item_id, seller_id, item_request, patch_api_called,
                 previous_actions=None, seller_sku_request=None, sku_id=None):
        self.client = client
        self.response = response
        self.item_id = item_id
        self.seller_id = seller_id
        self.item_request = item_request
        self.test_case_id = test_case_id
        self.patch_api_called = patch_api_called
        self.previous_actions = previous_actions
        self.seller_sku_request = seller_sku_request
        self.sku_id = sku_id

    def validate_response(self):
        expected_data = self.get_expected_data(self.patch_api_called, self.previous_actions, self.test_case_id)
        self.validate_item(self.response, expected_data, self.seller_id)
        self.validate_side_item(expected_data, self.response)
        self.validate_variant_and_customization(expected_data)

    def validate_variant_and_customization(self, expected_data):
        if sanitize_test_data(expected_data['item_variants']):
            get_item_variants_response = self.item_request.get_item_variants(self.client, self.seller_id, 200,
                                                                             self.item_id)
            if self.test_case_id == 'Patch_Item_17':
                assert len(get_item_variants_response) is 0
                return
            for item_variant_index, expected_item_variant_value in enumerate(
                    ast.literal_eval(expected_data['item_variants'])):
                assert_(expected_item_variant_value['cost'], get_item_variants_response[item_variant_index]['cost'])
                assert_(expected_item_variant_value['name'], get_item_variants_response[item_variant_index]['name'])
                assert_(expected_item_variant_value['pre_tax_price'],
                        get_item_variants_response[item_variant_index]['pre_tax_price'])
                assert_(expected_item_variant_value['sku_category_code'],
                        get_item_variants_response[item_variant_index]['sku_category_code'])
                for variant_index, variant_value in enumerate(expected_item_variant_value['variants']):
                    assert_(variant_value['variant_id'],
                            get_item_variants_response[item_variant_index]['variants'][variant_index]['variant_id'])
                if expected_item_variant_value.get('item_customisations'):
                    for variant_custom_index, value in enumerate(expected_item_variant_value['item_customisations']):
                        assert_(int(value['cost']),
                                get_item_variants_response[item_variant_index]['item_customisations'][
                                    variant_custom_index]['cost'])
                        assert_(int(value['delta_price']),
                                get_item_variants_response[item_variant_index]['item_customisations'][
                                    variant_custom_index]['delta_price'])
                        assert_(value['variant_name'],
                                get_item_variants_response[item_variant_index]['item_customisations'][
                                    variant_custom_index]['name'])
        if sanitize_test_data(expected_data['customised_item']):
            get_item_customisation_response = self.item_request.get_item_customisation(self.client, self.seller_id, 200,
                                                                                       self.item_id)
            for customisation_index, customisation_value in enumerate(
                    ast.literal_eval(expected_data['customised_item'])):
                assert_(int(customisation_value['cost']), get_item_customisation_response[customisation_index]['cost'])
                assert_(customisation_value['variant_name'],
                        get_item_customisation_response[customisation_index]['name'])
                assert_(int(customisation_value['delta_price']),
                        get_item_customisation_response[customisation_index]['delta_price'])

    def validate_get_item_response(self):
        expected_data = self.get_expected_data(self.patch_api_called, self.previous_actions, self.test_case_id)
        get_item_response = self.item_request.get_item_request(self.client, self.seller_id, self.item_id, 200)
        self.validate_item(get_item_response, expected_data, self.seller_id)
        self.validate_side_item(expected_data, get_item_response)
        self.validate_variant_and_customization(expected_data)

    def validate_item_exist_in_get_items_response(self):
        get_items_response = self.item_request.get_items_request(self.client, self.seller_id, 200)
        assert any(item_id['item_id'] == self.item_request.item_id for item_id in get_items_response)

    def validate_item_using_name_display_name(self):
        item_name = self.response['name']
        get_items_response_using_item_name = self.item_request.get_items_request(self.client, self.seller_id, 200,
                                                                                 item_name)
        assert any(item_id['item_id'] == self.item_request.item_id for item_id in get_items_response_using_item_name)
        item_display_name = self.response['display_name']
        get_items_response_using_item_display_name = self.item_request.get_items_request(self.client, self.seller_id,
                                                                                         200, item_display_name)
        assert any(item_id['item_id'] == self.item_request.item_id for item_id in
                   get_items_response_using_item_display_name)

    def validate_side_item(self, expected_data, response_data):
        if sanitize_test_data(expected_data['side_items']):
            side_item_list = json.loads(expected_data['side_items'])
            for side_item_index, side_item in enumerate(side_item_list):
                item_id = side_item['item_id']
                item_test_id = side_item['test_case']
                side_item_expected_data = self.get_expected_data(previous_actions=self.previous_actions,
                                                                 test_case_id=item_test_id)
                for response_side_item_data in response_data['side_items']:
                    if response_side_item_data['item']['item_id'] == item_id:
                        self.validate_item(response_side_item_data['item'], side_item_expected_data)
                    break

    def get_expected_data(self, patch_api_called=False, previous_actions=None, test_case_id=None):
        item_test_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME, test_case_id)[0]
        if patch_api_called:
            if test_case_id in ('Patch_Item_08', 'Patch_Item_15', 'Patch_Item_16','Patch_Item_14'):
                create_item_test_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME, previous_actions[1]['id'])[0]
            elif test_case_id in ('Patch_Item_09', 'Patch_Item_18'):
                create_item_test_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME, previous_actions[2]['id'])[0]
            elif test_case_id == 'Patch_Item_13_1':
                create_item_test_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME, previous_actions[4]['id'])[0]
            else:
                for previous_action in previous_actions:
                    if previous_action['type'] == 'create_item':
                        create_item_test_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME, previous_action['id'])[
                            0]
            create_item_json_copy = copy.deepcopy(create_item_test_data)
            for key in item_test_data:
                item_test_data[key] = sanitize_test_data(item_test_data[key])
                if item_test_data[key] or item_test_data[key] in (0,1):
                    create_item_json_copy[key] = item_test_data[key]
            return create_item_json_copy
        return item_test_data
