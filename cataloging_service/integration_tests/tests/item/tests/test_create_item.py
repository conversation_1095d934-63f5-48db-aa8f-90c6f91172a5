import pytest

from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.item.validation.item_validation import ItemValidation
from cataloging_service.integration_tests.tests.before_test_actions import *
from cataloging_service.integration_tests.resources.db_queries import *
from cataloging_service.integration_tests.utilities.common_utils import *


class TestCreateItem(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id, previous_test_cases",
        [
            ("Item_01", "Create items without customised_item and item_variants and side_item", 201, False,
             None, None, None, SELLER_ID[0], None),
            # -----------------------Test Cases Related to contains_alcohol key -------------------------------------
            ("Item_05", "Create a item without boolean field contains_alcohol", 400, False, None,
             "Invalid request data", None, SELLER_ID[0], None),
            ("Item_06", "Create a item with boolean field contains_alcohol as True", 201, False, None, None, None,
             SELLER_ID[0], None),
            ("Item_07", "Create a item with boolean field contains_alcohol as False", 201, False, None, None, None,
             SELLER_ID[0], None),
            ("Item_08", "Create a item with boolean field contains_alcohol as Null", 400, False, None,
             "Invalid request data", None, SELLER_ID[0], None),
            # -----------------------Test Cases Related to cost key ---------------------------------------------
            ("Item_09", "Create a item without having cost key", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_10", "Create a item without having cost key", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_11", "Create a item with cost in float", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_11_1", "Create a item for a seller which currency different from INR", 201, True, None, None, None,
             SELLER_ID[1], None),
            # -----------------------Test Cases Related to description key ---------------------------------------------
            ("Item_12", "Create a item without having description key", 201, False, None, None, None, SELLER_ID[0],
             None),
            ("Item_13", "Create a item having description as None", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_14", "Create a item with Blank Description", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_15", "Create a item with Description containing Special Characters", 201, False, None, None, None,
             SELLER_ID[0], None),
            # -----------------------Test Cases Related to Display name key -----------------------------------------
            ("Item_16", "Create a item without having Display name", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_17", "Create a item having Display name as None", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_18", "Create a item with Blank Display name", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_19", "Create a item with Display Name containing Special Characters", 201, False, None, None, None,
             SELLER_ID[0], None),
            # -----------------------Test Cases Related to Name key -----------------------------------------
            ("Item_20", "Create a item without having Name", 400, False, None, "Invalid request data", None,
             SELLER_ID[0], None),
            ("Item_21", "Create a item having Name as None", 400, False, None, "Invalid request data", None,
             SELLER_ID[0], None),
            ("Item_22", "Create a item with Blank Name", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_23", "Create a item with Name containing Special Characters", 201, False, None, None, None,
             SELLER_ID[0], None),
            # -----------------------Test Cases Related to Pre Tax key -----------------------------------------
            ("Item_24", "Create a item without having Pre Tax Price", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_25", "Create a item having Pre Tax Price as None", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_26", "Create a Item with Pre Tax Price in Float", 201, False, None, None, None, SELLER_ID[0], None),
            ("Item_27", "Create a Item with Pre Tax Price greater than Cost", 201, False, None, None, None,
             SELLER_ID[0], None),
            # -----------------------Test Cases Related to Prep Time key -----------------------------------------
            ("Item_28", "Create a item without having Prep Time", 400, False, None, "Invalid request data", None,
             SELLER_ID[0], None),
            ("Item_29", "Create a item having Prep Time as None", 400, False, None, "Invalid request data", None,
             SELLER_ID[0], None),
            ("Item_30", "Create a Item with Prep Time as Blank", 400, False, None, "Invalid request data"
             , None, SELLER_ID[0], None),
            ("Item_31", "Create a Item with Prep Time in Different Format", 400, False, None, "Invalid request data"
             , None, SELLER_ID[0], None),
            # -----------------------Test Cases Related to Print key -----------------------------------------
            ("Item_32", "Create a item without having Print Name", 201, False, None, None, None,
             SELLER_ID[0], None),
            ("Item_33", "Create a item having Print Name as None", 201, False, None, None, None,
             SELLER_ID[0], None),
            ("Item_34", "Create a Item with Print Name as Blank", 201, False, None, None, None, SELLER_ID[0], None),
            # -----------------------Test Cases Related to Item Variants -----------------------------------------
            ("Item_36", "Create a item having item variants", 201, False, None, None, None, SELLER_ID[0],
             Create_Item_36),
            ("Item_37", "Create a Item having empty Item Variants", 201, False, None, None, None, SELLER_ID[0],
             Create_Item_36),
            ("Item_38", "Create a Item having Item Variants as Null", 400, False, None, "Invalid request data"
             , None, SELLER_ID[0], Create_Item_36),
            ("Item_45", "Create a Item Having More Then One Variants In One Json", 201, False, None, None, None,
             SELLER_ID[0], Create_Item_45),
            ("Item_46", "Create a Item having 2 Json with one variant in each ", 201, False, None, None, None,
             SELLER_ID[0], Create_Item_46),
            ("Item_46_1", "Create a Item having customization in variant", 201, False, None, None, None,
             SELLER_ID[0], Create_Item_46_1),
            ("Item_46_2", "Create a Item having more then 1 customization in variant", 201, False, None, None, None,
             SELLER_ID[0], Create_Item_46_1),
            # -----------------------Test Cases Related to Side Items -----------------------------------------
            ("Item_39", "Create a Item without having side Items Key", 201, False, None, None, None, SELLER_ID[0],
             None),
            ("Item_40", "Create a Item with side item as Empty Array", 201, False, None, None, None, SELLER_ID[0],
             None),
            ("Item_41", "Create a Item with null side Item", 400, False, None, "Invalid request data"
             , None, SELLER_ID[0], None),
            ("Item_42", "Create a Item with a side item", 201, False, None, None, None, SELLER_ID[0], Create_Item_01),
            ("Item_43", "Create a item having more then one side item", 201, False, None, None, None, SELLER_ID[0],
             Create_Item_43),
            ("Item_44", "Create a item having side item which won't exist in the DB ", 400, False, None,
             "Invalid request data", None, SELLER_ID[0], Create_Item_01),
            ("Item_47", "Create a item having both side item and item variants", 201, False, None, None, None,
             SELLER_ID[0], Create_Item_47),
            # -----------------------Test Cases Related to Item Customisation -----------------------------------------
            ("Item_48", "Create a item having Item Customization", 201, False, None, None, None, SELLER_ID[0],
             Create_Item_48),
            ("Item_49", "Create a item having more then one Customization", 201, False, None, None, None, SELLER_ID[0],
             Create_Item_48),

            # ----------------------------Test cases related to food_type ------------------------------#

            ("Item_51", "create an item with food_type as NULL", 400, False, None, "Invalid request data", None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_52", "create an item with food_type as EMPTY", 400, False, None, "Invalid request data", None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_53", "create an item with food_type having special characters", 400, False, None,
             "Invalid request data", None, SELLER_ID[0],
             Create_Item_48),
            # --------------------------- Test cases related to allergen_info -----------------------------#

            ("Item_54", "create an item with allergen_info as NULL", 201, False, None, None, None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_55", "create an item with allergen_info as EMPTY", 201, False, None, None, None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_56", "create an item with allergen_info having special characters", 201, False, None,
             None, None, SELLER_ID[0],
             Create_Item_48),

            # --------------------------- Test cases related to calorie_info -----------------------------#

            ("Item_57", "create an item with calorie_info as NULL", 201, False, None, None, None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_58", "create an item with calorie_info as EMPTY", 201, False, None, None, None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_59", "create an item with calorie_info having special characters", 201, False, None,
             None, None, SELLER_ID[0],
             Create_Item_48),

            # --------------------------- Test cases related to image -----------------------------#

            ("Item_60", "create an item with image as NULL", 201, False, None, None, None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_61", "create an item with image as EMPTY", 201, False, None, None, None,
             SELLER_ID[0],
             Create_Item_48),
            ("Item_62", "create an item with image having special characters", 201, False, None,
             None, None, SELLER_ID[0],
             Create_Item_48),
            ("Item_63", "create an item without item code", 400, False, None,
             "Invalid request data", None, SELLER_ID[0],
             Create_Item_48),

        ])
    @pytest.mark.regression
    def test_create_item(self, client, test_case_id, tc_description, status_code,
                         skip_case, skip_message, error_message, existing_error_message_test_id, seller_id,
                         previous_test_cases):
        if skip_case:
            pytest.skip(skip_message)
        query_execute(DELETE_ITEM_VARIANT_VARIANT_ASSOCIATION)
        query_execute(DELETE_VARIANT)
        query_execute(DELETE_VARIANT_GROUP)
        query_execute(DELETE_SIDE_ITEM)
        query_execute(DELETE_ITEM)
        self.variant_request.variant_id = []

        if previous_test_cases:
            self.common_request_caller(client, previous_test_cases, seller_id)

        response = self.item_request.create_item_request(client, test_case_id, status_code, seller_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            item_id = self.item_request.item_id
            sku_id = self.item_request.sku_id
            self.validation(client, test_case_id, response, item_id, seller_id, self.item_request,
                            self.seller_sku_request, sku_id)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, item_id, seller_id, item_request, seller_sku_request, sku_id):
        validation = ItemValidation(client, test_case_id, response, item_id, seller_id, item_request, False,
                                    seller_sku_request, sku_id)
        validation.validate_response()
        validation.validate_get_item_response()
        validation.validate_item_exist_in_get_items_response()
        validation.validate_item_using_name_display_name()
