import pytest
from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.item.validation.item_validation import ItemValidation
from cataloging_service.integration_tests.tests.before_test_actions import *
from cataloging_service.integration_tests.resources.db_queries import *
from cataloging_service.integration_tests.utilities.common_utils import *


class TestUpdateItem(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id",
        [
            # -----------------------Test Cases Related to Name key -------------------------------------
            ("Patch_Item_29", Patch_Item_03, "Update Name for a Item (Item_07)", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_30", [{"id": "Item_22", "type": "create_item"}], "Add a Name for a Item (Item_21)",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_31", [{"id": "Item_01", "type": "create_item"}], "Delete Name for a Item (Item_01)",
             400, False, None, "Invalid request data", 'Item_21', SELLER_ID[0]),
            ("Patch_Item_32", Patch_Item_32, "Update Name for a Item containing variant and side item(Item_47)",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_33", Patch_Item_03, "Add Item Name with special Characters in it(Item_07)",
             200, False, None, None, None, SELLER_ID[0]),
            # -----------------------Test Cases Related to contains_alcohol key -------------------------------------
            ("Patch_Item_03", Patch_Item_03, "Update Contains Alcohol for a Item (Item_07) to True", 200, False, None,
             None, None, SELLER_ID[0]),
            ("Patch_Item_04", Create_Item_01, "Update Contains Alcohol for a Item to False (Item_06)", 200, False, None,
             None, None, SELLER_ID[0]),
            ("Patch_Item_05", Patch_Item_05, "Update Contains Alcohol for a Item having Item Variants (Item_36)", 200,
             False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_06", Patch_Item_06,
             "Update Contains Alcohol for a Item having Multiple Json Item Variants(Item_46)", 200, False, None, None,
             None, SELLER_ID[0]),
            ("Patch_Item_07", Patch_Item_07,
             "Update Contains Alcohol for a Item having Side Item as well as Item Variants (Item_45)", 200, False, None,
             None,
             None, SELLER_ID[0]),
            # -----------------------Test Cases Related to side_items key -------------------------------------
            ("Patch_Item_08", Patch_Item_08, "Add a Side Item to a Item", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_09", Patch_Item_09, "Update Side Item for a Item which already contains one side item", 200,
             False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_10", Patch_Item_10, "Add two side Side Items for a Item", 200,
             False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_11", Patch_Item_11, "Add Two Side Items for a Item which already contains one side item", 200,
             False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_12", Patch_Item_12, "Delete all Side Items from a Item", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_13", Patch_Item_13, "Add a Side Item to a Item which contains side items and variants as well",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_13_1", Patch_Item_13_1, "Add a Side Item which contains side item and variant customization",
             400, False, None, "Invalid request data", None, SELLER_ID[0]),
            # -----------------------Test Cases Related to Item Variants key -------------------------------------
            ("Patch_Item_14", Patch_Item_14, "Add a variant to a Item", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_15", Patch_Item_15, "Add Multiple Variants to a Item", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_16", Patch_Item_16, "Update Variants for a Item which already contains a variant", 200, False,
             None, None, None, SELLER_ID[0]),
            ("Patch_Item_17", Patch_Item_17, "Delete all the Variant for a Item", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_18", Patch_Item_18, "Add a Variant for a Item which contains side item as well as Variants",
             200, False, None, None, None, SELLER_ID[0]),
            # -----------------------Test Cases Related to Cost key -------------------------------------
            ("Patch_Item_19", Patch_Item_03, "Update Cost for a Item (Item_07)", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_20", Patch_Item_05, "Update Cost for a Item having Item Variants (Item_36)", 200, False, None,
             None, None, SELLER_ID[0]),
            ("Patch_Item_21", Patch_Item_06, "Update Cost for a Item having Mutiple Json Item Variants(Item_46)", 200,
             False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_22", Patch_Item_07,
             "Update Cost for a Item having Side Item as well as Item Variants (Item_45)", 200, False, None, None, None,
             SELLER_ID[0]),
            # -----------------------Test Cases Related to Description key -------------------------------------
            ("Patch_Item_23", Patch_Item_03, "Update Description for a Item (Item_07)", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_24", Patch_Item_24, "Add a Description for a Item (Item_12)", 200, False, None,
             None, None, SELLER_ID[0]),
            ("Patch_Item_25", [{"id": "Item_01", "type": "create_item"}], "Delete Description for a Item (Item_01)",
             200, False, None, None, None, SELLER_ID[0]),
            # -----------------------Test Cases Related to Display Name key -------------------------------------
            ("Patch_Item_26", Patch_Item_03, "Update Display Name for a Item (Item_07)", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_27", [{"id": "Item_16", "type": "create_item"}], "Add a Display Name for a Item (Item_16)",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_28", [{"id": "Item_01", "type": "create_item"}], "Delete Display Name for a Item (Item_01)",
             200, False, None, None, None, SELLER_ID[0]),
            # -----------------------Test Cases Related to Name key -------------------------------------
            ("Patch_Item_34", Patch_Item_03, "Update Print Name for a Item (Item_07)", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Item_35", [{"id": "Item_22", "type": "create_item"}], "Add a Print Name for a Item (Item_21)",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_36", [{"id": "Item_01", "type": "create_item"}], "Delete Print Name for a Item (Item_01)",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_37", Patch_Item_32, "Update Print  Name for a Item containing variant and side item(Item_47)",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_38", Patch_Item_03, "Add Item Print Name with special Characters in it(Item_07)",
             200, False, None, None, None, SELLER_ID[0]),

            # ---------------------------Test cases related to food_type--------------------------#

            ("Patch_Item_39", [{"id": "Item_22", "type": "create_item"}], "Update food type to vegan",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_40", [{"id": "Item_22", "type": "create_item"}], "Update food_type to non_vegetarian",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Item_41", [{"id": "Item_22", "type": "create_item"}], "Update food_type to NULL",
             400, False, None, "Invalid request data", None, SELLER_ID[0]),
            ("Patch_Item_42", [{"id": "Item_22", "type": "create_item"}], "Update food type to some special char.",
             400, False, None, "Invalid request data", None, SELLER_ID[0]),

        ])
    @pytest.mark.regression
    def test_update_item(self, client, test_case_id, previous_actions, tc_description, status_code,
                         skip_case, skip_message, error_message, existing_error_message_test_id, seller_id):
        if skip_case:
            pytest.skip(skip_message)

        query_execute(DELETE_ITEM_VARIANT_VARIANT_ASSOCIATION)
        query_execute(DELETE_VARIANT)
        query_execute(DELETE_VARIANT_GROUP)
        query_execute(DELETE_SIDE_ITEM)
        query_execute(DELETE_ITEM)
        query_execute(DELETE_ITEM_CUSTOMIZATION)
        self.variant_request.variant_id = []

        if previous_actions:
            self.common_request_caller(client, previous_actions, seller_id)

        item_id = self.item_request.item_id
        response = self.item_request.update_item_request(client, test_case_id, seller_id, status_code, item_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(client, test_case_id, response, item_id, seller_id, self.item_request, previous_actions)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, item_id, seller_id, item_request, previous_actions):
        validation = ItemValidation(client, test_case_id, response, item_id, seller_id, item_request, True,
                                    previous_actions)
        validation.validate_response()
        validation.validate_get_item_response()
        validation.validate_item_exist_in_get_items_response()
        validation.validate_item_using_name_display_name()
