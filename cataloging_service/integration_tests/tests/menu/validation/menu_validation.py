import json, copy, ast
from cataloging_service.integration_tests.config import sheet_names
from cataloging_service.integration_tests.utilities.common_utils import assert_, sanitize_test_data, del_none
from cataloging_service.integration_tests.utilities.excel_utils import get_test_case_data
from cataloging_service.integration_tests.tests.base_validation import BaseValidation


class MenuValidation(BaseValidation):

    def __init__(self, client, test_case_id, response, menu_id, seller_id, menu_request, patch_api_called,
                 previous_actions):
        self.client = client
        self.response = response
        self.menu_id = menu_id
        self.seller_id = seller_id
        self.menu_request = menu_request
        self.test_case_id = test_case_id
        self.patch_api_called = patch_api_called
        self.previous_actions = previous_actions

    def validate_response(self):
        expected_data = self.get_expected_data(self.patch_api_called, self.previous_actions,
                                               test_case_id=self.test_case_id)
        self.validate_menu(self.response, expected_data)
        if self.patch_api_called:
            assert_(expected_data['menu_id'], self.response['menu_id'])
            for index, item_value in enumerate(expected_data['menu_items']):
                assert_(item_value['display_order'], self.response['menu_items'][index]['display_order'])
                assert_(item_value['sold_out'], self.response['menu_items'][index]['sold_out'])
                self.validate_item(self.response['menu_items'][index]['item'], item_value['item'])
                if sanitize_test_data(expected_data['side_items']):
                    side_item_list = json.loads(expected_data['side_items'])
                    for side_item_index, side_item in enumerate(side_item_list):
                        item_id = side_item['item_id']
                        item_test_id = side_item['test_case']
                        side_item_expected_data = self.get_expected_data(previous_actions=self.previous_actions,
                                                                         test_case_id=item_test_id)
                        for response_side_item_data in self.response_data['side_items']:
                            if response_side_item_data['item']['item_id'] == item_id:
                                self.validate_item(response_side_item_data['item'], side_item_expected_data)
                            break

    def validate_get_menu_response(self):
        expected_data = self.get_expected_data(self.patch_api_called, self.previous_actions,
                                               test_case_id=self.test_case_id)
        get_menu_response = self.menu_request.get_menu_request(self.client, self.seller_id, self.menu_id, 200)
        self.validate_menu(get_menu_response, expected_data)
        if self.patch_api_called:
            assert_(expected_data['menu_id'], self.response['menu_id'])
            for index, item_value in enumerate(expected_data['menu_items']):
                assert_(item_value['display_order'], self.response['menu_items'][index]['display_order'])
                assert_(item_value['sold_out'], self.response['menu_items'][index]['sold_out'])
                self.validate_item(self.response['menu_items'][index]['item'], item_value['item'])


    def validate_menu_exist_in_get_menus_response(self):
        get_menus_response = self.menu_request.get_menus_request(self.client, self.seller_id, 200)
        assert any(menu_id['menu_id'] == self.menu_request.menu_id for menu_id in get_menus_response)

    def get_expected_data(self, patch_api_called=False, previous_actions=None, test_case_id=None):
        menu_test_data = get_test_case_data(sheet_names.MENU_SHEET_NAME, test_case_id)[0]
        if patch_api_called:
            expected_data = []
            if sanitize_test_data(menu_test_data['expected_menu']):
                menu_data = get_test_case_data(sheet_names.MENU_SHEET_NAME,
                                               json.loads(menu_test_data['expected_menu'])['test_case_id'])[0]
                menu_data['menu_id'] = json.loads(menu_test_data['expected_menu'])['menu_id']
                expected_data.append(menu_data)
            if sanitize_test_data(menu_test_data['expected_menu_items']):
                menu_items = []
                for items in ast.literal_eval(menu_test_data['expected_menu_items']):
                    menu_dict = {}
                    items_test_case = items['item']
                    for item in items_test_case:
                        item_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME, item['test_case_id'])[0]
                        menu_dict['display_order'] = items['display_order']
                        if "sold_out" in items:
                            menu_dict['sold_out'] = items['sold_out']
                        menu_dict['item'] = item_data
                    menu_items.append(menu_dict)
                expected_data[0]['menu_items'] = menu_items
                return expected_data[0]
            else:
                for previous_action in previous_actions:
                    if previous_action['type'] == 'create_item':
                        create_item_test_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME, previous_action['id'])[
                            0]
            create_item_json_copy = copy.deepcopy(create_item_test_data)
            for key in menu_test_data:
                menu_test_data[key] = sanitize_test_data(menu_test_data[key])
                if menu_test_data[key]:
                    create_item_json_copy[key] = menu_test_data[key]
            return create_item_json_copy
        return menu_test_data