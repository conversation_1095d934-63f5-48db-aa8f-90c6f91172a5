import pytest

from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.resources.db_queries import *
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.before_test_actions import *
from cataloging_service.integration_tests.tests.menu.validation.menu_validation import MenuValidation
from cataloging_service.integration_tests.utilities.common_utils import query_execute


class TestUpdateMenu(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id",
        [
            # Description
            ("Patch_Menu_01", Patch_Menu_01, "Add a simple item to a Menu",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_02", Patch_Menu_02, "Add 2 items to a Menu",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_03", Patch_Menu_03, "Add a Item having side_items to a Menu",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_04", Patch_Menu_04, "Add 2 items to a Menu.One simple Item and one having side Item",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_05", Patch_Menu_05,
             "Add 2 items to a Menu.One item having customised_item and another one simple item ",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_06", Patch_Menu_06, "Add a item having more then one side items to a Menu",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_07", Patch_Menu_07, "Add a item having item variants  to a Menu",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_08", Patch_Menu_08, "Add a item having 2 Item Variants in one json to a Menu",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_09", Patch_Menu_08, "Add a item having 2 different json of 2 Item Variants to a Menu",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_10", Patch_Menu_10, "Create a Category then Add Item to a Category",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_11", Patch_Menu_11, "Create 2 Categories and Add One Item to 2 Categories",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_12", Patch_Menu_12, "Create 2 Categories and add 1 Item in Each Category",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_13", Patch_Menu_13, "Create a Category and add a item having side item to the category",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_14", Patch_Menu_10,
             "Create a Category and add a item having more then one side items to the category",
             200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Menu_15", Patch_Menu_15, "Remove an Item from a Menu", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Menu_16", Patch_Menu_01, "Update Menu Description", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Menu_17", Patch_Menu_01, "Update Menu Display Name ", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Menu_18", Patch_Menu_01, "Menu Timing Updated", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Menu_19", Patch_Menu_01, "Menu Name Updated", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Menu_20", Patch_Menu_01, "Menu types Updated", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Menu_21", Patch_Menu_01, "Item added to Menu", 200, False, None, None, None,
             SELLER_ID[0]),
            ("Patch_Menu_22", Patch_Menu_01, "Change Menu Timing to NULL", 400, False, None, "Invalid request data",
             None, SELLER_ID[0]),

        ])
    @pytest.mark.regression
    def test_update_menu(self, client, test_case_id, previous_actions, tc_description, status_code,
                         skip_case, skip_message, error_message, existing_error_message_test_id, seller_id):
        if skip_case:
            pytest.skip(skip_message)

        query_execute(DELETE_MENU)
        query_execute(DELETE_ITEM_VARIANT_VARIANT_ASSOCIATION)
        query_execute(DELETE_VARIANT)
        query_execute(DELETE_VARIANT_GROUP)
        query_execute(DELETE_SIDE_ITEM)
        query_execute(DELETE_ITEM)
        query_execute(DELETE_ITEM_CUSTOMIZATION)
        query_execute(DELETE_COMBO)

        if previous_actions:
            self.common_request_caller(client, previous_actions, seller_id)

        response = self.menu_request.update_menu_request(client, test_case_id, status_code, seller_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(client, test_case_id, response, self.menu_request.menu_id, seller_id, self.menu_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, menu_id, seller_id, menu_request):
        validation = MenuValidation(client, test_case_id, response, menu_id, seller_id, menu_request, False, None)
        validation.validate_response()
        validation.validate_get_menu_response()
        validation.validate_menu_exist_in_get_menus_response()
