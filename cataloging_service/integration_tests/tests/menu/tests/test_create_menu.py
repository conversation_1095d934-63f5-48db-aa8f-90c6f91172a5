import pytest

from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.menu.validation.menu_validation import MenuValidation


class TestCreateMenu(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id",
        [
            # Description
            ("Menu_01", "Create a Menu with all the details like timings and days as well", 201, False,
             None, None, None, SELLER_ID[0]),
            ("Menu_02", "Create a Menu without Description", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_03", "Create a Menu with Blank Description", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_04", "Create a Menu with null Description", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_05", "Create a Menu containing Special Characters in Menu Description", 201, False, None, None, None,
             SELLER_ID[0]),
            #  Display Name
            ("Menu_06", "Create a Menu without Display Name", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_07", "Create a Menu with Blank Display Name", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_08", "Create a Menu with null Display Name", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_09", "Create a Menu containing Special Characters in Disply Name", 201, False, None, None, None,
             SELLER_ID[0]),
            #  Menu Timings
            ("Menu_10", "Create a Menu without Menu Timings", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_11", "Create a Menu with null Menu Timing", 400, False, None, "Invalid request data", None,
             SELLER_ID[0]),
            ("Menu_12", "Create a Menu with Empty Menu Timing Array", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_13", "Create a  Menu without start_time in Menu Timing Array", 400, False, None,
             "Invalid request data", None, SELLER_ID[0]),
            ("Menu_14", "Create a Menu without end_time in Menu Timing Array", 400, False, None, "Invalid request data",
             None, SELLER_ID[0]),
            ("Menu_15", "Create a Menu without days Array inside Menu Timing Array", 400, False, None,
             "Invalid request data", None, SELLER_ID[0]),
            ("Menu_16", "Create a Menu With Multiple Timings Array", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_17", "Create a Menu with Same Day Menu Days with Different Timing", 201, False, None, None, None,
             SELLER_ID[0]),
            #  Name
            ("Menu_18", "Create a Menu without Name", 400, False, None, "Invalid request data", None, SELLER_ID[0]),
            ("Menu_19", "Create a Menu with Blank Name", 201, False, None, None, None, SELLER_ID[0]),
            ("Menu_20", "Create a Menu with null Name", 400, False, None, "Invalid request data", None, SELLER_ID[0]),
            ("Menu_21", "Create a Menu containing Special Characters in  Name", 201, False, None, None, None,
             SELLER_ID[0]),
            ("Menu_22", "Create a Menu with Name which already exist", 201, False, None, None, None,
             SELLER_ID[0]),
            #  Menu Type
            ("Menu_23", "Create a Menu without Menu Type", 400, False, None, "Invalid request data", None,
             SELLER_ID[0]),
            ("Menu_24", "Create a Menu with Blank Menu Type", 400, False, None, "Invalid request data", None,
             SELLER_ID[0]),
            ("Menu_25", "Create a Menu with null Menu Type", 400, False, None, "Invalid request data", None,
             SELLER_ID[0]),
            ("Menu_26", "Create a Menu containing Special Characters in  Menu Type", 400, False, None,
             "Invalid request data", None, SELLER_ID[0]),
            ("Menu_27", "Create a Menu without menu code", 400, False, None,
             "Invalid request data", None, SELLER_ID[0]),

        ])
    @pytest.mark.regression
    def test_create_menu(self, client, test_case_id, tc_description, status_code,
                         skip_case, skip_message, error_message, existing_error_message_test_id, seller_id):
        if skip_case:
            pytest.skip(skip_message)

        response = self.menu_request.create_menu_request(client, test_case_id, status_code, seller_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(client, test_case_id, response, self.menu_request.menu_id, seller_id, self.menu_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, menu_id, seller_id, menu_request):
        validation = MenuValidation(client, test_case_id, response, menu_id, seller_id, menu_request, False, None)
        validation.validate_response()
        validation.validate_get_menu_response()
        validation.validate_menu_exist_in_get_menus_response()
