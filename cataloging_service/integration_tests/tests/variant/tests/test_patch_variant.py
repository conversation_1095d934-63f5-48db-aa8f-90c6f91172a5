import pytest
from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.variant.validations.variant_validations import VariantValidation
from cataloging_service.integration_tests.resources.db_queries import *
from cataloging_service.integration_tests.utilities.common_utils import *


class TestUpdateVariant(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, previous_actions, tc_description, status_code, skip_case, skip_message, error_message, "
        "existing_error_message_test_id, seller_id",
        [
            ("Patch_Variant_01", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update can_select_multiple to False", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_02", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update can_select_quantity to FALSE", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_03", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update Description", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_04", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update Display Name ", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_05", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update is_customization", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_06", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update maximum_selectable_quantity", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_07", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update minimum_selectable_quantity", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_08", [{"id": "variant_07", "type": "create_variant_group"}],
             "Update name of variant_07", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_09", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update variant_group_id of variant_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_10", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update variants of variant_01", 200, False, None, None, None, SELLER_ID[0]),

            # ------------------------Test Cases Related to can_select_multiple---------#

            ("Patch_Variant_11", [{"id": "variant_15", "type": "create_variant_group"}],
             "Update can_select_multiple to FALSE (variant_15)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_12", [{"id": "variant_19", "type": "create_variant_group"}],
             "Update can_select_multiple to FALSE (variant_19)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_13", [{"id": "variant_22", "type": "create_variant_group"}],
             "Update can_select_multiple to TRUE (variant_22)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_14", [{"id": "variant_16", "type": "create_variant_group"}],
             "Update can_select_multiple to FALSE (variant_16)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_15", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update can_select_multiple to FALSE (variant_02)", 200, False, None, None, None, SELLER_ID[0]),

            # ------------Test Cases Related to can_select_quantity------------------#

            ("Patch_Variant_16", [{"id": "variant_15", "type": "create_variant_group"}],
             "Update can_select_quantity to FALSE (variant_15)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_17", [{"id": "variant_19", "type": "create_variant_group"}],
             "Update can_select_quantity to FALSE (variant_19)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_18", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update can_select_quantity to TRUE (variant_01)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_19", [{"id": "variant_16", "type": "create_variant_group"}],
             "Update can_select_quantity to TRUE (variant_16)", 200, False, None, None, None, SELLER_ID[0]),

            # ------------Test Cases Related to Updated Description------------------#

            ("Patch_Variant_20", [{"id": "variant_15", "type": "create_variant_group"}],
             "Update description (variant_15)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_21", [{"id": "variant_16", "type": "create_variant_group"}],
             "Update description (variant_16)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_22", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update description (variant_02)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_23", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update description (variant_02)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_24", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update description (variant_02)", 200, False, None, None, None, SELLER_ID[0]),

            # ------------Test Cases Related to display_name------------------#

            ("Patch_Variant_25", [{"id": "variant_15", "type": "create_variant_group"}],
             "Update display_name (variant_15)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_26", [{"id": "variant_16", "type": "create_variant_group"}],
             "Update display_name (variant_16)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_27", [{"id": "variant_19", "type": "create_variant_group"}],
             "Update display_name (variant_19)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_28", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update display_name (variant_1)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_29", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update display_name (variant_2)", 200, False, None, None, None, SELLER_ID[0]),

            # ---------Test cases Related to is_customisation -------------------------#

            ("Patch_Variant_30", [{"id": "variant_15", "type": "create_variant_group"}],
             "Update is_customisation to TRUE(variant_15)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_31", [{"id": "variant_16", "type": "create_variant_group"}],
             "Update is_customisation to FALSE (variant_16)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_32", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update is_customisation to FALSE (variant_23)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_33", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update display_name (variant_1)", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_34", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update is_customisation to TRUE(variant_2)", 200, False, None, None, None, SELLER_ID[0]),

            # -------Test cases related to maximum selectable quantity -----------#

            ("Patch_Variant_35", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update maximum_selectable quantity of variant_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_36", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update maximum_selectable quantity of variant_02", 200, False, None, None, None, SELLER_ID[0]),

            # ---------Test cases related to minimum selectable quantity ----------#

            ("Patch_Variant_37", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update minimum_selectable quantity of variant_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_38", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update minimum_selectable quantity of variant_02", 200, False, None, None, None, SELLER_ID[0]),

            # ------------Test cases related to name of variant --------------------#

            ("Patch_Variant_39", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update name of variant_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_40", [{"id": "variant_02", "type": "create_variant_group"}],
             "Update name of variant_02", 200, False, None, None, None, SELLER_ID[0]),

            # ------------Test cases related to variants ----------------------------#

            ("Patch_Variant_41", [{"id": "variant_01", "type": "create_variant_group"}],
             "Update variants of variant_01", 200, False, None, None, None, SELLER_ID[0]),
            ("Patch_Variant_42", [{"id": "variant_45", "type": "create_variant_group"}],
             "Update variants of have more than one variant (Variant_45)", 200, False, None, None, None, SELLER_ID[0]),

        ])
    @pytest.mark.regression
    def test_update_variant(self, client, test_case_id, previous_actions, tc_description, status_code,
                            skip_case, skip_message, error_message, existing_error_message_test_id, seller_id):
        if skip_case:
            pytest.skip(skip_message)

        query_execute(DELETE_VARIANT)
        query_execute(DELETE_VARIANT_GROUP)

        if previous_actions:
            self.common_request_caller(client, previous_actions, seller_id)

        variant_id = self.variant_request.variant_group_id
        response = self.variant_request.update_variant_request(client, test_case_id, seller_id, status_code, variant_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_message)
        elif status_code in SUCCESS_CODES:
            self.validation(client, test_case_id, response, variant_id, seller_id, self.variant_request,
                            previous_actions)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, item_id, seller_id, item_request, previous_actions):
        validation = VariantValidation(client, test_case_id, response, item_id, seller_id, item_request, True,
                                       previous_actions)
        validation.validate_response()
        validation.validate_get_variant_response()
