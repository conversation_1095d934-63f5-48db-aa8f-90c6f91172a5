import pytest
from cataloging_service.integration_tests.config.common_config import ERROR_CODES, SUCCESS_CODES, SELLER_ID
from cataloging_service.integration_tests.tests.base_test import BaseTest
from cataloging_service.integration_tests.tests.variant.validations.variant_validations import VariantValidation
from cataloging_service.integration_tests.resources.db_queries import *
from cataloging_service.integration_tests.utilities.common_utils import *


class TestCreateVariant(BaseTest):
    @pytest.mark.parametrize(
        "test_case_id, tc_description, status_code, skip_case, skip_message, error_code, "
        "existing_error_message_test_id, seller_id",
        [
            # ---------------------------Test Case Related to can_select_multiple----------------------
            ("variant_01", "create variant with all the fields", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_02", "create variant with can_select_multiple as FALSE", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_03", "create variant with can_select_multiple as Blank String", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_04", "create a variant with can_select_multiple not in the request", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_05", "create a variant with can_select_multiple as NULL", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_06", "create variant with can_select_multiple with Special Characters", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            # ---------------------------Test Case Related to can_select_quantity------------------------#

            ("variant_07", "create variant with can_select_quantity as FALSE", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_08", "create variant with can_select_quantity as Blank String", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_09", "create a variant with can_select_quantity not in the request", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_10", "create a variant with can_select_quantity as NULL", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_11", "create variant with can_select_quantity with Special Characters", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_12", "create variant with can_select_multiple and can_select_quantity both not in the request",
             400, False, None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_13", "create variant with can_select_multiple and can_select_quantity both as NULL", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_14", "create variant with can_select_multiple and can_select_quantity both as EMPTY", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),

            # -----------------------------------------------Test case related to description------------------------
            ("variant_15", "create variant with description as Blank String", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_16", "create variant with description as NULL", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_17", "create variant with not sending description in the request", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_18", "create variant with description having special characters", 201, False,
             None, None, None, SELLER_ID[0]),
            # -----------------------------test case related to display_name -----------------------------------------
            ("variant_19", "create variant with can_select_quantity as Blank String", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_20", "create variant with display name as NULL", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_21", "create variant without sending display name in the request", 201, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_22", "create variant with special characters in display name ", 201, False,
             None, None, None, SELLER_ID[0]),
            # --------------------------------------Test case related to is_customization---------------------
            ("variant_23", "create variant with is_customisation as Blank String", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_24", "create variant with s_customisation as NULL", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_25", "create variant with is_customisation having special characters", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_26", "create variant with not sending s_customisation in the request", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_27", "create variant with not sending s_customisation as FALSE", 201, False,
             None, None, None, SELLER_ID[0]),
            # ------------------------Test case related to maximum_selectable_quantity-----------------#
            ("variant_28", "create variant with maximum_selectable_quantity as Blank String ", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_29", "create variant with maximum_selectable_quantity as NULL", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_30", "create variant with not sending  maximum_selectable_quantity in the request", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_31", "create variant with minimum_selectable_quantity having special characters", 201, False,
             None, None, None, SELLER_ID[0]),
            # ----------------------------Test case related to minimum_selectable_quantity----------------------
            ("variant_33", "create variant with minimum_selectable_quantity as Blank String ", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_34", "create variant with minimum_selectable_quantity as NULL", 201, False,
             None, None, None, SELLER_ID[0]),

            # ------------------------Test Cases Related to name ----------------------------#

            ("variant_35", "create variant with name as EMPTY", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_36", "create variant with name as NULL", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_37", "create variant with not sending name in the request", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_38", "create variant with special characters in the name ", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_39", "create variant with special characters in the name as TRUE", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),

            # ----------------------Test cases related to varaint_id ------------------#

            ("variant_40", "create variant with variant_id", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_41", "create variant with blank variant_group_id", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_42", "create variant with not sending variant_group_id in request", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_43", "create variant with variant group id as NULL ", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_44", "create variant with some special characters", 201, False,
             None, None, None, SELLER_ID[0]),

            # --------------------------Test cases related to variants---------------------#

            ("variant_45", "create a variant having more than one variants", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_46", "create a variant having only one variant", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_47", "create a variant for Test Case Id Item_46", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_48", "create a variant for with EMPTY Array", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_49", "create a variant having variants as NULL", 400, False,
             None, 'Invalid request data', None, SELLER_ID[0]),
            ("variant_50", "create a variant with variants name as Blank", 201, False,
             None, None, None, SELLER_ID[0]),
            ("variant_51", "create a variant having min selectable quantity greater than max selectable quantity", 201,
             True,
             None, None, None, SELLER_ID[0]),

        ])
    @pytest.mark.regression
    def test_create_variant(self, client, test_case_id, tc_description, status_code,
                            skip_case, skip_message, error_code, existing_error_message_test_id, seller_id):
        if skip_case:
            pytest.skip(skip_message)

        query_execute(DELETE_VARIANT)
        query_execute(DELETE_VARIANT_GROUP)

        response = self.variant_request.create_variant_request(client, test_case_id, status_code, seller_id)

        if status_code in ERROR_CODES:
            if existing_error_message_test_id:
                test_case_id = existing_error_message_test_id
            self.response_validation_negative_cases(response, test_case_id, error_code)
        elif status_code in SUCCESS_CODES:
            variant_id = self.variant_request.variant_id
            self.validation(client, test_case_id, response, variant_id, seller_id, self.variant_request)
        else:
            assert False, "Response status code is not matching"

    @staticmethod
    def validation(client, test_case_id, response, variant_id, seller_id, variant_request):
        validation = VariantValidation(client, test_case_id, response, variant_id, seller_id, variant_request, False)
        validation.validate_response()
        validation.validate_get_variant_response()
