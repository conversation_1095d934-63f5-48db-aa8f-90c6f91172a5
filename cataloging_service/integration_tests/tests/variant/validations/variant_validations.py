import json, copy, ast
from cataloging_service.integration_tests.config import sheet_names
from cataloging_service.integration_tests.utilities.common_utils import assert_, sanitize_test_data
from cataloging_service.integration_tests.utilities.excel_utils import get_test_case_data
from cataloging_service.integration_tests.tests.base_validation import BaseValidation


class VariantValidation(BaseValidation):
    def __init__(self, client, test_case_id, response, variant_id, seller_id, variant_request, patch_api_called,
                 previous_actions=None):
        self.client = client
        self.response = response
        self.variant_id = variant_id
        self.seller_id = seller_id
        self.variant_request = variant_request
        self.patch_api_called = patch_api_called
        self.previous_actions = previous_actions
        self.test_case_id = test_case_id

    def validate_response(self):
        test_data = self.get_test_data(patch_api_called=self.patch_api_called, previous_actions=self.previous_actions,
                                       test_case_id=self.test_case_id)
        self.validate_variant(self.response, test_data)

    def validate_get_variant_response(self):
        test_data = self.get_test_data(patch_api_called=self.patch_api_called, previous_actions=self.previous_actions,
                                       test_case_id=self.test_case_id)
        variant_response = self.variant_request.get_variant_request(self.client, self.seller_id, self.variant_id, 200)
        self.validate_variant(variant_response[0], test_data)

    def get_test_data(self, patch_api_called, previous_actions=None, test_case_id=None):
        variant_group_test_data = get_test_case_data(sheet_names.VARIANT_SHEET_NAME, test_case_id)[0]
        if patch_api_called:
            if test_case_id in ('Patch_Item_08', 'Patch_Item_15', 'Patch_Item_16'):
                create_variant_group_test_data = get_test_case_data(sheet_names.ITEM_SHEET_NAME,
                                                                    previous_actions[1]['id'])[0]
            else:
                for previous_action in previous_actions:
                    if previous_action['type'] == 'create_variant_group':
                        create_variant_group_test_data = \
                            get_test_case_data(sheet_names.VARIANT_SHEET_NAME, previous_action['id'])[
                                0]
            create_variant_json_copy = copy.deepcopy(create_variant_group_test_data)
            for key in variant_group_test_data:
                variant_group_test_data[key] = sanitize_test_data(variant_group_test_data[key])
                if variant_group_test_data[key] or variant_group_test_data[key] in (0, 1):
                    create_variant_json_copy[key] = variant_group_test_data[key]
            return create_variant_json_copy
        return variant_group_test_data
