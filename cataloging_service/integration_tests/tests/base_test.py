from cataloging_service.integration_tests.config.error_messages import ErrorMessage
from cataloging_service.integration_tests.requests.area_request import Area<PERSON>equest
from cataloging_service.integration_tests.requests.combo_request import Co<PERSON>Request
from cataloging_service.integration_tests.requests.menu_request import MenuRequest
from cataloging_service.integration_tests.utilities.common_utils import assert_
from cataloging_service.integration_tests.requests.item_request import ItemRequest
from cataloging_service.integration_tests.requests.variant_request import VariantRequest
from cataloging_service.integration_tests.requests.seller_sku_request import CatalogSkuRequest
from cataloging_service.integration_tests.requests.menu_request import MenuRequest


class BaseTest(object):
    item_request = ItemRequest()
    variant_request = VariantRequest()
    combo_request = ComboRequest()
    area_request = AreaRequest()
    seller_sku_request = CatalogSkuRequest()
    menu_request = MenuRequest()

    def common_request_caller(self, client, test_case_id_plus_action_to_be_performed_list, seller_id=None):
        for action in test_case_id_plus_action_to_be_performed_list:
            test_case_id = action['id'] if 'id' in action else None
            action_type = action['type']
            if action_type == 'create_item':
                self.item_request.create_item_request(client, test_case_id, 201, seller_id)
            elif action_type == 'create_variant_group':
                self.variant_request.create_variant_request(client, test_case_id, 201, seller_id)
            elif action_type == 'create_combo':
                self.combo_request.create_combo_request(client, test_case_id, 201, seller_id)
            elif action_type == 'create_menu':
                self.menu_request.create_menu_request(client, test_case_id, 201, seller_id)
            elif action_type == 'update_menu':
                self.menu_request.update_menu_request(client, test_case_id, seller_id, 200)
            elif action_type == 'create_area':
                self.area_request.create_area_request(client, test_case_id, 201, seller_id)
            elif action_type == 'update_area':
                self.area_request.update_area_request(client, test_case_id, 201, seller_id)
            elif action_type == 'get_area':
                self.area_request.get_area_request(client, test_case_id, 201, seller_id)
            else:
                raise ValueError(action['id'] + ' is not handled in Common request caller')

    @staticmethod
    def response_validation_negative_cases(response, test_case_id, error_message):
        assert (response['reason'] in ErrorMessage[test_case_id].value)
        assert_(response['message'], error_message)
