from treebo_commons.multitenancy.sqlalchemy import db_engine


# interpret the values for NULL/EMPTY/blank fields from excel
def sanitize_test_data(data):
    if data in ['', 'null', 'Null Key']:
        return None
    elif data == 'EMPTY':
        return ''
    elif data == 'EMPTY Json':
        return {}
    elif data == 'Empty Array':
        return []
    elif data == 'EMPTY String':
        return 'EMPTY String'
    elif isinstance(data, dict):
        if all(value is None for value in data.values()):
            return None
        elif all(value == 'NULL_KEY' or value is None for value in data.values()):
            return 'NULL_KEY'
        else:
            return data
    else:
        return data


# for deleting the null values and empty dict from object passed
def del_none(dict_object):
    for key, value in list(dict_object.items()):
        if isinstance(value, dict):
            del_none(value)
        elif isinstance(value, list):
            for val in value:
                if isinstance(val, dict):
                    del_none(val)
        if value is None:
            del dict_object[key]
        if value == 'NULL':
            dict_object[key] = None
    return dict_object


def assert_(actual_value, expected_value, failure_message=None):
    assert sanitize_test_data(actual_value) == sanitize_test_data(expected_value), str(
        failure_message) + ". ACTUAL: " + str(
        actual_value) + " EXPECTED: " + str(expected_value)


def query_execute(query):
    db_engine.get_session(None).execute(query)
    db_engine.get_session(None).commit()
