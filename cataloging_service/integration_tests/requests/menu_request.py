import json

from cataloging_service.integration_tests.builders import menu_builder
from cataloging_service.integration_tests.config.common_config import SUCCESS_CODES
from cataloging_service.integration_tests.config.sheet_names import *
from cataloging_service.integration_tests.config.request_uris import *
from cataloging_service.integration_tests.requests.base_request import BaseRequest


class MenuRequest(BaseRequest):
    def create_menu_request(self, client, test_case_id, status_code, seller_id):
        request_json = menu_builder.CreateMenuBuilder(MENU_SHEET_NAME, test_case_id).create_menu_request()
        url = CREATE_MENU_URL.format(seller_id=seller_id)
        response = self.request_processor(client, 'POST', url, status_code, request_json)
        if status_code in SUCCESS_CODES:
            self.menu_id = response['menu_id']
        return response

    def get_menu_request(self, client, seller_id, menu_id, status_code):
        url = GET_MENU_URL.format(seller_id=seller_id, menu_id=menu_id)
        response = self.request_processor(client, 'GET', url, status_code)
        return response

    def get_menus_request(self, client, seller_id, status_code):
        url = CREATE_MENU_URL.format(seller_id=seller_id)
        response = self.request_processor(client, 'GET', url, status_code)
        return response

    def update_menu_request(self, client, test_case_id, status_code, seller_id, menu_id=None):
        menu_id = self.menu_id if not menu_id else menu_id
        request_json = menu_builder.CreateMenuBuilder(MENU_SHEET_NAME, test_case_id).update_menu_request()
        url = GET_MENU_URL.format(seller_id=seller_id, menu_id=menu_id)
        response = self.request_processor(client, 'PATCH', url, status_code, request_json)
        if status_code in SUCCESS_CODES:
            self.menu_id = response['menu_id']
        return response
