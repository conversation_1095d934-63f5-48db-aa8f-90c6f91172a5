import json
from cataloging_service.integration_tests.builders import variant_builder, combo_builder, area_builder
from cataloging_service.integration_tests.config.common_config import SUCCESS_CODES
from cataloging_service.integration_tests.config.sheet_names import *
from cataloging_service.integration_tests.config.request_uris import *
from cataloging_service.integration_tests.requests.base_request import BaseRequest


class AreaRequest(BaseRequest):

    def create_area_request(self, client, test_case_id, status_code, seller_id):
        request_json = area_builder.CreateAreaBuilder(AREA_SHEET_NAME, test_case_id).create_area_request()
        url = CREATE_AREA_URL.format(seller_id=seller_id)
        response = self.request_processor(client, 'POST', url, status_code, request_json)
        if status_code in SUCCESS_CODES:
            self.area_id = response['area_id']
        return response

    def update_area_request(self, client, test_case_id, seller_id, status_code, area_id=None):
        area_id = self.area_id if not area_id else area_id
        request_json = area_builder.CreateAreaBuilder(AREA_SHEET_NAME, test_case_id).create_area_request()
        url = PATCH_AREA_URL.format(seller_id=seller_id, area_id=area_id)
        return self.request_processor(client, 'PATCH', url, status_code, request_json)

    def get_area_request(self, client, seller_id, area_id, status_code):
        url = GET_AREA_URL.format(seller_id=seller_id, area_id=area_id)
        return self.request_processor(client, 'GET', url, status_code)

    def get_areas_request(self, client, seller_id, status_code):
        url = GET_AREAS_URL.format(seller_id=seller_id)
        return self.request_processor(client, 'GET', url, status_code)
