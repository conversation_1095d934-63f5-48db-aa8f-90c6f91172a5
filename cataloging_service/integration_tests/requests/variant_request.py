from cataloging_service.integration_tests.builders import variant_builder
from cataloging_service.integration_tests.config.common_config import SUCCESS_CODES
from cataloging_service.integration_tests.config.sheet_names import *
from cataloging_service.integration_tests.config.request_uris import *
from cataloging_service.integration_tests.requests.base_request import BaseRequest


class VariantRequest(BaseRequest):
    def create_variant_request(self, client, test_case_id, status_code, seller_id):
        request_json = variant_builder.CreateVariantBuilder(VARIANT_SHEET_NAME, test_case_id).create_variant_request()
        url = CREATE_VARIANT_URL.format(seller_id=seller_id)
        response = self.request_processor(client, 'POST', url, status_code, request_json)
        if status_code in SUCCESS_CODES:
            self.variant_group_id = response['variant_group_id']
            for variant in response['variants']:
                self.variant_id.append(variant['variant_id'])
        return response

    def update_variant_request(self, client, test_case_id, seller_id, status_code, variant_id=None):
        variant_id = self.variant_id if not variant_id else variant_id
        request_json = variant_builder.CreateVariantBuilder(VARIANT_SHEET_NAME, test_case_id).create_variant_request()
        url = PATCH_VARIANT_URL.format(seller_id=seller_id, variant_group_id=variant_id)
        response = self.request_processor(client, 'PATCH', url, status_code, request_json)
        return response

    def get_variant_request(self, client, seller_id, variant_id, status_code):
        url = GET_VARIANT_URL.format(seller_id=seller_id, variant_id=variant_id)
        response = self.request_processor(client, 'GET', url, status_code)
        return response
