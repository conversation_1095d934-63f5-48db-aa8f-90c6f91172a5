from flask import json
import logging
from cataloging_service.integration_tests.utilities.common_utils import assert_

logger = logging.getLogger(__name__)


class BaseRequest(object):
    def __init__(self):
        self.menu_id = None
        self.item_id = None
        self.variant_id = []
        self.sku_id = None
        self.combo_id = None
        self.area_id = None

    def request_processor(self, client_, request_type, url, status_code, request_json=None, parameters=None,
                          x_hotel_id=None):
        headers = {'Content-Type': 'application/json'}
        if x_hotel_id:
            headers['X-Hotel-Id'] = x_hotel_id
        client_type = {
            "POST": client_.post,
            "PATCH": client_.patch,
            "GET": client_.get,
            "DELETE": client_.delete
        }

        print('\n\n' + '#' * 25 + 'REQUEST' + '#' * 25)
        print('REQUEST URL: ' + url + '\nREQUEST TYPE: ' + request_type + '\nHEADERS: ' + str(headers) +
              '\nREQUEST JSON: ' + str(request_json) + '\nREQUEST PARAMS: ' + str(parameters))
        response = client_type.get(request_type)(
            url,
            data=request_json,
            headers=headers
        )
        print('\n\n' + '#' * 25 + 'RESPONSE' + '#' * 25)
        if request_type == "DELETE":
            print('RESPONSE CODE: ' + str(response.status_code))
            return None

        print('RESPONSE CODE: ' + str(response.status_code) + '\nRESPONSE DATA: ' + json.dumps(response.json))
        assert_(response.status_code, status_code, 'Status code is not matching')
        return response.json
