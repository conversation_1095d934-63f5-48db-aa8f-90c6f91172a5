import json
from cataloging_service.integration_tests.builders import variant_builder, combo_builder
from cataloging_service.integration_tests.config.common_config import SUCCESS_CODES
from cataloging_service.integration_tests.config.sheet_names import *
from cataloging_service.integration_tests.config.request_uris import *
from cataloging_service.integration_tests.requests.base_request import BaseRequest


class ComboRequest(BaseRequest):

    def create_combo_request(self, client, test_case_id, status_code, seller_id):
        request_json = combo_builder.CreateComboBuilder(COMBO_SHEET_NAME, test_case_id).create_combo_request()
        url = CREATE_COMBO_URL.format(seller_id=seller_id)
        response = self.request_processor(client, 'POST', url, status_code, request_json)
        if status_code in SUCCESS_CODES:
            self.combo_id = response['combo_id']
        return response

    def update_combo_request(self, client, test_case_id, seller_id, status_code, combo_id=None):
        combo_id = self.combo_id if not combo_id else combo_id
        request_json = combo_builder.CreateComboBuilder(COMBO_SHEET_NAME, test_case_id).create_combo_request()
        url = PATCH_COMBO_URL.format(seller_id=seller_id, combo_id=combo_id)
        return self.request_processor(client, 'PATCH', url, status_code, request_json)

    def get_combo_request(self, client, seller_id, combo_id, status_code):
        url = GET_COMBO_URL.format(seller_id=seller_id, combo_id=combo_id)
        return self.request_processor(client, 'GET', url, status_code)

    def get_combos_request(self, client, seller_id, status_code, name_display_name=None):
        url = GET_COMBOS_URL.format(seller_id=seller_id)
        if name_display_name:
            url = GET_COMBOS_URL.format(seller_id=seller_id) + "/?name=" + name_display_name
        return self.request_processor(client, 'GET', url, status_code)

    def get_item_variants(self, client, seller_id, status_code, item_id):
        url = GET_ITEM_VARIANTS_URL.format(seller_id=seller_id, item_id=item_id)
        return self.request_processor(client, 'GET', url, status_code)

    def get_item_customisation(self, client, seller_id, status_code, item_id):
        url = GET_ITEM_CUSTOMISATION_URL.format(seller_id=seller_id, item_id=item_id)
        return self.request_processor(client, 'GET', url, status_code)

    def get_item_request(self, client, seller_id, item_id, status_code):
        url = GET_ITEM_URL.format(seller_id=seller_id, item_id=item_id)
        return self.request_processor(client, 'GET', url, status_code)
