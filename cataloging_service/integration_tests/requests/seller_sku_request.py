import json

from cataloging_service.integration_tests.builders import item_builder
from cataloging_service.integration_tests.config.common_config import SUCCESS_CODES
from cataloging_service.integration_tests.config.sheet_names import *
from cataloging_service.integration_tests.config.request_uris import *
from cataloging_service.integration_tests.requests.base_request import BaseRequest
from cataloging_service.integration_tests.utilities.common_utils import del_none
from cataloging_service.integration_tests.config.common_config import NULL_FIELD_TEST_CASES


class CatalogSkuRequest(BaseRequest):
    def get_seller_sku(self, client, status_code, seller_id):
        url = GET_SELLER_SKU_URL.format(seller_id=seller_id)
        response = self.request_processor(client, 'GET', url, status_code)
        return response
