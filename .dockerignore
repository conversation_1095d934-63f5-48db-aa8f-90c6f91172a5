# Git
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
*.md
docs/
*.txt

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Logs
*.log
logs/
log/

# Temporary files
tmp/
temp/
.tmp/

# Database
*.db
*.sqlite
*.sqlite3

# Environment files (keep .env.example if you have one)
.env.local
.env.development
.env.test
.env.production

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup files
*.bak
*.backup
*.old

# Archives
*.zip
*.tar.gz
*.rar

# Local development
.local/
.cache/
