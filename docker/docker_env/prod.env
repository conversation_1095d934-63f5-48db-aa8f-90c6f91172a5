APP_ENV=production
NEW_RELIC_ENVIRONMENT=production
LOG_ROOT=/var/log/
GUNICORN_ACCESSLOG=/var/log/cataloging-service/gunicorn.log
GUNICORN_ERRORLOG=/var/log/cataloging-service/gunicorn_error.log
NEW_RELIC_CONFIG_FILE=newrelic.ini
RMQ_HOST=amqp://cmuser:<EMAIL>:5672/catalog
DOCKER_REGISTRY=docker.treebo.pr:5000
SLACKBOT_HOOK=*****************************************************************************
