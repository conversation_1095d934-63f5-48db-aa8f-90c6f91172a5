APP_ENV=staging
LOG_ROOT=/var/log/
DB=************************************************************************
GUNICORN_ACCESSLOG=/var/log/cataloging-service/gunicorn.log
GUNICORN_ERRORLOG=/var/log/cataloging-service/gunicorn_error.log
NEW_RELIC_CONFIG_FILE=newrelic.ini
NEW_RELIC_ENVIRONMENT=staging
RMQ_HOST=amqp://rms:<EMAIL>:5672/catalog
SLACKBOT_HOOK=*****************************************************************************
