version: "2.1"

services:
  cataloging_cron_service:
    extends:
      file: base-docker-compose.yml
      service: cataloging-base-service
    command: /bin/sh -c "python manage.py superhero_create_hotels --tenant=$TENANT --filename=$FILENAME"
    container_name: "cataloging_cron"
    volumes: 
      - "/var/log/treebotech/resources/:/usr/src/app/resources/"
    environment:
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
