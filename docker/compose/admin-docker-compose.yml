version: "2.1"

services:
  cataloging-service-admin:
    ports:
      - "${CATALOGING_SERVICE_HOST_PORT}:8001"
    volumes:
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${HOST_DOC_ROOT}:/usr/src/doc"
    container_name: "catalog_admin_${ADMIN_TENANT_ID}"
    environment:
      - ADMIN_TENANT_ID
      - BUILD_NUMBER=${VERSION}
      - AWS_REGION=${regions}
      - CLUSTER_IDENTIFIER=${CLUSTER_IDENTIFIER}
      - AWS_SECRET_PREFIX=${AWS_SECRET_PREFIX}
    restart: always
    extends:
      file: base-docker-compose.yml
      service: cataloging-base-service
    entrypoint: /usr/src/app/scripts/gunicorn_start
