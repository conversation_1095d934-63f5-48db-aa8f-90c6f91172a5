version: "2.1"

services:
    cataloging-service:
     ports:
      - "${CATALOGING_SERVICE_HOST_PORT}:8001"
     volumes:
      - "${HOST_STATIC_ROOT}:/var/www/static/"
      - "${HOST_DOC_ROOT}:/usr/src/doc"
     container_name: "cataloging_app_service"
     extends:
      file: base-docker-compose.yml
      service: cataloging-base-service
     entrypoint: /usr/src/scripts/gunicorn_start
     
    rmq:
     extends:
      file: infra-compose.yml
      service: rmq
 

    db:
     extends:
      file: infra-compose.yml
      service: db
      
    redis:
     extends:
      file: infra-compose.yml
      service: redis

