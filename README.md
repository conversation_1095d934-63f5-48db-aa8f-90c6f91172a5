# README #

One source of truth for Treebo Cataloging needs.  
The project has been developed using Python3 and Flask.  

### Who do I talk to? ###

* <PERSON>hashank Rao  
* Vidit Sinha  

### Development Setup ###
* Install _postgres_ database  
```
    $ brew install postgres  
    $ brew services start postgresql  
```
* Install _redis_ cache  
```
    $ brew install redis  
    $ brew services start redis  
```
* Create the cataloging-service database and user    
```
    $ createdb cataloging_service  
    $ createuser treeboadmin -W #set password as 'treeboadmin' when prompted
```
* Install _virtualenvwrapper_ and create a python3 virtual environment for cataloging-service  
```  
    $ pip install virtualenvwrapper  
    $ mkvirtualenv -p python3 cataloging-service  
```
* Install the required python packages  
```  
    $ pip install -r requirements.txt  
```
* Apply database migrations  
```  
    $ python manage.py db upgrade  
```
* Run server  
```  
    $ python manage.py runserver  
```
* Cataloging service will now be running at 127.0.0.1:5000  
* Access the health check api - _127.0.0.1:5000/cataloging-service/ping_ to confirm if the server is working  
* Admin can be accessed through _127.0.0.1:5000/cataloging-service/admin_  

### Docker Based Setup ###

Following commands should be usefull for (first time) setup

Create a local docker network:
```            
fab localhost create_network:network="nw_cs"
```

Kill & Remove any existing docker container (Please note that this will also destroy postgres container)
```
fab stop_and_remove_all_containers
```

Create a container from postgres image:
```            
fab localhost setup_postgres:network='nw_cs',postgres_user='treeboadmin',postgres_pwd='treeboadmin',postgres_db=cataloging_service
```   

See that this container is available in the network (nw_cs) that you created
```         
docker network inspect nw_cs
```

Deploy the appropriate cataloging-service image (Do just this, if all you want is to upgrade the CS binaries)
```           
fab localhost deploy:tag_name='cataloging-service',version='v1.0',port='8001',network='nw_cs'
```

Check the docker containers deployed (You should see 2 of them running)
```            
docker ps
```

Just in case you wanted to see the logs of a container:
```            
docker logs cataloging_service_gunicorn
```

