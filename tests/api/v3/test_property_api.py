"""
This requires db since most of the API functionality speaks directly to db.
Mocking db and pagination proved worthless and cause complicated UTs
"""
import json

import pytest

from cataloging_service.app import create_app


@pytest.fixture(scope='module')
def app():
    # os.environ['APP_ENV'] = 'testing'
    return create_app()


class Constants:
    """ Requires atleast one record in db with exact name """
    id = '0016932'
    exact_name = 'Treebo 12792'


def test_v3_api_with_no_parameters(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/')
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert data['data'][0]


def test_v3_api_with_valid_id_filter(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties?id={id}'.format(id=Constants.id))
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert data['data'][0]['id'] == Constants.id


def test_v3_api_with_invalid_id_filter(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties?id=00')
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert not data['data']


def test_v3_api_with_invalid_status_filter(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties?status=invalid_status')
        assert rv.status_code == 400
        data = json.loads(rv.get_data(as_text=True))
        assert not data['data']
        assert 'ValidationError' in data['errors'][0]['type']


def test_v3_api_with_valid_fields(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties?fields=id')
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert data['data'][0]['id']
        data['data'][0].pop('id')
        assert data['data'][0] == {}  # after popping id the data should be empty since we are only asking id


def test_v3_api_with_invalid_fields(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties?fields=ida')
        assert rv.status_code == 500
        data = json.loads(rv.get_data(as_text=True))
        assert not data['data']
        assert 'AttributeError' in data['errors'][0]['type']


def test_v3_api_with_valid_multiple_fields(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties?fields=id,name')
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert data['data'][0]['id']
        assert data['data'][0]['name']
        data['data'][0].pop('id')
        data['data'][0].pop('name')
        assert data['data'][0] == {}


def test_v3_api_with_valid_nested_fields(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties?fields=location.address,id')
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert data['data'][0]['id']
        assert data['data'][0]['location']
        data['data'][0].pop('id')
        data['data'][0].pop('location')
        assert data['data'][0] == {}


def test_v3_api_with_valid_page_number(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?page=1')
        assert rv.status_code == 200


def test_v3_api_with_invalid_page_number(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?page=99999')
        assert rv.status_code == 400


def test_v3_api_with_valid_per_page_1(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?per_page=2')
        assert rv.status_code == 200


def test_v3_api_with_valid_per_page_2(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?per_page=200')
        assert rv.status_code == 200


def test_v3_api_with_invalid_per_page_1(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?per_page=0')
        assert rv.status_code == 200


def test_v3_api_with_invalid_per_page_2(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?per_page=201')
        assert rv.status_code == 400


def test_v3_api_with_invalid_per_page_3(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?per_page=99999')
        assert rv.status_code == 400


def test_v3_api_with_valid_name(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?name=test')
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert len(data['data']) > 1


def test_v3_api_with_invalid_name(app):
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?name=01010101')
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert not data['data']


def test_v3_api_with_exact_name(app):
    # If there is an exact match should yield only one result
    with app.test_client() as c:
        rv = c.get('/api/v3/properties/?name={n}'.format(n=Constants.exact_name))
        assert rv.status_code == 200
        data = json.loads(rv.get_data(as_text=True))
        assert len(data['data']) == 1
