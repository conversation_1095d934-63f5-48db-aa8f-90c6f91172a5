from unittest.case import TestCase
from unittest.mock import MagicMock

from cataloging_service.exceptions import CatalogingServiceException


class CatalogingServiceTestCase(TestCase):
    def assert_cataloging_service_exception(self, exception, error_code):
        self.assertEqual(type(exception), CatalogingServiceException)
        self.assertEqual(exception.error_details['code'], error_code)

    def get_mock_context_manager(self):
        cm = MagicMock()
        cm.__enter__.return_value = cm
        return cm
