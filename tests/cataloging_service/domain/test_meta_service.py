from unittest.mock import Mock, patch

from cataloging_service.constants import model_choices
from cataloging_service.domain.meta_service import MetaService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure.repositories import MetaRepository
from cataloging_service.models import <PERSON><PERSON><PERSON><PERSON>, GuestType, Param
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestMetaService(CatalogingServiceTestCase):
    def setUp(self):
        self.meta_repository = MetaRepository()
        self.meta_service = MetaService(self.meta_repository)

    @patch('flask_sqlalchemy._QueryProperty.__get__')
    def test_get_cuisines(self, query_mock):
        mock_cuisines = self.get_mock_cuisines()
        query_mock.return_value.all.return_value = mock_cuisines
        cuisines = self.meta_service.get_cuisines()

        self.assertEqual(mock_cuisines[0].name, cuisines[0].name)

    @patch('flask_sqlalchemy._QueryProperty.__get__')
    def test_get_guest_types(self, query_mock):
        mock_guest_types = self.get_mock_guest_types()
        query_mock.return_value.all.return_value = mock_guest_types
        guest_types = self.meta_service.get_guest_types()

        self.assertEqual(mock_guest_types[0].type, guest_types[0].type)

    def test_get_meta_dictionary(self):
        mock_dictionary = model_choices.get_choices()
        meta_dictionary = self.meta_service.get_meta_dictionary()

        self.assertEqual(mock_dictionary, meta_dictionary)

    def test_get_all_entities(self):
        all_entities = self.meta_service.sget_all_entities()
        self.assertTrue(len(all_entities), 78)

    @patch('flask_sqlalchemy._QueryProperty.__get__')
    def test_sget_all_entity_params(self, query_mock):
        mock_param = [self.get_mock_param('Sku', 'bundle_rule', 'a+b')]
        query_mock.return_value.filter_by.return_value.all.return_value = mock_param

        params = self.meta_service.sget_entity_params('Sku', 'bundle_rule', True)
        self.assertTrue(params[0].entity, mock_param[0].entity)

    @patch('flask_sqlalchemy._QueryProperty.__get__')
    def test_get_bundle_rule_when_rule_defined(self, query_mock):
        params = [self.get_mock_param('Sku', 'bundle_rule', 'a+b')]
        query_mock.return_value.filter_by.return_value.all.return_value = params
        value = self.meta_service.sget_bundle_rule()
        self.assertTrue(value, params[0].value)

    @patch('flask_sqlalchemy._QueryProperty.__get__')
    def test_get_bundle_rule_when_rule_not_defined(self, query_mock):
        query_mock.return_value.filter_by.return_value.all.return_value = None
        with self.assertRaises(CatalogingServiceException):
            self.meta_service.sget_bundle_rule()

    def get_mock_param(self, entity, field, value):
        param = Param()
        param.entity = entity
        param.field = field
        param.value = value
        param.validate = True
        return param

    def get_mock_cuisines(self):
        cuisine = Cuisine()
        cuisine.name='biriyani'

        return [cuisine]

    def get_mock_guest_types(self):
        guest_type = GuestType()
        guest_type.type = 'Eunuchs'

        return [guest_type]
