import datetime
from unittest import mock
from unittest.mock import patch, Mock

from cataloging_service.api.request_objects import PropertyRequest
from cataloging_service.client import AmazonS3Client
from cataloging_service.constants import error_codes, model_choices, constants
from cataloging_service.constants.model_choices import PropertyChoices, PropertyDetailChoices, StandardStatusChoices
from cataloging_service.domain import MetaService
from cataloging_service.domain.property_service import PropertyService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure import decorators
from cataloging_service.models import GuestType, Property, PropertyDetail, BankDetail, Owner, \
    City, Location, Room, RoomTypeConfiguration, RoomType, Description, NeighbouringPlace, MicroMarket, Locality, \
    GuestFacingProcess, Provider, Param, SkuActivation, Sku, PropertySku
from cataloging_service.models import Notification
from cataloging_service.utils import Utils
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestPropertyService(CatalogingServiceTestCase):
    def setUp(self):
        self.mock_db = patch.object(decorators, 'db', new=Mock())
        self.mock_utils = patch.object(decorators, 'Utils')
        self.mock_db.start()
        self.mock_utils.start()

        self.property_repository = Mock()
        self.location_repository = Mock()
        self.file_repository = Mock()
        self.meta_repository = Mock()
        self.aws_client = Mock()
        self.property_repository.persist.side_effect = lambda x: x
        self.location_repository.persist.side_effect = lambda x: x
        self.file_repository.persist.side_effect = lambda x: x
        self.messaging_service = Mock()
        self.property_service = PropertyService(self.property_repository, self.location_repository,
                                                self.file_repository, self.meta_repository, self.messaging_service,
                                                self.aws_client)

    def tearDown(self):
        self.mock_db.stop()
        self.mock_utils.stop()

    def test_property_creation(self):
        mock_create_property_request = self.get_mock_property_request()
        self.property_repository.get_guest_types.return_value = [self.get_mock_guest_type()]

        self.location_repository.get_city.return_value = self.get_mock_city()
        self.property_repository.get_property.return_value = None

        property_created = self.property_service.create_property(mock_create_property_request)
        self.assertEqual(property_created.old_name, mock_create_property_request.name.old_name)
        self.assertEqual(property_created.name, mock_create_property_request.name.new_name)
        self.assertEqual(property_created.legal_name, mock_create_property_request.name.legal_name)
        self.assertEqual(property_created.hx_id, mock_create_property_request.hx_id)

    def test_property_creation_with_invalid_city(self):
        mock_create_property_request = self.get_mock_property_request()

        self.location_repository.get_city.return_value = None

        try:
            self.property_service.create_property(mock_create_property_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.CITY_NOT_FOUND)
        else:
            self.fail()

    def test_property_creation_id_collision(self):
        mock_create_property_request = self.get_mock_property_request()

        self.location_repository.get_city.return_value = self.get_mock_city()
        self.property_repository.get_property.return_value = self.get_mock_property_object()

        try:
            self.property_service.create_property(mock_create_property_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.PROPERTY_ID_CLASHED)
        else:
            self.fail()

    def test_property_updation(self):
        modify_property_request = self.get_mock_property_request()
        mock_property = self.get_mock_property_object_for_updation()
        self.property_repository.get_guest_types.return_value = [self.get_mock_guest_type()]

        property_updated = self.property_service.update_property(mock_property, modify_property_request)
        self.assertEqual(property_updated.old_name, modify_property_request.name.old_name)
        self.assertEqual(property_updated.name, modify_property_request.name.new_name)
        self.assertEqual(property_updated.legal_name, modify_property_request.name.legal_name)
        self.assertEqual(property_updated.hx_id, modify_property_request.hx_id)

    def test_new_property_updation(self):
        modify_property_request = self.get_mock_property_request()
        mock_property = self.get_mock_property_object()
        self.property_repository.get_guest_types.return_value = [self.get_mock_guest_type()]

        property_updated = self.property_service.update_property(mock_property, modify_property_request)
        self.assertEqual(property_updated.old_name, modify_property_request.name.old_name)
        self.assertEqual(property_updated.name, modify_property_request.name.new_name)
        self.assertEqual(property_updated.legal_name, modify_property_request.name.legal_name)
        self.assertEqual(property_updated.hx_id, modify_property_request.hx_id)

    def test_property_location_creation(self):
        mock_property_object = self.get_mock_property_object()
        create_property_request = self.get_mock_property_request()
        location_created = self.property_service._create_property_location(mock_property_object,
                                                                           create_property_request.location)

        self.assertEqual(location_created.property_id, mock_property_object.id)
        self.assertEqual(location_created.city_id, create_property_request.location.city_id)
        self.assertEqual(location_created.latitude, create_property_request.location.latitude)
        self.assertEqual(location_created.longitude, create_property_request.location.longitude)
        self.assertEqual(location_created.pincode, create_property_request.location.pincode)
        self.assertEqual(location_created.postal_address, create_property_request.location.postal_address)
        self.assertEqual(location_created.maps_link, create_property_request.location.maps_link)
        self.assertEqual(location_created.micro_market_id, create_property_request.location.micro_market_id)
        self.assertEqual(location_created.locality_id, create_property_request.location.locality_id)

    def test_property_location_updation(self):
        update_location_request = self.get_mock_property_request()
        location_updated = self.property_service._update_property_location(Mock(),
                                                                           update_location_request.location)

        self.assertEqual(location_updated.city_id, update_location_request.location.city_id)
        self.assertEqual(location_updated.latitude, update_location_request.location.latitude)
        self.assertEqual(location_updated.longitude, update_location_request.location.longitude)
        self.assertEqual(location_updated.pincode, update_location_request.location.pincode)
        self.assertEqual(location_updated.postal_address, update_location_request.location.postal_address)
        self.assertEqual(location_updated.maps_link, update_location_request.location.maps_link)
        self.assertEqual(location_updated.micro_market_id, update_location_request.location.micro_market_id)
        self.assertEqual(location_updated.locality_id, update_location_request.location.locality_id)

    def test_property_details_creation(self):
        mock_property_object = self.get_mock_property_object()
        property_details_request = self.get_mock_property_request().property_details

        details_created = self.property_service._create_property_details(mock_property_object, property_details_request)
        self.assertEqual(details_created.property_id, mock_property_object.id)
        self.assertEqual(details_created.neighbourhood_type, property_details_request.neighbourhood_type)
        self.assertEqual(details_created.neighbourhood_detail, property_details_request.neighbourhood_detail)
        self.assertEqual(details_created.property_type, property_details_request.property_type)
        self.assertEqual(details_created.property_style, property_details_request.style)
        self.assertEqual(details_created.style_detail, property_details_request.style_detail)
        self.assertEqual(details_created.construction_year, property_details_request.construction_year)
        self.assertEqual(details_created.building_style, property_details_request.building_type)
        self.assertEqual(details_created.floor_count, property_details_request.floor_count)
        self.assertEqual(details_created.star_rating, property_details_request.star_rating)
        self.assertEqual(details_created.previous_franchise, property_details_request.previously_different_franchise)
        self.assertEqual(details_created.reception_landline, property_details_request.reception_landline)
        self.assertEqual(details_created.reception_mobile, property_details_request.reception_mobile)

    def test_property_details_updation(self):
        property_details_request = self.get_mock_property_request().property_details

        details_updated = self.property_service._update_property_details(Mock(), property_details_request)
        self.assertEqual(details_updated.neighbourhood_type, property_details_request.neighbourhood_type)
        self.assertEqual(details_updated.neighbourhood_detail, property_details_request.neighbourhood_detail)
        self.assertEqual(details_updated.property_type, property_details_request.property_type)
        self.assertEqual(details_updated.property_style, property_details_request.style)
        self.assertEqual(details_updated.style_detail, property_details_request.style_detail)
        self.assertEqual(details_updated.construction_year, property_details_request.construction_year)
        self.assertEqual(details_updated.building_style, property_details_request.building_type)
        self.assertEqual(details_updated.floor_count, property_details_request.floor_count)
        self.assertEqual(details_updated.star_rating, property_details_request.star_rating)
        self.assertEqual(details_updated.previous_franchise, property_details_request.previously_different_franchise)
        self.assertEqual(details_updated.reception_landline, property_details_request.reception_landline)
        self.assertEqual(details_updated.reception_mobile, property_details_request.reception_mobile)

    def test_bank_detail_creation(self):
        mock_detail_object = self.get_mock_property_detail_object()
        bank_details_request = self.get_mock_property_request().property_details.bank_details

        bank_details = self.property_service._create_property_bank(mock_detail_object, bank_details_request)

        self.assertEqual(bank_details.account_name, bank_details_request.account_name)
        self.assertEqual(bank_details.account_number, bank_details_request.account_number)
        self.assertEqual(bank_details.ifsc_code, bank_details_request.ifsc_code)
        self.assertEqual(bank_details.bank, bank_details_request.bank)
        self.assertEqual(bank_details.branch, bank_details_request.branch)
        self.assertEqual(bank_details.account_type, bank_details_request.type)
        self.assertEqual(bank_details.property_detail, mock_detail_object)

    def test_bank_detail_updation(self):
        bank_details_request = self.get_mock_property_request().property_details.bank_details

        bank_details = self.property_service._update_property_bank(Mock(), bank_details_request)

        self.assertEqual(bank_details.account_name, bank_details_request.account_name)
        self.assertEqual(bank_details.account_number, bank_details_request.account_number)
        self.assertEqual(bank_details.ifsc_code, bank_details_request.ifsc_code)
        self.assertEqual(bank_details.bank, bank_details_request.bank)
        self.assertEqual(bank_details.branch, bank_details_request.branch)
        self.assertEqual(bank_details.account_type, bank_details_request.type)

    def test_guest_facing_detail_creation(self):
        mock_property_object = self.get_mock_property_object()
        property_details_request = self.get_mock_property_request().guest_facing_details

        guest_details = self.property_service._create_guest_facing_details(mock_property_object,
                                                                           property_details_request)

        self.assertEqual(guest_details.property_id, mock_property_object.id)
        self.assertEqual(guest_details.checkin_time, property_details_request.checkin_time)
        self.assertEqual(guest_details.free_early_checkin, property_details_request.free_early_checkin_time)
        self.assertEqual(guest_details.checkout_time, property_details_request.checkout_time)
        self.assertEqual(guest_details.free_late_checkout, property_details_request.free_late_checkout_time)
        self.assertEqual(guest_details.early_checkin_fee, property_details_request.early_checkin_fee)
        self.assertEqual(guest_details.late_checkout_fee, property_details_request.late_checkout_fee)

    def test_guest_facing_detail_updation(self):
        guest_facing_details_request = self.get_mock_property_request().guest_facing_details

        guest_details = self.property_service._update_guest_facing_details(Mock(),
                                                                           guest_facing_details_request)

        self.assertEqual(guest_details.checkin_time, guest_facing_details_request.checkin_time)
        self.assertEqual(guest_details.free_early_checkin, guest_facing_details_request.free_early_checkin_time)
        self.assertEqual(guest_details.checkout_time, guest_facing_details_request.checkout_time)
        self.assertEqual(guest_details.free_late_checkout, guest_facing_details_request.free_late_checkout_time)
        self.assertEqual(guest_details.early_checkin_fee, guest_facing_details_request.early_checkin_fee)
        self.assertEqual(guest_details.late_checkout_fee, guest_facing_details_request.late_checkout_fee)

    def test_owner_creation(self):
        mock_property_object = self.get_mock_property_object()
        owner_request = self.get_mock_property_request().owners[0]

        owner_created, owner_assoc = self.property_service._create_property_owner(mock_property_object, owner_request)

        self.assertEqual(owner_created.first_name, owner_request.first_name)
        self.assertEqual(owner_created.middle_name, owner_request.middle_name)
        self.assertEqual(owner_created.last_name, owner_request.last_name)
        self.assertEqual(owner_created.gender, owner_request.gender)
        self.assertEqual(owner_created.phone_number, owner_request.phone_number)
        self.assertEqual(owner_created.date_of_birth, owner_request.dob)
        self.assertEqual(owner_created.email, owner_request.email)
        self.assertEqual(owner_created.occupation, owner_request.occupation)
        self.assertEqual(owner_created.education, owner_request.education)

        self.assertEqual(owner_assoc.property_id, mock_property_object.id)
        self.assertEqual(owner_assoc.primary, owner_request.is_primary_owner)

    def test_owner_creation_without_phone(self):
        mock_property_object = self.get_mock_property_object()
        owner_request = self.get_mock_property_request().owners[0]
        owner_request.phone_number = None

        try:
            self.property_service._create_property_owner(mock_property_object, owner_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.PHONE_REQUIRED_FOR_PRIMARY_OWNER)
        else:
            self.fail()

    def test_owner_creation_without_email(self):
        mock_property_object = self.get_mock_property_object()
        owner_request = self.get_mock_property_request().owners[0]
        owner_request.email = None

        try:
            self.property_service._create_property_owner(mock_property_object, owner_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.EMAIL_REQUIRED_FOR_PRIMARY_OWNER)
        else:
            self.fail()

    def test_landmark_creation(self):
        mock_property_object = self.get_mock_property_object()
        landmark_request = self.get_mock_property_request().landmarks[0]

        landmark = self.property_service._create_landmark(mock_property_object, landmark_request)

        self.assertEqual(landmark.property_id, mock_property_object.id)
        self.assertEqual(landmark.type, landmark_request.type)
        self.assertEqual(landmark.name, landmark_request.name)
        self.assertEqual(landmark.latitude, landmark_request.latitude)
        self.assertEqual(landmark.longitude, landmark_request.longitude)
        self.assertEqual(landmark.distance_from_property, landmark_request.hotel_distance)
        self.assertEqual(landmark.property_direction, landmark_request.hotel_direction)
        self.assertEqual(landmark.hatchback_cab_fare, landmark_request.hatchback_cab_fare)
        self.assertEqual(landmark.sedan_cab_fare, landmark_request.sedan_cab_fare)

    def test_description_creation(self):
        mock_property_object = self.get_mock_property_object()
        description_request = self.get_mock_property_request().description

        description = self.property_service._create_description(mock_property_object, description_request)
        self.assertEqual(description.property_id, mock_property_object.id)
        self.assertEqual(description.property_description, description_request.property_description)
        self.assertEqual(description.acacia_description, description_request.acacia_description)
        self.assertEqual(description.oak_description, description_request.oak_description)
        self.assertEqual(description.maple_description, description_request.maple_description)
        self.assertEqual(description.mahogany_description, description_request.mahogany_description)
        self.assertEqual(description.trilight_one, description_request.trilight_one)
        self.assertEqual(description.trilight_two, description_request.trilight_two)
        self.assertEqual(description.trilight_three, description_request.trilight_three)

    def test_description_updation(self):
        description_request = self.get_mock_property_request().description

        description = self.property_service._update_description(Mock(), description_request)
        self.assertEqual(description.property_description, description_request.property_description)
        self.assertEqual(description.acacia_description, description_request.acacia_description)
        self.assertEqual(description.oak_description, description_request.oak_description)
        self.assertEqual(description.maple_description, description_request.maple_description)
        self.assertEqual(description.mahogany_description, description_request.mahogany_description)
        self.assertEqual(description.trilight_one, description_request.trilight_one)
        self.assertEqual(description.trilight_two, description_request.trilight_two)
        self.assertEqual(description.trilight_three, description_request.trilight_three)

    def test_neighbourhood_creation(self):
        mock_property_object = self.get_mock_property_object()
        neighbourhood_request = self.get_mock_property_request().neighbouring_places

        neighbourhood = self.property_service._create_neighbourhood(mock_property_object, neighbourhood_request)
        self.assertEqual(neighbourhood.property_id, mock_property_object.id)
        self.assertEqual(neighbourhood.nearest_hospital, neighbourhood_request.nearest_hospital)
        self.assertEqual(neighbourhood.utility_shops, neighbourhood_request.utility_shops)
        self.assertEqual(neighbourhood.restaurants, neighbourhood_request.restaurants)
        self.assertEqual(neighbourhood.tourist_spots, neighbourhood_request.tourist_spots)
        self.assertEqual(neighbourhood.corporate_offices, neighbourhood_request.corporate_offices)
        self.assertEqual(neighbourhood.popular_malls, neighbourhood_request.popular_malls)
        self.assertEqual(neighbourhood.shopping_streets, neighbourhood_request.shopping_streets)
        self.assertEqual(neighbourhood.city_centre, neighbourhood_request.city_centre)

    def test_neighbourhood_updation(self):
        neighbourhood_request = self.get_mock_property_request().neighbouring_places

        neighbourhood = self.property_service._create_neighbourhood(Mock(), neighbourhood_request)
        self.assertEqual(neighbourhood.nearest_hospital, neighbourhood_request.nearest_hospital)
        self.assertEqual(neighbourhood.utility_shops, neighbourhood_request.utility_shops)
        self.assertEqual(neighbourhood.restaurants, neighbourhood_request.restaurants)
        self.assertEqual(neighbourhood.tourist_spots, neighbourhood_request.tourist_spots)
        self.assertEqual(neighbourhood.corporate_offices, neighbourhood_request.corporate_offices)
        self.assertEqual(neighbourhood.popular_malls, neighbourhood_request.popular_malls)
        self.assertEqual(neighbourhood.shopping_streets, neighbourhood_request.shopping_streets)
        self.assertEqual(neighbourhood.city_centre, neighbourhood_request.city_centre)

    def test_check_property_sign_status_for_wrong_status(self):
        mock_property_object = self.get_mock_property_object()
        mock_property_object.status = model_choices.PropertyChoices.STATUS_NOT_SIGNING
        sign = self.property_service.can_property_be_signed(mock_property_object)

        self.assertFalse(sign)

    def test_check_property_sign_status_without_property_detail(self):
        mock_property_object = self.get_mock_property_object()
        mock_property_object.status = model_choices.PropertyChoices.STATUS_NEAR_CONFIRMED
        sign = self.property_service.can_property_be_signed(mock_property_object)

        self.assertFalse(sign)

    def test_check_property_sign_status_without_bank_detail(self):
        mock_property_object = self.get_mock_property_object()
        mock_property_object.status = model_choices.PropertyChoices.STATUS_NEAR_CONFIRMED
        mock_property_object.property_detail = PropertyDetail()
        sign = self.property_service.can_property_be_signed(mock_property_object)

        self.assertFalse(sign)

    def test_check_property_sign_status_without_primary_owner(self):
        mock_property_object = self.get_mock_property_object()
        mock_property_object.status = model_choices.PropertyChoices.STATUS_NEAR_CONFIRMED
        mock_property_object.property_detail = PropertyDetail()
        mock_property_object.property_detail.bank_detail = BankDetail()
        self.property_repository.get_property_primary_owners.return_value = list()
        sign = self.property_service.can_property_be_signed(mock_property_object)

        self.assertFalse(sign)

    def test_check_property_sign_status_without_files(self):
        mock_property_object = self.get_mock_property_object()
        mock_property_object.status = model_choices.PropertyChoices.STATUS_NEAR_CONFIRMED
        mock_property_object.property_detail = PropertyDetail()
        mock_property_object.property_detail.bank_detail = BankDetail()
        self.property_repository.get_property_primary_owners.return_value = [Owner()]
        self.file_repository.get_distinct_property_file_types.return_value = set()
        sign = self.property_service.can_property_be_signed(mock_property_object)

        self.assertFalse(sign)

    def test_check_property_sign_status_with_everything(self):
        mock_property_object = self.get_mock_property_object()
        mock_property_object.status = model_choices.PropertyChoices.STATUS_NEAR_CONFIRMED
        mock_property_object.property_detail = PropertyDetail()
        mock_property_object.property_detail.bank_detail = BankDetail()
        self.property_repository.get_property_primary_owners.return_value = [Owner()]
        self.file_repository.get_distinct_property_file_types.return_value = constants.SIGN_FILES
        sign = self.property_service.can_property_be_signed(mock_property_object)

        self.assertTrue(sign)

    def test_signed_date_validation(self):

        mock_property = self.get_mock_property_object()
        mock_property.signed_date = None

        try:
            PropertyService.validate_signed_status_dates(mock_property)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.SIGNED_DATE_REQUIRED)
        else:
            self.fail()

    def test_launched_date_validation(self):
        mock_property = self.get_mock_property_object()
        mock_property.signed_date = datetime.datetime.now().date()

        try:
            PropertyService.validate_launch_status_dates(mock_property)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.CONTRACTUAL_LAUNCH_DATE_REQUIRED)
        else:
            self.fail()

        mock_property.contractual_launch_date = datetime.datetime.now().date()
        try:
            PropertyService.validate_launch_status_dates(mock_property)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.LAUNCH_DATE_REQUIRED)
        else:
            self.fail()

        mock_property.launched_date = datetime.datetime.now().date().replace(year=1000)
        try:
            PropertyService.validate_launch_status_dates(mock_property)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.INVALID_DATE_ORDER)
        else:
            self.fail()

    def test_churned_date_validation(self):
        mock_property = self.get_mock_property_object()
        mock_property.signed_date = datetime.datetime.now().date().replace(year=1000)
        mock_property.launched_date = datetime.datetime.now().date()
        mock_property.contractual_launch_date = datetime.datetime.now().date()

        try:
            PropertyService.validate_churned_dates(mock_property)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.CHURNED_DATE_REQUIRED)
        else:
            self.fail()

        mock_property.churned_date = datetime.datetime.now().date().replace(year=1000)
        try:
            PropertyService.validate_churned_dates(mock_property)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.INVALID_DATE_ORDER)
        else:
            self.fail()

    def test_validate_and_sanitize_property_dates_signed(self):
        mock_property = self.get_mock_property_object()
        mock_property.signed_date = datetime.datetime.now().date().replace(year=1000)
        mock_property.launched_date = datetime.datetime.now().date().replace(year=2000)
        mock_property.contractual_launch_date = datetime.datetime.now().date()
        mock_property.churned_date = datetime.datetime.now().date().replace()
        mock_property.status = PropertyChoices.STATUS_SIGNED

        PropertyService.validate_and_sanitize_property_dates(mock_property)

        self.assertIsNone(mock_property.launched_date)
        self.assertIsNone(mock_property.churned_date)

    def test_validate_and_sanitize_property_dates_confirmed(self):
        mock_property = self.get_mock_property_object()
        mock_property.signed_date = datetime.datetime.now().date().replace(year=1000)
        mock_property.launched_date = datetime.datetime.now().date().replace(year=2000)
        mock_property.contractual_launch_date = datetime.datetime.now().date()
        mock_property.churned_date = datetime.datetime.now().date().replace()
        mock_property.status = PropertyChoices.STATUS_NEAR_CONFIRMED

        PropertyService.validate_and_sanitize_property_dates(mock_property)

        self.assertIsNone(mock_property.launched_date)
        self.assertIsNone(mock_property.churned_date)
        self.assertIsNone(mock_property.signed_date)

    def test_validate_and_sanitize_property_dates_live(self):
        mock_property = self.get_mock_property_object()
        mock_property.signed_date = datetime.datetime.now().date().replace(year=1000)
        mock_property.launched_date = datetime.datetime.now().date().replace(year=2000)
        mock_property.contractual_launch_date = datetime.datetime.now().date()
        mock_property.churned_date = datetime.datetime.now().date().replace()
        mock_property.status = PropertyChoices.STATUS_LIVE

        PropertyService.validate_and_sanitize_property_dates(mock_property)

        self.assertIsNone(mock_property.churned_date)

    def test_validate_and_sanitize_property_dates_churned(self):
        mock_property = self.get_mock_property_object()
        mock_property.signed_date = datetime.datetime.now().date().replace(year=1000)
        mock_property.launched_date = datetime.datetime.now().date().replace(year=2000)
        mock_property.contractual_launch_date = datetime.datetime.now().date()
        mock_property.churned_date = datetime.datetime.now().date().replace()
        mock_property.status = PropertyChoices.STATUS_CHURNED

        PropertyService.validate_and_sanitize_property_dates(mock_property)

        self.assertIsNotNone(mock_property.churned_date)
        self.assertIsNotNone(mock_property.signed_date)
        self.assertIsNotNone(mock_property.contractual_launch_date)
        self.assertIsNotNone(mock_property.launched_date)

    def test_property_object_incomplete(self):
        mock_property = Property()
        status, reason = self.property_service._is_property_object_complete(mock_property)

        self.assertFalse(status)

    def test_property_object_complete(self):
        mock_property = Property()
        mock_property.name = 'Test'
        mock_property.hx_id = '1272'
        mock_property.old_name = 'Test'
        mock_property.legal_name = 'Test'
        mock_property.signed_date = datetime.datetime.now().date()
        mock_property.contractual_launch_date = datetime.datetime.now().date()
        mock_property.launched_date = datetime.datetime.now().date()
        status, reason = self.property_service._is_property_object_complete(mock_property)

        self.assertTrue(status)

    def test_property_detail_incomplete(self):
        mock_property_detail = PropertyDetail()
        status, reason = self.property_service._is_property_detail_complete(mock_property_detail)

        self.assertFalse(status)

    def test_property_detail_complete(self):
        mock_property_detail = PropertyDetail()
        mock_property_detail.construction_year = 2000
        mock_property_detail.floor_count = 10
        mock_property_detail.star_rating = 7
        mock_property_detail.reception_landline = '1234567'
        mock_property_detail.reception_mobile = '1234567'
        mock_property_detail.neighbourhood_type = PropertyDetailChoices.NEIGHBOURHOOD_BUSY_MARKET
        mock_property_detail.property_type = PropertyDetailChoices.PROPERTY_HOMESTAY_COTTAGE_VILLA
        mock_property_detail.property_style = PropertyDetailChoices.PROPERTY_STYLE_GUEST_HOUSE
        mock_property_detail.building_style = PropertyDetailChoices.BUILDING_FLOORS_IN_EACH_BUILDING
        status, reason = self.property_service._is_property_detail_complete(mock_property_detail)

        self.assertTrue(status)

    def test_property_location_incomplete(self):
        mock_property_location = Location()
        status, reason = self.property_service._is_property_location_complete(mock_property_location)

        self.assertFalse(status)

    def test_property_location_complete(self):
        mock_property_location = Location()
        mock_property_location.latitude = '12.222222'
        mock_property_location.longitude = '12.222222'
        mock_property_location.pincode = '123'
        mock_property_location.postal_address = 'Address'
        mock_property_location.maps_link = 'ww.google.com'
        mock_property_location.micro_market = MicroMarket()
        mock_property_location.locality = Locality()
        status, reason = self.property_service._is_property_location_complete(mock_property_location)

        self.assertTrue(status)

    def test_incomplete_room(self):
        mock_property = Property()
        mock_property.rooms = [Room()]
        status, reason = self.property_service._is_property_room_information_complete(mock_property)

        self.assertFalse(status)

    def test_not_enough_rooms(self):
        oak = RoomType()
        oak.type = 'OAK'
        mock_property = Property()
        room = Room()
        room.building_number = '1'
        room.floor_number = 1
        room.size = '200 sq. ft'
        room.room_type = oak
        mock_property.rooms = [room]
        status, reason = self.property_service._is_property_room_information_complete(mock_property)

        self.assertFalse(status)

    def test_incomplete_room_config(self):
        oak = RoomType()
        oak.type = 'OAK'

        accacia = RoomType()
        accacia.type = 'ACCACIA'

        mock_property = Property()
        room = Room()
        room.building_number = '1'
        room.floor_number = 1
        room.size = '200 sq. ft'
        room.room_type = oak
        mock_property.rooms = [room] * 10

        config = RoomTypeConfiguration()
        mock_property.room_type_configurations = [config]
        status, reason = self.property_service._is_property_room_information_complete(mock_property)

        self.assertFalse(status)

    def test_room_config_not_present(self):
        oak = RoomType()
        oak.type = 'OAK'

        accacia = RoomType()
        accacia.type = 'ACCACIA'

        mock_property = Property()
        room = Room()
        room.building_number = '1'
        room.floor_number = 1
        room.size = '200 sq. ft'
        room.room_type = oak
        mock_property.rooms = [room] * 10

        config = RoomTypeConfiguration()
        config.max_occupancy = 2
        config.adults = 2
        config.mm_id = 12
        config.room_type = accacia
        mock_property.room_type_configurations = [config]
        status, reason = self.property_service._is_property_room_information_complete(mock_property)

        self.assertFalse(status)

    def test_room_details_complete(self):
        oak = RoomType()
        oak.type = 'OAK'

        mock_property = Property()
        room = Room()
        room.building_number = '1'
        room.floor_number = 1
        room.size = '200 sq. ft'
        room.room_type = oak
        mock_property.rooms = [room] * 10

        config = RoomTypeConfiguration()
        config.max_occupancy = 2
        config.adults = 2
        config.mm_id = 12
        config.room_type = oak
        config.children = 3
        config.max_total = 4
        mock_property.room_type_configurations = [config]
        status, reason = self.property_service._is_property_room_information_complete(mock_property)

        self.assertTrue(status)

    def test_incomplete_guest_details(self):
        status, reason = self.property_service._is_guest_facing_details_complete(None)

        self.assertFalse(status)

    def test_complete_guest_details(self):
        status, reason = self.property_service._is_guest_facing_details_complete(GuestFacingProcess())

        self.assertTrue(status)

    def test_complete_guest_type_incomplete(self):
        status, reason = self.property_service._is_guest_type_complete(None)

        self.assertFalse(status)

    def test_complete_guest_types(self):
        status, reason = self.property_service._is_guest_type_complete([GuestType()])

        self.assertTrue(status)

    def test_incomplete_description(self):
        mock_description = Description()
        status, reason = self.property_service._is_property_description_complete(mock_description)

        self.assertFalse(status)

    def test_complete_description(self):
        mock_description = Description()
        mock_description.property_description = 'Property'
        mock_description.acacia_description = 'Accacia'
        mock_description.oak_description = 'Oak'
        mock_description.maple_description = 'Maple'
        mock_description.mahogany_description = 'Mahogany'
        mock_description.trilight_one = 'T1'
        mock_description.trilight_two = 'T2'
        mock_description.trilight_three = 'T3'
        status, reason = self.property_service._is_property_description_complete(mock_description)

        self.assertTrue(status)

    def test_incomplete_neighbourhood(self):
        mock_neighbourhood = NeighbouringPlace()
        status, reason = self.property_service._is_property_neighbourhood_complete(mock_neighbourhood)

        self.assertFalse(status)

    def test_complete_neighbourhood(self):
        mock_neighbourhood = NeighbouringPlace()
        mock_neighbourhood.nearest_hospital = 'Hospital'
        mock_neighbourhood.utility_shops = 'Shops'
        mock_neighbourhood.restaurants = 'Restaurants'
        mock_neighbourhood.tourist_spots = 'Tourist Spots'
        mock_neighbourhood.corporate_offices = 'Offices'
        mock_neighbourhood.popular_malls = 'Malls'
        mock_neighbourhood.shopping_streets = 'Shopping'
        mock_neighbourhood.city_centre = 'City Centre'
        status, reason = self.property_service._is_property_neighbourhood_complete(mock_neighbourhood)

        self.assertTrue(status)

    def test_incomplete_files_uploaded(self):
        mock_property = Property()
        mock_property.property_detail = PropertyDetail()
        mock_property.property_detail.previous_franchise = True
        self.file_repository.get_distinct_property_file_types.return_value = set()

        status, reason = self.property_service._are_launch_files_uploaded(mock_property)
        self.assertFalse(status)

    def test_complete_files_uploaded(self):
        mock_property = Property()
        mock_property.property_detail = PropertyDetail()
        mock_property.property_detail.previous_franchise = True
        launch_files = set()
        launch_files.update(constants.LAUNCH_FILES)
        launch_files.add(model_choices.GoogleDriveFileTypeChoices.FILE_TYPE_OLD_FRANCHISE_TERMINATION_DOCUMENT)
        self.file_repository.get_distinct_property_file_types.return_value = launch_files

        status, reason = self.property_service._are_launch_files_uploaded(mock_property)
        self.assertTrue(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_property_detail_incomplete(self, property_object_complete,
    #                                                                property_detail_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_location_incomplete(self, property_object_complete,
    #                                                         property_detail_complete, location_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (True, '')
    #     location_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_room_information_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_room_incomplete(self, property_object_complete,
    #                                                     property_detail_complete, location_complete, room_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (True, '')
    #     location_complete.return_value = (True, '')
    #     room_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_facing_details_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_room_information_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_guest_incomplete(self, property_object_complete,
    #                                                      property_detail_complete, location_complete, room_complete,
    #                                                      guest_facing_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (True, '')
    #     location_complete.return_value = (True, '')
    #     room_complete.return_value = (True, '')
    #     guest_facing_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_type_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_facing_details_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_room_information_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_guest_type_incomplete(self, property_object_complete,
    #                                                           property_detail_complete, location_complete,
    #                                                           room_complete,
    #                                                           guest_facing_complete, guest_type_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (True, '')
    #     location_complete.return_value = (True, '')
    #     room_complete.return_value = (True, '')
    #     guest_facing_complete.return_value = (True, '')
    #     guest_type_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_description_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_type_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_facing_details_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_room_information_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_description_incomplete(self, property_object_complete,
    #                                                            property_detail_complete, location_complete,
    #                                                            room_complete, guest_facing_complete,
    #                                                            guest_type_complete, description_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (True, '')
    #     location_complete.return_value = (True, '')
    #     room_complete.return_value = (True, '')
    #     guest_facing_complete.return_value = (True, '')
    #     guest_type_complete.return_value = (True, '')
    #     description_complete.return_value = (False, '')
    #
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_neighbourhood_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_description_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_type_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_facing_details_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_room_information_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_neighbourhood_incomplete(self, property_object_complete,
    #                                                              property_detail_complete, location_complete,
    #                                                              room_complete, guest_facing_complete,
    #                                                              guest_type_complete, description_complete,
    #                                                              neighbourhood_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (True, '')
    #     location_complete.return_value = (True, '')
    #     room_complete.return_value = (True, '')
    #     guest_facing_complete.return_value = (True, '')
    #     guest_type_complete.return_value = (True, '')
    #     description_complete.return_value = (True, '')
    #     neighbourhood_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._are_launch_files_uploaded')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_neighbourhood_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_description_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_type_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_guest_facing_details_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_room_information_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_file_incomplete(self, property_object_complete,
    #                                                     property_detail_complete, location_complete,
    #                                                     room_complete, guest_facing_complete, guest_type_complete,
    #                                                     description_complete, neighbourhood_complete, file_complete):
    #     property_object_complete.return_value = (True, '')
    #     property_detail_complete.return_value = (True, '')
    #     location_complete.return_value = (True, '')
    #     room_complete.return_value = (True, '')
    #     guest_facing_complete.return_value = (True, '')
    #     guest_type_complete.return_value = (True, '')
    #     description_complete.return_value = (True, '')
    #     neighbourhood_complete.return_value = (True, '')
    #     file_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    @patch('cataloging_service.domain.property_service.PropertyService._are_launch_files_uploaded')
    @patch('cataloging_service.domain.property_service.PropertyService._is_property_neighbourhood_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_property_description_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_guest_type_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_guest_facing_details_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_property_room_information_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_property_location_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_property_detail_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    @patch('cataloging_service.domain.property_service.PropertyService._is_sku_created')
    def test_is_property_not_launchable(self, property_object_complete,
                                        property_detail_complete, location_complete,
                                        room_complete, guest_facing_complete, guest_type_complete,
                                        description_complete, neighbourhood_complete, file_complete, sku_created):
        property_object_complete.return_value = (True, '')
        property_detail_complete.return_value = (True, '')
        location_complete.return_value = (True, '')
        room_complete.return_value = (True, '')
        guest_facing_complete.return_value = (True, '')
        guest_type_complete.return_value = (True, '')
        description_complete.return_value = (True, '')
        neighbourhood_complete.return_value = (True, '')
        file_complete.return_value = (True, '')
        sku_created.return_value = (True, '')
        status, reason = self.property_service.is_property_launchable(Property())

        self.assertTrue(status)

    # @patch('cataloging_service.domain.property_service.PropertyService._is_property_object_complete')
    # def test_is_property_not_launchable_property_incomplete(self, property_object_complete):
    #     property_object_complete.return_value = (False, '')
    #     status, reason = self.property_service.is_property_launchable(Property())
    #
    #     self.assertFalse(status)

    @patch('cataloging_service.domain.property_service.Utils.send_property_status_change_mail')
    @patch('cataloging_service.domain.property_service.Utils.send_recce_mail')
    @patch('cataloging_service.domain.property_service.Utils.send_content_mail')
    def test_sign_property(self, mock_send_content_email, mock_send_recce_mail, mock_send_signed_mail):
        mock_property_object = self.get_mock_property_object()
        self.meta_repository.get_notificaion_object.return_value = self.get_mock_notification_object()
        saved_object = self.property_service.sign_property(mock_property_object)
        self.assertEqual(saved_object.status, model_choices.PropertyChoices.STATUS_SIGNED)
        self.assertIsNotNone(saved_object.signed_date)
        self.assertEqual(mock_send_content_email.call_count, 1)
        self.assertEqual(mock_send_recce_mail.call_count, 1)
        self.assertEqual(mock_send_signed_mail.call_count, 1)

    def test_get_property(self):
        mock_property = self.get_mock_property_object()
        self.property_repository.get_property.return_value = mock_property

        property = self.property_service.get_property(1)
        self.assertEqual(mock_property, property)

    def test_get_property_with_invalid_property_id(self):
        self.property_repository.get_property.return_value = None

        try:
            self.property_service.get_property(1)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.PROPERTY_NOT_FOUND)
        else:
            self.fail()

    def test_get_properties(self):
        mock_property = self.get_mock_property_object()
        self.property_repository.get_all_properties.return_value = [mock_property]

        properties = self.property_service.get_all_properties()
        self.assertEqual([mock_property], properties)

    def test_modified_logic_of_prefix_with_zero_with_valid_length_input_string(self):
        self.assertEqual(['7000000', '600000s', '50000ss', '4000sss', '300ssss', '20sssss', '1ssssss', 'sssssss'],
                         [Utils.prefix_with_zero('s'*n, 7) for n in range(8)])

    def test_modified_logic_of_prefix_with_zero_with_invalid_length_input_string(self):
        with self.assertRaises(ValueError):
            Utils.prefix_with_zero('ssssssss', 7)

    def test_created_name_for_anomalous_name_of_image_file_to_be_uploaded_to_s3(self):
        file_name_1 = " "
        self.assertEqual(AmazonS3Client().refine_file_name_for_s3(file_name_1), "_")
        file_name_2 = None
        with self.assertRaises(TypeError):
            AmazonS3Client().refine_file_name_for_s3(file_name_2)
        file_name_3 = ""
        self.assertEqual(AmazonS3Client().refine_file_name_for_s3(file_name_3), "")
        file_name_4 = "Entrance gate (2).jpg"
        self.assertEqual(AmazonS3Client().refine_file_name_for_s3(file_name_4), "Entrance_gate_2_.jpg")
        file_name_5 = "Reception  (2)-.jpg"
        self.assertEqual(AmazonS3Client().refine_file_name_for_s3(file_name_5), "Reception_2_.jpg")
        file_name_6 = "Reception  (2) .jpg"
        self.assertEqual(AmazonS3Client().refine_file_name_for_s3(file_name_6), "Reception_2_.jpg")
        file_name_7 = "Reception  2 .jpg"
        self.assertEqual(AmazonS3Client().refine_file_name_for_s3(file_name_7), "Reception_2_.jpg")

    def test_get_properties_by_provider_and_photel_codes_with_valid_provider(self):
        provider = self.get_mock_provider()
        mock_property = self.get_mock_property_object()
        self.property_repository.get_properties_by_provider_and_htl_codes.return_value = [mock_property]
        properties = self.property_service.get_properties_by_provider_and_photel_codes(provider, [])
        self.assertEqual([mock_property], properties)

    def test_get_properties_by_provider_and_photel_codes_with_invalid_provider(self):
        mock_property = self.get_mock_property_object()
        self.property_repository.get_properties_by_provider_and_htl_codes.return_value = [mock_property]
        with self.assertRaises(CatalogingServiceException):
            self.property_service.get_properties_by_provider_and_photel_codes(Provider(), [])

    def get_mock_guest_type(self):
        guest_type = GuestType()
        guest_type.id = 1
        guest_type.type = 'Guest Type 1'

        return guest_type

    def get_mock_property_detail_object(self):
        property_detail = PropertyDetail()
        property_detail.id = 1

        return property_detail

    def get_mock_city(self):
        city = City()
        city.id = 1
        city.name = 'Bangalore'

        return city

    def get_mock_property_object(self):
        property = Property()
        property.id = 1

        return property

    def get_mock_provider(self):
        provider = Provider()
        provider.id = 1
        provider.name = 'WANDERTRAIL'
        provider.code = 'prov-wander'
        provider.status = StandardStatusChoices.ACTIVE
        return provider

    def get_mock_property_object_for_updation(self):
        return Mock()

    def get_mock_notification_object(self):
        notification = Notification()
        notification.receivers = '<EMAIL>,<EMAIL>'
        return notification

    def get_mock_property_request(self):
        dictionary = {
            'name': {'legal_name': 'Ruptub Solutions', 'new_name': 'Treebo Elmas', 'old_name': 'Soon to be Treebo'},
            'suited_to': [1, 2],
            'location': {'city_id': 1, 'micro_market_id': 1, 'locality_id': 1, 'latitude': 10.55, 'longitude': 11.35,
                         'maps_link': 'www.google.com', 'pincode': 1231235, 'postal_address': 'HSR Layout'},
            'guest_facing_details': {'checkin_time': '10:00:00', 'free_early_checkin_time': '01:00:00',
                                     'checkout_time': '18:10:00', 'free_late_checkout_time': '18:30:35',
                                     'early_checkin_fee': 'Kuch nahin', 'late_checkout_fee': 'Bohot hai'}, 'owners': [
                {'first_name': 'Tukarm', 'middle_name': '', 'last_name': 'Omble', 'gender': 'M',
                 'email': '<EMAIL>', 'phone_number': '12312312', 'dob': '1983-01-01', 'occupation': 'Hotelier',
                 'education': '10th std', 'is_primary_owner': 'true', 'is_leased': 'true'}], 'landmarks': [
                {'type': 'AIRPORT', 'name': 'KIA', 'latitude': 10.43, 'longitude': 11.45, 'hotel_distance': 12,
                 'hotel_direction': 'west', 'hatchback_cab_fare': '109', 'sedan_cab_fare': '123'}],
            'description': {'property_description': 'Good Hotel', 'acacia_description': 'Good room',
                            'oak_description': 'Better Room', 'maple_description': 'Awesome room',
                            'mahogany_description': 'You cant imagine how good this is!!', 'trilight_one': '1',
                            'trilight_two': '2', 'trilight_three': '3'},
            'neighbouring_places': {'nearest_hospital': 'Victoria Hospital', 'city_centre': 'BDA',
                                    'corporate_offices': 'Treebo', 'utility_shops': 'BDA', 'popular_malls': 'BDA',
                                    'restaurants': 'A2B', 'shopping_streets': 'Brigade Road', 'tourist_spots': 'BDA'},
            'property_details': {
                'bank_details': {'account_name': 'Bank Account', 'account_number': '543215', 'bank': 'axis',
                                 'branch': 'HSR', 'ifsc_code': '43215', 'type': 'SAVINGS'},
                'building_type': 'INDEPENDENT_MULTIPLE', 'construction_year': 2005, 'floor_count': 10,
                'neighbourhood_detail': 'Nothingspecial', 'neighbourhood_type': 'RESIDENTIAL',
                'previously_different_franchise': False, 'property_type': 'HOMESTAY_COTTAGE_VILLA',
                'reception_landline': '1231231', 'reception_mobile': '*********', 'star_rating': 5,
                'style': 'PLAIN_VANILLA', 'style_detail': 'New style'}, 'hx_id': '*********'}

        return PropertyRequest(dictionary)

    def get_mock_param(self, id, entity, field, value):
        param = Param()
        param.id = id
        param.entity = entity
        param.field = field
        param.value = value
        param.validate = True
        return param

    def get_given_mock_sku(self, id, name):
        sku = Sku()
        sku.id = id
        sku.name = name
        sku.code = Utils.slugify(name)
        sku.hsn_sac = 'stay_hsn'
        sku.saleable = True
        sku.is_modular = True
        return sku

    def get_mock_activation_object(self):
        sku_activation = SkuActivation()
        sku_activation.id = 1
        sku_activation.property = self.get_mock_property_object()
        sku_activation.property_id = 1
        sku_activation.sku = self.get_given_mock_sku(1, "OAK")
        sku_activation.sku_id = 1
        sku_activation.param = self.get_mock_param(1, "SkuActivation", "service_id", "ITS")
        sku_activation.service_id = 1
        return sku_activation

    def get_mock_property_sku(self, sku, status, saleable):
        property_sku = PropertySku()
        property_sku.id = 1
        property_sku.property_id = 1
        property_sku.property = self.get_mock_property_object()
        property_sku.sku_id = sku.id
        property_sku.sku = sku
        property_sku.status = status
        property_sku.saleable = saleable
        return property_sku

    def get_meta_repository_rget_entity_param_by_key_val_side_effect(self, *args):
        if args[0] == 'SkuActivation' and args[1] == 'service_id' and args[2] == 'ITS':
            return self.get_mock_param(1, 'SkuActivation', 'service_id', 'ITS')
        elif args[0] == 'SkuActivation' and args[1] == 'service_id' and args[2] == 'PRICING':
            return self.get_mock_param(2, 'SkuActivation', 'service_id', 'PRICING')
        elif args[0] == 'SkuActivation' and args[1] == 'service_id' and args[2] == 'TAXATION':
            return self.get_mock_param(3, 'SkuActivation', 'service_id', 'TAXATION')
        else:
            return None

    @patch('cataloging_service.domain.service_provider')
    def test_update_bundles_and_sku_activation_status_for_invalid_property(self, service_provider):
        self.property_repository.get_property.side_effect = lambda *args: self.get_mock_property_object() \
            if args[0] == 1 else None
        with self.assertRaises(CatalogingServiceException) as error:
            self.property_service.update_bundles_and_sku_activation_status_for_property(2, "ITS", ["oak"])
        self.assertEqual(error.exception.error_details.get('code', None), error_codes.PROPERTY_NOT_FOUND)

    @patch('cataloging_service.domain.service_provider')
    def test_update_bundles_and_sku_activation_status_for_valid_property_and_invalid_service_name(self,
                                                                                                  service_provider):
        self.property_repository.get_property.side_effect = lambda *args: self.get_mock_property_object() \
            if args[0] == 1 else None
        service_provider.meta_service = MetaService(self.meta_repository)
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        with self.assertRaises(CatalogingServiceException) as error:
            self.property_service.update_bundles_and_sku_activation_status_for_property(1, 'ITSS', ["oak"])
        self.assertEqual(error.exception.error_details.get('code', None), error_codes.PARAM_NOT_FOUND)

    @patch('cataloging_service.domain.service_provider')
    def test_update_bundles_and_sku_activation_status_for_valid_property_valid_service_invalid_sku(self,
                                                                                                   service_provider):
        service_provider.meta_service = MetaService(self.meta_repository)
        self.property_repository.get_property.side_effect = lambda *args: self.get_mock_property_object() \
            if args[0] == 1 else None
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        mock_sku = self.get_given_mock_sku(1, "OAK")
        self.property_repository.rget_all_property_skus_by_property_and_sku.side_effect = lambda *args: \
            self.get_mock_property_sku(mock_sku, StandardStatusChoices.ACTIVE, True) \
            if args[0] == 1 and args[1][0] == "oak" else None
        with self.assertRaises(CatalogingServiceException) as error:
            self.property_service.update_bundles_and_sku_activation_status_for_property(1, 'ITS', ["oakk"])
        self.assertEqual(error.exception.error_details.get('code', None), error_codes.PROPERTY_SKU_NOT_FOUND)

    @patch('cataloging_service.domain.service_provider')
    def test_update_bundles_and_sku_activation_status_for_valid_property_valid_service_invalid_sku_params(self,
                                                                                                    service_provider):
        service_provider.meta_service = MetaService(self.meta_repository)
        self.property_repository.get_property.side_effect = lambda *args: self.get_mock_property_object() \
            if args[0] == 1 else None
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        mock_sku = self.get_given_mock_sku(1, "OAK")
        self.property_repository.rget_all_property_skus_by_property_and_sku.side_effect = lambda *args: \
            self.get_mock_property_sku(mock_sku, StandardStatusChoices.ACTIVE, True) \
            if args[0] == 1 and args[1] == ["oak"] else None
        with self.assertRaises(CatalogingServiceException) as error:
            self.property_service.update_bundles_and_sku_activation_status_for_property(1, "ITS", "oak")
        self.assertEqual(error.exception.error_details.get('code', None), error_codes.INVALID_REQUEST_DATA)

    @patch('cataloging_service.domain.service_provider')
    def test_update_bundles_and_sku_activation_status_for_property_service_sku_all_present_with_cs(self,
                                                                                                   service_provider):
        service_provider.meta_service = MetaService(self.meta_repository)
        self.property_repository.get_property.side_effect = lambda *args: self.get_mock_property_object() \
            if args[0] == 1 else None
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        mock_sku = self.get_given_mock_sku(1, "OAK")
        self.property_repository.rget_all_property_skus_by_property_and_sku.side_effect = lambda *args: \
            [self.get_mock_property_sku(mock_sku, StandardStatusChoices.ACTIVE, True)] \
            if args[0] == 1 and args[1] == ["oak"] else None

        self.property_repository.rcheck_if_stock_exists_by_property_and_service.side_effect = lambda *args: \
            True if args[0] == 1 and args[1] == 1 and args[2] == 1 else False

        # mock method call
        self.property_service.activate_property_stock = lambda x: x

        sku_activation = self.property_service.update_bundles_and_sku_activation_status_for_property(1, "ITS", ["oak"])
        self.assertEqual(None, sku_activation)

    @patch('cataloging_service.domain.service_provider')
    def test_update_bundles_and_sku_activation_status_for_property_service_sku_not_present_with_cs(self,
                                                                                                   service_provider):
        service_provider.meta_service = MetaService(self.meta_repository)
        self.property_repository.get_property.side_effect = lambda *args: self.get_mock_property_object() \
            if args[0] == 1 else None
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self \
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        mock_sku = self.get_given_mock_sku(1, "OAK")
        self.property_repository.rget_all_property_skus_by_property_and_sku.side_effect = lambda *args: \
            [self.get_mock_property_sku(mock_sku, StandardStatusChoices.ACTIVE, True)] \
            if args[0] == 1 and args[1] == ["oak"] else None

        self.property_repository.rcheck_if_stock_exists_by_property_and_service.side_effect = lambda *args: \
            False if args[0] == 1 and args[1] == 1 and args[2] == 1 else True

        # mock method call
        self.property_service.activate_property_stock = lambda x: x
        self.property_repository.persist_all.side_effect = lambda *args: args[0]

        sku_activation = self.property_service.update_bundles_and_sku_activation_status_for_property(1, "ITS", ["oak"])
        self.assertEqual(1, len(sku_activation))

    @patch('cataloging_service.domain.service_provider')
    def test_activate_property_stock_with_services_updated_with_property_sku_status_INACTIVE(self, service_provider):

        service_provider.meta_service = MetaService(self.meta_repository)
        mock_param = self.get_mock_param(1, "SkuActivation", "service_id", "ITS")
        self.meta_repository.rget_entity_param.return_value = [mock_param]

        self.property_repository.rcount_of_stock_for_property_and_all_services.return_value = 1

        self.property_repository.persist_all.side_effect = lambda *args: args[0]
        mock_sku = self.get_given_mock_sku(1, "OAK")
        mock_property_sku = self.get_mock_property_sku(mock_sku, StandardStatusChoices.INACTIVE, True)

        property_skus = self.property_service.activate_property_stock([mock_property_sku])
        self.assertEqual(len(property_skus), 1)

    @patch('cataloging_service.domain.service_provider')
    def test_activate_property_stock_with_services_updated_with_property_sku_status_ACTIVE(self, service_provider):

        service_provider.meta_service = MetaService(self.meta_repository)
        mock_param = self.get_mock_param(1, "SkuActivation", "service_id", "ITS")
        self.meta_repository.rget_entity_param.return_value = [mock_param]

        self.property_repository.rcount_of_stock_for_property_and_all_services.return_value = 1

        self.property_repository.persist_all.side_effect = lambda *args: args[0]
        mock_sku = self.get_given_mock_sku(1, "OAK")
        mock_property_sku = self.get_mock_property_sku(mock_sku, StandardStatusChoices.ACTIVE, True)

        property_skus = self.property_service.activate_property_stock([mock_property_sku])
        self.assertEqual(property_skus, None)

    @patch('cataloging_service.domain.service_provider')
    def test_activate_property_stock_with_services_updated_with_partial_update_of_services(self, service_provider):

        service_provider.meta_service = MetaService(self.meta_repository)
        mock_param = self.get_mock_param(1, "SkuActivation", "service_id", "ITS")
        self.meta_repository.rget_entity_param.return_value = [mock_param, mock_param]

        self.property_repository.rcount_of_stock_for_property_and_all_services.return_value = 1

        self.property_repository.persist_all.side_effect = lambda *args: args[0]
        mock_sku = self.get_given_mock_sku(1, "OAK")
        mock_property_sku = self.get_mock_property_sku(mock_sku, StandardStatusChoices.ACTIVE, True)

        property_skus = self.property_service.activate_property_stock([mock_property_sku])
        self.assertEqual(property_skus, None)

    def test_make_given_property_sku_unsaleable(self):
        mock_sku = self.get_given_mock_sku(1, 'OAK')
        mock_property_sku_list = [self.get_mock_property_sku(mock_sku, StandardStatusChoices.INACTIVE, True)]
        self.property_repository.rget_property_sku_by_ids.return_value = mock_property_sku_list
        self.property_service.make_given_property_sku_unsaleable(mock_property_sku_list)
        self.assertFalse(mock_property_sku_list[0].saleable)
