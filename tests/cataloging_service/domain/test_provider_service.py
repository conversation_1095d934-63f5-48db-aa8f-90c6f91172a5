from unittest.mock import Mock, patch

from cataloging_service.constants.model_choices import StandardStatusChoices
from cataloging_service.domain import ProviderService, PropertyService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.models import RatePlan, Provider, ProviderRoomTypeMapping, RoomType, Property, \
    RoomTypeConfiguration
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestProviderService(CatalogingServiceTestCase):
    MOCK_ROOM_TYPE_ID = 1
    MOCK_PROPERTY_ID = 1
    MOCK_ROOM_TYPE_CONFIG_ID = 1

    def setUp(self):
        self.provider_repository = Mock()
        self.provider_service = ProviderService(self.provider_repository)

    def get_mock_property_object(self):
        property = Property()
        property.id = self.MOCK_PROPERTY_ID

        return property

    def get_mock_room_type_config_object(self):
        room_type_config = RoomTypeConfiguration()
        room_type_config.id = self.MOCK_ROOM_TYPE_CONFIG_ID
        room_type_config.property_id = self.MOCK_PROPERTY_ID
        room_type_config.room_type_id = self.MOCK_ROOM_TYPE_ID
        room_type_config.ext_room_name = 'SILVER'
        room_type_config.ext_room_code = 'silver'
        room_type_config.room_type = self.get_mock_room_type_object()

        return room_type_config

    def get_mock_provider(self):
        provider = Provider()
        provider.id = 1
        provider.name = 'WANDERTRAIL'
        provider.code = 'prov-wander'
        provider.rate_plan = self.get_mock_rate_plans()
        provider.status = StandardStatusChoices.ACTIVE
        return provider

    def get_mock_rate_plan(self):
        rate_plan = RatePlan()
        rate_plan.ext_id = 1
        rate_plan.plan = 'PROVIDER-PLAN'
        return rate_plan

    def get_mock_rate_plans(self):
        plans = [self.get_mock_rate_plan()]
        return plans

    def get_mock_room_type_object(self):
        room_type = RoomType()
        room_type.id = self.MOCK_ROOM_TYPE_ID
        room_type.type = 'ACACIA'

        return room_type

    def get_mock_room_mappings(self):
        room_mapping = ProviderRoomTypeMapping()
        room_mapping.provider = self.get_mock_provider()
        room_mapping.room_type = self.get_mock_room_type_object()
        room_mapping.ext_room_code = 'CODE'
        room_mapping.ext_room_name = 'DELUXE'
        return [room_mapping]

    def test_get_provider_with_invalid_code(self):
        with self.assertRaises(CatalogingServiceException):
            self.provider_service.sget_provider('')

    def test_get_provider_with_valid_code(self):
        provider = self.get_mock_provider()
        self.provider_repository.rget_provider_by_code.side_effect = lambda *args: self.get_mock_provider() \
            if args[0] == 'prov-wander' else None
        requested_provider = self.provider_service.sget_provider('prov-wander')
        self.assertEqual(requested_provider.name, provider.name)
        with self.assertRaises(CatalogingServiceException):
            self.provider_service.sget_provider('something')

    def test_get_all_providers(self):
        provider = self.get_mock_provider()
        rate_plans = self.get_mock_rate_plans()
        self.provider_repository.rget_all_providers.return_value = [provider]
        codes = ['prov-wander']
        all_providers = self.provider_service.sget_all_providers(codes)
        self.assertEqual(len(all_providers), len([provider]))
        self.assertEqual(all_providers[0].rate_plan[0].plan, rate_plans[0].plan)

    def test_sget_all_ext_room_configs_property(self):
        provider = self.get_mock_provider()
        mock_room_type_configurations = [self.get_mock_room_type_config_object()]
        self.provider_repository.rget_all_ext_room_configs_property.return_value = mock_room_type_configurations
        expected_mapping = [{'treebo_type': 'ACACIA', 'treebo_code': None, 'name': 'SILVER','code': 'silver'}]
        mappings = self.provider_service.sget_all_ext_room_configs_property(provider)
        self.assertEqual(mappings[0]['treebo_type'], expected_mapping[0]['treebo_type'])

    @patch('cataloging_service.domain.service_provider')
    def test_sget_all_properties_by_provider(self, service_provider):
        property_repository = Mock()
        service_provider.property_service = PropertyService(property_repository, Mock(), Mock(), Mock(), Mock(), Mock())
        provider = self.get_mock_provider()
        self.provider_repository.rget_provider_by_code.return_value = provider
        mock_property = self.get_mock_property_object()
        property_repository.get_properties_by_provider_and_htl_codes.return_value = [mock_property]
        properties = self.provider_service.sget_all_properties_by_provider(provider, [])
        self.assertEqual([mock_property], properties)

