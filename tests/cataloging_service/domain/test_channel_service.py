from unittest.mock import Mock

from cataloging_service.domain import ChannelService, MessagingService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.models import Channel, SubChannel, Application, PricingPolicy
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestChannelService(CatalogingServiceTestCase):

    def setUp(self):
        self.channel_repository = Mock()
        self.mock_publisher = Mock()
        self.messaging_service = MessagingService(self.mock_publisher)
        self.channel_service = ChannelService(self.channel_repository, self.messaging_service)

    @staticmethod
    def get_mock_sub_channels():
        sub_channels = []
        sub_channel = SubChannel()
        sub_channel.id = "tripadvisor"
        sub_channel.name = "TripAdvisor"
        sub_channel.status = "ACTIVE"
        sub_channel.channel_code = "direct"
        sub_channels.append(sub_channel)

        sub_channel = SubChannel()
        sub_channel.id = "trivago"
        sub_channel.name = "Trivago"
        sub_channel.status = "ACTIVE"
        sub_channel.channel_code = "direct"
        sub_channels.append(sub_channel)
        return sub_channels

    @staticmethod
    def get_mock_applications():
        applications = []
        application = Application()
        application.id = "web-app"
        application.name = "Web App"
        application.status = "ACTIVE"
        application.channel_code = "direct"
        applications.append(application)

        application = Application()
        application.id = "android-app"
        application.name = "Android App"
        application.status = "ACTIVE"
        application.channel_code = "direct"
        applications.append(application)
        return applications

    @staticmethod
    def get_mock_channels():
        channels = []
        channel = Channel()
        channel.id = "direct"
        channel.name = "Direct"
        channel.status = "ACTIVE"
        channel.sub_channels = TestChannelService.get_mock_sub_channels()
        channel.applications = TestChannelService.get_mock_applications()
        channels.append(channel)

        channel = Channel()
        channel.id = "ota"
        channel.name = "OTA"
        channel.status = "ACTIVE"
        channels.append(channel)
        channel.sub_channels = []
        return channels

    @staticmethod
    def get_mock_pricing_policies():
        policies = []
        policy = PricingPolicy()
        policy.id = 1
        policy.name = "RP"
        policy.code = "rp"
        policy.status = "ACTIVE"
        policies.append(policy)
        return policies

    def test_get_all_channels(self):
        channels = TestChannelService.get_mock_sub_channels()
        self.channel_repository.get_all_channels.return_value = channels
        all_channels = self.channel_service.get_all_channels()
        self.assertEqual(all_channels, channels)

    def test_get_all_policies(self):
        policies = TestChannelService.get_mock_pricing_policies()
        self.channel_repository.rget_all_pricing_policies.return_value = policies
        all_policies = self.channel_service.sget_all_pricing_policies()
        self.assertEqual(all_policies, policies)

    def test_get_all_channels_by_codes(self):
        channels = TestChannelService.get_mock_sub_channels()
        self.channel_repository.get_all_channels.return_value = channels
        ids = ['direct', 'ota']
        all_channels = self.channel_service.get_all_channels(ids)
        self.assertEqual(all_channels, channels)

    def test_get_channel(self):
        channel = TestChannelService.get_mock_sub_channels()[1]
        self.channel_repository.get_channel.return_value = channel
        requested_channel = self.channel_service.get_channel('ota')
        self.assertEqual(requested_channel, channel)

    def test_get_channel_for_sub_channel(self):
        channel = TestChannelService.get_mock_sub_channels()[0]
        self.channel_repository.get_channel_for_sub_channel.return_value = channel
        requested_channel = self.channel_service.get_channel_for_sub_channel('tripadvisor')
        self.assertEqual(requested_channel, channel)

    def test_get_sub_channels_for_channel(self):
        channel = TestChannelService.get_mock_channels()[0]
        self.channel_repository.get_channel.return_value = channel
        sub_channels = self.channel_service.get_sub_channels('direct')
        self.assertEqual(sub_channels, channel.sub_channels)

    def test_get_channel_with_invalid_code(self):
        channel = TestChannelService.get_mock_sub_channels()[1]
        self.channel_repository.get_channel.side_effect = lambda code: None if code != channel.channel_code else channel
        with self.assertRaises(CatalogingServiceException):
            self.channel_service.get_channel('otaa')

    def test_get_sub_channels_for_invalid_channel(self):
        channel = TestChannelService.get_mock_sub_channels()[0]
        self.channel_repository.get_channel.side_effect = lambda code: None if code != channel.channel_code else channel
        with self.assertRaises(CatalogingServiceException):
            self.channel_service.get_channel('otaa')

    def test_get_all_applications(self):
        applications = TestChannelService.get_mock_applications()
        self.channel_repository.get_all_applications.return_value = applications
        all_applications = self.channel_service.get_all_applications()
        self.assertEqual(all_applications, applications)

    def test_get_all_applications_by_Ids(self):
        applications = TestChannelService.get_mock_applications()
        self.channel_repository.get_all_applications.return_value = applications
        ids = ['web-app', 'android-app']
        all_applications = self.channel_service.get_all_applications(ids)
        self.assertEqual(all_applications, applications)

    def test_get_applications_for_channel(self):
        channel = TestChannelService.get_mock_channels()[0]
        self.channel_repository.get_channel.return_value = channel
        applications = self.channel_service.get_applications('direct')
        self.assertEqual(applications, channel.applications)

    def test_get_applications_for_invalid_channel(self):
        channel = TestChannelService.get_mock_sub_channels()[0]
        self.channel_repository.get_channel.side_effect = lambda code: None if code != channel.channel_code else channel
        with self.assertRaises(CatalogingServiceException):
            self.channel_service.get_channel('otaa')

    def test_publish_channels(self):
        channels = self.get_mock_channels()
        self.channel_repository.get_all_channels.return_value = channels
        self.channel_service.publish_channels([])
        self.assertEqual(self.mock_publisher.publish.call_count, 2)

    def test_publish_sub_channels(self):
        sub_channels = self.get_mock_sub_channels()
        self.channel_repository.get_all_sub_channels.return_value = sub_channels
        self.channel_service.publish_sub_channels([])
        self.assertEqual(self.mock_publisher.publish.call_count, 2)

    def test_publish_applications(self):
        applications = self.get_mock_applications()
        self.channel_repository.get_all_applications.return_value = applications
        self.channel_service.publish_applications([])
        self.assertEqual(self.mock_publisher.publish.call_count, 2)
