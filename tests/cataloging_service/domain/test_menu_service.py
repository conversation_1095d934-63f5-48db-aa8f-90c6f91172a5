from datetime import datetime, <PERSON><PERSON><PERSON>
import json
from unittest import mock
from unittest.mock import patch, Mock

from cataloging_service.api.request_objects import MenuRequest, MenuTimingRequest, MenuEditRequest
from cataloging_service.api.request_objects import MenuCreateRequest, MenuTimingRequest
from cataloging_service.constants import error_codes, model_choices, constants
from cataloging_service.domain.menu_service import MenuService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure import decorators

from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase
from tests.factories import MenuFactory


class TestMenuService(CatalogingServiceTestCase):
    def setUp(self):
        self.menu_repository = Mock()
        self.menu_repository.persist.side_effect = lambda x: x

        self.menu_repository._update.side_effect = lambda x: x

        self.menu_repository.delete_all.side_effect = None
        self.menu_service = MenuService(menu_repository=self.menu_repository)

    def tearDown(self):
        pass
    
    def test_create_menu_without_menu_timing(self):
        menu_request = self._get_create_menu_request()
        menu_request.menu_timings = []

        menu = self.menu_service.create_menu(menu_request)
        self.assertEqual(menu.name, menu_request.name)
        self.assertEqual(menu.display_name, menu_request.display_name)
        self.assertEqual(menu.description, menu_request.description)
        self.assertEqual(len(menu.menu_timings), 0)


    def test_create_menu_with_menu_timing(self):
        menu_request = self._get_create_menu_request()

        menu = self.menu_service.create_menu(menu_request)
        self.assertEqual(menu.name, menu_request.name)
        self.assertEqual(menu.display_name, menu_request.display_name)
        self.assertEqual(menu.description, menu_request.description)
        self.assertEqual(len(menu.menu_timings), 1)
        self.assertEqual(menu.menu_timings[0].start_time, menu_request.menu_timings[0].start_time)
        self.assertEqual(json.loads(menu.menu_timings[0].days.decode()), menu_request.menu_timings[0].days)
    

    def _get_create_menu_request(self):
        dictionary =  {
            "name" : "Breakfast Menu",
            "display_name": "Super breakfast menu",
            "description": "Weekday breakfast menu",
            "menu_timings": self._get_menu_timings()
            }
        return MenuCreateRequest(seller_id=1, dictionary=dictionary)
    
    def _get_menu_timings(self):
        dictionary = {
            "days": ["SUN", "MON"],
            "start_time": datetime.now(),
            "end_time": datetime.now() + timedelta(hours=2),
        }
        return [dictionary]
    

    def test_edit_menu_with_empty_name(self):
        menu_id = 1
        self.menu_repository.load_for_update.return_value = self.get_mock_load_for_update()
        edit_menu_request = self._get_edit_menu_request()
        delattr(edit_menu_request, "name")

        edited_menu = self.menu_service.edit_menu(menu_id=menu_id, edit_menu_request=edit_menu_request)
        
        self.assertEqual(edited_menu.name, "Breakfast Menu")
        self.assertEqual(edited_menu.display_name, edit_menu_request.display_name)
        self.assertEqual(edited_menu.description, edit_menu_request.description)
        self.assertEqual(edited_menu.menu_type, edit_menu_request.menu_type)
        self.assertIsNotNone(edited_menu.menu_timings)
        self.assertEqual(len(edited_menu.menu_timings), 1)


    def test_edit_menu_with_empty_description(self):
        menu_id = 1
        self.menu_repository.load_for_update.return_value = self.get_mock_load_for_update()
        edit_menu_request = self._get_edit_menu_request()
        delattr(edit_menu_request, "description")

        edited_menu = self.menu_service.edit_menu(menu_id=menu_id, edit_menu_request=edit_menu_request)
        
        self.assertEqual(edited_menu.name, edit_menu_request.name)
        self.assertEqual(edited_menu.display_name, edit_menu_request.display_name)
        self.assertEqual(edited_menu.description, "Daily breakfast menu")
        self.assertEqual(edited_menu.menu_type, edit_menu_request.menu_type)
        self.assertIsNotNone(edited_menu.menu_timings)
        self.assertEqual(len(edited_menu.menu_timings), 1)
    

    def test_edit_menu_with_empty_display_name(self):
        menu_id = 1
        self.menu_repository.load_for_update.return_value = self.get_mock_load_for_update()
        edit_menu_request = self._get_edit_menu_request()
        delattr(edit_menu_request, "display_name")

        edited_menu = self.menu_service.edit_menu(menu_id=menu_id, edit_menu_request=edit_menu_request)
        
        self.assertEqual(edited_menu.name, edit_menu_request.name)
        self.assertEqual(edited_menu.display_name, "breakfast")
        self.assertEqual(edited_menu.description, edit_menu_request.description)
        self.assertEqual(edited_menu.menu_type, edit_menu_request.menu_type)
        self.assertIsNotNone(edited_menu.menu_timings)
        self.assertEqual(len(edited_menu.menu_timings), 1)
    

    def test_edit_menu_with_empty_menu_type(self):
        menu_id = 1
        self.menu_repository.load_for_update.return_value = self.get_mock_load_for_update()
        edit_menu_request = self._get_edit_menu_request()
        delattr(edit_menu_request, "menu_type")

        edited_menu = self.menu_service.edit_menu(menu_id=menu_id, edit_menu_request=edit_menu_request)
        
        self.assertEqual(edited_menu.name, edit_menu_request.name)
        self.assertEqual(edited_menu.display_name, edit_menu_request.display_name)
        self.assertEqual(edited_menu.description, edit_menu_request.description)
        self.assertEqual(edited_menu.menu_type, "RESTAURANT")
        self.assertIsNotNone(edited_menu.menu_timings)
        self.assertEqual(len(edited_menu.menu_timings), 1)
    
    
    def test_edit_menu_with_menu_categories(self):
        menu_id = 1
        self.menu_repository.load_for_update.return_value = self.get_mock_load_for_update()
        edit_menu_request = self._get_edit_menu_request()

        edited_menu = self.menu_service.edit_menu(menu_id=menu_id, edit_menu_request=edit_menu_request)
        
        self.assertEqual(edited_menu.name, edit_menu_request.name)
        self.assertEqual(edited_menu.display_name, edit_menu_request.display_name)
        self.assertEqual(edited_menu.description, edit_menu_request.description)
        self.assertEqual(edited_menu.menu_type, edit_menu_request.menu_type)
        self.assertIsNotNone(edited_menu.menu_categories)
        self.assertEqual((edited_menu.menu_categories[0].name), "Dinner")
    
    def test_edit_menu_with_menu_items(self):
        menu_id = 1
        self.menu_repository.load_for_update.return_value = self.get_mock_load_for_update()
        edit_menu_request = self._get_edit_menu_request()

        edited_menu = self.menu_service.edit_menu(menu_id=menu_id, edit_menu_request=edit_menu_request)
        
        self.assertEqual(edited_menu.name, edit_menu_request.name)
        self.assertEqual(edited_menu.display_name, edit_menu_request.display_name)
        self.assertEqual(edited_menu.description, edit_menu_request.description)
        self.assertEqual(edited_menu.menu_type, edit_menu_request.menu_type)
        self.assertEqual(len(edited_menu.menu_items), 2)
        self.assertEqual((edited_menu.menu_items[0].display_order), 0)
        self.assertEqual((edited_menu.menu_items[1].display_order), 1)
        self.assertFalse((edited_menu.menu_items[0].sold_out))
        self.assertTrue((edited_menu.menu_items[1].sold_out))

    def get_mock_load_for_update(self):
        return MenuFactory()
           
    
    def _get_edit_menu_request(self):
        dictionary = {
            "name": "Brunch",
            "display_name": "Brunch",
            "description": "Weekday brunch menu",
            "menu_timings": self._get_menu_timings(),
            "menu_type": "DELIVERY",
            "menu_categories" : self._get_menu_categories(),
            "menu_items": self._get_menu_items(),
            "menu_combos": self._get_menu_combos(),
        }
        return MenuEditRequest(seller_id=1, menu_id=1, dictionary=dictionary)
    
    def _get_menu_categories(self):
        dictionary = {
            "name": "Dinner"
        }

        return [dictionary]
    
    def _get_menu_items(self):
        menu_item_1 = {
            "menu_id": 1,
            "item_id": 1,
            "sold_out": False
        }
        menu_item_2 = {
            "menu_id": 1,
            "item_id": 2,
            "sold_out": True
        }
        
        return [menu_item_1, menu_item_2]
    
    def _get_menu_combos(self):
        dictionary = {
            "menu_id": 1,
            "combo_id": 1,
            "sold_out": False
        }

        return [dictionary]