from unittest.mock import Mock

from cataloging_service.constants import error_codes
from cataloging_service.constants.model_choices import StandardStatusChoices
from cataloging_service.domain import OTAService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.models import OTA, Property, OtaProperty, Provider, PropertyDetail
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestOtaService(CatalogingServiceTestCase):

    def setUp(self):
        self.property_repository = Mock()
        self.meta_repository = Mock()
        self.email_client = Mock()
        self.ota_service = OTAService(self.property_repository, self.meta_repository, self.email_client)

    def get_mock_ota(self):
        ota = OTA()
        ota.id=1
        ota.code = 'ota_1'
        return ota

    def get_mock_property(self):
        property = Property()
        property.id="PROP_1"
        property.property_detail = self.get_mock_property_detail_object()
        return property

    def get_mock_ota_property(self):
        ota_property = OtaProperty()
        ota_property.property = self.get_mock_property()
        ota_property.ota = self.get_mock_ota()
        return ota_property

    def get_mock_provider_non_treebo(self):
        provider = Provider()
        provider.id = 1
        provider.name = 'WANDERTRAIL'
        provider.code = 'prov-wander'
        provider.status = StandardStatusChoices.ACTIVE
        return provider

    def get_mock_provider_treebo(self):
        provider = Provider()
        provider.id = 1
        provider.name = 'TREEBO'
        provider.code = 'treebo'
        provider.status = StandardStatusChoices.ACTIVE
        return provider

    def get_mock_property_detail_object(self):
        property_detail = PropertyDetail()
        property_detail.id = 1
        property_detail.provider = self.get_mock_provider_non_treebo()
        return property_detail

    def test_get_ota_with_valid_code(self):
        mock_ota = self.get_mock_ota()
        self.property_repository.get_ota.side_effect = lambda *args: self.get_mock_ota() \
            if args[0] == 'ota_1' else None
        requested_ota = self.ota_service.get_ota('ota_1')
        self.assertEqual(requested_ota.code, mock_ota.code)
        with self.assertRaises(CatalogingServiceException):
            self.ota_service.get_ota('something')

    def test_get_ota_with_invalid_code(self):
        self.property_repository.get_ota.side_effect = lambda *args: self.get_mock_ota() \
            if args[0] == 'ota_1' else None
        try:
            self.ota_service.get_ota('something')
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.OTA_NOT_FOUND)
        else:
            self.fail()

    def test_get_ota_property_with_valid_property_and_ota(self):
        mock_ota_property = self.get_mock_ota_property()
        self.property_repository.get_ota_property.side_effect = lambda *args: self.get_mock_ota_property() \
            if args[0] == 'PROP_1' and args[1] == 'ota_1' else None
        requested_ota_property = self.ota_service.get_ota_property('PROP_1', 'ota_1')
        self.assertEqual(requested_ota_property.ota.code, mock_ota_property.ota.code)
        with self.assertRaises(CatalogingServiceException):
            self.ota_service.get_ota_property('something', 'something')

    def test_get_ota_property_with_invalid_property_and_ota(self):
        self.property_repository.get_ota_property.side_effect = lambda *args: self.get_mock_ota_property() \
            if args[0] == 'PROP_1' and args[1] == 'ota_1' else None
        try:
            self.ota_service.get_ota_property('something', 'ota_1')
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.OTA_PROPERTY_NOT_FOUND)
        else:
            self.fail()

    def test_check_if_property_is_provided_by_treebo_when_provider_is_not_treebo(self):
        mock_ota_property = self.get_mock_ota_property()
        self.assertFalse(self.ota_service.check_if_property_is_provided_by_treebo(ota_property=mock_ota_property))

    def test_check_if_property_is_provided_by_treebo_when_provider_is_none(self):
        mock_ota_property = self.get_mock_ota_property()
        mock_ota_property.property.property_detail.provider = None
        self.assertTrue(self.ota_service.check_if_property_is_provided_by_treebo(ota_property=mock_ota_property))

    def test_check_if_property_is_provided_by_treebo_when_property_detail_is_none(self):
        mock_ota_property = self.get_mock_ota_property()
        mock_ota_property.property.property_detail = None
        self.assertTrue(self.ota_service.check_if_property_is_provided_by_treebo(ota_property=mock_ota_property))

    def test_check_if_property_is_provided_by_treebo_when_provider_is_treebo(self):
        mock_ota_property = self.get_mock_ota_property()
        mock_ota_property.property.property_detail.provider = self.get_mock_provider_treebo()
        self.assertTrue(self.ota_service.check_if_property_is_provided_by_treebo(ota_property=mock_ota_property))


