from unittest.mock import Mock, patch

from cataloging_service.domain import SkuService, PropertyService, MetaService, MessagingService, RoomService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure import decorators
from cataloging_service.models import Sku<PERSON>ate<PERSON><PERSON>, <PERSON>, Sku, SkuBundle, RoomTypeConfiguration, RoomType, Param
from cataloging_service.utils import Utils
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestSkuService(CatalogingServiceTestCase):

    def setUp(self):
        # mock context manager
        self.context_manager = self.get_mock_context_manager()
        self.mock_db = patch.object(decorators, 'db', new=Mock())
        self.mock_utils = patch.object(decorators, 'Utils')
        self.mock_db.start()
        self.mock_utils.start()
        self.mock_publisher = Mock()
        self.messaging_service = MessagingService(self.mock_publisher)
        self.sku_repository = Mock()
        self.sku_redis_repository = Mock()
        self.property_repository = Mock()
        self.location_repository = Mock()
        self.file_repository = Mock()
        self.meta_repository = Mock()
        self.aws_client = Mock()
        self.meta_service = MetaService(self.meta_repository)
        self.room_service = RoomService(self.property_repository, self.messaging_service)
        self.sku_service = SkuService(self.sku_repository, self.sku_redis_repository, self.messaging_service)
        self.property_service = PropertyService(self.property_repository, self.location_repository,
                                                self.file_repository, self.meta_repository, self.messaging_service,
                                                self.aws_client)

    def tearDown(self):
        self.mock_db.stop()
        self.mock_utils.stop()

    @staticmethod
    def get_mock_sku_categories():
        sku_categories = []
        sku_category = SkuCategory()
        sku_category.id = 1
        sku_category.name = 'Stay'
        sku_category.status = 'ACTIVE'
        sku_category.hsn_sac = 'stay_hsn'
        sku_categories.append(sku_category)
        sku_category = SkuCategory()
        sku_category.id = 2
        sku_category.name = 'Alcohol'
        sku_category.status = 'ACTIVE'
        sku_category.hsn_sac = 'alcohol_hsn'
        sku_categories.append(sku_category)
        return sku_categories

    def get_mock_property(self):
        property = Property()
        property.id = '0000165'
        property.sku_categories = self.get_mock_sku_categories()
        property.room_type_configurations = [self.get_mock_room_type_config_object()]
        return property

    @staticmethod
    def get_mock_sku():
        sku_categories = TestSkuService.get_mock_sku_categories()
        skus = []
        sku = Sku()
        sku.id = 1
        sku.name = 'OAK1'
        sku.hsn_sac = 'stay_hsn'
        sku.code = 'oak1'
        sku.saleable = False
        sku.chargeable_per_occupant = False
        sku.is_modular = True
        sku.category_id = sku_categories[0].id
        skus.append(sku)
        sku = Sku()
        sku.id = 2
        sku.name = 'BAR'
        sku.hsn_sac = 'alcohol_hsn'
        sku.code = 'bar'
        sku.saleable = True
        sku.chargeable_per_occupant = False
        sku.is_modular = True
        sku.category_id = sku_categories[1].id
        skus.append(sku)
        sku = Sku()
        sku.id = 3
        sku.name = 'WIFI'
        sku.code = Utils.slugify(sku.name)
        sku.hsn_sac = 'stay_hsn'
        sku.code = 'wifi'
        sku.saleable = True
        sku.chargeable_per_occupant = True
        sku.is_modular = True
        sku.category_id = sku_categories[0].id
        skus.append(sku)
        return skus

    def get_mock_room_type_object(self):
        room_type = RoomType()
        room_type.id = 1
        room_type.type = 'OAK'

        return room_type

    def get_mock_room_type_config_object(self):
        room_type_config = RoomTypeConfiguration()
        room_type_config.id = 1
        room_type_config.room_type_id = self.get_mock_room_type_object().id
        room_type_config.room_type = self.get_mock_room_type_object()
        room_type_config.adults=2
        room_type_config.children=1

        return room_type_config

    def get_mock_param(self, entity, field, value):
        param = Param()
        param.entity = entity
        param.field = field
        param.value = value
        param.validate = True
        return param

    def get_given_mock_sku(self, id, name):
        sku = Sku()
        sku.id = id
        sku.name = name
        sku.code = Utils.slugify(name)
        sku.hsn_sac = 'stay_hsn'
        sku.saleable = True
        sku.is_modular = True
        return sku

    def get_mock_bundle_with_1_adult_and_oak(self, with_child=False):
        sku = Sku()
        sku.id = 1
        sku.name = 'OAK-2-1'
        sku.code = 1
        sku.is_modular = False
        sku_bundles_with_adult = [SkuBundle(id=1, sku=Sku(name='OAK'), count=1),
                                  SkuBundle(id=2, sku=Sku(name='OAK-ADULT'), count=1), ]
        sku_bundles_with_adult_child = [SkuBundle(id=1, sku=Sku(name='OAK'), count=1),
                                        SkuBundle(id=2, sku=Sku(name='OAK-ADULT'), count=1),
                                        SkuBundle(id=3, sku=Sku(name='OAK-CHILD'), count=1)]
        if with_child:
            sku.bundle = sku_bundles_with_adult_child
        else:
            sku.bundle = sku_bundles_with_adult
        return sku

    @staticmethod
    def get_mock_room_config_skus():
        return [Sku(id=1, name='OAK', code='oak', flat_count_for_creation=1, is_modular=True),
                Sku(id=2, name='MAPLE', code='maple', flat_count_for_creation=1, is_modular=True),
                Sku(id=3, name='MAHOGANY', code='mahogany', flat_count_for_creation=1, is_modular=True),
                Sku(id=4, name='ACACIA', code='acacia', flat_count_for_creation=1, is_modular=True)]

    @staticmethod
    def get_mock_occupancy_skus():
        return [Sku(id=5, name='OAK-ADULT', code='oak-adult', flat_count_for_creation=0, is_modular=True),
                Sku(id=6, name='OAK-CHILD', code='oak-child', flat_count_for_creation=0, is_modular=True),
                Sku(id=9, name='ACACIA-ADULT', code='acacia-adult', flat_count_for_creation=0, is_modular=True),
                Sku(id=10, name='ACACIA-CHILD', code='acacia-child', flat_count_for_creation=0, is_modular=True),
                Sku(id=11, name='MAPLE-ADULT', code='maple-adult', flat_count_for_creation=0, is_modular=True),
                Sku(id=12, name='MAPLE-CHILD', code='maple-child', flat_count_for_creation=0, is_modular=True),
                Sku(id=13, name='MAHOGANY-ADULT', code='mahogany-adult', flat_count_for_creation=0, is_modular=True),
                Sku(id=14, name='MAHOGANY-CHILD', code='mahogany-child', flat_count_for_creation=0, is_modular=True)
                ]

    @staticmethod
    def get_mock_chargeable_skus():
        return [Sku(id=7, name='WIFI', code='wifi', flat_count_for_creation=1, is_modular=True),
                Sku(id=8, name='BREAKFAST', code='breakfast', flat_count_for_creation=0, is_modular=True)]

    def get_mock_sku_bundle(self, sku_id, sku_name):
        sku_bundle = SkuBundle()
        sku_bundle.bundle = self.get_given_mock_sku(sku_id, sku_name)
        return sku_bundle

    def get_mock_bundle(self):
        sku = Sku()
        sku.name = 'Acacia-adult-wifi'
        sku.sku_s=self.get_mock_sku()

    def get_meta_repository_rget_entity_param_by_key_val_side_effect(self, *args):
        if args[0] == 'Sku' and args[1] == 'tax_type_id' and args[2] == 'derived':
            return self.get_mock_param('Sku', 'tax_type_id', 'derived')
        elif args[0] == 'Sku' and args[1] == 'sku_type_id' and args[2] == 'bundle':
            return self.get_mock_param('Sku', 'sku_type_id', 'bundle')
        else:
            return None

    def get_sku_repository_rget_sku_by_codes_side_effect(self, *args):
        if 'maple' in args[0]:
            return self.get_mock_room_config_skus()
        elif 'oak-adult' in args[0]:
            return self.get_mock_occupancy_skus()
        elif 'breakfast' in args[0]:
            return self.get_mock_chargeable_skus()
        else:
            return []

    @staticmethod
    def get_mock_bundle_rule_value():
        return "{ \"room_config_skus\": { \"maple\": \"maple\", " \
               "\"mahogany\": \"mahogany\", \"oak\": \"oak\", " \
               "\"acacia\": \"acacia\" }, " \
               "\"occupancy_skus\":{ \"acacia-adult\": \"acacia-adult\", " \
               "\"acacia-child\": \"acacia-child\", \"oak-adult\": \"oak-adult\", " \
               "\"oak-child\": \"oak-child\", \"maple-adult\": \"maple-adult\", " \
               "\"maple-child\": \"maple-child\", \"mahogany-adult\": \"mahogany-adult\", " \
               "\"mahogany-child\": \"mahogany-child\"}, " \
               "\"chargeable_skus_per_occupant\": { \"breakfast\": \"breakfast\" , \"wifi\": \"wifi\"} }"

    @staticmethod
    def get_mock_bundle_rule_value_with_breakfast():
        return "{ \"room_config_skus\": { \"maple\": \"maple\", " \
               "\"mahogany\": \"mahogany\", \"oak\": \"oak\", " \
               "\"acacia\": \"acacia\" }, " \
               "\"occupancy_skus\":{ \"acacia-adult\": \"acacia-adult\", " \
               "\"acacia-child\": \"acacia-child\", \"oak-adult\": \"oak-adult\", " \
               "\"oak-child\": \"oak-child\", \"maple-adult\": \"maple-adult\", " \
               "\"maple-child\": \"maple-child\", \"mahogany-adult\": \"mahogany-adult\", " \
               "\"mahogany-child\": \"mahogany-child\"}, " \
               "\"chargeable_skus_per_occupant\": { \"breakfast\": \"breakfast\"} }"

    def test_get_all_categories(self):
        sku_categories = self.get_mock_sku_categories()
        self.sku_repository.rget_all_sku_categories.return_value = sku_categories
        all_sku_categories = self.sku_service.sget_all_sku_categories()

        self.assertEqual(sku_categories, all_sku_categories)

    def test_get_all_categories_by_codes(self):
        sku_categories = self.get_mock_sku_categories()
        self.sku_repository.rget_all_sku_categories_by_codes.return_value = sku_categories
        codes = ['stay1', 'alcohol2']
        all_sku_categories = self.sku_service.sget_all_sku_categories_by_codes(codes)

        self.assertEqual(sku_categories, all_sku_categories)

    def test_get_category_with_valid_id(self):
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category.return_value = sku_category
        requested_sku_category = self.sku_service.sget_sku_category(1)
        self.assertEqual(sku_category, requested_sku_category)

    def test_category_with_invalid_id(self):
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category.side_effect = lambda sku_category_id: \
            None if sku_category_id != sku_category.id \
            else sku_category
        with self.assertRaises(CatalogingServiceException):
            self.sku_service.sget_sku_category(3)

    def test_get_category_with_valid_code(self):
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category_by_code.return_value = sku_category
        requested_sku_category = self.sku_service.sget_sku_category_by_code('alcohol2')
        self.assertEqual(sku_category, requested_sku_category)

    def test_category_with_invalid_code(self):
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category_by_code.side_effect = lambda code: \
            None if code != sku_category.code \
            else sku_category
        with self.assertRaises(CatalogingServiceException):
            self.sku_service.sget_sku_category_by_code('taxi3')

    def test_categories_for_property(self):
        property = self.get_mock_property()
        self.property_repository.get_property.return_value = property
        sku_categories = self.property_service.get_sku_categories('0000165')

        self.assertEqual(property.sku_categories, sku_categories)

    def test_categories_for_invalid_property(self):
        property = self.get_mock_property()
        self.property_repository.get_property.side_effect = lambda property_id: None if property_id != property.id \
            else property
        with self.assertRaises(CatalogingServiceException):
            self.property_service.get_sku_categories('123')

    def test_validate_bundle_rule(self):
        skus = self.get_mock_sku()
        self.sku_repository.rget_sku_by_codes.return_value = skus
        occupancy_equiv_sku = skus[2]
        self.sku_repository.rget_all_sku_charged_per_occupant.side_effect = \
            lambda *args: [occupancy_equiv_sku] if args[0][0] == "wifi" else []

        with self.assertRaises(ValueError):
            self.sku_service.validate_bundle_rule("bibi")
        with self.assertRaises(Exception):
            self.sku_service.validate_bundle_rule("[]")
        with self.assertRaises(Exception):
            self.sku_service.validate_bundle_rule("[\"\"]")
        with self.assertRaises(CatalogingServiceException):
            self.sku_service.validate_bundle_rule("[\"oak2\"]")
        with self.assertRaises(CatalogingServiceException):
            self.sku_service.validate_bundle_rule("[\"oak1\"]")

    def test_create_unique_identifier_from_sku_list_and_validate_for_modular_sku(self):
        form = Mock()
        form.name.data = 'OAK'
        self.sku_repository.rcheck_if_sku_identifier_exists.return_value = False
        identifier = self.sku_service.create_unique_identifier_for_sku(form=form)
        self.assertEqual(identifier, 'sku:oak:0')

    def test_create_unique_identifier_from_sku_list_and_validate_for_bundled_sku(self):
        form = Mock()
        form.name.data = 'OAK'
        self.sku_repository.rcheck_if_sku_identifier_exists.return_value = True
        with self.assertRaises(Exception):
            self.sku_service.create_unique_identifier_for_sku(form=form)

    def test_update_identifier_in_sku_with_validation(self):
        sku = Sku(id=1, name='OAK')
        sku.bundle = [SkuBundle(id=1, sku=sku, count=1)]
        self.sku_repository.rcheck_if_sku_identifier_exists.return_value = False
        identifier = self.sku_service.update_identifier_in_sku(sku=sku)
        self.assertEqual(identifier, 'sku:oak:1')

    def test_update_identifier_in_sku_with_invalidation(self):
        sku = Sku(id=1, name='OAK')
        sku.bundle = [SkuBundle(id=1, sku=sku, count=1)]
        self.sku_repository.rcheck_if_sku_identifier_exists.return_value = True
        with self.assertRaises(Exception):
            self.sku_service.update_identifier_in_sku(sku=sku)

    # NIGHTMARE!!
    @patch('cataloging_service.domain.service_provider')
    def test_applicable_skus_to_property_correct_params(self, service_provider):
        service_provider.meta_service = self.meta_service
        property = self.get_mock_property()
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category_by_code.return_value = sku_category

        # for validating bundle rule
        bundle_rule_value = self.get_mock_bundle_rule_value()
        self.meta_repository.rget_entity_param.return_value = [self.get_mock_param('Sku', 'bundle_rule',
                                                                                   bundle_rule_value)]
        self.sku_repository.rget_sku_by_codes.side_effect = lambda *args: self\
            .get_sku_repository_rget_sku_by_codes_side_effect(*args)

        self.sku_repository.rget_skus_by_tags.side_effect = lambda *args: \
            [self.get_given_mock_sku(4, 'OAK-1-1')] if "oak-1" in args[0] else None

        applicable_skus = self.sku_service.applicable_skus_to_property(property)
        self.assertEqual(len(applicable_skus), 6)
        adult_sku = list(filter(lambda applicable_sku: applicable_sku.code == 'oak-adult', applicable_skus))
        self.assertEqual(len(adult_sku), 1)

    # NIGHTMARE!!
    @patch('cataloging_service.domain.service_provider')
    def test_applicable_skus_to_property_incorrect_chargeable_skus(self, service_provider):
        service_provider.meta_service = self.meta_service
        property = self.get_mock_property()
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category_by_code.return_value = sku_category

        # for validating bundle rule
        bundle_rule_value = self.get_mock_bundle_rule_value_with_breakfast()
        self.meta_repository.rget_entity_param.return_value = [self.get_mock_param('Sku', 'bundle_rule',
                                                                                   bundle_rule_value)]
        self.sku_repository.rget_sku_by_codes.side_effect = lambda *args: self\
            .get_sku_repository_rget_sku_by_codes_side_effect(*args)

        with self.assertRaises(Exception) as error:
            self.sku_service.applicable_skus_to_property(property)

    @patch('cataloging_service.domain.service_provider')
    def test_applicable_skus_to_property_incorrect_params(self, service_provider):
        service_provider.meta_service=self.meta_service
        property = self.get_mock_property()
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        with self.assertRaises(Exception):
            self.sku_service.applicable_skus_to_property(property)

    @patch('cataloging_service.domain.service_provider')
    def test_bundle_creation_with_1_adult(self, service_provider):
        """
        https://stackoverflow.com/a/51131557/1790760
        :return:
        """
        service_provider.meta_service = self.meta_service
        property = self.get_mock_property()
        # Room configuration with single adult
        room_config = self.get_mock_room_type_config_object()
        room_config.adults = 1
        room_config.children = 0
        property.room_type_configurations = [room_config]

        self.sku_repository.rget_sku_by_codes.side_effect = lambda *args: self \
            .get_sku_repository_rget_sku_by_codes_side_effect(*args)

        # call to _get_dict_parameter_for_create_skus_method()
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category_by_code.return_value = sku_category

        # for validating bundle rule
        bundle_rule_value = self.get_mock_bundle_rule_value()
        self.meta_repository.rget_entity_param.return_value = [self.get_mock_param('Sku', 'bundle_rule',
                                                                                   bundle_rule_value)]
        # existing set of skus already present
        self.sku_repository.rcheck_if_sku_identifier_exists.return_value = False

        self.sku_repository.db.session.no_autoflush = self.context_manager
        self.sku_repository.persist_all.side_effect = lambda *args: args[0]

        bundles = self.sku_service.create_new_sku_for_property(property)
        self.assertEqual(4, len(bundles[0].bundle))
        adult_sku = list(filter(lambda sku_bundle: sku_bundle.sku.name == 'OAK-ADULT', bundles[0].bundle)).pop()
        self.assertEqual(0, adult_sku.count)

    @patch('cataloging_service.domain.service_provider')
    def test_bundle_creation_with_1_adult_1_child_and_corresponding_wifi_count(self, service_provider):
        """
        https://stackoverflow.com/a/51131557/1790760
        :return:
        """
        service_provider.meta_service = self.meta_service
        property = self.get_mock_property()
        # Room configuration with single adult
        room_config = self.get_mock_room_type_config_object()
        room_config.adults = 1
        room_config.children = 1
        property.room_type_configurations = [room_config]

        self.sku_repository.rget_sku_by_codes.side_effect = lambda *args: self \
            .get_sku_repository_rget_sku_by_codes_side_effect(*args)

        # call to _get_dict_parameter_for_create_skus_method()
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        sku_category = self.get_mock_sku_categories()[1]
        self.sku_repository.rget_sku_category_by_code.return_value = sku_category

        # for validating bundle rule
        bundle_rule_value = self.get_mock_bundle_rule_value()
        self.meta_repository.rget_entity_param.return_value = [self.get_mock_param('Sku', 'bundle_rule',
                                                                                   bundle_rule_value)]
        # existing set of skus already present
        self.sku_repository.rcheck_if_sku_identifier_exists.return_value = False

        self.sku_repository.db.session.no_autoflush = self.context_manager
        self.sku_repository.persist_all.side_effect = lambda *args: args[0]

        bundles = self.sku_service.create_new_sku_for_property(property)
        self.assertEqual(4, len(bundles[0].bundle))
        breakfast_sku = next((sku_bundle for sku_bundle in bundles[1].bundle if
                              str.lower(sku_bundle.sku.name) == 'breakfast'), None)
        self.assertEqual(2, breakfast_sku.count)
        wifi_sku = next((sku_bundle for sku_bundle in bundles[1].bundle if str.lower(sku_bundle.sku.name) == 'wifi'), None)
        self.assertEqual(1, wifi_sku.count)

    @patch('cataloging_service.domain.service_provider')
    def test_tag_creation_with_adult_and_children(self, service_provider):
        service_provider.room_service = self.room_service
        self.property_repository.get_all_room_types_by_codes.return_value = [self.get_mock_room_type_object()]
        mock_bundle = self.get_mock_bundle_with_1_adult_and_oak(with_child=False)
        tag = self.sku_service.update_tag_for_parent_sku(mock_bundle)
        self.assertEqual(tag, 'OAK-2')
        mock_bundle = self.get_mock_bundle_with_1_adult_and_oak(with_child=True)
        tag = self.sku_service.update_tag_for_parent_sku(mock_bundle)
        self.assertEqual(tag, 'OAK-2-1')

    @patch('cataloging_service.domain.service_provider')
    def test_bundle_creation_with_incorrect_params(self, service_provider):
        service_provider.meta_service = self.meta_service
        property = self.get_mock_property()
        self.meta_repository.rget_entity_param_by_key_val.side_effect = lambda *args: self\
            .get_meta_repository_rget_entity_param_by_key_val_side_effect(*args)
        with self.assertRaises(Exception):
            self.sku_service.create_new_sku_for_property(property)

    @patch('cataloging_service.domain.service_provider')
    def test_get_room_type_dict(self, service_provider):
        service_provider.room_service = self.room_service
        self.property_repository.get_all_room_types_by_codes.return_value = [self.get_mock_room_type_object()]
        room_type_dict = self.sku_service.get_room_type_dict()
        self.assertEqual(room_type_dict.get('OAK').type, 'OAK')

    def test_publish_categories(self):
        sku_categories = self.get_mock_sku_categories()
        self.sku_repository.rget_all_sku_categories.return_value = sku_categories
        self.sku_service.publish_categories([])
        self.assertEqual(self.mock_publisher.publish.call_count, 2)

    def test_publish_sku(self):
        skus = self.get_mock_sku()
        self.sku_repository.rget_sku_by_id.return_value = skus
        self.sku_service.publish_sku([])
        self.assertEqual(self.mock_publisher.publish.call_count, 3)

    @patch('cataloging_service.domain.service_provider')
    def test_scheck_if_bundle_rule_is_unique(self, service_provider):
        service_provider.meta_service = self.meta_service
        # for validating bundle rule
        bundle_rule_value = self.get_mock_bundle_rule_value()
        self.meta_repository.rget_entity_param.return_value = [self.get_mock_param('Sku', 'bundle_rule',
                                                                                   bundle_rule_value)]
        exists = self.sku_service.scheck_if_bundle_rule_is_unique()
        self.assertTrue(exists)





