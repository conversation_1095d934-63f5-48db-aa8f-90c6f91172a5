import copy
from unittest.mock import Mock, patch

from cataloging_service.api.request_objects import RoomTypeConfigurationRequest, RoomRequest
from cataloging_service.constants import error_codes
from cataloging_service.domain.room_service import RoomService
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.infrastructure import decorators
from cataloging_service.models import Property, RoomTypeConfiguration, RoomType, Room
from tests.cataloging_service.cataloging_service_test_case import CatalogingServiceTestCase


class TestRoomService(CatalogingServiceTestCase):
    MOCK_PROPERTY_ID = 1
    MOCK_ROOM_TYPE_ID = 1
    MOCK_MINIMUM_OCCUPANCY = 1
    MOCK_MAXIMUM_OCCUPANCY = 2
    MOCK_MAX_TOTAL = 2
    MOCK_CHILDREN = 1
    MOCK_ADULTS = 1
    MOCK_EXTRA_BED_FORM = 'MATTRESS'

    MOCK_ROOM_TYPE_CONFIG_ID = 1
    DUPLICATE_CONFIG_ID = 10

    MOCK_MM_ID = '123'
    MOCK_ROOM_NUMBER = '101'
    MOCK_BUILDING_NUMBER = 'B22'
    MOCK_FLOOR_NUMBER = 1
    MOCK_ROOM_SIZE = '1x12'
    MOCK_ROOM_ID = 1

    def setUp(self):
        self.mock_db = patch.object(decorators, 'db', new=Mock())
        self.mock_utils = patch.object(decorators, 'Utils')
        self.mock_db.start()
        self.mock_utils.start()
        self.room_repository = Mock()
        self.messaging_service = Mock()
        self.room_repository.persist.side_effect = lambda x: x
        self.room_service = RoomService(self.room_repository, self.messaging_service)

    def tearDown(self):
        self.mock_db.stop()
        self.mock_utils.stop()

    def test_get_room_type_configurations(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_type_configurations = [self.get_mock_room_type_config_object()]
        self.room_repository.get_property_room_type_configurations.return_value = mock_room_type_configurations
        self.room_repository.get_property_room_count_map.return_value = {self.MOCK_ROOM_TYPE_ID: 10}

        room_type_configurations = self.room_service.get_room_type_configurations(mock_property_object)

        self.assertEqual(room_type_configurations, mock_room_type_configurations)
        self.assertEqual(room_type_configurations[0].room_count, 10)

    def test_get_room_type_configurations_without_room_count(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_type_configurations = [self.get_mock_room_type_config_object()]
        self.room_repository.get_property_room_type_configurations.return_value = mock_room_type_configurations

        room_type_configurations = self.room_service.get_room_type_configurations(mock_property_object, False)

        self.assertEqual(room_type_configurations, mock_room_type_configurations)

    def test_room_config_addition(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_config_addition_request = self.get_mock_room_config_request()
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = None
        self.room_repository.get_property_room_type_configuration_by_mm_id.return_value = None
        self.room_repository.get_room_type.return_value = self.get_mock_room_type_object()
        room_type_config_object = self.room_service.add_property_room_type_config(mock_property_object,
                                                                                  mock_room_config_addition_request)

        self.assertEqual(room_type_config_object.property_id, mock_property_object.id)
        self.assertEqual(room_type_config_object.room_type_id, mock_room_config_addition_request.room_type_id)
        self.assertEqual(room_type_config_object.extra_bed, mock_room_config_addition_request.extra_bed_form)
        self.assertEqual(room_type_config_object.min_occupancy, mock_room_config_addition_request.minimum_occupancy)
        self.assertEqual(room_type_config_object.max_occupancy, mock_room_config_addition_request.maximum_occupancy)
        self.assertEqual(room_type_config_object.adults, mock_room_config_addition_request.adults)

    def test_room_config_addition_with_duplicate_config(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_config_addition_request = self.get_mock_room_config_request()
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = self.get_mock_room_type_config_object()

        try:
            self.room_service.add_property_room_type_config(mock_property_object, mock_room_config_addition_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.DUPLICATE_ROOM_TYPE_CONFIGURATION)
        else:
            self.fail()

    def test_room_config_addition_with_duplicate_mm_id(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_config_addition_request = self.get_mock_room_config_request()
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = None
        self.room_repository.get_property_room_type_configuration_by_mm_id.return_value = self.get_mock_room_type_config_object()

        try:
            self.room_service.add_property_room_type_config(mock_property_object, mock_room_config_addition_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.DUPLICATE_ROOM_TYPE_CONFIGURATION)
        else:
            self.fail()

    def test_room_config_addition_with_invalid_room_type(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_config_addition_request = self.get_mock_room_config_request()
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = None
        self.room_repository.get_property_room_type_configuration_by_mm_id.return_value = None
        self.room_repository.get_room_type.return_value = None

        try:
            self.room_service.add_property_room_type_config(mock_property_object, mock_room_config_addition_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.ROOM_TYPE_NOT_FOUND)
        else:
            self.fail()

    def test_room_config_updation(self):
        mock_room_type_config_object = self.get_mock_room_type_config_object()
        mock_room_config_updation_request = self.get_mock_room_config_request()
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = mock_room_type_config_object
        self.room_repository.get_property_room_type_configuration_by_mm_id.return_value = mock_room_type_config_object
        self.room_repository.get_room_type.return_value = self.get_mock_room_type_object()
        room_type_config_object = self.room_service.update_property_room_type_config(mock_room_type_config_object,
                                                                                     mock_room_config_updation_request)

        self.assertEqual(room_type_config_object.room_type_id, mock_room_config_updation_request.room_type_id)
        self.assertEqual(room_type_config_object.extra_bed, mock_room_config_updation_request.extra_bed_form)
        self.assertEqual(room_type_config_object.min_occupancy, mock_room_config_updation_request.minimum_occupancy)
        self.assertEqual(room_type_config_object.max_occupancy, mock_room_config_updation_request.maximum_occupancy)
        self.assertEqual(room_type_config_object.adults, mock_room_config_updation_request.adults)

    def test_room_config_updation_with_duplicate_config(self):
        mock_room_type_config_object = self.get_mock_room_type_config_object()
        mock_room_config_updation_request = self.get_mock_room_config_request()
        duplicate_config_object = copy.copy(mock_room_type_config_object)
        duplicate_config_object.id = self.DUPLICATE_CONFIG_ID
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = duplicate_config_object

        try:
            self.room_service.update_property_room_type_config(mock_room_type_config_object,
                                                               mock_room_config_updation_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.DUPLICATE_ROOM_TYPE_CONFIGURATION)
        else:
            self.fail()

    def test_room_config_updation_with_invalid_room_type(self):
        mock_room_type_config_object = self.get_mock_room_type_config_object()
        mock_room_config_updation_request = self.get_mock_room_config_request()
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = None
        self.room_repository.get_property_room_type_configuration_by_mm_id.return_value = None
        self.room_repository.get_room_type.return_value = None

        try:
            self.room_service.update_property_room_type_config(mock_room_type_config_object,
                                                               mock_room_config_updation_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.ROOM_TYPE_NOT_FOUND)
        else:
            self.fail()

    def test_room_addition(self):
        mock_room_type_object = self.get_mock_room_type_object()
        self.room_repository.get_room_type.return_value = mock_room_type_object
        self.room_repository.get_property_room_by_number.return_value = None
        self.room_repository.get_room_by_mm_id.return_value = None
        room_config = self.get_mock_room_type_config_object()
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = room_config

        mock_property_object = self.get_mock_property_object()
        room_addition_request = self.get_mock_room_request()
        added_room = self.room_service.add_property_room(mock_property_object, room_addition_request)

        self.assertEqual(added_room.property_id, mock_property_object.id)
        self.assertEqual(added_room.room_number, room_addition_request.room_number)
        self.assertEqual(added_room.room_type, mock_room_type_object)
        self.assertEqual(added_room.building_number, room_addition_request.building_number)
        self.assertEqual(added_room.floor_number, room_addition_request.floor_number)
        self.assertEqual(added_room.size, room_addition_request.size)
        self.assertEqual(added_room.is_active, room_addition_request.is_active)

    def test_get_rooms(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_objects = [self.get_mock_room_object()]
        self.room_repository.get_property_rooms.return_value = mock_room_objects

        rooms = self.room_service.get_property_rooms(mock_property_object)

        self.assertEqual(rooms, mock_room_objects)

    def test_get_property_room_type_config(self):
        mock_property_object = self.get_mock_property_object()
        mock_room_config_object = self.get_mock_room_type_config_object()
        self.room_repository.get_property_room_type_configuration.return_value = mock_room_config_object

        room_config = self.room_service.get_property_room_type_config(mock_property_object, 1)

        self.assertEqual(room_config, mock_room_config_object)

    def test_get_property_room_type_config_with_invalid_config(self):
        mock_property_object = self.get_mock_property_object()
        self.room_repository.get_property_room_type_configuration.return_value = None

        try:
            self.room_service.get_property_room_type_config(mock_property_object, 1)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.ROOM_TYPE_CONFIG_NOT_FOUND)
        else:
            self.fail()

    def test_get_property_room(self):
        mock_property_object = self.get_mock_property_object()
        mock_room = self.get_mock_room_object()
        self.room_repository.get_property_room.return_value = mock_room

        room = self.room_service.get_property_room(mock_property_object, 1)

        self.assertEqual(room, mock_room)

    def test_get_property_rooms(self):
        mock_property_object = self.get_mock_property_object()
        mock_rooms = [self.get_mock_room_object()]
        self.room_repository.get_property_rooms.return_value = mock_rooms

        rooms = self.room_service.get_property_rooms(mock_property_object)

        self.assertEqual(rooms, mock_rooms)

    def test_get_property_room_invalid_room(self):
        mock_property_object = self.get_mock_property_object()
        self.room_repository.get_property_room.return_value = None

        try:
            self.room_service.get_property_room(mock_property_object, 1)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.ROOM_NOT_FOUND)
        else:
            self.fail()

    def test_room_addition_with_invalid_room_type(self):
        self.room_repository.get_room_type.return_value = None

        mock_property_object = self.get_mock_property_object()
        room_addition_request = self.get_mock_room_request()
        try:
            self.room_service.add_property_room(mock_property_object, room_addition_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.ROOM_TYPE_NOT_FOUND)
        else:
            self.fail()

    def test_room_addition_with_duplicate_room_number(self):
        mock_room_type_object = self.get_mock_room_type_object()
        self.room_repository.get_room_type.return_value = mock_room_type_object
        self.room_repository.get_property_room_by_number.return_value = self.get_mock_room_object()

        mock_property_object = self.get_mock_property_object()
        room_addition_request = self.get_mock_room_request()
        try:
            self.room_service.add_property_room(mock_property_object, room_addition_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.DUPLICATE_ROOM)
        else:
            self.fail()

    def test_room_updation(self):
        mock_room_object = self.get_mock_room_object()
        mock_room_type_object = self.get_mock_room_type_object()
        self.room_repository.get_room_type.return_value = mock_room_type_object
        self.room_repository.get_property_room_by_number.return_value = None
        self.room_repository.get_room_by_mm_id.return_value = None

        room_updation_request = self.get_mock_room_request()
        self.room_repository.get_property_room_type_configurations.return_value = [
            self.get_mock_room_type_config_object()]
        self.room_repository.get_property_room_type_configuration_by_room_type.return_value = \
            self.get_mock_room_type_config_object()
        updated_room = self.room_service.update_property_room(mock_room_object, room_updation_request)

        self.assertEqual(updated_room.room_number, room_updation_request.room_number)
        self.assertEqual(updated_room.building_number, room_updation_request.building_number)
        self.assertEqual(updated_room.floor_number, room_updation_request.floor_number)
        self.assertEqual(updated_room.size, room_updation_request.size)
        self.assertEqual(updated_room.is_active, room_updation_request.is_active)

    def test_room_updation_with_invalid_room_type(self):
        self.room_repository.get_room_type.return_value = None
        mock_room_object = self.get_mock_room_object()

        room_updation_request = self.get_mock_room_request()
        try:
            self.room_service.update_property_room(mock_room_object, room_updation_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.ROOM_TYPE_NOT_FOUND)
        else:
            self.fail()

    def test_room_updation_with_duplicate_room_number(self):
        mock_room_object = self.get_mock_room_object()
        duplicate_room_object = copy.copy(mock_room_object)
        mock_room_object.id = 10
        mock_room_type_object = self.get_mock_room_type_object()
        self.room_repository.get_room_type.return_value = mock_room_type_object
        self.room_repository.get_property_room_by_number.return_value = duplicate_room_object

        room_updation_request = self.get_mock_room_request()
        try:
            self.room_service.update_property_room(mock_room_object, room_updation_request)
        except Exception as exception:
            self.assert_cataloging_service_exception(exception, error_codes.DUPLICATE_ROOM)
        else:
            self.fail()

    def test_validate_room_type_config(self):
        # Invalid max_total > sum of adult+children
        with self.assertRaises(CatalogingServiceException):
            self.room_service.validate_room_type_config(1, 1, 3, 1)
        # Invalid max_total is empty
        with self.assertRaises(CatalogingServiceException):
            self.room_service.validate_room_type_config(1, 1, None, 1)
        # Invalid max_total is 0
        with self.assertRaises(CatalogingServiceException):
            self.room_service.validate_room_type_config(1, 1, 0, 1)
        # Invalid negative max_total
        with self.assertRaises(CatalogingServiceException):
            self.room_service.validate_room_type_config(1, 1, -1, None)
        # Invalid min_occupancy with 0
        with self.assertRaises(CatalogingServiceException):
            self.room_service.validate_room_type_config(1, 1, 1, 0)
        # Invalid min_occupancy -1
        with self.assertRaises(CatalogingServiceException):
            self.room_service.validate_room_type_config(1, 1, 1, -1)
        # Invalid min_occupancy > max(adult, children)
        with self.assertRaises(CatalogingServiceException):
            self.room_service.validate_room_type_config(1, 1, 1, 3)

    def get_mock_property_object(self):
        property = Property()
        property.id = self.MOCK_PROPERTY_ID

        return property

    def get_mock_room_config_request(self):
        dictionary = {'room_type_id': self.MOCK_ROOM_TYPE_ID, 'minimum_occupancy': self.MOCK_MINIMUM_OCCUPANCY,
                      'maximum_occupancy': self.MOCK_MAXIMUM_OCCUPANCY, 'adults': self.MOCK_ADULTS,
                      'extra_bed_form': self.MOCK_EXTRA_BED_FORM, 'max_total': self.MOCK_MAX_TOTAL,
                      'children': self.MOCK_CHILDREN}
        return RoomTypeConfigurationRequest(dictionary)

    def get_mock_room_request(self):
        dictionary = {'mm_id': self.MOCK_MM_ID, 'room_number': self.MOCK_ROOM_NUMBER,
                      'room_type_id': self.MOCK_ROOM_TYPE_ID, 'building_number': self.MOCK_BUILDING_NUMBER,
                      'floor_number': self.MOCK_FLOOR_NUMBER, 'size': self.MOCK_ROOM_SIZE, 'is_active': True}

        return RoomRequest(dictionary)

    def get_mock_room_type_object(self):
        room_type = RoomType()
        room_type.id = self.MOCK_ROOM_TYPE_ID

        return room_type

    def get_mock_room_type_config_object(self):
        room_type_config = RoomTypeConfiguration()
        room_type_config.id = self.MOCK_ROOM_TYPE_CONFIG_ID
        room_type_config.property_id = self.MOCK_PROPERTY_ID
        room_type_config.room_type_id = self.MOCK_ROOM_TYPE_ID

        return room_type_config

    def get_mock_room_object(self):
        room = Room()
        room.id = self.MOCK_ROOM_ID
        room.property_id = self.MOCK_PROPERTY_ID

        return room
