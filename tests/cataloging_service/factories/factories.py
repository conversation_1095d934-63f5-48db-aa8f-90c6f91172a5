import factory

from cataloging_service.models import Menu, RestaurantMenuCategory, MenuTiming, MenuItem,MenuCombo, MenuItem, MenuItemCategory, MenuComboCategory


class Item(factory.Factory):
    id = 1
    name = "Pizza"
    code = "pizza"
    description = "Great"
    sku_category_id = "1"
    display_name = "Margherita Neopolitan Pizza"
    print_name = "Basic cheese pizza"
    prep_time = "00:20:00"
    use_as_side = False
    contains_alcohol = False
    pre_tax_price = 450.0
    tax_value = 20
    cost = 470
    sku_id = 1


class MenuItemFactory(factory.Factory):
    id = 1
    menu_id = 1
    item_id = 1
    sold_out = False
    display_order = 0


class MenuCategoryFactory(factory.Factory):
    name = "South Indian Food"
    display_order = 0

    
    class Meta:
        model = RestaurantMenuCategory


class MenuTimingFactory(factory.Factory):
    days = ["SUN", "MON"]
    start_time = "20:00:00"
    end_time = "22:00:00"

    class Meta:
        model = MenuTiming


class MenuFactory(factory.Factory):
    name = "Breakfast Menu"
    description = "Daily breakfast menu"
    display_name = "breakfast"
    menu_type = "RESTAURANT"
    seller = "1002"

    menu_timings = factory.List([factory.SubFactory(MenuTimingFactory) for _ in range(2)])
    menu_categories = factory.List([factory.SubFactory(MenuCategoryFactory)])
    # menu_items = [factory.SubFactory()]
    # menu_combos = [factory.SubFactory()]

    class Meta:
        model = Menu

