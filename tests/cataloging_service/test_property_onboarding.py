from unittest.mock import Mock, patch
from datetime import datetime
import pytest


IMPORTS_AVAILABLE = True
try:
    from cataloging_service.schemas.property_onboarding import (
        PropertyOnboardingRequestSchema,
        PropertyOnboardingResponseSchema
    )
    from cataloging_service.domain.services.template.property_onboarding_service import PropertyOnboardingService
    from cataloging_service.domain.entities.properties.property_onboarding import PropertyOnboardingEntity
except ImportError as e:
    print(f"Import error: {e}")
    print("Some tests will be skipped due to missing dependencies")
    IMPORTS_AVAILABLE = False
    PropertyOnboardingRequestSchema = None
    PropertyOnboardingResponseSchema = None
    PropertyOnboardingService = None
    PropertyOnboardingEntity = None


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Cataloging service imports not available")
class TestPropertyOnboardingSchemas:
    """Test property onboarding request and response schemas"""

    def test_request_schema_valid(self):
        """Test valid request schema"""
        valid_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {"key": "value"}
        }
        
        schema = PropertyOnboardingRequestSchema.model_validate(valid_data)
        assert schema.property_id == "PROP123"
        assert schema.brand_id == 1
        assert schema.auto_create_departments is True
        assert schema.auto_create_profit_centers is True
        assert schema.custom_config == {"key": "value"}
    
    def test_request_schema_defaults(self):
        """Test request schema with default values"""
        minimal_data = {
            "property_id": "PROP123",
            "brand_id": 1
        }
        
        schema = PropertyOnboardingRequestSchema.model_validate(minimal_data)
        assert schema.auto_create_departments is True  # Default value
        assert schema.auto_create_profit_centers is True  # Default value
        assert schema.custom_config == {}  # Default empty dict
    
    def test_request_schema_invalid_brand_id(self):
        """Test request schema with invalid brand_id"""
        invalid_data = {
            "property_id": "PROP123",
            "brand_id": -1,
            "auto_create_departments": True,
            "auto_create_profit_centers": True
        }
        
        with pytest.raises(ValueError):
            PropertyOnboardingRequestSchema.model_validate(invalid_data)
    
    def test_request_schema_missing_required_fields(self):
        """Test request schema with missing required fields"""
        invalid_data = {
            "property_id": "PROP123"
            # Missing brand_id
        }
        
        with pytest.raises(ValueError):
            PropertyOnboardingRequestSchema.model_validate(invalid_data)
    
    def test_response_schema_valid(self):
        """Test valid response schema"""
        valid_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "departments_created": 3,
            "profit_centers_created": 2,
            "onboarding_status": "COMPLETED",
            "errors": [],
            "warnings": ["Some warning"],
            "onboarded_at": datetime.now()
        }
        
        schema = PropertyOnboardingResponseSchema.model_validate(valid_data)
        assert schema.property_id == "PROP123"
        assert schema.brand_id == 1
        assert schema.departments_created == 3
        assert schema.profit_centers_created == 2
        assert schema.onboarding_status == "COMPLETED"
        assert len(schema.warnings) == 1


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Cataloging service imports not available")
class TestPropertyOnboardingEntity:
    """Test property onboarding entity business logic"""

    def test_entity_creation(self):
        """Test entity creation with valid data"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1,
            template_filters={"key": "value"}
        )
        
        assert entity.property_id == "PROP123"
        assert entity.brand_id == 1
        assert entity.template_filters == {"key": "value"}
        assert entity.onboarding_status == "PENDING"
        assert entity.departments_created == 0
        assert entity.profit_centers_created == 0
    
    def test_entity_completion(self):
        """Test entity completion logic"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1
        )
        
        # Initially not completed
        assert not entity.is_completed()
        
        # Mark as completed
        entity.mark_completed()
        assert entity.onboarding_status == "COMPLETED"
        assert entity.is_completed()
        assert entity.onboarded_at is not None
    
    def test_entity_error_handling(self):
        """Test entity error handling"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1
        )
        
        entity.add_error("Test error")
        assert len(entity.errors) == 1
        assert "Test error" in entity.errors
        
        entity.mark_completed()
        assert not entity.is_completed()  # Has errors
    
    def test_entity_warnings(self):
        """Test entity warning handling"""
        entity = PropertyOnboardingEntity(
            property_id="PROP123",
            brand_id=1
        )
        
        entity.add_warning("Test warning")
        assert len(entity.warnings) == 1
        assert entity.has_warnings()
        
        entity.mark_completed()
        assert entity.is_completed()  # Warnings don't prevent completion


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Cataloging service imports not available")
class TestPropertyOnboardingService:
    """Test property onboarding service"""

    @pytest.fixture
    def mock_dependencies(self):
        """Create mock dependencies for the service"""
        return {
            'department_template_service': Mock(),
            'profit_center_template_service': Mock(),
            'property_department_service': Mock(),
            'transaction_default_mapping_service': Mock()
        }
    
    @pytest.fixture
    def onboarding_service(self, mock_dependencies):
        """Create onboarding service with mocked dependencies"""
        with patch('cataloging_service.domain.services.template.property_onboarding_service.service_provider') as mock_service_provider:
            mock_service_provider.property_service = Mock()
            mock_service_provider.meta_service = Mock()
            mock_service_provider.seller_service = Mock()
            mock_service_provider.sku_service = Mock()

            service = PropertyOnboardingService(**mock_dependencies)

            service.property_service = mock_service_provider.property_service
            service.meta_service = mock_service_provider.meta_service
            service.seller_service = mock_service_provider.seller_service
            service.sku_service = mock_service_provider.sku_service

            return service
    
    def test_validate_property_success(self, onboarding_service):
        """Test successful property validation"""
        mock_property = Mock()
        mock_property.id = "PROP123"
        onboarding_service.property_service.get_property.return_value = mock_property
        
        result = onboarding_service._validate_property("PROP123")
        assert result == mock_property
        onboarding_service.property_service.get_property.assert_called_once_with("PROP123")
    
    def test_validate_property_not_found(self, onboarding_service):
        """Test property validation when property not found"""
        onboarding_service.property_service.get_property.return_value = None
        
        with pytest.raises(ValueError, match="Property with ID 'INVALID' not found"):
            onboarding_service._validate_property("INVALID")
    
    def test_generate_seller_id(self, onboarding_service):
        """Test seller ID generation"""
        seller_id = onboarding_service._generate_seller_id("PROP123", "FRONTDESK")

        assert seller_id == "PROP123_FRONTDESK"
        assert len(seller_id) > 5  # Should have property and template code

    
    def test_onboard_property_success(self, onboarding_service):
        """Test successful property onboarding"""
        with patch('cataloging_service.domain.services.template.property_onboarding_service.logger'):
            mock_property = Mock()
            mock_property.legal_name = "Test Hotel Ltd"
            onboarding_service.property_service.get_property.return_value = mock_property
            onboarding_service.department_template_service.get_auto_create_templates.return_value = []
            onboarding_service.profit_center_template_service.get_auto_create_templates.return_value = []
            onboarding_service.property_department_service.get_departments_by_property.return_value = []

            request = PropertyOnboardingRequestSchema.model_validate({
                "property_id": "PROP123",
                "brand_id": 1,
                "auto_create_departments": True,
                "auto_create_profit_centers": True
            })

            result = onboarding_service.onboard_property(request)

            assert isinstance(result, PropertyOnboardingResponseSchema)
            assert result.property_id == "PROP123"
            assert result.brand_id == 1
            assert result.onboarding_status == "COMPLETED"
    
    def test_onboard_property_with_error(self, onboarding_service):
        """Test property onboarding with error"""
        onboarding_service.property_service.get_property.side_effect = Exception("Test error")
        
        request = PropertyOnboardingRequestSchema.model_validate({
            "property_id": "PROP123",
            "brand_id": 1
        })
        
        result = onboarding_service.onboard_property(request)
        
        assert isinstance(result, PropertyOnboardingResponseSchema)
        assert len(result.errors) > 0
        assert "Test error" in result.errors[0]


@pytest.mark.skipif(not IMPORTS_AVAILABLE, reason="Cataloging service imports not available")
class TestPropertyOnboardingIntegration:
    """Integration tests for property onboarding"""

    def test_request_response_schema_compatibility(self):
        """Test that request and response schemas are compatible"""
        request_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "auto_create_departments": True,
            "auto_create_profit_centers": True,
            "custom_config": {"test": "value"}
        }
        
        response_data = {
            "property_id": "PROP123",
            "brand_id": 1,
            "departments_created": 2,
            "profit_centers_created": 1,
            "onboarding_status": "COMPLETED",
            "errors": [],
            "warnings": [],
            "onboarded_at": datetime.now()
        }
        
        request_schema = PropertyOnboardingRequestSchema.model_validate(request_data)
        response_schema = PropertyOnboardingResponseSchema.model_validate(response_data)
        
        assert request_schema.property_id == response_schema.property_id
        assert request_schema.brand_id == response_schema.brand_id


class TestBasicFunctionality:
    """Basic tests that don't require cataloging service imports"""

    def test_mock_functionality(self):
        """Test that mocking works correctly"""
        mock_obj = Mock()
        mock_obj.test_method.return_value = "test_value"

        result = mock_obj.test_method()
        assert result == "test_value"
        mock_obj.test_method.assert_called_once()

    def test_datetime_functionality(self):
        """Test datetime functionality"""
        now = datetime.now()
        assert isinstance(now, datetime)
        assert now.year >= 2023

    def test_patch_functionality(self):
        """Test that patching works correctly"""
        with patch('datetime.datetime') as mock_datetime:
            mock_now = Mock()
            mock_now.isoformat.return_value = "2023-01-01T00:00:00"
            mock_datetime.now.return_value = mock_now

            result = mock_datetime.now().isoformat()
            assert result == "2023-01-01T00:00:00"
