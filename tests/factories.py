import factory

from cataloging_service.models import Menu, Property, RestaurantMenuCategory, MenuTiming, MenuItem, MenuCombo, MenuItem, \
    MenuItemCategory, MenuComboCategory, ComboItem, Combo, Item, Seller, SellerCategory, City, State, Country, ItemVariant, \
    SkuCategory, SideItem, ItemCustomisation, Variant, VariantGroup, RestaurantArea, RestaurantTable, TableSeat, \
    Kitchen, Property


class SkuCategoryFactory(factory.Factory):
    class Meta:
        model = SkuCategory


class CountryFactory(factory.Factory):

    name = "India"
    iso_code = "007"

    class Meta:
        model = Country


class StateFactory(factory.Factory):
    name = "Maharashtra"

    country = factory.SubFactory(CountryFactory)

    class Meta:
        model = State


class CityFactory(factory.Factory):
    name = "Mumbai"
    state = factory.SubFactory(StateFactory)

    class Meta:
        model = City


class SellerCategoryFactory(factory.Factory):
    name = "test_seller_category"
    code = "top_10"
    is_active = True

    class Meta:
        model = SellerCategory


class SellerFactory(factory.Factory):
    seller_id = "1"
    name = "test_seller"

    gstin = "121212121212"
    legal_name = "Test Seller"
    legal_address = "Kharghar"
    pincode = "410210"
    legal_signature = "test"
    legal_pincode = "410210"
    phone_number = "1232112312"
    status = "enabled"
    base_currency_code = "Rs"
    timezone = "GMT"

    seller_category = factory.SubFactory(SellerCategoryFactory)
    city = factory.SubFactory(CityFactory)

    class Meta:
        model = Seller


class ComboItemFactory(factory.Factory):
    combo_id = 1
    item_id = 1

    class Meta:
        model = ComboItem


class MenuComboFactory(factory.Factory):
    menu_id = 1
    combo_id = 1
    display_order = 0
    sold_out = False

    class Meta:
        model = MenuCombo


class ComboFactory(factory.Factory):
    name = "Pizza + Coke"
    description = "Pizza + coke combo"
    display_name = "Pizza + coke deluxe combo"
    prep_time = "00:20:00"
    contains_alcohol = False
    pre_tax_price = 500.0
    cost = 620.0
    sku_id = 222
    seller_id = 1

    class Meta:
        model = Combo


class ItemVariantFactory(factory.Factory):
    name = "Size"
    display_order = 0
    pre_tax_price = 50
    cost = 100
    sku_id = 1111

    class Meta:
        model = ItemVariant


class SideItemFactory(factory.Factory):
    class Meta:
        model = SideItem


class VariantGroupFactory(factory.Factory):
    name = "Crust"
    display_name = "Pizza crust"
    can_select_multiple = False
    can_select_quantity = True
    is_customisation = True

    class Meta:
        model = VariantGroup


class VariantFactory(factory.Factory):
    variant_group = factory.SubFactory(VariantGroupFactory)

    class Meta:
        model = Variant


class ItemCustomisationFactory(factory.Factory):
    cost = 20

    class Meta:
        model = ItemCustomisation


class ItemFactory(factory.Factory):
    name = "Pizza"
    description = "Great"
    sku_category_code = "food"
    display_name = "Margherita Neopolitan Pizza"
    print_name = "Basic cheese pizza"
    prep_time = "00:20:00"
    use_as_side = False
    contains_alcohol = False
    pre_tax_price = 450.0
    cost = 470
    sku_id = 921

    class Meta:
        model = Item


class MenuItemFactory(factory.Factory):
    menu_id = 1
    item_id = 1
    sold_out = False
    display_order = 0

    class Meta:
        model = MenuItem


class MenuCategoryFactory(factory.Factory):
    name = "South Indian Food"
    display_order = 0

    class Meta:
        model = RestaurantMenuCategory


class MenuTimingFactory(factory.Factory):
    days = b'["SUN", "MON"]'
    start_time = "20:00:00"
    end_time = "22:00:00"

    class Meta:
        model = MenuTiming


class MenuFactory(factory.Factory):
    name = "Breakfast Menu"
    description = "Daily breakfast menu"
    display_name = "breakfast"
    menu_types = ["restaurant"]

    menu_timings = factory.List([factory.SubFactory(MenuTimingFactory) for _ in range(2)])
    menu_categories = factory.List([factory.SubFactory(MenuCategoryFactory)])

    class Meta:
        model = Menu


class TableSeatFactory(factory.Factory):
    class Meta:
        model = TableSeat


class RestaurantTableFactory(factory.Factory):
    name = "RT-01"
    table_number = "1"
    current_status = "EMPTY"
    x_coordinate = 25
    y_coordinate = 25
    width = 50
    height = 60
    seats = factory.List([factory.SubFactory(TableSeatFactory) for _ in range(2)])

    class Meta:
        model = RestaurantTable


class RestaurantAreaFactory(factory.Factory):
    name = "Lawn"
    display_order = 1
    seller_id = "1"

    class Meta:
        model = RestaurantArea


class PropertyFactory(factory.Factory):
    id = "1"
    name = "Test Property"
    hx_id = "1"
    old_name = "test property"

    class Meta:
        model = Property


class KitchenFactory(factory.Factory):
    name = "Test kitchen"

    class Meta:
        model = Kitchen
