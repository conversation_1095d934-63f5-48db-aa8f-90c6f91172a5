import codecs
import inspect
import sys

import sadisplay

from cataloging_service import models

classes = inspect.getmembers(sys.modules[models.__name__], inspect.isclass)
classes = list(map(lambda c: c[1], classes))

desc = sadisplay.describe(
    classes,
    show_methods=False,
    show_properties=False,
    show_indexes=False,
)
with codecs.open('schema.dot', 'w', encoding='utf-8') as f:
    f.write(sadisplay.dot(desc))

# to generate png: dot -Tpng schema.dot -o schema.png

