*.py[cod]

# C extensions
*.so

# Packages
*.egg
*.egg-info
*venv
build
eggs
parts
bin
var
sdist
develop-eggs
.installed.cfg
lib
lib64
node_modules
__pycache__

# Installer logs
pip-log.txt

# Unit test / coverage reports
.coverage
.tox
nosetests.xml
cover/
coverage.xml

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject
.idea
*~
*.swp
*.swo
*#*
.DS*
*.rdb*
*.log*
pip-log.txt
hotels.log*

media
webapp/dist

# angular configs
angular/node_modules
angular/dist
angular/.tmp
angular/.sass-cache
angular/app/bower_components
angular/app/styles/main.css

src/*
webapp/conf/local.py
*.sublime-*
*.dot
htmlcov/
instance/*
.vscode/*
cs.code-workspace
.env
runserver.py
